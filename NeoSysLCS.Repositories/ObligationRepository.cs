using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Web;
using System.Xml.Linq;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Repositories
{
    public class ObligationRepository
    {
        private readonly IGenericRepository<Obligation> _genericObligationRepository;
        private readonly IGenericRepository<Forderungsversion> _genericForderungsverionenRepository;
        private readonly UnitOfWork _unitOfWork;
        private int _currentLang;
        private readonly NeoSysLCS_Dev _context;

        public ObligationRepository(NeoSysLCS_Dev context)
        {
            _context = context;
            _unitOfWork = new UnitOfWork();
            _genericObligationRepository = new GenericRepository<Obligation>(context);
            _genericForderungsverionenRepository = new GenericRepository<Forderungsversion>(context);

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
            _currentLang = sprache.SpracheID;
        }

        /// <summary>
        /// Gets the obligation by id
        /// </summary>
        /// <param name="id">The id.</param>
        /// <returns>Returns the Obligation</returns>
        public ObligationViewModel GetByID(object id)
        {
            var obligation = _genericObligationRepository.GetByID(id);
            ObligationViewModel vm = new ObligationViewModel();
            vm.ObligationUebersetzungID = GetIdOfObligationTranslation(obligation.ObligationID, _currentLang);
            vm.SpracheID = _currentLang;
            vm.ObjektID = obligation.ObjektID;
            vm.ObligationID = obligation.ObligationID;
            vm.Name = ResolveObligationName(obligation);
            vm.Forderungsversions = obligation.Forderungsversions.AsQueryable();
            vm.ForderungsversionsCount = obligation.Forderungsversions.Count();
            return vm;
        }

        /// <summary>
        /// Inserts the specified Obligation.
        /// </summary>
        /// <param name="entity">The entity.</param>
        public void Insert(ObligationViewModel viewModel)
        {
            Obligation entity = new Obligation();
            entity.ObjektID = viewModel.ObjektID;
            switch (_currentLang)
            {
                case 1:
                    entity.NameDE = viewModel.Name;
                    break;
                case 2:
                    entity.NameFR = viewModel.Name;
                    break;
                case 3:
                    entity.NameIT = viewModel.Name;
                    break;
                case 4:
                    entity.NameEN = viewModel.Name;
                    break;
                default:
                    //entity.NameDE = viewModel.Name;
                    break;

            }
            _genericObligationRepository.Insert(entity);
        }

        /// <summary>
        /// Updates the obligation from the specified view model.
        /// </summary>
        /// <param name="viewModel">The view model.</param>
        public void Update(ObligationViewModel viewModel)
        {
            Obligation obligation = _genericObligationRepository.GetByID(viewModel.ObligationID);

            switch (_currentLang)
            {
                case 1:
                    obligation.NameDE = viewModel.Name;
                    break;
                case 2:
                    obligation.NameFR = viewModel.Name;
                    break;
                case 3:
                    obligation.NameIT = viewModel.Name;
                    break;
                case 4:
                    obligation.NameEN = viewModel.Name;
                    break;
                default:
                    //obligation.NameDE = viewModel.Name;
                    break;

            }

            _genericObligationRepository.Update(obligation);
        }


        /// <summary>
        /// Deletes the specified entity.
        /// </summary>
        /// <param name="entityToDelete">The entity to delete.</param>
        public void Delete(int obligationID)
        {
            _genericObligationRepository.Delete(obligationID);
        }

        public IQueryable<ObligationViewModel> GetObligationViewModelsByObjektId(int objektId)
        {

            var obligations = (from x in _context.Obligations
                                where x.ObjektID == objektId
                                select new ObligationViewModel()
                                {
                                    ObligationID = x.ObligationID,
                                    ObjektID = x.ObjektID,
                                    SpracheID = _currentLang,
                                    Name = (_currentLang == 1 ? (x.NameDE ?? x.NameFR ?? x.NameIT ?? x.NameEN) : null) ??
                                              (_currentLang == 2 ? (x.NameFR ?? x.NameDE ?? x.NameIT ?? x.NameEN) : null) ??
                                              (_currentLang == 3 ? (x.NameIT ?? x.NameDE ?? x.NameFR ?? x.NameEN) : null) ??
                                              (_currentLang == 4 ? (x.NameEN ?? x.NameDE ?? x.NameFR ?? x.NameIT) : null),
                                    Forderungsversions = x.Forderungsversions.AsQueryable(),
                                    ForderungsversionsCount = x.Forderungsversions.Count()
                                }).ToList();

            foreach (ObligationViewModel obligation in obligations)
            {
                if (obligation.ForderungsversionsCount != 0)
                {
                    DateTime lastChange = obligation.Forderungsversions.ToList().OrderBy(o => o.Inkrafttretung).Select(x => x.Inkrafttretung).FirstOrDefault();
                    obligation.LastChange = lastChange;
                }
            }

            return obligations.AsQueryable();
        }

        /// <summary>
        /// Gets the obligation uebersetzungen by id.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>Returns the viewmodel with uebersetzungen</returns>
        public IQueryable<ObligationViewModel> GetTranslatedViewModelsBy(int id)
        {
            Obligation obligation = _genericObligationRepository.GetByID(id);

            List<int> sprachIds = _context.Sprachen.Select(x => x.SpracheID).ToList();
            List<ObligationViewModel> translatedObligations = new List<ObligationViewModel>();

            foreach (int sprachId in sprachIds)
            {
                ObligationViewModel translatedObligation = new ObligationViewModel();
                translatedObligation.ObligationUebersetzungID = GetIdOfObligationTranslation(id, sprachId);
                translatedObligation.SpracheID = sprachId;
                translatedObligation.ObjektID = obligation.ObjektID;
                translatedObligation.ObligationID = obligation.ObligationID;
                translatedObligation.Forderungsversions = obligation.Forderungsversions.AsQueryable();
                translatedObligation.ForderungsversionsCount = obligation.Forderungsversions.Count();
                switch (sprachId)
                {
                    case 1:
                        translatedObligation.Name = obligation.NameDE;
                        break;
                    case 2:
                        translatedObligation.Name = obligation.NameFR;
                        break;
                    case 3:
                        translatedObligation.Name = obligation.NameIT;
                        break;
                    case 4:
                        translatedObligation.Name = obligation.NameEN;
                        break;
                    default:
                        //obligation.NameDE = vm.Name;
                        break;

                }
                translatedObligations.Add(translatedObligation);

            }

            return translatedObligations.AsQueryable();
        }

        public void InsertUebersetzungObligation(ObligationViewModel vm, int obligationID)
        {
            Obligation obligation = _genericObligationRepository.GetByID(obligationID);

            switch (vm.SpracheID)
            {
                case 1:
                    obligation.NameDE = vm.Name;
                    break;
                case 2:
                    obligation.NameFR = vm.Name;
                    break;
                case 3:
                    obligation.NameIT = vm.Name;
                    break;
                case 4:
                    obligation.NameEN = vm.Name;
                    break;
                default:
                    //obligation.NameDE = vm.Name;
                    break;

            }

            _genericObligationRepository.Update(obligation);
        }


        public void SaveObligationForderungsversions(string selectedIDs, Int32 obligationID)
        {
            Obligation obligation = _genericObligationRepository.Get(k => k.ObligationID == obligationID, null, "Forderungsversions").FirstOrDefault();

            string[] ids = selectedIDs.Split(',');

            if (selectedIDs != "")
            {
                foreach (var id in ids)
                {
                    Forderungsversion forderungsversion = _genericForderungsverionenRepository.GetByID(Convert.ToInt32(id));
                    if (!obligation.Forderungsversions.Contains(forderungsversion))
                    {
                        obligation.Forderungsversions.Add(forderungsversion);
                    }
                }
            }
            foreach (var forderungsversion in obligation.Forderungsversions.ToArray())
            {
                if (!ids.ToList().Contains(forderungsversion.ForderungsversionID.ToString()))
                {
                    obligation.Forderungsversions.Remove(forderungsversion);
                }
            }
            if (obligation.Forderungsversions.Count == 0)
            {
                obligation.Forderungsversions = null;
            }
        }

        private string ResolveObligationName(Obligation obligation)
        {
            var name = (_currentLang == 1
                       ? (obligation.NameDE ?? obligation.NameFR ??
                           obligation.NameIT ?? obligation.NameEN)
                       : null) ??
                   (_currentLang == 2
                       ? (obligation.NameFR ?? obligation.NameDE ??
                           obligation.NameIT ?? obligation.NameEN)
                       : null) ??
                   (_currentLang == 3
                       ? (obligation.NameIT ?? obligation.NameDE ??
                           obligation.NameFR ?? obligation.NameEN)
                       : null) ??
                   (_currentLang == 4
                       ? (obligation.NameEN ?? obligation.NameDE ??
                           obligation.NameFR ?? obligation.NameIT)
                       : null);
            return name;
        }

        private string GetIdOfObligationTranslation(int obligationID, int langID)
        {
            return obligationID.ToString() + "_" + langID.ToString();
        }

        public IQueryable<ObligationViewModel> GetObligationViewModels()
        {

            var obligations = (from x in _context.Obligations
                               select new ObligationViewModel()
                               {
                                   ObligationID = x.ObligationID,
                                   ObjektID = x.ObjektID,
                                   SpracheID = _currentLang,
                                   Name = (_currentLang == 1 ? (x.NameDE ?? x.NameFR ?? x.NameIT ?? x.NameEN) : null) ??
                                             (_currentLang == 2 ? (x.NameFR ?? x.NameDE ?? x.NameIT ?? x.NameEN) : null) ??
                                             (_currentLang == 3 ? (x.NameIT ?? x.NameDE ?? x.NameFR ?? x.NameEN) : null) ??
                                             (_currentLang == 4 ? (x.NameEN ?? x.NameDE ?? x.NameFR ?? x.NameIT) : null),
                                   Forderungsversions = x.Forderungsversions.AsQueryable(),
                                   ForderungsversionsCount = x.Forderungsversions.Count()
                               }).ToList();

            foreach (ObligationViewModel obligation in obligations)
            {
                if (obligation.ForderungsversionsCount != 0)
                {
                    DateTime lastChange = obligation.Forderungsversions.ToList().OrderBy(o => o.Inkrafttretung).Select(x => x.Inkrafttretung).FirstOrDefault();
                    obligation.LastChange = lastChange;
                }
            }

            return obligations.AsQueryable();
        }

    }
}