using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.Interfaces;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Repositories
{
    /// <summary>
    /// The repository of pflichten
    /// </summary>
    public class PflichtRepository : IReloadable<Pflicht>
    {
        private readonly IGenericRepository<Pflicht> _genericPflichtRepository;
        private readonly IGenericRepository<Objekt> _genericObjektRepository;
        private readonly IGenericRepository<Standort> _genericStandortRepository;
        private readonly IGenericRepository<StandortObjekt> _genericStandortObjektRepository;
        private readonly IGenericRepository<KundendokumentPflicht> _genericKundendokumentPflichtRepository;
        private readonly UnitOfWork _unitOfWork;
        private readonly NeoSysLCS_Dev _context;
        private readonly int _currentLang;

        /// <summary>
        /// Initializes a new instance.
        /// </summary>
        /// <param name="contextCandidate">The context candidate.</param>
        /// <exception cref="System.ArgumentNullException">contextCandidate</exception>
        public PflichtRepository(NeoSysLCS_Dev contextCandidate)
        {
            _genericPflichtRepository = new GenericRepository<Pflicht>(contextCandidate);
            _genericObjektRepository = new GenericRepository<Objekt>(contextCandidate);
            _genericStandortRepository = new GenericRepository<Standort>(contextCandidate);
            _genericStandortObjektRepository = new GenericRepository<StandortObjekt>(contextCandidate);
            _genericKundendokumentPflichtRepository = new GenericRepository<KundendokumentPflicht>(contextCandidate);
            _unitOfWork = new UnitOfWork(contextCandidate);

            _context = contextCandidate;

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
            _currentLang = sprache.SpracheID;
        }

        /// <summary>
        /// Gets the pflicht by id.
        /// </summary>
        /// <param name="id">The id.</param>
        /// <returns>Returns the pflicht</returns>
        public Pflicht GetByID(object id)
        {
            Pflicht pflicht =
               _genericPflichtRepository.Get(
               e => e.PflichtID == (int)id,
               null,
               "Objekte"
               ).FirstOrDefault();

            return pflicht;
        }

        /// <summary>
        /// Inserts the pflicht from the specified view model.
        /// </summary>
        /// <param name="viewModel">The view model.</param>
        public void Insert(PflichtViewModel viewModel)
        {
            var lang = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            var sprache = _context.Sprachen.FirstOrDefault(s => s.Lokalisierung == lang);

            var pflicht = new Pflicht();
            pflicht.ErlassfassungID = viewModel.ErlassfassungID;
            pflicht.GueltigBis = viewModel.GueltigBis;
            pflicht.GueltigVon = viewModel.GueltigVon;
            pflicht.ErstelltAm = DateTime.Now;
            pflicht.ErstelltVonID = HttpContext.Current.User.Identity.GetUserId();
            var userID = HttpContext.Current.User.Identity.GetUserId();
            if (viewModel.Freigabe == true)
            {
                pflicht.SetFreigabe(true, userID);
            }
            else
            {
                pflicht.SetFreigabe(false, null);
            }

            if (viewModel.QsFreigabe == true)
            {
                pflicht.SetQsFreigabe(true, userID);
            }
            else
            {
                pflicht.SetQsFreigabe(false, null);
            }

            //populate other languages
            foreach (var s in _context.Sprachen)
            {
                if (s.Lokalisierung != lang)
                {
                }
            }

            _genericPflichtRepository.Insert(pflicht);
        }

        /// <summary>
        /// Updates the pflicht from the specified view model.
        /// </summary>
        /// <param name="viewModel">The view model.</param>
        public void Update(PflichtViewModel viewModel)
        {
            var lang = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;

            Pflicht pflicht = _genericPflichtRepository.GetByID(viewModel.PflichtID);
            pflicht.PflichtID = viewModel.PflichtID;
            pflicht.GueltigBis = viewModel.GueltigBis;
            pflicht.GueltigVon = viewModel.GueltigVon;
            pflicht.BearbeitetAm = DateTime.Now;
            pflicht.BearbeitetVonID = HttpContext.Current.User.Identity.GetUserId();

            var userID = HttpContext.Current.User.Identity.GetUserId();
            if (viewModel.Freigabe == true)
            {
                pflicht.SetFreigabe(true, userID);
            }
            else
            {
                pflicht.SetFreigabe(false, null);
            }

            if (viewModel.QsFreigabe == true)
            {
                pflicht.SetQsFreigabe(true, userID);
            }
            else
            {
                pflicht.SetQsFreigabe(false, null);
            }

            _genericPflichtRepository.Update(pflicht);
        }

        /// <summary>
        /// Deletes the pflicht by the specified id.
        /// </summary>
        /// <param name="id">The id.</param>
        public void Delete(int id)
        {
            Pflicht pflicht = _genericPflichtRepository.Get(
                e => e.PflichtID == id, null, "Objekte"
            ).FirstOrDefault();

            if (pflicht != null)
            {
                pflicht.Objekte.Clear();
                _genericPflichtRepository.Update(pflicht);
            }
           
            _genericPflichtRepository.Delete(id);
        }

        /// <summary>
        /// Reloads the specified entity from the database.
        /// </summary>
        /// <param name="entityToReload">The entity to reload.</param>
        public void Reload(Pflicht entityToReload)
        {
            _genericPflichtRepository.Reload(entityToReload);
        }

        /// <summary>
        /// Gets all feasible pflichten view models by standort.
        /// </summary>
        /// <param name="standortId">The standort id.</param>
        /// <returns>Returns the view models</returns>
        public IQueryable<KundendokumentPflichtViewModel> GetAllFeasiblePflichtenViewModelsByStandort(int standortId)
        {
            return from x in _context.Pflichten
                from ob in x.Objekte
                join st in _context.StandortObjekte on ob.ObjektID equals st.ObjektID
                join sta in _context.Standorte on st.StandortID equals sta.StandortID
                where (sta.StandortID == standortId
                       && st.StandortID == standortId
                       && (x.GueltigBis > DateTime.Now || x.GueltigBis == null)
                       && x.QsFreigabe == true
                       && x.Objekte.Any(o => o.ObjektID == st.ObjektID)
                       && sta.Herausgeber.Contains(x.Erlassfassung.Erlass.Herausgeber))
                select new KundendokumentPflichtViewModel()
                {
                    PflichtID = x.PflichtID,
                    ErlassID = x.Erlassfassung.ErlassID,
                    GueltigBis = x.GueltigBis,
                    GueltigVon = x.GueltigVon,
                    ErstelltAm = x.ErstelltAm,
                    ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                    ErstelltVonID = x.ErstelltVonID,
                    BearbeitetAm = x.BearbeitetAm,
                    BearbeitetVonID = x.BearbeitetVonID,
                    BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                };

        }

        /// <summary>
        /// Gets all pflichten view models by erlassfassung id.
        /// </summary>
        /// <param name="erlassFassungId">The erlassfassung id.</param>
        /// <returns>Returns the view models</returns>
        public IQueryable<PflichtViewModel> GetAllPflichtViewModelsByErlassfassungId(int erlassFassungId)
        {
            return from x in _context.Pflichten
                where (x.ErlassfassungID == erlassFassungId)
                select new PflichtViewModel()
                {
                    PflichtID = x.PflichtID,
                    ErlassfassungID = x.ErlassfassungID,
                    GueltigBis = x.GueltigBis,
                    GueltigVon = x.GueltigVon,
                    ErlassID = x.Erlassfassung.ErlassID,
                    ErlassSrNummer = x.Erlassfassung.Erlass.SrNummer ?? "",
                    Freigabe = x.Freigabe,
                    QsFreigabe = x.QsFreigabe,
                    ErstelltAm = x.ErstelltAm,
                    ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                    ErstelltVonID = x.ErstelltVonID,
                    BearbeitetAm = x.BearbeitetAm,
                    BearbeitetVonID = x.BearbeitetVonID,
                    BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                };
        }

        /// <summary>
        /// Saves the objects asignements to the specified pflicht.
        /// </summary>
        /// <param name="selectedIDs">The selected ids.</param>
        /// <param name="pflichtID">The pflicht id.</param>
        public void SaveObjects(string selectedIDs, string pflichtID)
        {
            //Get all selected keys from e.customArgs on GridView callback

            int pfliID = Convert.ToInt32(pflichtID);

            Pflicht pflicht = _genericPflichtRepository.Get(e => e.PflichtID == pfliID, null, "Objekte").FirstOrDefault();

            //pflicht.Objekte.Clear();

            string[] ids = selectedIDs.Split(',');

            if (selectedIDs != "")
            {
                foreach (var id in ids)
                {
                    Objekt obj = _genericObjektRepository.GetByID(Convert.ToInt32(id));
                    if (!pflicht.Objekte.Contains(obj))
                    {
                        pflicht.Objekte.Add(obj);
                    }
                }
            }
            foreach (var pfo in pflicht.Objekte.ToArray())
            {
                if (!ids.ToList().Contains(pfo.ObjektID.ToString()))
                {
                    pflicht.Objekte.Remove(pfo);
                }
            }
            if (pflicht.Objekte.Count == 0)
            {
                pflicht.Objekte = null;
            }


            _genericPflichtRepository.Update(pflicht);

        }

        /// <summary>
        /// Gets the selected standort objekt pflichten view models by standort.
        /// </summary>
        /// <param name="standortId">The standort id.</param>
        /// <param name="ids">The ids.</param>
        /// <param name="kundendokumentId">The kundendokument id.</param>
        /// <returns>Returns the view models</returns>
        public IQueryable<KundendokumentStandortobjektPflichtViewModel> GetSelectedStandortObjektPflichtenViewModelsByStandort(int standortId, ISet<int> ids, int? kundendokumentId = null)
        {
            return from x in _context.KundendokumentPflichten
                   join tx in _context.Pflichten on x.PflichtID equals tx.PflichtID
                   where(x.KundendokumentID == kundendokumentId && x.PflichtID == tx.PflichtID)
                select new KundendokumentStandortobjektPflichtViewModel()
                {
                    ID = x.StandortObjektID + "_" + x.PflichtID,
                    PflichtID = x.PflichtID,
                    StandortObjektID = x.StandortObjektID,
                    ErlassID = tx.Erlassfassung.ErlassID,
                    ErlassfassungID = tx.ErlassfassungID,
                    GueltigBis = tx.GueltigBis,
                    GueltigVon = tx.GueltigVon,
                    Kundenbezug = x.Kundenbezug,
                };
        }

        /// <summary>
        /// Gets all erlassfasungsId by specific pflichten.
        /// </summary>
        /// <param name="ids">The ids.</param>
        /// <returns>Returns the erlassfassungids</returns>
        public IEnumerable<Int32> GetPflichtenIds(ISet<int> ids)
        {
            var erlassfassungsIds = new List<Int32>();
            var pflichten = _genericPflichtRepository.Get(p => ids.Contains(p.PflichtID), null, null);
            foreach (var pflicht in pflichten)
            {
                erlassfassungsIds.Add(pflicht.ErlassfassungID);
            }

            return erlassfassungsIds;
        }


        /// <summary>
        /// Gets all published standort objekt pflichten view models by standort.
        /// </summary>
        /// <param name="standortId">The standort identifier.</param>
        /// <returns>Returns the view models</returns>
        public IQueryable<KundendokumentStandortobjektPflichtViewModel> GetAllPublishedStandortObjektPflichtenViewModelsByStandort(int standortId)
        {
            return from x in _context.Pflichten
                   from st in _context.StandortObjekte
                   where (st.StandortID == standortId && x.Objekte.Any(o => o.ObjektID == st.ObjektID)
                            && x.QsFreigabe.HasValue && x.QsFreigabe.Value)
                   select new KundendokumentStandortobjektPflichtViewModel()
                   {
                       ID = st.StandortObjektID + "_" + x.PflichtID,
                       PflichtID = x.PflichtID,
                       StandortObjektID = st.StandortObjektID,
                       ErlassID = x.Erlassfassung.ErlassID,
                       ErlassfassungID = x.ErlassfassungID,
                       GueltigBis = x.GueltigBis,
                       GueltigVon = x.GueltigVon,
                   };
        }

        /// <summary>
        /// Gets the pflichten by object.
        /// </summary>
        /// <param name="objectID">The object id.</param>
        /// <returns>Returns the view models</returns>
        public IQueryable<PflichtViewModel> GetByObject(int objectID)
        {
            return (from x in _context.Pflichten
                   where (x.Objekte.Any(o => o.ObjektID == objectID) && x.Freigabe.Value && x.QsFreigabe.Value)
                   select new PflichtViewModel()
                   {
                       PflichtID = x.PflichtID,
                       ErlassfassungID = x.ErlassfassungID,
                       GueltigBis = x.GueltigBis,
                       GueltigVon = x.GueltigVon,
                       ErlassID = x.Erlassfassung.ErlassID,
                       ErlassSrNummer = x.Erlassfassung.Erlass.SrNummer ?? "",
                       Freigabe = x.Freigabe,
                       QsFreigabe = x.QsFreigabe,
                       ErstelltAm = x.ErstelltAm,
                       ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                       ErstelltVonID = x.ErstelltVonID,
                       BearbeitetAm = x.BearbeitetAm,
                       BearbeitetVonID = x.BearbeitetVonID,
                       BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                   }).OrderBy(p => p.PflichtID);

        }

        /// <summary>
        /// Gets all pflichten view models.
        /// </summary>
        /// <returns>TReturns the view models</returns>
        public IQueryable<PflichtViewModel> GetAllViewModels()
        {
            return from x in _context.Pflichten
                where(x.Freigabe.Value && x.QsFreigabe.Value)
                select new PflichtViewModel()
                {
                    PflichtID = x.PflichtID,
                    ErlassfassungID = x.ErlassfassungID,
                    GueltigBis = x.GueltigBis,
                    GueltigVon = x.GueltigVon,
                    ErlassID = x.Erlassfassung.ErlassID,
                    ErlassSrNummer = x.Erlassfassung.Erlass.SrNummer ?? "",
                    Freigabe = x.Freigabe,
                    QsFreigabe = x.QsFreigabe,
                    ErstelltAm = x.ErstelltAm,
                    ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                    ErstelltVonID = x.ErstelltVonID,
                    BearbeitetAm = x.BearbeitetAm,
                    BearbeitetVonID = x.BearbeitetVonID,
                    BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                };
        }
    }
}