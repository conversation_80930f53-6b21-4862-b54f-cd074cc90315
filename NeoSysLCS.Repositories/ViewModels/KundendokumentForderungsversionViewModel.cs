using System;
using System.ComponentModel.DataAnnotations;
using NeoSysLCS.DomainModel.Models;
using System.Linq;
using System.Collections.Generic;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class KundendokumentForderungsversionViewModel : BaseViewModel
    {

        public int KundendokumentForderungsversionID { get; set; }
        public string ID { get; set; }
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderung_Singular")]
        public int ForderungID;

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderung_Singular")]
        public int ForderungsversionID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Singular")]
        public int ErlassID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Singular")]
        public string ErlassTitel { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Singular")]
        public string ErlassTitelFR { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Singular")]
        public string ErlassTitelIT { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Singular")]
        public string ErlassTitelEN { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlassfassung_Singular")]
        public int ErlassfassungID { get; set; }

        public string ErlassQuelle { get; set; }
        public string ErlassQuelleFR { get; set; }
        public string ErlassQuelleIT { get; set; }
        public string ErlassQuelleEN { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Kundendokument_Singular")]
        public int KundendokumentID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_StandortObjekt_Singular")]
        public int StandortObjektID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_StandortObjekt_Singular")]
        public string StandortObjektTitel { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_StandortObjekt_Singular")]
        public string StandortObjektTitelFR { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_StandortObjekt_Singular")]
        public string StandortObjektTitelIT { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_StandortObjekt_Singular")]
        public string StandortObjektTitelEN { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Artikel_Singular")]
        public string ArtikelNummer { get; set; }

        public string ArtikelQuelle { get; set; }

        public string ArtikelQuelleFR { get; set; }
        public string ArtikelQuelleIT { get; set; }
        public string ArtikelQuelleEN { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Kundenbezug_Singular")]
        public string Kundenbezug { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderungsversion_Version")]
        public int VersionsNummer { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderungsversion_Inkrafttretung")]
        public DateTime Inkrafttretung { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderungsversion_Aufhebung")]
        public DateTime? Aufhebung { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderungsversion_InternerKommentar")]
        public string InternerKommentar { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderungsversion_Beschreibung")]
        public string Beschreibung { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderungsversion_Beschreibung")]
        public string BeschreibungFR { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderungsversion_Beschreibung")]
        public string BeschreibungIT { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderungsversion_Beschreibung")]
        public string BeschreibungEN { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderungsversion_Nachweispflicht_Kurz")]
        public bool Nachweispflicht { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderungsversion_Bewilligungspflicht_Kurz")]
        public bool Bewilligungspflicht { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_Kommentar")]
        public string Kommentar { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_Erfuellung")]
        public KundendokumentErfuellung Erfuellung { get; set; }

        public string Massnahme { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt")]
        public DateTime? LetzterPruefZeitpunkt { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_NaechstePruefungAm")]
        public DateTime? NaechstePruefungAm { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_Pruefmethode")]
        public string Pruefmethode { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_Verantwortlich")]
        public string Verantwortlich { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_Relevant")]
        public bool Relevant { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_Ablageort")]
        public string Ablageort { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_QsFreigabe")]
        public bool QsFreigabe { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_Status")]
        public KundendokumentItemStatus Status { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_SrNummer")]
        public string SrNummer { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_Spalte1")]
        public string Spalte1 { get; set; }
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_Spalte2")]
        public string Spalte2 { get; set; }
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_Spalte3")]
        public string Spalte3 { get; set; }
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_Spalte4")]
        public string Spalte4 { get; set; }
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_Spalte5")]
        public string Spalte5 { get; set; }
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_Spalte6")]
        public string Spalte6 { get; set; }
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_Spalte7")]
        public string Spalte7 { get; set; }
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_Spalte8")]
        public string Spalte8 { get; set; }
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_Spalte9")]
        public string Spalte9 { get; set; }
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_Spalte10")]
        public string Spalte10 { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Rechtsbereich_Plural")]
        public IQueryable<RechtsbereichViewModel> Rechtsbereiche { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Rechtsbereich_Plural")]
        //public IQueryable<RechtsbereichViewModel> RechtsbereicheFR { get; set; }
        public IQueryable<RechtsbereichViewModel> RechtsbereicheFR { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Rechtsbereich_Plural")]
        public IQueryable<RechtsbereichViewModel> RechtsbereicheIT { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Rechtsbereich_Plural")]
        public IQueryable<RechtsbereichViewModel> RechtsbereicheEN { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Objektkategorie_Singular")]
        public IQueryable<ObjektkategorieViewModel> Objektkategorie { get; set; }
        public string ErlassSrNummer { get; set; }
        public DateTime ErlassfassungInkraftretung { get; set; }
        public DateTime? ErlassfassungBearbeitetAm { get; set; }
        public int ShortcutID { get; set; }
        public string Shortcut { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Abkuerzung")]
        public string Abkuerzung { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderung_Handlungsprio")]
        public string Handlungsprioritaet { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_ReviewDate")]
        public DateTime ReviewDatum { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderung_Anwendungsbereich")]
        public string Anwendungsbereich { get; set; }

        public string Rechtspflicht { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_VerantwortlicheStellen")]
        public string Stellen { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderung_StatusReview")]
        public string StatusReview { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderung_AktuelleHandlungsprio")]
        public string AktuelleHandlungsPrio { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderungsversion_Changed")]
        public string DurchgefuehrtVon { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderung_ReviewBemerkung")]
        public string ReviewBemerkung { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderung_NoRelevance")]
        public string NoRelevance { get; set; }

        public string ExportStatus { get; set; }
        public string NachweispflichtText { get; set; }
        public string BewilligungspflichtText { get; set; }
        public string stdObjUebersetzungen { get; set; }
        public string erlUebersetzungen { get; set; }
        public int rechtsbereichCount { get; set; }
        public int objekteCount { get; set; }
        public string fvUbersetzungen { get; set; }
        public Erlass Erlass { get; set; }
        public int ArtikelID { get; set; }
        public int RechtsnormID { get; set; }
        public int MassnahmeNewCount { get; set; }
        public int MassnahmeInProgressCount { get; set; }
        public int MassnahmeFinishedCount { get; set; }
    }
}