using System;
using System.Linq;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class ConsultationViewModel : BaseViewModel
    {
        public ConsultationViewModel()
        {

        }

        public int ConsultationId { get; set; }
        public string Title { get; set; }
        public ConsultationStatus Status { get; set; }
        public DateTime EntryDate { get; set; }
        public DateTime? OpenedDate { get; set; }
        public DateTime? Deadline { get; set; }
        public DateTime? CompletedDate { get; set; }
        public IQueryable<Erlass> ErlasseCollection { get; set; }
        public int ErlasseCount { get; set; }
        public IQueryable<ErlassViewModel> Erlasse { get; set; }
        public string ConsultationUebersetzungID { get; set; }
        public string Uebersetzung { get; set; }
        public int SpracheID { get; set; }
        public string Quelle { get; set; }
        public IQueryable<StandortViewModel> Standorte { get; set; }
        public string StandortIDs { get; set; }
        public int MassnahmeNewCount { get; set; }
        public int MassnahmeInProgressCount { get; set; }
        public int MassnahmeFinishedCount { get; set; }
    }
}