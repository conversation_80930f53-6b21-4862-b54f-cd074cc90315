
using System;
using System.ComponentModel.DataAnnotations;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class MassnahmeViewModel : BaseViewModel
    {
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Massnahme_Singular")]
        public int MassnahmeID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Massnahme_Betreff")]
        public string Betreff { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Massnahme_Bemerkung")]
        public string Bemerkung { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Massnahme_Singular")]
        public string MassnahmeText { get; set; }

        public string Uebersetzung { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Massnahme_Status")]
        public MassnahmeStatus Status { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Massnahme_Termin")]
        public DateTime? Termin { get; set; }

        public int? StandortID { get; set; }

        public string StandortName { get; set; }

        public int ShortcutID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentForderungsversion_Verantwortlich")]
        public string Shortcut { get; set; }

        public string MassnahmeUebersetzungID { get; set; }
        
        public int SpracheID { get; set; }

        public int KundeMassnahmeID { get; set; }
        public string Link { get; set; }
        public string LinkDe { get; set; }
        public string LinkFr { get; set; }
        public string LinkIt { get; set; }
        public string LinkEn { get; set; }
        public int? AritkelID { get; set; }
        public int OriginID { get; set; }
        public int KundeID { get; set; }
        public bool EmailNotification { get; set; }
    }
}