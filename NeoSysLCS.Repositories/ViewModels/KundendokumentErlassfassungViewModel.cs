using System;
using System.ComponentModel.DataAnnotations;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class KundendokumentErlassfassungViewModel : BaseViewModel
    {
        public int KundendokumentErlassfassungID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentErlassfassung_Betroffen")]
        public bool Betroffen { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentErlassfassung_QSFreigabe")]
        public bool QsFreigabe { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_KundendokumentErlassfassung_Status")]
        public KundendokumentItemStatus Status { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Kundendokument_Singular")]
        public int KundendokumentID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlassfassung_Singular")]
        public int ErlassfassungID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Herausgeber_Singular")]
        public int HerausgeberID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Herausgeber_Singular")]
        public string HerausgeberName { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Singular")]
        public int ErlassID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlassfassung_BetroffenKommentar")]
        public string BetroffenKommentar { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlassfassung_NichtBetroffenKommentar")]
        public string NichtBetroffenKommentar { get; set; }

        public string RelevanterKommentar { get; set; }

        public string Massnahme { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlassfassung_Beschluss")]
        public DateTime Beschluss { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlassfassung_Inkrafttretung")]
        public DateTime Inkrafttretung { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Quelle")]
        public string Quelle { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Titel")]
        public string ErlassTitel { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Abkuerzung")]
        public string ErlassAbkuerzung { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_SrNummer")]
        public string ErlassSrNummer { get; set; }

        public string ErlassQuelle { get; set; }
        public string Kommentar { get; set; }
        public string Erlasstyp { get; set; }
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Kerninhalte")]
        public string Kerninhalte { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Sachgebiete")]
        public string Sachgebiete { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_VerantwortlicheStellen")]
        public string Stellen { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Erscheinungsort")]
        public string Erscheinungsort { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_ErschienenAm")]
        public DateTime Erscheinungsdatum { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_GesetzlicheAenderung")]
        public DateTime LastChange { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Norm")]
        public string Norm { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Seit")]
        public DateTime NormSeit { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Ersetzt")]
        public string ErsetztDurch { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Bemerkung")]
        public string Bemerkung { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Anwendungsbereiche")]
        public string Anwendungsbereiche { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Rechtsnormen")]
        public string Rechtsnormen { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Quellenbezeichnung")]
        public string Quellenbezeichnung { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_ReviewDate")]
        public DateTime ReviewDatum { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_NoRelevance")]
        public string NoRelevance { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Durchfuehrung")]
        public string Durchfuehrung { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Stammfassung")]
        public string Stammfassung { get; set; }

        // Additional Fields for export
        public int OrderID { get; set; }
        public string BeschlussExport { get; set; }
        public string BeschlussQuelle { get; set; }
        public string BeschlussQuelleFR { get; set; }
        public string BeschlussQuelleIT { get; set; }
        public string BeschlussQuelleEN { get; set; }
        public string InkraftretenExport { get; set; }
        public string InkraftretenExportFR { get; set; }
        public string InkraftretenExportIT { get; set; }
        public string InkraftretenExportEN { get; set; }
        public string InkraftretenQuelle { get; set; }
        public string InkraftretenQuelleFR { get; set; }
        public string InkraftretenQuelleIT { get; set; }
        public string InkraftretenQuelleEN { get; set; }
        public string RelevanterKommentarLinkText { get; set; }
        public string ErlassQuelleLinkText { get; set; }
        public string ErlassTitelFR { get; set; }
        public string ErlassTitelIT { get; set; }
        public string ErlassTitelEN { get; set; }
        public string ErlassQuelleFR { get; set; }
        public string ErlassQuelleIT { get; set; }
        public string ErlassQuelleEN { get; set; }
        public string RelevanterKommentarFR { get; set; }
        public string RelevanterKommentarIT { get; set; }
        public string RelevanterKommentarEN { get; set; }
        public string ErlassSrNummerFR { get; set; }
        public string ErlassSrNummerIT { get; set; }
        public string ErlassSrNummerEN { get; set; }
        public string BeschlussExportFR { get; set; }
        public string BeschlussExportIT { get; set; }
        public string BeschlussExportEN { get; set; }
        public string ErlassQuelleLinkTextFR { get; set; }
        public string ErlassQuelleLinkTextIT { get; set; }
        public string ErlassQuelleLinkTextEN { get; set; }
        public string RelevanterKommentarLinkTextFR { get; set; }
        public string RelevanterKommentarLinkTextIT { get; set; }
        public string RelevanterKommentarLinkTextEN { get; set; }
        public KundendokumentItemStatus StatusFR { get; set; }
        public KundendokumentItemStatus StatusIT { get; set; }
        public KundendokumentItemStatus StatusEN { get; set; }
        public string BetroffenKommentarFR { get; set; }
        public string BetroffenKommentarIT { get; set; }
        public string BetroffenKommentarEN { get; set; }
        public string LinkBetroffenKommentar { get; set; }
        public string LinkBetroffenKommentarFR { get; set; }        
        public string LinkBetroffenKommentarIT { get; set; }
        public string LinkBetroffenKommentarEN { get; set; }
        public string NichtBetroffenKommentarFR { get; set; }
        public string NichtBetroffenKommentarIT { get; set; }
        public string NichtBetroffenKommentarEN { get; set; }
        public string LinkNichtBetroffenKommentar { get; set; }
        public string LinkNichtBetroffenKommentarFR { get; set; }
        public string LinkNichtBetroffenKommentarIT { get; set; }
        public string LinkNichtBetroffenKommentarEN { get; set; }
        public string InternerKommentar { get; set; }
        public string InternerKommentarFR { get; set; }
        public string InternerKommentarIT { get; set; }
        public string InternerKommentarEN { get; set; }

        public string erlassUebersetzungen { get; set; }
        public string erlassfassungUebersetzungen { get; set; }
        public int MassnahmeNewCount { get; set; }
        public int MassnahmeInProgressCount { get; set; }
        public int MassnahmeFinishedCount { get; set; }
    }
}