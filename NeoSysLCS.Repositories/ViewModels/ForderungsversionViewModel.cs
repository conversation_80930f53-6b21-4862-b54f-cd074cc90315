using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.ViewModels.Validations;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class ForderungsversionViewModel : BaseViewModel
    {
        public ForderungsversionViewModel()
        {
            Objekte = new List<Objekt>();
            Rechtsbereiche = new List<Rechtsbereich>();
            Bewilligungspflicht = false;
            Nachweispflicht = false;
        }

        public int ForderungsversionID { get; set; }
        public int ForderungID { get; set; }
        public int? VorversionID { get; set; } // Vorversion
        public int? NachfolgeversionID { get; set; } // Nachfolgeversion
        public int ErlassfassungID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Artikel_Singular")]
        [Required]
        public int? ArtikelID { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Artikel_Singular")]
        public string ArtikelNummer { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderungsversion_Inkrafttretung")]
        public DateTime Inkrafttretung { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderungsversion_Aufhebung")]
        [ForderungAufhebung]
        public DateTime? Aufhebung { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderungsversion_Bewilligungspflicht_Kurz")]
        public bool Bewilligungspflicht { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderungsversion_Nachweispflicht_Kurz")]
        public bool Nachweispflicht { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderungsversion_InternerKommentar")]
        public string InternerKommentar { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderungsversion_Beschreibung")]
        //[Required]
        public string Beschreibung { get; set; }
        public string BeschreibungFranz { get; set; }
        public string BeschreibungItal { get; set; }
        public string BeschreibungEn { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Forderungsversion_Version")]
        public int VersionsNummer { get; set; }

        [Display(Name = "Freigabe (TRANSLATE ME!)")]
        public Boolean? Freigabe { get; set; }

        [Display(Name = "Freigabe QS")]
        public Boolean? QsFreigabe { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Titel")]
        public string ErlassTitel { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_SrNummer")]
        public string ErlassSrNummer { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Abkuerzung")]
        public string ErlassAbkuerzung { get; set; }

        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Entitaet_Erlass_Singular")]
        public int? ErlassID { get; set; }

        public ICollection<Objekt> Objekte { get; set; }
        public ICollection<Rechtsbereich> Rechtsbereiche { get; set; }
        public DateTime? ErlassfassungInkrafttretung { get; set; }
        public string ForderungsversionUebersetzungID { get; set; }
        public int SpracheID { get; set; }
        public string erlUebersetzungen { get; set; }
        public int rechtsbereichCount { get; set; }
        public int objekteCount { get; set; }
        public bool isSelected {get;set;}
    }
}
