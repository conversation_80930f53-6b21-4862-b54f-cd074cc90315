
using System;
using System.ComponentModel.DataAnnotations;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class OfferViewModel : BaseViewModel
    {
        public int OfferID { get; set; }

        public DateTime? ReceivedAt { get; set; }

        public int? ExistingCustomerID { get; set; }
        public string ExistingCustomerName { get; set; }

        public string NewCustomerName { get; set; }

        [Required(AllowEmptyStrings = false, ErrorMessageResourceType = typeof(Resources.Properties.Resources), ErrorMessageResourceName = "Fehler_FehlendeWerte")]
        [Display(Name = "Projektleiter")]
        public string ProjectManagerID { get; set; }
        public string ProjectManagerName { get; set; }

        public string Comment { get; set; }

        [Required(AllowEmptyStrings = false, ErrorMessageResourceType = typeof(Resources.Properties.Resources), ErrorMessageResourceName = "Fehler_FehlendeWerte")]
        [Display(Name = "Status")]
        public OfferStatus Status { get; set; }

        public DateTime? SentAt { get; set; }

        public DateTime? ConsultationAt { get; set; }

        [Required(AllowEmptyStrings = false, ErrorMessageResourceType = typeof(Resources.Properties.Resources), ErrorMessageResourceName = "Fehler_FehlendeWerte")]
        [Display(Name = "Offert Volumen")]
        public int OfferVolume { get; set; }

        public string Link { get; set; }

        public string Remark { get; set; }

        public DateTime? CompletedAt { get; set; }

        public DateTime? InvoicedAt { get; set; }

        public DateTime? ContractCreatedAt { get; set; }

        public DateTime? ContractReturnedAt { get; set; }

        public string ProjectNumberUpdate { get; set; }

        public DateTime? UpdateDate { get; set; }

        public int? Reccurence { get; set; }
        public DateTime? AcceptedAt { get; set; }

    }
}