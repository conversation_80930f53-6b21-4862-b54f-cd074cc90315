using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class DateRangePickerModel
    {
        [Display(Name = "Start Date")]
        public DateTime Start { get; set; }

        [Display(Name = "End Date")]
        public DateTime End { get; set; }

        public int StandortID { get; set; }
    }
}