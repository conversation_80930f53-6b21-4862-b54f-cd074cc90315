
using System;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class EvaluationViewModel
    {
        public string EvaluationID { get; set; }

        public string ProjectManagerID { get; set; }
        public ApplicationUser ProjectManager { get; set; }

        public string Month { get; set; }

        public string Year { get; set; }

        public EvaluationType EvaluationType { get; set; }

        public int EvaluationValue { get; set; }
        public int EvaluationValue2 { get; set; }
        public int EvaluationValue3 { get; set; }
        public DateTime? CreatedOn { get; set; }
        public string Name { get; set; }
    }
}