using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories
{
    /// <summary>
    /// Repository for Allgemeine Informationen
    /// </summary>
    public class AllgemeineKundeninformationRepository
    {
        private readonly IGenericRepository<AllgemeineKundeninformation> _genericKundeninformationRepository;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="context">database context</param>
        public AllgemeineKundeninformationRepository(NeoSysLCS_Dev context)
        {
            _genericKundeninformationRepository = new GenericRepository<AllgemeineKundeninformation>(context);
        }

        /// <summary>
        /// Updates the Kundeninformation
        /// </summary>
        /// <param name="kundeninformation">The Kundeninformation</param>
        public void Update(AllgemeineKundeninformation kundeninformation)
        {
            _genericKundeninformationRepository.Update(kundeninformation);
        }

        /// <summary>
        /// Deletes the Kundeninformation with the given ID
        /// </summary>
        /// <param name="id">The ID</param>
        public void Delete(int id)
        {
            _genericKundeninformationRepository.Delete(id);
        }


        /// <summary>
        /// Inserts the specified kundeninformation.
        /// </summary>
        /// <param name="kundeninformation">The kundeninformation.</param>
        public void Insert(AllgemeineKundeninformation kundeninformation)
        {
            _genericKundeninformationRepository.Insert(kundeninformation);
        }


        /// <summary>
        /// Gets the localised kundeninformation.
        /// </summary>
        /// <returns>Returns the kundeninformation.</returns>
        public AllgemeineKundeninformation GetLocalisedAllgemeineKundeninformation()
        {
            var localisedKundeninformation = _genericKundeninformationRepository.Get(ki => ki.Sprache.Lokalisierung == CultureInfo.CurrentUICulture.TwoLetterISOLanguageName, q => q.OrderBy(s => s.Sprache.Reihenfolge),
                "Sprache").FirstOrDefault();

            return localisedKundeninformation;
        }

        /// <summary>
        /// Gets the allgmeine kundeninformation uebersetzungen.
        /// </summary>
        /// <returns>Returns the uebersetzungen</returns>
        public List<AllgemeineKundeninformation> GetAllgmeineKundeninformationUebersetzungen()
        {
            var uebersetzungen = _genericKundeninformationRepository.Get(null, q => q.OrderBy(s => s.Sprache.Reihenfolge), "Sprache").ToList();

            return uebersetzungen;
        }
        
        /// <summary>
        /// Populates the kundeninformation with empty uebersetzungen
        /// </summary>
        /// <param name="availableSprachen">The available sprachen.</param>
        public void PopulatedAllgemeineKundeninformationUebersetzungen(IEnumerable<Sprache> availableSprachen)
        {
            IEnumerable<AllgemeineKundeninformation> uebersetzungen = GetAllgmeineKundeninformationUebersetzungen();

            if (availableSprachen.Count() == uebersetzungen.Count())
            {
                //already populated
                return;
            }

            foreach (var sprache in availableSprachen)
            {
                Boolean spracheFound = false;
                foreach (var uebersetzung in uebersetzungen)
                {
                    if (uebersetzung.Sprache.Name.Equals(sprache.Name))
                    {
                        spracheFound = true;
                        break;
                    }
                }

                if (!spracheFound)
                {
                    // Create translation for missing language
                    var newUebersetzung = new AllgemeineKundeninformation { SpracheID = sprache.SpracheID };

                    Insert(newUebersetzung);
                }
            }
        }
    }
}