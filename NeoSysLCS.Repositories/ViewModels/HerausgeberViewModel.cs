using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace NeoSysLCS.Repositories.ViewModels
{
    public class HerausgeberViewModel : BaseViewModel
    {

        public int HerausgeberID { get; set; }
        [Display(Name = "Name (TRANSLATE ME!)")]
        public string Name { get; set; }
        public string HerausgeberUebersetzungID { get; set; }
        public int SpracheID { get; set; }

        public HerausgeberViewModel()
        {
        }

        public HerausgeberViewModel(string name, int spracheId)
        {
            this.Name = name;
            this.SpracheID = spracheId;
        }

        public override bool Equals(object obj)
        {
            return ((HerausgeberViewModel)obj).HerausgeberID == HerausgeberID;
        }
        public override int GetHashCode()
        {
            return HerausgeberID.GetHashCode();
        }

        public int? StandortID { get; set; }
    }
}