using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Web;
using System.Web.Mvc.Html;
using System.Xml.Linq;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Repositories
{
    /// <summary>
    /// The repository for kundendokument forderungsversionen
    /// </summary>
    public class KundendokumentForderungsversionRepository
    {
        private readonly IGenericRepository<KundendokumentForderungsversion> _genericKundendokumentForderungsversionenRepository;
        private readonly IGenericRepository<StandortObjekt> _genericStandortObjektRepository;
        private readonly IGenericRepository<Erlass> _genericErlassRepository;
        private readonly NeoSysLCS_Dev _context;
        private int _currentLang;
        private readonly UnitOfWork unitOfWork;

        /// <summary>
        /// Initializes a new instance of the <see cref="KundendokumentForderungsversionRepository"/> class.
        /// </summary>
        /// <param name="contextCandidate">The context candidate.</param>
        public KundendokumentForderungsversionRepository(NeoSysLCS_Dev contextCandidate)
        {
            _context = contextCandidate;
            unitOfWork = new UnitOfWork();
            _genericKundendokumentForderungsversionenRepository = new GenericRepository<KundendokumentForderungsversion>(contextCandidate);
            _genericStandortObjektRepository = new GenericRepository<StandortObjekt>(contextCandidate);
            _genericErlassRepository = new GenericRepository<Erlass>(contextCandidate);
            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
            _currentLang = sprache.SpracheID;

        }

        /// <summary>
        /// Gets the kundendokument forderungsversion by id.
        /// </summary>
        /// <param name="id">The id.</param>
        /// <returns>Returns the kundendokument forderungsversion</returns>
        public KundendokumentForderungsversion GetByID(object id)
        {
            return _genericKundendokumentForderungsversionenRepository.GetByID(id);
        }

        /// <summary>
        /// Gets the kundendokument forderungsversion by id.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <param name="includeProperties">The include properties.</param>
        /// <returns>Returns the kundendokument forderungsversion</returns>
        public KundendokumentForderungsversion GetByID(int id, string includeProperties)
        {
            return _genericKundendokumentForderungsversionenRepository.Get(e => e.KundendokumentForderungsversionID == id, null,
               includeProperties).FirstOrDefault();
        }



        /// <summary>
        /// Updates the qs flags of the kundendokument forderungsversion.
        /// </summary>
        /// <param name="viewModel">The view model.</param>
        public void UpdateQs(KundendokumentForderungsversionViewModel viewModel)
        {
            var kfv =
             _genericKundendokumentForderungsversionenRepository.GetByID(viewModel.KundendokumentForderungsversionID);

            //set qs flag
            kfv.QsFreigabe = viewModel.QsFreigabe;

            //BearbeitetAm and BearbeitetVonID should only be changed if a neosys customer makes a change to the forderungsversion
            //kfv.BearbeitetAm = DateTime.Now;
            //kfv.BearbeitetVonID = HttpContext.Current.User.Identity.GetUserId();
            _genericKundendokumentForderungsversionenRepository.Update(kfv);
        }

        /// <summary>
        /// Updates the kundendokument forderungs version with the changes of the end user.
        /// </summary>
        /// <param name="viewModel">The view model.</param>
        public void UpdateEndUser(KundendokumentForderungsversionViewModel viewModel)
        {
            var kfv =
                _genericKundendokumentForderungsversionenRepository.GetByID(viewModel.KundendokumentForderungsversionID);

            kfv.Erfuellung = viewModel.Erfuellung;
            kfv.LetztePruefungAm = viewModel.LetzterPruefZeitpunkt;
            kfv.NaechstePruefungAm = viewModel.NaechstePruefungAm;
            kfv.Pruefmethode = viewModel.Pruefmethode;
            kfv.Verantwortlich = viewModel.Verantwortlich;
            kfv.Kommentar = viewModel.Kommentar;
            kfv.Ablageort = viewModel.Ablageort;
            kfv.Kundenbezug = viewModel.Kundenbezug;
            kfv.BearbeitetAm = DateTime.Now;
            kfv.BearbeitetVonID = HttpContext.Current.User.Identity.GetUserId();
            _genericKundendokumentForderungsversionenRepository.Update(kfv);
        }

        /// <summary>
        /// Updates the kundendokument forderungsversion with the additional data.
        /// </summary>
        /// <param name="viewModel">The view model.</param>
        public void UpdateAdditionalData(KundendokumentForderungsversionViewModel viewModel)
        {
            var kfv =
                _genericKundendokumentForderungsversionenRepository.GetByID(viewModel.KundendokumentForderungsversionID);

            kfv.Spalte1 = viewModel.Spalte1;
            kfv.Spalte2 = viewModel.Spalte2;
            kfv.Spalte3 = viewModel.Spalte3;
            kfv.Spalte4 = viewModel.Spalte4;
            kfv.Spalte5 = viewModel.Spalte5;
            kfv.Spalte6 = viewModel.Spalte6;
            kfv.Spalte7 = viewModel.Spalte7;
            kfv.Spalte8 = viewModel.Spalte8;
            kfv.Spalte9 = viewModel.Spalte9;
            kfv.Spalte10 = viewModel.Spalte10;

            kfv.BearbeitetAm = DateTime.Now;
            kfv.BearbeitetVonID = HttpContext.Current.User.Identity.GetUserId();
            _genericKundendokumentForderungsversionenRepository.Update(kfv);
        }

        /// <summary>
        /// Updates the kundendokument forderungsversion.
        /// </summary>
        /// <param name="kundendokumentForderungsversion">The kundendokument forderungsversion.</param>
        public void Update(KundendokumentForderungsversion kundendokumentForderungsversion)
        {
            kundendokumentForderungsversion.BearbeitetAm = DateTime.Now;
            kundendokumentForderungsversion.BearbeitetVonID = HttpContext.Current.User.Identity.GetUserId();
            _genericKundendokumentForderungsversionenRepository.Update(kundendokumentForderungsversion);
        }

        public List<int> GetAllObjectIdsByForderungsversion(List<int> kundendokumentIds)
        {
            return (from x in _context.KundendokumentForderungsversionen
                    from stdObj in _context.StandortObjekte.Where(curSta => curSta.StandortObjektID == x.StandortObjektID)
                    where (kundendokumentIds.Contains(x.KundendokumentID)
                             && x.QsFreigabe
                             && x.Relevant
                             && (x.Status != KundendokumentItemStatus.Removed || x.Status != KundendokumentItemStatus.Replaced))
                    select stdObj.ObjektID.HasValue ? stdObj.ObjektID.Value : 0).OrderByDescending(x => x).Distinct().ToList();
        }

        /// <summary>
        /// Deletes the kundendokument forderungsversion with the specified id.
        /// </summary>
        /// <param name="id">The id.</param>
        public void Delete(int id)
        {
            _genericKundendokumentForderungsversionenRepository.Delete(id);
        }

        /// <summary>
        /// Deletes the range.
        /// </summary>
        /// <param name="entitiesToDelete">The entities to delete.</param>
        public void DeleteRange(IEnumerable<KundendokumentForderungsversion> entitiesToDelete)
        {
            _genericKundendokumentForderungsversionenRepository.DeleteRange(entitiesToDelete);
        }

        /// <summary>
        /// Sets all QS Freigabe flags of the kundendokument forderungsversionen of the specified kundendokument to true.
        /// </summary>
        /// <param name="kundendokumentID">The kundendokument id.</param>
        public void ApproveAll(int kundendokumentID)
        {
            var forderungen = _genericKundendokumentForderungsversionenRepository.Get(f => f.KundendokumentID == kundendokumentID &&
                f.Relevant && f.Status != KundendokumentItemStatus.Removed &&
                f.Status != KundendokumentItemStatus.Replaced);
            foreach (var forderung in forderungen)
            {
                forderung.QsFreigabe = true;
                //forderung.BearbeitetAm = DateTime.Now;
                //forderung.BearbeitetVonID = HttpContext.Current.User.Identity.GetUserId();

                _context.Entry(forderung).State = System.Data.Entity.EntityState.Modified;
                //_genericKundendokumentForderungsversionenRepository.Update(forderung);
            }
            _context.SaveChanges();

        }

        /// <summary>
        /// Gets the kundendokument forderungsversionen by kundendokument.
        /// </summary>
        /// <param name="kundendokumentID">The kundendokument id.</param>
        /// <returns>Returns the kundendokument forderungsversionen</returns>
        public List<KundendokumentForderungsversion> GetKundendokumentForderungsversionenByKundendokument(int? kundendokumentID)
        {
            if (!kundendokumentID.HasValue)
            {
                return new List<KundendokumentForderungsversion>();
            }
            //Fixed NEOS-303 Shortcuts not copied to new kundendokument
            var kundendokumenteForderungsversionen =
                    _genericKundendokumentForderungsversionenRepository.Get(f => f.KundendokumentID == kundendokumentID,
                        null, "Forderungsversion, Shortcut").ToList();

            return kundendokumenteForderungsversionen;
        }

        /// <summary>
        /// Gets the kundendokument forderungsversionen by kundendokument.
        /// </summary>
        /// <param name="kundendokumentID">The kundendokument id.</param>
        /// <returns>Returns the kundendokument forderungsversionen</returns>
        public IEnumerable<KundendokumentForderungsversion> GetRelevantKundendokumentForderungsversionenByKundendokument(int? kundendokumentID)
        {
            if (!kundendokumentID.HasValue)
            {
                return new List<KundendokumentForderungsversion>();
            }

            var kundendokumenteForderungsversionen =
                    _genericKundendokumentForderungsversionenRepository.Get(f => f.KundendokumentID == kundendokumentID && f.Relevant,
                        null, "Forderungsversion");

            return kundendokumenteForderungsversionen;
        }

        /// <summary>
        /// Return all freigegebene kundendokument forderungsversion view models for given kundenDokumentIDs.
        /// </summary>
        /// <param name="kundendokumentIDs"></param>
        /// <param name="showOnlyRemoved"></param>
        /// <param name="spracheID"></param>
        /// <returns></returns>
        public IQueryable<MasterKundendokumentForderungsversionViewModel>
            GetAllFreigegebeneForderungsversionViewModels(List<int> kundenDokumentIDs)
        {

            var fordVersionen = (from x in _context.KundendokumentForderungsversionen
                                 from stdObj in _context.StandortObjekte.Where(curSta => curSta.StandortObjektID == x.StandortObjektID)
                                 from obj in _context.Objekte.Where(obj => obj.ObjektID == stdObj.ObjektID)
                                 from std in _context.Standorte.Where(curStd => curStd.StandortID == stdObj.StandortID)
                                 from fv in _context.Forderungsversionen.Where(curFV => curFV.ForderungsversionID == x.ForderungsversionID)
                                 from f in _context.Forderungen.Where(curF => curF.ForderungID == fv.ForderungID)
                                 from erlF in _context.Erlassfassungen.Where(curErlF => curErlF.ErlassfassungID == fv.ErlassfassungID)
                                 from erl in _context.Erlasse.Where(curErl => curErl.ErlassID == erlF.ErlassID)
                                 from art in _context.Artikel.Where(curArt => curArt.ArtikelID == f.ArtikelID)
                                 where (kundenDokumentIDs.Contains(x.KundendokumentID)
                                          && x.QsFreigabe
                                          && x.Relevant
                                          && (x.Status != KundendokumentItemStatus.Removed || x.Status != KundendokumentItemStatus.Replaced))
                                 select new MasterKundendokumentForderungsversionViewModel()
                                 {
                                     KundendokumentForderungsversionID = x.KundendokumentForderungsversionID,
                                     KundendokumentID = x.KundendokumentID,
                                     Kundenbezug = x.Kundenbezug,

                                     StandortObjektID = obj.ObjektID, // TODO changed to objectID
                                     StandortID = std.StandortID,
                                     StandortName = std.Name,

                                     Status = x.Status,
                                     //Erfuellung cannot be a nullable enum because of the comboboxfilter of the gridview...
                                     Erfuellung = x.Erfuellung.HasValue ? (x.Erfuellung.Value == KundendokumentErfuellung.NotDefined ? KundendokumentErfuellung.NotEdited : x.Erfuellung.Value) : KundendokumentErfuellung.NotEdited,
                                     LetzterPruefZeitpunkt = x.LetztePruefungAm,
                                     NaechstePruefungAm = x.NaechstePruefungAm,
                                     Pruefmethode = (x.Pruefmethode == "" || x.Pruefmethode == null) ? " " : x.Pruefmethode,
                                     Verantwortlich = (x.Verantwortlich == "" || x.Verantwortlich == null) ? " " : x.Verantwortlich,
                                     Ablageort = (x.Ablageort == "" || x.Ablageort == null) ? " " : x.Ablageort,
                                     QsFreigabe = x.QsFreigabe,
                                     Kommentar = (x.Kommentar == "" || x.Kommentar == null) ? " " : x.Kommentar,
                                     ForderungsversionID = x.ForderungsversionID,
                                     ShortcutID = (x.Shortcut != null) ? (x.Shortcut.ShortcutID) : 1,
                                     Shortcut = (x.Shortcut != null) ? (x.Shortcut.Name) : "",
                                     Spalte1 = (x.Spalte1 == "" || x.Spalte1 == null) ? " " : x.Spalte1,
                                     Spalte2 = (x.Spalte2 == "" || x.Spalte2 == null) ? " " : x.Spalte2,
                                     Spalte3 = (x.Spalte3 == "" || x.Spalte3 == null) ? " " : x.Spalte3,
                                     Spalte4 = (x.Spalte4 == "" || x.Spalte4 == null) ? " " : x.Spalte4,
                                     Spalte5 = (x.Spalte5 == "" || x.Spalte5 == null) ? " " : x.Spalte5,
                                     Spalte6 = (x.Spalte6 == "" || x.Spalte6 == null) ? " " : x.Spalte6,
                                     Spalte7 = (x.Spalte7 == "" || x.Spalte7 == null) ? " " : x.Spalte7,
                                     Spalte8 = (x.Spalte8 == "" || x.Spalte8 == null) ? " " : x.Spalte8,
                                     Spalte9 = (x.Spalte9 == "" || x.Spalte9 == null) ? " " : x.Spalte9,
                                     Spalte10 = (x.Spalte10 == "" || x.Spalte10 == null) ? " " : x.Spalte10,
                                     ErstelltAm = x.ErstelltAm,
                                     ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : "",
                                     ErstelltVonID = x.ErstelltVonID,
                                     BearbeitetAm = x.BearbeitetAm,
                                     BearbeitetVonID = x.BearbeitetVonID,
                                     BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : "",
                                     stdObjUebersetzungen = obj.Uebersetzung, //TODO changed to objectID
                                     ArtikelID = (f.Artikel != null) ? f.Artikel.ArtikelID : 0,
                                     ArtikelNummer = (f.Artikel != null) ? f.Artikel.Nummer : "",
                                     ArtikelQuelle = (_currentLang == 1 ? art.QuelleDE : null) ??
                                                                  (_currentLang == 2 ? (art.QuelleFR != "" && art.QuelleFR != null ? art.QuelleFR : art.QuelleDE) : null) ??
                                                                  (_currentLang == 3 ? (art.QuelleIT != "" && art.QuelleIT != null ? art.QuelleIT : art.QuelleFR) : null) ??
                                                                  (_currentLang == 4 ? (art.QuelleEN != "" && art.QuelleEN != null ? art.QuelleEN : art.QuelleDE) : null),
                                     ErlassID = erlF.ErlassID,
                                     ErlassfassungID = fv.ErlassfassungID,
                                     VersionsNummer = fv.VersionsNummer,
                                     Bewilligungspflicht = fv.Bewilligungspflicht,
                                     BewilligungspflichtText = fv.Bewilligungspflicht ? "x" : "",
                                     Nachweispflicht = fv.Nachweispflicht,
                                     NachweispflichtText = fv.Nachweispflicht ? "x" : "",
                                     Beschreibung = (_currentLang == 1 ? fv.BeschreibungDE : null) ??
                                                                  (_currentLang == 2 ? (fv.BeschreibungFR != "" && fv.BeschreibungFR != null ? fv.BeschreibungFR : fv.BeschreibungDE) : null) ??
                                                                  (_currentLang == 3 ? (fv.BeschreibungIT != "" && fv.BeschreibungIT != null ? fv.BeschreibungIT : fv.BeschreibungDE) : null) ??
                                                                  (_currentLang == 4 ? (fv.BeschreibungEN != "" && fv.BeschreibungEN != null ? fv.BeschreibungEN : fv.BeschreibungDE) : null),
                                     Rechtsbereiche = from tu in _context.Rechtsbereiche
                                                      where tu.Standorte.Contains(stdObj.Standort) && tu.Forderungsversionen.Contains(fv)
                                                      select new RechtsbereichViewModel()
                                                      {
                                                          Name = (_currentLang == 1 ? tu.NameDE : null) ??
                                                                  (_currentLang == 2 ? (tu.NameFR != "" && tu.NameFR != null ? tu.NameFR : tu.NameDE) : null) ??
                                                                  (_currentLang == 3 ? (tu.NameIT != "" && tu.NameIT != null ? tu.NameIT : tu.NameIT) : null) ??
                                                                  (_currentLang == 4 ? (tu.NameEN != "" && tu.NameEN != null ? tu.NameEN : tu.NameEN) : null),
                                                          RechtsbereichID = tu.RechtsbereichID,
                                                      },
                                     erlUebersetzungen = erl.Uebersetzung,
                                     ErlassSrNummer = erl.SrNummer ?? "",
                                     ErlassfassungInkraftretung = erlF.Inkrafttretung,
                                     ErlassfassungBearbeitetAm = erlF.Beschluss,
                                     MassnahmeNewCount = _context.Massnahme
                                            .Where(m => m.OriginID == x.KundendokumentForderungsversionID && m.Status == MassnahmeStatus.New)
                                            .Count(),
                                     MassnahmeInProgressCount = _context.Massnahme
                                            .Where(m => m.OriginID == x.KundendokumentForderungsversionID && m.Status == MassnahmeStatus.InProgress)
                                            .Count(),
                                     MassnahmeFinishedCount = _context.Massnahme
                                            .Where(m => m.OriginID == x.KundendokumentForderungsversionID && m.Status == MassnahmeStatus.Finished)
                                            .Count()
                                 }).ToList();

            foreach (var fordVersion in fordVersionen)
            {
                XDocument uebersetzungStdObjTitel = XDocument.Parse(fordVersion.stdObjUebersetzungen);
                fordVersion.StandortObjektTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungStdObjTitel, "Name", _currentLang);

                XDocument uebersetzungErlTitel = XDocument.Parse(fordVersion.erlUebersetzungen);
                fordVersion.ErlassTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlTitel, "Titel", _currentLang);

                fordVersion.ForderungsVersionName = fordVersion.ErlassTitel + " - " + fordVersion.ArtikelNummer;
            }

            return fordVersionen.AsQueryable();

        }

        /// <summary>
        /// Gets all freigegebene kundendokument forderungsversion view models by kundendokument.
        /// </summary>
        /// <param name="kundendokumentID">The kundendokument id.</param>
        /// <returns>Returns the view models</returns>
        public IQueryable<KundendokumentForderungsversionViewModel> GetAllFreigegebeneKundendokumentForderungsversionViewModels(int? kundendokumentID, bool showOnlyRemoved, int standortId, int? spracheID)
        {
            int? kundenId = _genericKundendokumentForderungsversionenRepository.GetKundeIdOfCurrentUser();
            //IQueryable<KundendokumentForderungsversion> kundendokumentForderungsversionen;
            if (spracheID.HasValue)
            {
                _currentLang = spracheID.Value;
            }

            if (showOnlyRemoved)
            {
                var fordVersionen = (from x in _context.KundendokumentForderungsversionen
                       from stdObj in _context.StandortObjekte.Where(curSta => curSta.StandortObjektID == x.StandortObjektID)
                       from fv in _context.Forderungsversionen.Where(curFV => curFV.ForderungsversionID == x.ForderungsversionID)
                       from f in _context.Forderungen.Where(curF => curF.ForderungID == fv.ForderungID)
                       from erlF in _context.Erlassfassungen.Where(curErlF => curErlF.ErlassfassungID == fv.ErlassfassungID)
                       from erl in _context.Erlasse.Where(curErl => curErl.ErlassID == erlF.ErlassID)
                       from art in _context.Artikel.Where(curArt => curArt.ArtikelID == f.ArtikelID)
                       where (x.KundendokumentID == kundendokumentID
                                // && showOnlyRemoved
                                // && x.QsFreigabe
                                // && x.Relevant
                                && (kundenId == null || x.KundeID == kundenId)
                                && (x.Status == KundendokumentItemStatus.Removed || x.Status == KundendokumentItemStatus.Replaced))
                       select new KundendokumentForderungsversionViewModel()
                       {
                           KundendokumentForderungsversionID = x.KundendokumentForderungsversionID,
                           KundendokumentID = x.KundendokumentID,
                           Kundenbezug = x.Kundenbezug,
                           StandortObjektID = x.StandortObjektID,
                           Status = x.Status,
                           //Erfuellung cannot be a nullable enum because of the comboboxfilter of the gridview...
                           Erfuellung = x.Erfuellung.HasValue ? (x.Erfuellung.Value == KundendokumentErfuellung.NotDefined ? KundendokumentErfuellung.NotEdited : x.Erfuellung.Value) : KundendokumentErfuellung.NotEdited,
                           LetzterPruefZeitpunkt = x.LetztePruefungAm,
                           NaechstePruefungAm = x.NaechstePruefungAm,
                           Pruefmethode = (x.Pruefmethode == "" || x.Pruefmethode == null) ? " " : x.Pruefmethode,
                           //Verantwortlich = (x.Verantwortlich == "" || x.Verantwortlich == null) ? " " : x.Verantwortlich,
                           Ablageort = (x.Ablageort == "" || x.Ablageort == null) ? " " : x.Ablageort,
                           QsFreigabe = x.QsFreigabe,
                           Kommentar = (x.Kommentar == "" || x.Kommentar == null) ? " " : x.Kommentar,
                           ForderungsversionID = x.ForderungsversionID,
                           ShortcutID = (x.Shortcut != null) ? (x.Shortcut.ShortcutID) : 1,
                           Shortcut = (x.Shortcut != null) ? (x.Shortcut.Name) : "",
                           Spalte1 = (x.Spalte1 == "" || x.Spalte1 == null) ? " " : x.Spalte1,
                           Spalte2 = (x.Spalte2 == "" || x.Spalte2 == null) ? " " : x.Spalte2,
                           Spalte3 = (x.Spalte3 == "" || x.Spalte3 == null) ? " " : x.Spalte3,
                           Spalte4 = (x.Spalte4 == "" || x.Spalte4 == null) ? " " : x.Spalte4,
                           Spalte5 = (x.Spalte5 == "" || x.Spalte5 == null) ? " " : x.Spalte5,
                           Spalte6 = (x.Spalte6 == "" || x.Spalte6 == null) ? " " : x.Spalte6,
                           Spalte7 = (x.Spalte7 == "" || x.Spalte7 == null) ? " " : x.Spalte7,
                           Spalte8 = (x.Spalte8 == "" || x.Spalte8 == null) ? " " : x.Spalte8,
                           Spalte9 = (x.Spalte9 == "" || x.Spalte9 == null) ? " " : x.Spalte9,
                           Spalte10 = (x.Spalte10 == "" || x.Spalte10 == null) ? " " : x.Spalte10,
                           ErstelltAm = x.ErstelltAm,
                           ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : "",
                           ErstelltVonID = x.ErstelltVonID,
                           BearbeitetAm = x.BearbeitetAm,
                           BearbeitetVonID = x.BearbeitetVonID,
                           BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : "",
                           stdObjUebersetzungen = stdObj.Uebersetzung,
                           ArtikelID = (f.Artikel != null) ? f.Artikel.ArtikelID : 0,
                           ArtikelNummer = (f.Artikel != null) ? f.Artikel.Nummer : "",
                           ArtikelQuelle = (_currentLang == 1 ? art.QuelleDE : null) ??
                                                        (_currentLang == 2 ? (art.QuelleFR != "" && art.QuelleFR != null ? art.QuelleFR : art.QuelleDE) : null) ??
                                                        (_currentLang == 3 ? (art.QuelleIT != "" && art.QuelleIT != null ? art.QuelleIT : art.QuelleFR) : null) ??
                                                        (_currentLang == 4 ? (art.QuelleEN != "" && art.QuelleEN != null ? art.QuelleEN : art.QuelleDE) : null),
                           ErlassID = erlF.ErlassID,
                           ErlassfassungID = fv.ErlassfassungID,
                           VersionsNummer = fv.VersionsNummer,
                           Bewilligungspflicht = fv.Bewilligungspflicht,
                           BewilligungspflichtText = fv.Bewilligungspflicht ? "x" : "",
                           Nachweispflicht = fv.Nachweispflicht,
                           NachweispflichtText = fv.Nachweispflicht ? "x" : "",
                           Beschreibung = (_currentLang == 1 ? fv.BeschreibungDE : null) ??
                                                        (_currentLang == 2 ? (fv.BeschreibungFR != "" && fv.BeschreibungFR != null ? fv.BeschreibungFR : fv.BeschreibungDE) : null) ??
                                                        (_currentLang == 3 ? (fv.BeschreibungIT != "" && fv.BeschreibungIT != null ? fv.BeschreibungIT : fv.BeschreibungDE) : null) ??
                                                        (_currentLang == 4 ? (fv.BeschreibungEN != "" && fv.BeschreibungEN != null ? fv.BeschreibungEN : fv.BeschreibungDE) : null),
                           Rechtsbereiche = from tu in _context.Rechtsbereiche
                                            where tu.Standorte.Contains(stdObj.Standort) && tu.Forderungsversionen.Contains(fv)
                                            select new RechtsbereichViewModel()
                                            {
                                                Name = (_currentLang == 1 ? tu.NameDE : null) ??
                                                        (_currentLang == 2 ? (tu.NameFR != "" && tu.NameFR != null ? tu.NameFR : tu.NameDE) : null) ??
                                                        (_currentLang == 3 ? (tu.NameIT != "" && tu.NameIT != null ? tu.NameIT : tu.NameIT) : null) ??
                                                        (_currentLang == 4 ? (tu.NameEN != "" && tu.NameEN != null ? tu.NameEN : tu.NameEN) : null),
                                                RechtsbereichID = tu.RechtsbereichID,
                                            },
                           erlUebersetzungen = erl.Uebersetzung,
                           ErlassSrNummer = erl.SrNummer ?? "",
                           ErlassfassungInkraftretung = erlF.Inkrafttretung,
                           ErlassfassungBearbeitetAm = erlF.Beschluss,
                           MassnahmeNewCount = _context.Massnahme
                                            .Where(m => m.OriginID == x.KundendokumentForderungsversionID && m.Status == MassnahmeStatus.New)
                                            .Count(),
                           MassnahmeInProgressCount = _context.Massnahme
                                            .Where(m => m.OriginID == x.KundendokumentForderungsversionID && m.Status == MassnahmeStatus.InProgress)
                                            .Count(),
                           MassnahmeFinishedCount = _context.Massnahme
                                            .Where(m => m.OriginID == x.KundendokumentForderungsversionID && m.Status == MassnahmeStatus.Finished)
                                            .Count()
                       }).ToList();

                foreach (var fordVersion in fordVersionen)
                {
                    XDocument uebersetzungStdObjTitel = XDocument.Parse(fordVersion.stdObjUebersetzungen);
                    fordVersion.StandortObjektTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungStdObjTitel, "Name", _currentLang);

                    XDocument uebersetzungErlTitel = XDocument.Parse(fordVersion.erlUebersetzungen);
                    fordVersion.ErlassTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlTitel, "Titel", _currentLang);
                }

                return fordVersionen.AsQueryable();
            }
            else
            {
                var fordVersionen = (from x in _context.KundendokumentForderungsversionen
                       from stdObj in _context.StandortObjekte.Where(curSta => curSta.StandortObjektID == x.StandortObjektID)
                       from fv in _context.Forderungsversionen.Where(curFV => curFV.ForderungsversionID == x.ForderungsversionID)
                       from f in _context.Forderungen.Where(curF => curF.ForderungID == fv.ForderungID)
                       from erlF in _context.Erlassfassungen.Where(curErlF => curErlF.ErlassfassungID == fv.ErlassfassungID)
                       from erl in _context.Erlasse.Where(curErl => curErl.ErlassID == erlF.ErlassID)
                       from art in _context.Artikel.Where(curArt => curArt.ArtikelID == f.ArtikelID)
                       where (x.KundendokumentID == kundendokumentID
                                && x.QsFreigabe
                                && x.Relevant
                                && (kundenId == null || x.KundeID == kundenId)
                                && (x.Status != KundendokumentItemStatus.Removed || x.Status != KundendokumentItemStatus.Replaced))
                       select new KundendokumentForderungsversionViewModel()
                       {
                           KundendokumentForderungsversionID = x.KundendokumentForderungsversionID,
                           KundendokumentID = x.KundendokumentID,
                           Kundenbezug = x.Kundenbezug,
                           StandortObjektID = x.StandortObjektID,
                           Status = x.Status,
                           //Erfuellung cannot be a nullable enum because of the comboboxfilter of the gridview...
                           Erfuellung = x.Erfuellung.HasValue ? (x.Erfuellung.Value == KundendokumentErfuellung.NotDefined ? KundendokumentErfuellung.NotEdited : x.Erfuellung.Value) : KundendokumentErfuellung.NotEdited,
                           LetzterPruefZeitpunkt = x.LetztePruefungAm,
                           NaechstePruefungAm = x.NaechstePruefungAm,
                           Pruefmethode = (x.Pruefmethode == "" || x.Pruefmethode == null) ? " " : x.Pruefmethode,
                           Verantwortlich = (x.Verantwortlich == "" || x.Verantwortlich == null) ? " " : x.Verantwortlich,
                           Ablageort = (x.Ablageort == "" || x.Ablageort == null) ? " " : x.Ablageort,
                           QsFreigabe = x.QsFreigabe,
                           Kommentar = (x.Kommentar == "" || x.Kommentar == null) ? " " : x.Kommentar,
                           ForderungsversionID = x.ForderungsversionID,
                           ShortcutID = (x.Shortcut != null) ? (x.Shortcut.ShortcutID) : 1,
                           Shortcut = (x.Shortcut != null) ? (x.Shortcut.Name) : "",
                           Spalte1 = (x.Spalte1 == "" || x.Spalte1 == null) ? " " : x.Spalte1,
                           Spalte2 = (x.Spalte2 == "" || x.Spalte2 == null) ? " " : x.Spalte2,
                           Spalte3 = (x.Spalte3 == "" || x.Spalte3 == null) ? " " : x.Spalte3,
                           Spalte4 = (x.Spalte4 == "" || x.Spalte4 == null) ? " " : x.Spalte4,
                           Spalte5 = (x.Spalte5 == "" || x.Spalte5 == null) ? " " : x.Spalte5,
                           Spalte6 = (x.Spalte6 == "" || x.Spalte6 == null) ? " " : x.Spalte6,
                           Spalte7 = (x.Spalte7 == "" || x.Spalte7 == null) ? " " : x.Spalte7,
                           Spalte8 = (x.Spalte8 == "" || x.Spalte8 == null) ? " " : x.Spalte8,
                           Spalte9 = (x.Spalte9 == "" || x.Spalte9 == null) ? " " : x.Spalte9,
                           Spalte10 = (x.Spalte10 == "" || x.Spalte10 == null) ? " " : x.Spalte10,
                           ErstelltAm = x.ErstelltAm,
                           ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : "",
                           ErstelltVonID = x.ErstelltVonID,
                           BearbeitetAm = x.BearbeitetAm,
                           BearbeitetVonID = x.BearbeitetVonID,
                           BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : "",
                           stdObjUebersetzungen = stdObj.Uebersetzung,
                           ArtikelID = (f.Artikel != null) ? f.Artikel.ArtikelID : 0,
                           ArtikelNummer = (f.Artikel != null) ? f.Artikel.Nummer : "",
                           ArtikelQuelle = (_currentLang == 1 ? art.QuelleDE : null) ??
                                                        (_currentLang == 2 ? (art.QuelleFR != "" && art.QuelleFR != null ? art.QuelleFR : art.QuelleDE) : null) ??
                                                        (_currentLang == 3 ? (art.QuelleIT != "" && art.QuelleIT != null ? art.QuelleIT : art.QuelleFR) : null) ??
                                                        (_currentLang == 4 ? (art.QuelleEN != "" && art.QuelleEN != null ? art.QuelleEN : art.QuelleDE) : null),
                           ErlassID = erlF.ErlassID,
                           ErlassfassungID = fv.ErlassfassungID,
                           VersionsNummer = fv.VersionsNummer,
                           Bewilligungspflicht = fv.Bewilligungspflicht,
                           BewilligungspflichtText = fv.Bewilligungspflicht ? "x" : "",
                           Nachweispflicht = fv.Nachweispflicht,
                           NachweispflichtText = fv.Nachweispflicht ? "x" : "",
                           Beschreibung = (_currentLang == 1 ? fv.BeschreibungDE : null) ??
                                                        (_currentLang == 2 ? (fv.BeschreibungFR != "" && fv.BeschreibungFR != null ? fv.BeschreibungFR : fv.BeschreibungDE) : null) ??
                                                        (_currentLang == 3 ? (fv.BeschreibungIT != "" && fv.BeschreibungIT != null ? fv.BeschreibungIT : fv.BeschreibungDE) : null) ??
                                                        (_currentLang == 4 ? (fv.BeschreibungEN != "" && fv.BeschreibungEN != null ? fv.BeschreibungEN : fv.BeschreibungDE) : null),
                           Rechtsbereiche = from tu in _context.Rechtsbereiche
                                            where tu.Standorte.Contains(stdObj.Standort) && tu.Forderungsversionen.Contains(fv)
                                            select new RechtsbereichViewModel()
                                            {
                                                Name = (_currentLang == 1 ? tu.NameDE : null) ??
                                                        (_currentLang == 2 ? (tu.NameFR != "" && tu.NameFR != null ? tu.NameFR : tu.NameDE) : null) ??
                                                        (_currentLang == 3 ? (tu.NameIT != "" && tu.NameIT != null ? tu.NameIT : tu.NameIT) : null) ??
                                                        (_currentLang == 4 ? (tu.NameEN != "" && tu.NameEN != null ? tu.NameEN : tu.NameEN) : null),
                                                RechtsbereichID = tu.RechtsbereichID,
                                            },
                           erlUebersetzungen = erl.Uebersetzung,
                           ErlassSrNummer = erl.SrNummer ?? "",
                           ErlassfassungInkraftretung = erlF.Inkrafttretung,
                           ErlassfassungBearbeitetAm = erlF.Beschluss,
                           MassnahmeNewCount = _context.Massnahme
                                            .Where(m => m.OriginID == x.KundendokumentForderungsversionID && m.Status == MassnahmeStatus.New)
                                            .Count(),
                           MassnahmeInProgressCount = _context.Massnahme
                                            .Where(m => m.OriginID == x.KundendokumentForderungsversionID && m.Status == MassnahmeStatus.InProgress)
                                            .Count(),
                           MassnahmeFinishedCount = _context.Massnahme
                                            .Where(m => m.OriginID == x.KundendokumentForderungsversionID && m.Status == MassnahmeStatus.Finished)
                                            .Count()
                       }).ToList();

                foreach (var fordVersion in fordVersionen)
                {
                    XDocument uebersetzungStdObjTitel = XDocument.Parse(fordVersion.stdObjUebersetzungen);
                    fordVersion.StandortObjektTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungStdObjTitel, "Name", _currentLang);

                    XDocument uebersetzungErlTitel = XDocument.Parse(fordVersion.erlUebersetzungen);
                    fordVersion.ErlassTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlTitel, "Titel", _currentLang);
                }

                return fordVersionen.AsQueryable();
            }
        }

        public IQueryable<KundendokumentForderungsversionViewModel> GetAllFreigegebeneKundendokumentForderungsversionViewModelsByShortcut(int? kundendokumentID, bool showOnlyRemoved, int standortId, string userID, int? spracheID)
        {

            int? kundenId = _genericKundendokumentForderungsversionenRepository.GetKundeIdOfCurrentUser();
            //IQueryable<KundendokumentForderungsversion> kundendokumentForderungsversionen;
            if (spracheID.HasValue)
            {
                _currentLang = spracheID.Value;
            }

            List<int> shortcutsIds = unitOfWork.ShortcutRepository.GetAllShortcutIdsByStandortAndUserId(userID, standortId);


            if (showOnlyRemoved)
            {
                var fordVersionen = (from x in _context.KundendokumentForderungsversionen
                       from stdObj in _context.StandortObjekte.Where(curSta => curSta.StandortObjektID == x.StandortObjektID)
                       from fv in _context.Forderungsversionen.Where(curFV => curFV.ForderungsversionID == x.ForderungsversionID)
                       from f in _context.Forderungen.Where(curF => curF.ForderungID == fv.ForderungID)
                       from erlF in _context.Erlassfassungen.Where(curErlF => curErlF.ErlassfassungID == fv.ErlassfassungID)
                       from erl in _context.Erlasse.Where(curErl => curErl.ErlassID == erlF.ErlassID)
                       from art in _context.Artikel.Where(curArt => curArt.ArtikelID == f.ArtikelID)
                       where (x.KundendokumentID == kundendokumentID
                                // && showOnlyRemoved
                                // && x.QsFreigabe
                                // && x.Relevant
                                && (kundenId == null || x.KundeID == kundenId)
                                && (x.Status == KundendokumentItemStatus.Removed || x.Status == KundendokumentItemStatus.Replaced)
                                && shortcutsIds.Contains(x.Shortcut.ShortcutID))
                       select new KundendokumentForderungsversionViewModel()
                       {
                           KundendokumentForderungsversionID = x.KundendokumentForderungsversionID,
                           KundendokumentID = x.KundendokumentID,
                           Kundenbezug = x.Kundenbezug,
                           StandortObjektID = x.StandortObjektID,
                           Status = x.Status,
                           //Erfuellung cannot be a nullable enum because of the comboboxfilter of the gridview...
                           Erfuellung = x.Erfuellung.HasValue ? (x.Erfuellung.Value == KundendokumentErfuellung.NotDefined ? KundendokumentErfuellung.NotEdited : x.Erfuellung.Value) : KundendokumentErfuellung.NotEdited,
                           LetzterPruefZeitpunkt = x.LetztePruefungAm,
                           NaechstePruefungAm = x.NaechstePruefungAm,
                           Pruefmethode = (x.Pruefmethode == "" || x.Pruefmethode == null) ? " " : x.Pruefmethode,
                           Verantwortlich = (x.Verantwortlich == "" || x.Verantwortlich == null) ? " " : x.Verantwortlich,
                           Ablageort = (x.Ablageort == "" || x.Ablageort == null) ? " " : x.Ablageort,
                           QsFreigabe = x.QsFreigabe,
                           Kommentar = (x.Kommentar == "" || x.Kommentar == null) ? " " : x.Kommentar,
                           ForderungsversionID = x.ForderungsversionID,
                           ShortcutID = (x.Shortcut != null) ? (x.Shortcut.ShortcutID) : 1,
                           Shortcut = (x.Shortcut != null) ? (x.Shortcut.Name) : "",
                           Spalte1 = (x.Spalte1 == "" || x.Spalte1 == null) ? " " : x.Spalte1,
                           Spalte2 = (x.Spalte2 == "" || x.Spalte2 == null) ? " " : x.Spalte2,
                           Spalte3 = (x.Spalte3 == "" || x.Spalte3 == null) ? " " : x.Spalte3,
                           Spalte4 = (x.Spalte4 == "" || x.Spalte4 == null) ? " " : x.Spalte4,
                           Spalte5 = (x.Spalte5 == "" || x.Spalte5 == null) ? " " : x.Spalte5,
                           Spalte6 = (x.Spalte6 == "" || x.Spalte6 == null) ? " " : x.Spalte6,
                           Spalte7 = (x.Spalte7 == "" || x.Spalte7 == null) ? " " : x.Spalte7,
                           Spalte8 = (x.Spalte8 == "" || x.Spalte8 == null) ? " " : x.Spalte8,
                           Spalte9 = (x.Spalte9 == "" || x.Spalte9 == null) ? " " : x.Spalte9,
                           Spalte10 = (x.Spalte10 == "" || x.Spalte10 == null) ? " " : x.Spalte10,
                           ErstelltAm = x.ErstelltAm,
                           ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : "",
                           ErstelltVonID = x.ErstelltVonID,
                           BearbeitetAm = x.BearbeitetAm,
                           BearbeitetVonID = x.BearbeitetVonID,
                           BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : "",
                           stdObjUebersetzungen = stdObj.Uebersetzung,
                           ArtikelID = (f.Artikel != null) ? f.Artikel.ArtikelID : 0,
                           ArtikelNummer = (f.Artikel != null) ? f.Artikel.Nummer : "",
                           ArtikelQuelle = (_currentLang == 1 ? art.QuelleDE : null) ??
                                                        (_currentLang == 2 ? (art.QuelleFR != "" && art.QuelleFR != null ? art.QuelleFR : art.QuelleDE) : null) ??
                                                        (_currentLang == 3 ? (art.QuelleIT != "" && art.QuelleIT != null ? art.QuelleIT : art.QuelleFR) : null) ??
                                                        (_currentLang == 4 ? (art.QuelleEN != "" && art.QuelleEN != null ? art.QuelleEN : art.QuelleDE) : null),
                           ErlassID = erlF.ErlassID,
                           ErlassfassungID = fv.ErlassfassungID,
                           VersionsNummer = fv.VersionsNummer,
                           Bewilligungspflicht = fv.Bewilligungspflicht,
                           BewilligungspflichtText = fv.Bewilligungspflicht ? "x" : "",
                           Nachweispflicht = fv.Nachweispflicht,
                           NachweispflichtText = fv.Nachweispflicht ? "x" : "",
                           Beschreibung = (_currentLang == 1 ? fv.BeschreibungDE : null) ??
                                                        (_currentLang == 2 ? (fv.BeschreibungFR != "" && fv.BeschreibungFR != null ? fv.BeschreibungFR : fv.BeschreibungDE) : null) ??
                                                        (_currentLang == 3 ? (fv.BeschreibungIT != "" && fv.BeschreibungIT != null ? fv.BeschreibungIT : fv.BeschreibungDE) : null) ??
                                                        (_currentLang == 4 ? (fv.BeschreibungEN != "" && fv.BeschreibungEN != null ? fv.BeschreibungEN : fv.BeschreibungDE) : null),
                           Rechtsbereiche = from tu in _context.Rechtsbereiche
                                            where tu.Standorte.Contains(stdObj.Standort) && tu.Forderungsversionen.Contains(fv)
                                            select new RechtsbereichViewModel()
                                            {
                                                Name = (_currentLang == 1 ? tu.NameDE : null) ??
                                                        (_currentLang == 2 ? (tu.NameFR != "" && tu.NameFR != null ? tu.NameFR : tu.NameDE) : null) ??
                                                        (_currentLang == 3 ? (tu.NameIT != "" && tu.NameIT != null ? tu.NameIT : tu.NameIT) : null) ??
                                                        (_currentLang == 4 ? (tu.NameEN != "" && tu.NameEN != null ? tu.NameEN : tu.NameEN) : null),
                                                RechtsbereichID = tu.RechtsbereichID,
                                            },
                           erlUebersetzungen = erl.Uebersetzung,
                           ErlassSrNummer = erl.SrNummer ?? "",
                           ErlassfassungInkraftretung = erlF.Inkrafttretung,
                           ErlassfassungBearbeitetAm = erlF.Beschluss,
                           MassnahmeNewCount = _context.Massnahme
                                            .Where(m => m.OriginID == x.KundendokumentForderungsversionID && m.Status == MassnahmeStatus.New)
                                            .Count(),
                           MassnahmeInProgressCount = _context.Massnahme
                                            .Where(m => m.OriginID == x.KundendokumentForderungsversionID && m.Status == MassnahmeStatus.InProgress)
                                            .Count(),
                           MassnahmeFinishedCount = _context.Massnahme
                                            .Where(m => m.OriginID == x.KundendokumentForderungsversionID && m.Status == MassnahmeStatus.Finished)
                                            .Count()
                       }).ToList();

                foreach (var fordVersion in fordVersionen)
                {
                    XDocument uebersetzungStdObjTitel = XDocument.Parse(fordVersion.stdObjUebersetzungen);
                    fordVersion.StandortObjektTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungStdObjTitel, "Name", _currentLang);

                    XDocument uebersetzungErlTitel = XDocument.Parse(fordVersion.erlUebersetzungen);
                    fordVersion.ErlassTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlTitel, "Titel", _currentLang);
                }

                return fordVersionen.AsQueryable();
            }
            else
            {
                var fordVersionen = (from x in _context.KundendokumentForderungsversionen
                       from stdObj in _context.StandortObjekte.Where(curSta => curSta.StandortObjektID == x.StandortObjektID)
                       from fv in _context.Forderungsversionen.Where(curFV => curFV.ForderungsversionID == x.ForderungsversionID)
                       from f in _context.Forderungen.Where(curF => curF.ForderungID == fv.ForderungID)
                       from erlF in _context.Erlassfassungen.Where(curErlF => curErlF.ErlassfassungID == fv.ErlassfassungID)
                       from erl in _context.Erlasse.Where(curErl => curErl.ErlassID == erlF.ErlassID)
                       from art in _context.Artikel.Where(curArt => curArt.ArtikelID == f.ArtikelID)
                       where (x.KundendokumentID == kundendokumentID
                                && x.QsFreigabe
                                && x.Relevant
                                && (kundenId == null || x.KundeID == kundenId)
                                && (x.Status != KundendokumentItemStatus.Removed || x.Status != KundendokumentItemStatus.Replaced)
                                && shortcutsIds.Contains(x.Shortcut.ShortcutID))
                       select new KundendokumentForderungsversionViewModel()
                       {
                           KundendokumentForderungsversionID = x.KundendokumentForderungsversionID,
                           KundendokumentID = x.KundendokumentID,
                           Kundenbezug = x.Kundenbezug,
                           StandortObjektID = x.StandortObjektID,
                           Status = x.Status,
                           //Erfuellung cannot be a nullable enum because of the comboboxfilter of the gridview...
                           Erfuellung = x.Erfuellung.HasValue ? (x.Erfuellung.Value == KundendokumentErfuellung.NotDefined ? KundendokumentErfuellung.NotEdited : x.Erfuellung.Value) : KundendokumentErfuellung.NotEdited,
                           LetzterPruefZeitpunkt = x.LetztePruefungAm,
                           NaechstePruefungAm = x.NaechstePruefungAm,
                           Pruefmethode = (x.Pruefmethode == "" || x.Pruefmethode == null) ? " " : x.Pruefmethode,
                           Verantwortlich = (x.Verantwortlich == "" || x.Verantwortlich == null) ? " " : x.Verantwortlich,
                           Ablageort = (x.Ablageort == "" || x.Ablageort == null) ? " " : x.Ablageort,
                           QsFreigabe = x.QsFreigabe,
                           Kommentar = (x.Kommentar == "" || x.Kommentar == null) ? " " : x.Kommentar,
                           ForderungsversionID = x.ForderungsversionID,
                           ShortcutID = (x.Shortcut != null) ? (x.Shortcut.ShortcutID) : 1,
                           Shortcut = (x.Shortcut != null) ? (x.Shortcut.Name) : "",
                           Spalte1 = (x.Spalte1 == "" || x.Spalte1 == null) ? " " : x.Spalte1,
                           Spalte2 = (x.Spalte2 == "" || x.Spalte2 == null) ? " " : x.Spalte2,
                           Spalte3 = (x.Spalte3 == "" || x.Spalte3 == null) ? " " : x.Spalte3,
                           Spalte4 = (x.Spalte4 == "" || x.Spalte4 == null) ? " " : x.Spalte4,
                           Spalte5 = (x.Spalte5 == "" || x.Spalte5 == null) ? " " : x.Spalte5,
                           Spalte6 = (x.Spalte6 == "" || x.Spalte6 == null) ? " " : x.Spalte6,
                           Spalte7 = (x.Spalte7 == "" || x.Spalte7 == null) ? " " : x.Spalte7,
                           Spalte8 = (x.Spalte8 == "" || x.Spalte8 == null) ? " " : x.Spalte8,
                           Spalte9 = (x.Spalte9 == "" || x.Spalte9 == null) ? " " : x.Spalte9,
                           Spalte10 = (x.Spalte10 == "" || x.Spalte10 == null) ? " " : x.Spalte10,
                           ErstelltAm = x.ErstelltAm,
                           ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : "",
                           ErstelltVonID = x.ErstelltVonID,
                           BearbeitetAm = x.BearbeitetAm,
                           BearbeitetVonID = x.BearbeitetVonID,
                           BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : "",
                           stdObjUebersetzungen = stdObj.Uebersetzung,
                           ArtikelID = (f.Artikel != null) ? f.Artikel.ArtikelID : 0,
                           ArtikelNummer = (f.Artikel != null) ? f.Artikel.Nummer : "",
                           ArtikelQuelle = (_currentLang == 1 ? (art.QuelleDE ?? art.QuelleFR ?? art.QuelleIT ?? art.QuelleEN) : null) ??
                                                        (_currentLang == 2 ? (art.QuelleFR ?? art.QuelleDE ?? art.QuelleIT ?? art.QuelleEN) : null) ??
                                                        (_currentLang == 3 ? (art.QuelleIT ?? art.QuelleDE ?? art.QuelleFR ?? art.QuelleEN) : null) ??
                                                        (_currentLang == 4 ? (art.QuelleEN ?? art.QuelleDE ?? art.QuelleFR ?? art.QuelleIT) : null),
                           ErlassID = erlF.ErlassID,
                           ErlassfassungID = fv.ErlassfassungID,
                           VersionsNummer = fv.VersionsNummer,
                           Bewilligungspflicht = fv.Bewilligungspflicht,
                           BewilligungspflichtText = fv.Bewilligungspflicht ? "x" : "",
                           Nachweispflicht = fv.Nachweispflicht,
                           NachweispflichtText = fv.Nachweispflicht ? "x" : "",
                           Beschreibung = (_currentLang == 1 ? fv.BeschreibungDE : null) ??
                                                        (_currentLang == 2 ? (fv.BeschreibungFR != "" && fv.BeschreibungFR != null ? fv.BeschreibungFR : fv.BeschreibungDE) : null) ??
                                                        (_currentLang == 3 ? (fv.BeschreibungIT != "" && fv.BeschreibungIT != null ? fv.BeschreibungIT : fv.BeschreibungDE) : null) ??
                                                        (_currentLang == 4 ? (fv.BeschreibungEN != "" && fv.BeschreibungEN != null ? fv.BeschreibungEN : fv.BeschreibungDE) : null),
                           Rechtsbereiche = from tu in _context.Rechtsbereiche
                                            where tu.Standorte.Contains(stdObj.Standort) && tu.Forderungsversionen.Contains(fv)
                                            select new RechtsbereichViewModel()
                                            {
                                                Name = (_currentLang == 1 ? (tu.NameDE ?? tu.NameFR ?? tu.NameIT ?? tu.NameEN) : null) ??
                                                        (_currentLang == 2 ? (tu.NameFR ?? tu.NameDE ?? tu.NameIT ?? tu.NameEN) : null) ??
                                                        (_currentLang == 3 ? (tu.NameIT ?? tu.NameDE ?? tu.NameFR ?? tu.NameEN) : null) ??
                                                        (_currentLang == 4 ? (tu.NameEN ?? tu.NameDE ?? tu.NameFR ?? tu.NameIT) : null),
                                                RechtsbereichID = tu.RechtsbereichID,
                                            },
                           erlUebersetzungen = erl.Uebersetzung,
                           ErlassSrNummer = erl.SrNummer ?? "",
                           ErlassfassungInkraftretung = erlF.Inkrafttretung,
                           ErlassfassungBearbeitetAm = erlF.Beschluss,
                           MassnahmeNewCount = _context.Massnahme
                                            .Where(m => m.OriginID == x.KundendokumentForderungsversionID && m.Status == MassnahmeStatus.New)
                                            .Count(),
                           MassnahmeInProgressCount = _context.Massnahme
                                            .Where(m => m.OriginID == x.KundendokumentForderungsversionID && m.Status == MassnahmeStatus.InProgress)
                                            .Count(),
                           MassnahmeFinishedCount = _context.Massnahme
                                            .Where(m => m.OriginID == x.KundendokumentForderungsversionID && m.Status == MassnahmeStatus.Finished)
                                            .Count()
                       }).ToList();

                foreach (var fordVersion in fordVersionen)
                {
                    XDocument uebersetzungStdObjTitel = XDocument.Parse(fordVersion.stdObjUebersetzungen);
                    fordVersion.StandortObjektTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungStdObjTitel, "Name", _currentLang);

                    XDocument uebersetzungErlTitel = XDocument.Parse(fordVersion.erlUebersetzungen);
                    fordVersion.ErlassTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlTitel, "Titel", _currentLang);
                }

                return fordVersionen.AsQueryable();
            }
        }

        /// <summary>
        /// Gets all relevant kundendokument forderungsversion view models of the specified kundendokument for the excel export.
        /// </summary>
        /// <param name="kundendokumentID">The kundendokument id.</param>
        /// <returns>Returns the view models</returns>
        public IQueryable<KundendokumentForderungsversionViewModel> GetAllFreigegebeneKundendokumentForderungsversionViewModelsToExport(int? kundendokumentID, bool showOnlyRemoved, int standortId)
        {
            int? kundenId = _genericKundendokumentForderungsversionenRepository.GetKundeIdOfCurrentUser();

            var fordVersionen = (from x in _context.KundendokumentForderungsversionen
                   from stdObj in _context.StandortObjekte.Where(curSta => curSta.StandortObjektID == x.StandortObjektID)
                   from fv in _context.Forderungsversionen.Where(curFV => curFV.ForderungsversionID == x.ForderungsversionID)
                   from f in _context.Forderungen.Where(curF => curF.ForderungID == fv.ForderungID)
                   from erlF in _context.Erlassfassungen.Where(curErlF => curErlF.ErlassfassungID == fv.ErlassfassungID)
                   from erl in _context.Erlasse.Where(curErl => curErl.ErlassID == erlF.ErlassID)
                   from art in _context.Artikel.Where(curArt => curArt.ArtikelID == f.ArtikelID)
                   where (x.KundendokumentID == kundendokumentID && x.QsFreigabe && x.Relevant && (kundenId == null ||x.KundeID == kundenId)
                           && (x.Status != KundendokumentItemStatus.Removed || x.Status != KundendokumentItemStatus.Replaced))
                   select new KundendokumentForderungsversionViewModel()
                   {
                       KundendokumentForderungsversionID = x.KundendokumentForderungsversionID,
                       Rechtsbereiche = from tu in _context.Rechtsbereiche
                                        where x.Forderungsversion.Rechtsbereiche.Contains(tu) && x.StandortObjekt.Standort.Rechtsbereiche.Contains(tu)
                                        select new RechtsbereichViewModel()
                                        {
                                            Name = tu.NameDE ?? tu.NameFR ?? tu.NameIT ?? tu.NameEN ?? null,
                                            RechtsbereichID = tu.RechtsbereichID,
                                        },
                       RechtsbereicheFR = from tu in _context.Rechtsbereiche
                                          where x.Forderungsversion.Rechtsbereiche.Contains(tu) && x.StandortObjekt.Standort.Rechtsbereiche.Contains(tu)
                                          select new RechtsbereichViewModel()
                                          {
                                              Name = tu.NameFR ?? tu.NameDE ?? tu.NameIT ?? tu.NameEN ?? null,
                                              RechtsbereichID = tu.RechtsbereichID,
                                          },
                       RechtsbereicheIT = from tu in _context.Rechtsbereiche
                                          where x.Forderungsversion.Rechtsbereiche.Contains(tu) && x.StandortObjekt.Standort.Rechtsbereiche.Contains(tu)
                                          select new RechtsbereichViewModel()
                                          {
                                              Name = tu.NameIT ?? tu.NameDE ?? tu.NameFR ?? tu.NameEN ?? null,
                                              RechtsbereichID = tu.RechtsbereichID,
                                          },
                       RechtsbereicheEN = from tu in _context.Rechtsbereiche
                                          where x.Forderungsversion.Rechtsbereiche.Contains(tu) && x.StandortObjekt.Standort.Rechtsbereiche.Contains(tu)
                                          select new RechtsbereichViewModel()
                                          {
                                              Name = tu.NameEN ?? tu.NameDE ?? tu.NameFR ?? tu.NameIT ?? null,
                                              RechtsbereichID = tu.RechtsbereichID,
                                          },

                       stdObjUebersetzungen = stdObj.Uebersetzung,
                       ArtikelQuelle = art.QuelleDE ?? art.QuelleFR ?? art.QuelleIT ?? art.QuelleEN ?? null,
                       ArtikelQuelleFR = art.QuelleFR ?? art.QuelleDE ?? art.QuelleIT ?? art.QuelleEN ?? null,
                       ArtikelQuelleIT = art.QuelleIT ?? art.QuelleDE ?? art.QuelleFR ?? art.QuelleEN ?? null,
                       ArtikelQuelleEN = art.QuelleEN ?? art.QuelleDE ?? art.QuelleFR ?? art.QuelleIT ?? null,
                       erlUebersetzungen = erl.Uebersetzung,
                       Beschreibung = fv.BeschreibungDE ?? fv.BeschreibungFR ?? fv.BeschreibungIT ?? fv.BeschreibungEN ?? null,
                       BeschreibungFR = fv.BeschreibungFR ?? fv.BeschreibungDE ?? fv.BeschreibungIT ?? fv.BeschreibungEN ?? null,
                       BeschreibungIT = fv.BeschreibungIT ?? fv.BeschreibungDE ?? fv.BeschreibungFR ?? fv.BeschreibungEN ?? null,
                       BeschreibungEN = fv.BeschreibungEN ?? fv.BeschreibungDE ?? fv.BeschreibungFR ?? fv.BeschreibungIT ?? null,
                       ArtikelID = (f.Artikel != null) ? f.Artikel.ArtikelID : 0,
                       ArtikelNummer = (f.Artikel != null) ? f.Artikel.Nummer : "Keine Nummer vorhanden",
                       ErlassSrNummer = erl.SrNummer ?? "Keine Nummer vorhanden",
                       Bewilligungspflicht = x.Forderungsversion.Bewilligungspflicht,
                       Nachweispflicht = x.Forderungsversion.Nachweispflicht,
                       ExportStatus = (x.Status == KundendokumentItemStatus.New || x.Status == KundendokumentItemStatus.NewVersion) ? "x" : null,
                       Kundenbezug = x.Kundenbezug,
                       LetzterPruefZeitpunkt = x.LetztePruefungAm,
                       NaechstePruefungAm = x.NaechstePruefungAm,
                       Pruefmethode = x.Pruefmethode ?? "",
                       //Fixed NEOS-303 Shortcuts not correctly exported
                       //Fixed NEOS-444 Shortcut "Anna" in Import
                       Verantwortlich = x.Shortcut != null ? (x.Shortcut.ShortcutID == 1 ? "" : x.Shortcut.Name) : "",
                       Ablageort = x.Ablageort ?? "",
                       Kommentar = x.Kommentar ?? "",
                       Spalte1 = x.Spalte1 ?? "",
                       Spalte2 = x.Spalte2 ?? "",
                       Spalte3 = x.Spalte3 ?? "",
                       Spalte4 = x.Spalte4 ?? "",
                       Spalte5 = x.Spalte5 ?? "",
                       Spalte6 = x.Spalte6 ?? "",
                       Spalte7 = x.Spalte7 ?? "",
                       Spalte8 = x.Spalte8 ?? "",
                       Spalte9 = x.Spalte9 ?? "",
                       Spalte10 = x.Spalte10 ?? "",
                       Inkrafttretung = fv.Inkrafttretung,
                       Erfuellung = x.Erfuellung.HasValue ? (x.Erfuellung.Value == KundendokumentErfuellung.NotDefined ? KundendokumentErfuellung.NotEdited : x.Erfuellung.Value) : KundendokumentErfuellung.NotEdited,
                       BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                       BearbeitetAm = x.BearbeitetAm
                   }).ToList();

            foreach (var fordVersion in fordVersionen)
            {

                if (fordVersion.Beschreibung != null && fordVersion.Beschreibung.Length > 1000)
                {
                    fordVersion.Beschreibung = fordVersion.Beschreibung.Substring(0, 1000);
                }

                if (fordVersion.BeschreibungFR != null && fordVersion.BeschreibungFR.Length > 1000)
                {
                    fordVersion.BeschreibungFR = fordVersion.BeschreibungFR.Substring(0, 1000);
                }

                if (fordVersion.BeschreibungIT != null && fordVersion.BeschreibungIT.Length > 1000)
                {
                    fordVersion.BeschreibungIT = fordVersion.BeschreibungIT.Substring(0, 1000);
                }

                if (fordVersion.BeschreibungEN != null && fordVersion.BeschreibungEN.Length > 1000)
                {
                    fordVersion.BeschreibungEN = fordVersion.BeschreibungEN.Substring(0, 1000);
                }

                XDocument uebersetzungStdObj = XDocument.Parse(fordVersion.stdObjUebersetzungen);
                fordVersion.StandortObjektTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungStdObj, "Name", 1);
                fordVersion.StandortObjektTitelFR = XMLHelper.GetUebersetzungFromXmlField(uebersetzungStdObj, "Name", 2);
                fordVersion.StandortObjektTitelIT = XMLHelper.GetUebersetzungFromXmlField(uebersetzungStdObj, "Name", 3);
                fordVersion.StandortObjektTitelEN = XMLHelper.GetUebersetzungFromXmlField(uebersetzungStdObj, "Name", 4);

                XDocument uebersetzungErlass = XDocument.Parse(fordVersion.erlUebersetzungen);
                fordVersion.ErlassTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Titel", 1);
                fordVersion.ErlassTitelFR = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Titel", 2);
                fordVersion.ErlassTitelIT = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Titel", 3);
                fordVersion.ErlassTitelEN = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Titel", 4);
                fordVersion.ErlassQuelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Quelle", 1);
                fordVersion.ErlassQuelleFR = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Quelle", 2);
                fordVersion.ErlassQuelleIT = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Quelle", 3);
                fordVersion.ErlassQuelleEN = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Quelle", 4);
            }

            return fordVersionen.AsQueryable();
        }

        public IQueryable<KundendokumentForderungsversionViewModel> Test(int? kundendokumentID, bool showOnlyRemoved, int standortId)
        {
            int? kundenId = _genericKundendokumentForderungsversionenRepository.GetKundeIdOfCurrentUser();

            return from x in _context.KundendokumentForderungsversionen
                   from fv in _context.Forderungsversionen.Where(curFv => curFv.ForderungsversionID == x.ForderungsversionID)
                   from f in _context.Forderungen.Where(curF => curF.ForderungID == fv.ForderungID)
                   from art in _context.Artikel.Where(curArt => curArt.ArtikelID == f.ArtikelID)
                   where (x.KundendokumentID == kundendokumentID && x.QsFreigabe && x.Relevant && (kundenId == null || x.KundeID == kundenId)
                            && (x.Status != KundendokumentItemStatus.Removed || x.Status != KundendokumentItemStatus.Replaced))
                   select new KundendokumentForderungsversionViewModel()
                   {
                       KundendokumentForderungsversionID = x.KundendokumentForderungsversionID,
                       ArtikelQuelle = (_currentLang == 1 ? art.QuelleDE : null) ??
                                                        (_currentLang == 2 ? (art.QuelleFR != "" && art.QuelleFR != null ? art.QuelleFR : art.QuelleDE) : null) ??
                                                        (_currentLang == 3 ? (art.QuelleIT != "" && art.QuelleIT != null ? art.QuelleIT : art.QuelleFR) : null) ??
                                                        (_currentLang == 4 ? (art.QuelleEN != "" && art.QuelleEN != null ? art.QuelleEN : art.QuelleDE) : null),
                   };
        }

        public IQueryable<KundendokumentForderungsversionViewModel> GetKundendokumentForderungsversionViewModelsByKundendokumentToQuenticExport(int? kundendokumentID)
        {
            int? kundenId = _genericKundendokumentForderungsversionenRepository.GetKundeIdOfCurrentUser();

            Kundendokument kundendokument = unitOfWork.KundendokumentRepository.GetByID(kundendokumentID.Value);
            DateTime lastReview = kundendokument.BearbeitetAm != null ? kundendokument.BearbeitetAm.Value : kundendokument.ErstelltAm.Value;

            var fordVersionen = (from x in _context.KundendokumentForderungsversionen
                   from fv in _context.Forderungsversionen.Where(curFv => curFv.ForderungsversionID == x.ForderungsversionID)
                   from f in _context.Forderungen.Where(curF => curF.ForderungID == fv.ForderungID)
                   from art in _context.Artikel.Where(curArt => curArt.ArtikelID == f.ArtikelID)
                   from erl in _context.Erlasse.Where(curErl => curErl.ErlassID == art.ErlassID)
                   where (x.KundendokumentID == kundendokumentID && x.QsFreigabe && x.Relevant && (kundenId == null || x.KundeID == kundenId)
                          && (x.Status != KundendokumentItemStatus.Removed || x.Status != KundendokumentItemStatus.Replaced))
                   select new KundendokumentForderungsversionViewModel()
                   {
                       //ArtikelNr + Standortobjekt
                       KundendokumentForderungsversionID = f.ForderungID,
                       ForderungID = f.ForderungID,
                       StandortObjektID = x.StandortObjektID,
                       ArtikelID = (f.Artikel != null) ? f.Artikel.ArtikelID : 0,
                       ArtikelNummer = (f.Artikel != null) ? f.Artikel.Nummer : "Keine Nummer vorhanden",
                       ErlassID = x.Forderungsversion.Erlassfassung.ErlassID,
                       erlUebersetzungen = erl.Uebersetzung,
                       stdObjUebersetzungen = x.StandortObjekt.Uebersetzung,
                       SrNummer = erl.SrNummer,
                       Beschreibung = (_currentLang == 1 ? fv.BeschreibungDE : null) ??
                                                        (_currentLang == 2 ? (fv.BeschreibungFR != "" && fv.BeschreibungFR != null ? fv.BeschreibungFR : fv.BeschreibungDE) : null) ??
                                                        (_currentLang == 3 ? (fv.BeschreibungIT != "" && fv.BeschreibungIT != null ? fv.BeschreibungIT : fv.BeschreibungDE) : null) ??
                                                        (_currentLang == 4 ? (fv.BeschreibungEN != "" && fv.BeschreibungEN != null ? fv.BeschreibungEN : fv.BeschreibungDE) : null),
                       Handlungsprioritaet = x.Forderungsversion.Bewilligungspflicht ? "hoch" : (x.Forderungsversion.Nachweispflicht ? "hoch" : "mittel"),
                       ReviewDatum = lastReview,
                       Rechtspflicht = "gültig",
                       RechtsnormID = x.Forderungsversion.Erlassfassung.ErlassID
                   }).ToList();

            foreach (var fordVersion in fordVersionen)
            {
                XDocument uebersetzungErlTitel = XDocument.Parse(fordVersion.erlUebersetzungen);
                XDocument uebersetzungStandortTitel = XDocument.Parse(fordVersion.stdObjUebersetzungen);
                fordVersion.ErlassTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlTitel, "Titel", _currentLang);
                fordVersion.Abkuerzung = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlTitel, "Abkuerzung", _currentLang);
                fordVersion.StandortObjektTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungStandortTitel, "Name", _currentLang);
                fordVersion.SrNummer += ", " + fordVersion.ErlassTitel;
                fordVersion.ArtikelNummer += ", " + fordVersion.StandortObjektTitel;
            }

            return fordVersionen.AsQueryable();
        }

        /// <summary>
        /// Gets all relevant kundendokument forderungsversion view models of the specified kundendokument.
        /// </summary>
        /// <param name="kundendokumentID">The kundendokument id.</param>
        /// <returns>Returns the view models</returns>
        public IQueryable<KundendokumentForderungsversionViewModel> GetAllRelevantKundendokumentForderungsversionViewModels(int? kundendokumentID, int standortId)
        {
            var fordVersionen = (from x in _context.KundendokumentForderungsversionen
                   from fv in _context.Forderungsversionen.Where(curFv => curFv.ForderungsversionID == x.ForderungsversionID)
                   from f in _context.Forderungen.Where(curF => curF.ForderungID == fv.ForderungID)
                   from art in _context.Artikel.Where(curArt => curArt.ArtikelID == f.ArtikelID)
                   from stdObj in _context.StandortObjekte.Where(curSta => curSta.StandortObjektID == x.StandortObjektID)
                   from erlF in _context.Erlassfassungen.Where(curErlF => curErlF.ErlassfassungID == fv.ErlassfassungID)
                   from erl in _context.Erlasse.Where(curErl => curErl.ErlassID == erlF.ErlassID)
                   where (x.KundendokumentID == kundendokumentID && x.Relevant)
                   select new KundendokumentForderungsversionViewModel()
                   {
                       KundendokumentForderungsversionID = x.KundendokumentForderungsversionID,
                       KundendokumentID = x.KundendokumentID,
                       Kundenbezug = x.Kundenbezug,
                       StandortObjektID = x.StandortObjektID,
                       Status = x.Status,
                       //Erfuellung cannot be a nullable enum because of the comboboxfilter of the gridview...
                       Erfuellung = x.Erfuellung.HasValue ? (x.Erfuellung.Value == KundendokumentErfuellung.NotDefined ? KundendokumentErfuellung.NotEdited : x.Erfuellung.Value) : KundendokumentErfuellung.NotEdited,
                       LetzterPruefZeitpunkt = x.LetztePruefungAm,
                       NaechstePruefungAm = x.NaechstePruefungAm,
                       Pruefmethode = x.Pruefmethode ?? "",
                       Verantwortlich = x.Verantwortlich ?? "",
                       Ablageort = x.Ablageort ?? "",
                       QsFreigabe = x.QsFreigabe,
                       Kommentar = x.Kommentar ?? "",
                       ForderungsversionID = x.ForderungsversionID,
                       Spalte1 = x.Spalte1 ?? "",
                       Spalte2 = x.Spalte2 ?? "",
                       Spalte3 = x.Spalte3 ?? "",
                       Spalte4 = x.Spalte4 ?? "",
                       Spalte5 = x.Spalte5 ?? "",
                       Spalte6 = x.Spalte6 ?? "",
                       Spalte7 = x.Spalte7 ?? "",
                       Spalte8 = x.Spalte8 ?? "",
                       Spalte9 = x.Spalte9 ?? "",
                       Spalte10 = x.Spalte10 ?? "",
                       ErstelltAm = x.ErstelltAm,
                       ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : "",
                       ErstelltVonID = x.ErstelltVonID,
                       BearbeitetAm = x.BearbeitetAm,
                       BearbeitetVonID = x.BearbeitetVonID,
                       BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : "",
                       stdObjUebersetzungen = stdObj.Uebersetzung,
                       ArtikelID = (f.Artikel != null) ? f.Artikel.ArtikelID : 0,
                       ArtikelNummer = (x.Forderungsversion.Forderung.Artikel != null) ? x.Forderungsversion.Forderung.Artikel.Nummer : "",
                       ArtikelQuelle = (_currentLang == 1 ? art.QuelleDE : null) ??
                                                        (_currentLang == 2 ? (art.QuelleFR != "" && art.QuelleFR != null ? art.QuelleFR : art.QuelleDE) : null) ??
                                                        (_currentLang == 3 ? (art.QuelleIT != "" && art.QuelleIT != null ? art.QuelleIT : art.QuelleFR) : null) ??
                                                        (_currentLang == 4 ? (art.QuelleEN != "" && art.QuelleEN != null ? art.QuelleEN : art.QuelleDE) : null),
                       ErlassID = x.Forderungsversion.Erlassfassung.ErlassID,
                       ErlassfassungID = x.Forderungsversion.ErlassfassungID,
                       VersionsNummer = x.Forderungsversion.VersionsNummer,
                       Bewilligungspflicht = x.Forderungsversion.Bewilligungspflicht,
                       Nachweispflicht = x.Forderungsversion.Nachweispflicht,
                       Beschreibung = (_currentLang == 1 ? fv.BeschreibungDE : null) ??
                                                        (_currentLang == 2 ? (fv.BeschreibungFR != "" && fv.BeschreibungFR != null ? fv.BeschreibungFR : fv.BeschreibungDE) : null) ??
                                                        (_currentLang == 3 ? (fv.BeschreibungIT != "" && fv.BeschreibungIT != null ? fv.BeschreibungIT : fv.BeschreibungDE) : null) ??
                                                        (_currentLang == 4 ? (fv.BeschreibungEN != "" && fv.BeschreibungEN != null ? fv.BeschreibungEN : fv.BeschreibungDE) : null),
                       Rechtsbereiche = from tu in _context.Rechtsbereiche
                                        where tu.Standorte.Contains(stdObj.Standort) && tu.Forderungsversionen.Contains(fv)
                                        select new RechtsbereichViewModel()
                                        {
                                            Name = (_currentLang == 1 ? tu.NameDE : null) ??
                                                    (_currentLang == 2 ? (tu.NameFR != "" && tu.NameFR != null ? tu.NameFR : tu.NameDE) : null) ??
                                                    (_currentLang == 3 ? (tu.NameIT != "" && tu.NameIT != null ? tu.NameIT : tu.NameIT) : null) ??
                                                    (_currentLang == 4 ? (tu.NameEN != "" && tu.NameEN != null ? tu.NameEN : tu.NameEN) : null),
                                            RechtsbereichID = tu.RechtsbereichID,
                                        },
                       erlUebersetzungen = erl.Uebersetzung,
                   }).ToList();

            foreach (var fordVersion in fordVersionen)
            {
                XDocument uebersetzungStdObjTitel = XDocument.Parse(fordVersion.stdObjUebersetzungen);
                fordVersion.StandortObjektTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungStdObjTitel, "Name", _currentLang);

                XDocument uebersetzungErlTitel = XDocument.Parse(fordVersion.erlUebersetzungen);
                fordVersion.ErlassTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlTitel, "Titel", _currentLang);
            }

            return fordVersionen.AsQueryable();
        }

        /// <summary>
        /// Gets all removed kundendokument forderungsversion view models of the specified kundendokument.
        /// </summary>
        /// <param name="kundendokumentID">The kundendokument identifier.</param>
        /// <returns></returns>
        public IQueryable<KundendokumentForderungsversionViewModel> GetAllRemovedKundendokumentForderungsversionViewModels(int? kundendokumentID, int standortId)
        {
            var fordVersionen = (from x in _context.KundendokumentForderungsversionen
                   from fv in _context.Forderungsversionen.Where(curFv => curFv.ForderungsversionID == x.ForderungsversionID)
                   from f in _context.Forderungen.Where(curF => curF.ForderungID == fv.ForderungID)
                   from art in _context.Artikel.Where(curArt => curArt.ArtikelID == f.ArtikelID)
                   from erlF in _context.Erlassfassungen.Where(curErlF => curErlF.ErlassfassungID == fv.ErlassfassungID)
                   from erl in _context.Erlasse.Where(curErl => curErl.ErlassID == erlF.ErlassID)
                   from stdObj in _context.StandortObjekte.Where(curSta => curSta.StandortObjektID == x.StandortObjektID)
                   where (x.KundendokumentID == kundendokumentID && x.Relevant == false
                            && (x.Status == KundendokumentItemStatus.Removed || x.Status == KundendokumentItemStatus.Replaced))
                   select new KundendokumentForderungsversionViewModel()
                   {
                       KundendokumentForderungsversionID = x.KundendokumentForderungsversionID,
                       KundendokumentID = x.KundendokumentID,
                       Kundenbezug = x.Kundenbezug,
                       StandortObjektID = x.StandortObjektID,
                       Status = x.Status,
                       //Erfuellung cannot be a nullable enum because of the comboboxfilter of the gridview...
                       Erfuellung = x.Erfuellung.HasValue ? (x.Erfuellung.Value == KundendokumentErfuellung.NotDefined ? KundendokumentErfuellung.NotEdited : x.Erfuellung.Value) : KundendokumentErfuellung.NotEdited,
                       LetzterPruefZeitpunkt = x.LetztePruefungAm,
                       NaechstePruefungAm = x.NaechstePruefungAm,
                       Pruefmethode = x.Pruefmethode ?? "",
                       Verantwortlich = x.Verantwortlich ?? "",
                       Ablageort = x.Ablageort ?? "",
                       QsFreigabe = x.QsFreigabe,
                       Kommentar = x.Kommentar ?? "",
                       ForderungsversionID = x.ForderungsversionID,
                       Spalte1 = x.Spalte1 ?? "",
                       Spalte2 = x.Spalte2 ?? "",
                       Spalte3 = x.Spalte3 ?? "",
                       Spalte4 = x.Spalte4 ?? "",
                       Spalte5 = x.Spalte5 ?? "",
                       Spalte6 = x.Spalte6 ?? "",
                       Spalte7 = x.Spalte7 ?? "",
                       Spalte8 = x.Spalte8 ?? "",
                       Spalte9 = x.Spalte9 ?? "",
                       Spalte10 = x.Spalte10 ?? "",
                       ErstelltAm = x.ErstelltAm,
                       ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : "",
                       ErstelltVonID = x.ErstelltVonID,
                       BearbeitetAm = x.BearbeitetAm,
                       BearbeitetVonID = x.BearbeitetVonID,
                       BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : "",
                       stdObjUebersetzungen = stdObj.Uebersetzung,
                       ArtikelID = (f.Artikel != null) ? f.Artikel.ArtikelID : 0,
                       ArtikelNummer = (x.Forderungsversion.Forderung.Artikel != null) ? x.Forderungsversion.Forderung.Artikel.Nummer : "",
                       ArtikelQuelle = (_currentLang == 1 ? art.QuelleDE : null) ??
                                                        (_currentLang == 2 ? (art.QuelleFR != "" && art.QuelleFR != null ? art.QuelleFR : art.QuelleDE) : null) ??
                                                        (_currentLang == 3 ? (art.QuelleIT != "" && art.QuelleIT != null ? art.QuelleIT : art.QuelleFR) : null) ??
                                                        (_currentLang == 4 ? (art.QuelleEN != "" && art.QuelleEN != null ? art.QuelleEN : art.QuelleDE) : null),
                       ErlassID = x.Forderungsversion.Erlassfassung.ErlassID,
                       ErlassfassungID = x.Forderungsversion.ErlassfassungID,
                       VersionsNummer = x.Forderungsversion.VersionsNummer,
                       Bewilligungspflicht = x.Forderungsversion.Bewilligungspflicht,
                       Nachweispflicht = x.Forderungsversion.Nachweispflicht,
                       Beschreibung = (_currentLang == 1 ? fv.BeschreibungDE : null) ??
                                                        (_currentLang == 2 ? (fv.BeschreibungFR != "" && fv.BeschreibungFR != null ? fv.BeschreibungFR : fv.BeschreibungDE) : null) ??
                                                        (_currentLang == 3 ? (fv.BeschreibungIT != "" && fv.BeschreibungIT != null ? fv.BeschreibungIT : fv.BeschreibungDE) : null) ??
                                                        (_currentLang == 4 ? (fv.BeschreibungEN != "" && fv.BeschreibungEN != null ? fv.BeschreibungEN : fv.BeschreibungDE) : null),
                       Rechtsbereiche = from tu in _context.Rechtsbereiche
                                        where tu.Standorte.Contains(stdObj.Standort) && tu.Forderungsversionen.Contains(fv)
                                        select new RechtsbereichViewModel()
                                        {
                                            Name = (_currentLang == 1 ? tu.NameDE : null) ??
                                                    (_currentLang == 2 ? (tu.NameFR != "" && tu.NameFR != null ? tu.NameFR : tu.NameDE) : null) ??
                                                    (_currentLang == 3 ? (tu.NameIT != "" && tu.NameIT != null ? tu.NameIT : tu.NameIT) : null) ??
                                                    (_currentLang == 4 ? (tu.NameEN != "" && tu.NameEN != null ? tu.NameEN : tu.NameEN) : null),
                                            RechtsbereichID = tu.RechtsbereichID,
                                        },
                       erlUebersetzungen = erl.Uebersetzung,
                   }).ToList();

            foreach (var fordVersion in fordVersionen)
            {
                XDocument uebersetzungStdObjTitel = XDocument.Parse(fordVersion.stdObjUebersetzungen);
                fordVersion.StandortObjektTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungStdObjTitel, "Name", _currentLang);

                XDocument uebersetzungErlTitel = XDocument.Parse(fordVersion.erlUebersetzungen);
                fordVersion.ErlassTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlTitel, "Titel", _currentLang);
            }

            return fordVersionen.AsQueryable();

        }

        /// <summary>
        /// Gets the counts of the states of all kundendokument forderungsversion of the specified kunde.
        /// </summary>
        /// <param name="id">The kunde id.</param>
        /// <returns></returns>
        public ArrayList GetAllRelevantKundendokumentForderungsversionViewModelsCount(object id, int standortId = 0)
        {
            var list = new ArrayList();
            var kundeID = Convert.ToInt32(id);
            int erfuelltResult = 0;
            int nErfuelltResult = 0;
            int inBearbeitungResult = 0;
            int nichtBearbeitetResult = 0;
            //group by StandortID first otherwise OrderByDescending is useless
            //Fixed NEOS-310 Kundendokument is not shown when PublizierenAm = current day
            var currentDay = DateTime.Today.AddDays(1);
            var kundendokument = (from k in new GenericRepository<Kundendokument>(_context).Get().ToList()
                                  where k.KundeID == kundeID && k.Status == KundendokumentStatus.Approved && k.PublizierenAm < currentDay
                                  group k by k.StandortID into grp
                                  select grp.OrderByDescending(a => a.PublizierenAm).FirstOrDefault()).ToList();

            /*
            -	Es werden alle freigegebenen und publizierten (also jeweils nur die letzten) Kundendokumente über jeden Standort betrachtet
            -	Es werden alles Forderungen (in der letzten Version), welche:
                o	für den Kunden relevant sind
                o	nicht den Status entfernt oder ersetzt haben
            -	Dann werden Sie je nach Status gruppiert:
                o	erfüllt (Häkchen durch Kunde)
                o	nicht erfüllt (Häkchen durch Kunde)
                o	in Bearbeitung 

             */
            foreach (var kdoc in kundendokument)
            {

                if (standortId != 0)
                {

                    erfuelltResult += _genericKundendokumentForderungsversionenRepository.Get(
                        f =>
                            f.Relevant && f.Erfuellung == KundendokumentErfuellung.Yes &&
                            f.Status != KundendokumentItemStatus.Removed &&
                            f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                            f.KundendokumentID == kdoc.KundendokumentID &&
                            f.StandortObjekt.StandortID == standortId
                        ).Count();

                    nErfuelltResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                f.Erfuellung == KundendokumentErfuellung.No &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.KundendokumentID == kdoc.KundendokumentID &&
                                f.StandortObjekt.StandortID == standortId
                            ).Count();

                    inBearbeitungResult += _genericKundendokumentForderungsversionenRepository.Get(
                        f =>
                            f.Relevant && f.Erfuellung == KundendokumentErfuellung.UnderClarification &&
                            f.Status != KundendokumentItemStatus.Removed &&
                            f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                            f.KundendokumentID == kdoc.KundendokumentID &&
                            f.StandortObjekt.StandortID == standortId
                        ).Count();
                    nichtBearbeitetResult += _genericKundendokumentForderungsversionenRepository.Get(
                        f =>
                            f.Relevant && (f.Erfuellung == KundendokumentErfuellung.NotDefined || f.Erfuellung == null || f.Erfuellung == KundendokumentErfuellung.NotEdited) &&
                            f.Status != KundendokumentItemStatus.Removed &&
                            f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                            f.KundendokumentID == kdoc.KundendokumentID &&
                            f.StandortObjekt.StandortID == standortId
                        ).Count();
                }
                else
                {
                    erfuelltResult += _genericKundendokumentForderungsversionenRepository.Get(
                    f =>
                        f.Relevant && f.Erfuellung == KundendokumentErfuellung.Yes &&
                        f.Status != KundendokumentItemStatus.Removed &&
                        f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                        f.KundendokumentID == kdoc.KundendokumentID
                    ).Count();

                    nErfuelltResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                f.Erfuellung == KundendokumentErfuellung.No &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.KundendokumentID == kdoc.KundendokumentID
                            ).Count();

                    inBearbeitungResult += _genericKundendokumentForderungsversionenRepository.Get(
                        f =>
                            f.Relevant && f.Erfuellung == KundendokumentErfuellung.UnderClarification &&
                            f.Status != KundendokumentItemStatus.Removed &&
                            f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                            f.KundendokumentID == kdoc.KundendokumentID
                        ).Count();
                    nichtBearbeitetResult += _genericKundendokumentForderungsversionenRepository.Get(
                        f =>
                            f.Relevant && (f.Erfuellung == KundendokumentErfuellung.NotDefined || f.Erfuellung == null || f.Erfuellung == KundendokumentErfuellung.NotEdited) &&
                            f.Status != KundendokumentItemStatus.Removed &&
                            f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                            f.KundendokumentID == kdoc.KundendokumentID
                        ).Count();
                }



            }



            list.Add(new { name = Resources.Properties.Resources.Chart_Label_Erfuellt, value = erfuelltResult });

            list.Add(new { name = Resources.Properties.Resources.Chart_Label_NichtErfuellt, value = nErfuelltResult });

            list.Add(new { name = Resources.Properties.Resources.Chart_Label_InAbklaeurung, value = inBearbeitungResult });

            list.Add(new { name = Resources.Properties.Resources.Chart_Label_NotEdited, value = nichtBearbeitetResult });

            return list;
        }

        public ArrayList GetAllRelevantKundendokumentForderungsversionViewModelsBCount(object id, int standortId = 0)
        {
            var list = new ArrayList();
            var kundeID = Convert.ToInt32(id);
            int erfuelltResult = 0;
            int nErfuelltResult = 0;
            int inBearbeitungResult = 0;
            int nichtBearbeitetResult = 0;

            //group by StandortID first otherwise OrderByDescending is useless
            //Fixed NEOS-310 Kundendokument is not shown when PublizierenAm = current day
            var currentDay = DateTime.Today.AddDays(1);
            var kundendokument = (from k in new GenericRepository<Kundendokument>(_context).Get().ToList()
                                  where k.KundeID == kundeID && k.Status == KundendokumentStatus.Approved && k.PublizierenAm < currentDay
                                  group k by k.StandortID into grp
                                  select grp.OrderByDescending(a => a.PublizierenAm).FirstOrDefault()).ToList();

            /*
            -	Es werden alle freigegebenen und publizierten (also jeweils nur die letzten) Kundendokumente über jeden Standort betrachtet
            -	Es werden alles Forderungen (in der letzten Version), welche:
                o	für den Kunden relevant sind
                o	nicht den Status entfernt oder ersetzt haben
            -	Dann werden Sie je nach Status gruppiert:
                o	erfüllt (Häkchen durch Kunde)
                o	nicht erfüllt (Häkchen durch Kunde)
                o	in Bearbeitung 

             */
            foreach (var kdoc in kundendokument)
            {

                if (standortId != 0)
                {

                    erfuelltResult += _genericKundendokumentForderungsversionenRepository.Get(
                        f =>
                            f.Relevant &&
                            f.Erfuellung == KundendokumentErfuellung.Yes &&
                            f.Forderungsversion.Bewilligungspflicht &&
                            f.Status != KundendokumentItemStatus.Removed &&
                            f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                            f.KundendokumentID == kdoc.KundendokumentID &&
                            f.StandortObjekt.StandortID == standortId
                        ).Count();

                    nErfuelltResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                f.Erfuellung == KundendokumentErfuellung.No &&
                                f.Forderungsversion.Bewilligungspflicht &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.KundendokumentID == kdoc.KundendokumentID &&
                                f.StandortObjekt.StandortID == standortId
                            ).Count();

                    inBearbeitungResult += _genericKundendokumentForderungsversionenRepository.Get(
                        f =>
                            f.Relevant && f.Erfuellung == KundendokumentErfuellung.UnderClarification &&
                            f.Forderungsversion.Bewilligungspflicht &&
                            f.Status != KundendokumentItemStatus.Removed &&
                            f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                            f.KundendokumentID == kdoc.KundendokumentID &&
                            f.StandortObjekt.StandortID == standortId
                        ).Count();

                    nichtBearbeitetResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                (f.Erfuellung == KundendokumentErfuellung.NotDefined || f.Erfuellung == null || f.Erfuellung == KundendokumentErfuellung.NotEdited) &&
                                f.Forderungsversion.Bewilligungspflicht &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.KundendokumentID == kdoc.KundendokumentID &&
                                f.StandortObjekt.StandortID == standortId
                            ).Count();
                }
                else
                {
                    erfuelltResult += _genericKundendokumentForderungsversionenRepository.Get(
                    f =>
                        f.Relevant &&
                        f.Erfuellung == KundendokumentErfuellung.Yes &&
                        f.Forderungsversion.Bewilligungspflicht &&
                        f.Status != KundendokumentItemStatus.Removed &&
                        f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                        f.KundendokumentID == kdoc.KundendokumentID
                    ).Count();

                    nErfuelltResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                f.Erfuellung == KundendokumentErfuellung.No &&
                                f.Forderungsversion.Bewilligungspflicht &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.KundendokumentID == kdoc.KundendokumentID
                            ).Count();

                    inBearbeitungResult += _genericKundendokumentForderungsversionenRepository.Get(
                        f =>
                            f.Relevant &&
                            f.Erfuellung == KundendokumentErfuellung.UnderClarification &&
                            f.Forderungsversion.Bewilligungspflicht &&
                            f.Status != KundendokumentItemStatus.Removed &&
                            f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                            f.KundendokumentID == kdoc.KundendokumentID
                        ).Count();
                    nichtBearbeitetResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                (f.Erfuellung == KundendokumentErfuellung.NotDefined || f.Erfuellung == null || f.Erfuellung == KundendokumentErfuellung.NotEdited) &&
                                f.Forderungsversion.Bewilligungspflicht &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.KundendokumentID == kdoc.KundendokumentID
                            ).Count();
                }



            }



            list.Add(new { name = Resources.Properties.Resources.Chart_Label_Erfuellt, value = erfuelltResult });

            list.Add(new { name = Resources.Properties.Resources.Chart_Label_NichtErfuellt, value = nErfuelltResult });
            
            list.Add(new { name = Resources.Properties.Resources.Chart_Label_InAbklaeurung, value = inBearbeitungResult });

            list.Add(new { name = Resources.Properties.Resources.Chart_Label_NotEdited, value = nichtBearbeitetResult });

            return list;
        }

        public ArrayList GetAllRelevantKundendokumentForderungsversionViewModelsNCount(object id, int standortId = 0)
        {
            var list = new ArrayList();
            var kundeID = Convert.ToInt32(id);
            int erfuelltResult = 0;
            int nErfuelltResult = 0;
            int inBearbeitungResult = 0;
            int nichtBearbeitetResult = 0;

            //group by StandortID first otherwise OrderByDescending is useless
            //Fixed NEOS-310 Kundendokument is not shown when PublizierenAm = current day
            var currentDay = DateTime.Today.AddDays(1);
            var kundendokument = (from k in new GenericRepository<Kundendokument>(_context).Get().ToList()
                                  where k.KundeID == kundeID && k.Status == KundendokumentStatus.Approved && k.PublizierenAm < currentDay
                                  group k by k.StandortID into grp
                                  select grp.OrderByDescending(a => a.PublizierenAm).FirstOrDefault()).ToList();

            /*
            -	Es werden alle freigegebenen und publizierten (also jeweils nur die letzten) Kundendokumente über jeden Standort betrachtet
            -	Es werden alles Forderungen (in der letzten Version), welche:
                o	für den Kunden relevant sind
                o	nicht den Status entfernt oder ersetzt haben
            -	Dann werden Sie je nach Status gruppiert:
                o	erfüllt (Häkchen durch Kunde)
                o	nicht erfüllt (Häkchen durch Kunde)
                o	in Bearbeitung 

             */
            foreach (var kdoc in kundendokument)
            {

                if (standortId != 0)
                {

                    erfuelltResult += _genericKundendokumentForderungsversionenRepository.Get(
                        f =>
                            f.Relevant &&
                            f.Erfuellung == KundendokumentErfuellung.Yes &&
                            f.Forderungsversion.Nachweispflicht &&
                            f.Status != KundendokumentItemStatus.Removed &&
                            f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                            f.KundendokumentID == kdoc.KundendokumentID &&
                            f.StandortObjekt.StandortID == standortId
                        ).Count();

                    nErfuelltResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                f.Erfuellung == KundendokumentErfuellung.No &&
                                f.Forderungsversion.Nachweispflicht &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.KundendokumentID == kdoc.KundendokumentID &&
                                f.StandortObjekt.StandortID == standortId
                            ).Count();

                    inBearbeitungResult += _genericKundendokumentForderungsversionenRepository.Get(
                        f =>
                            f.Relevant && f.Erfuellung == KundendokumentErfuellung.UnderClarification &&
                            f.Forderungsversion.Nachweispflicht &&
                            f.Status != KundendokumentItemStatus.Removed &&
                            f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                            f.KundendokumentID == kdoc.KundendokumentID &&
                            f.StandortObjekt.StandortID == standortId
                        ).Count();

                    nichtBearbeitetResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                (f.Erfuellung == KundendokumentErfuellung.NotDefined || f.Erfuellung == null || f.Erfuellung == KundendokumentErfuellung.NotEdited) &&
                                f.Forderungsversion.Nachweispflicht &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.KundendokumentID == kdoc.KundendokumentID &&
                                f.StandortObjekt.StandortID == standortId
                            ).Count();
                }
                else
                {
                    erfuelltResult += _genericKundendokumentForderungsversionenRepository.Get(
                    f =>
                        f.Relevant &&
                        f.Erfuellung == KundendokumentErfuellung.Yes &&
                        f.Forderungsversion.Nachweispflicht &&
                        f.Status != KundendokumentItemStatus.Removed &&
                        f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                        f.KundendokumentID == kdoc.KundendokumentID
                    ).Count();

                    nErfuelltResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                f.Erfuellung == KundendokumentErfuellung.No &&
                                f.Forderungsversion.Nachweispflicht &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.KundendokumentID == kdoc.KundendokumentID
                            ).Count();

                    inBearbeitungResult += _genericKundendokumentForderungsversionenRepository.Get(
                        f =>
                            f.Relevant &&
                            f.Erfuellung == KundendokumentErfuellung.UnderClarification &&
                            f.Forderungsversion.Nachweispflicht &&
                            f.Status != KundendokumentItemStatus.Removed &&
                            f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                            f.KundendokumentID == kdoc.KundendokumentID
                        ).Count();

                    nichtBearbeitetResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                (f.Erfuellung == KundendokumentErfuellung.NotDefined || f.Erfuellung == null || f.Erfuellung == KundendokumentErfuellung.NotEdited) &&
                                f.Forderungsversion.Nachweispflicht &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.KundendokumentID == kdoc.KundendokumentID
                            ).Count();
                }



            }



            list.Add(new { name = Resources.Properties.Resources.Chart_Label_Erfuellt, value = erfuelltResult });

            list.Add(new { name = Resources.Properties.Resources.Chart_Label_NichtErfuellt, value = nErfuelltResult });

            list.Add(new { name = Resources.Properties.Resources.Chart_Label_InAbklaeurung, value = inBearbeitungResult });

            list.Add(new { name = Resources.Properties.Resources.Chart_Label_NotEdited, value = nichtBearbeitetResult });

            return list;
        }

        public ArrayList GetAllRelevantKundendokumentForderungsversionViewModelsBandNCount(object id, int standortId = 0)
        {
            var list = new ArrayList();
            var kundeID = Convert.ToInt32(id);
            int erfuelltResult = 0;
            int nErfuelltResult = 0;
            int inBearbeitungResult = 0;
            int nichtBearbeitetResult = 0;

            //group by StandortID first otherwise OrderByDescending is useless
            //Fixed NEOS-310 Kundendokument is not shown when PublizierenAm = current day
            var currentDay = DateTime.Today.AddDays(1);
            var kundendokument = (from k in new GenericRepository<Kundendokument>(_context).Get().ToList()
                                  where k.KundeID == kundeID && k.Status == KundendokumentStatus.Approved && k.PublizierenAm < currentDay
                                  group k by k.StandortID into grp
                                  select grp.OrderByDescending(a => a.PublizierenAm).FirstOrDefault()).ToList();

            /*
            -	Es werden alle freigegebenen und publizierten (also jeweils nur die letzten) Kundendokumente über jeden Standort betrachtet
            -	Es werden alles Forderungen (in der letzten Version), welche:
                o	für den Kunden relevant sind
                o	nicht den Status entfernt oder ersetzt haben
            -	Dann werden Sie je nach Status gruppiert:
                o	erfüllt (Häkchen durch Kunde)
                o	nicht erfüllt (Häkchen durch Kunde)
                o	in Bearbeitung 

             */
            foreach (var kdoc in kundendokument)
            {

                if (standortId != 0)
                {

                    erfuelltResult += _genericKundendokumentForderungsversionenRepository.Get(
                        f =>
                            f.Relevant &&
                            f.Erfuellung == KundendokumentErfuellung.Yes &&
                            f.Forderungsversion.Bewilligungspflicht &&
                            f.Forderungsversion.Nachweispflicht &&
                            f.Status != KundendokumentItemStatus.Removed &&
                            f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                            f.KundendokumentID == kdoc.KundendokumentID &&
                            f.StandortObjekt.StandortID == standortId
                        ).Count();

                    nErfuelltResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                f.Erfuellung == KundendokumentErfuellung.No &&
                                f.Forderungsversion.Bewilligungspflicht &&
                                f.Forderungsversion.Nachweispflicht &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.KundendokumentID == kdoc.KundendokumentID &&
                                f.StandortObjekt.StandortID == standortId
                            ).Count();

                    inBearbeitungResult += _genericKundendokumentForderungsversionenRepository.Get(
                        f =>
                            f.Relevant && f.Erfuellung == KundendokumentErfuellung.UnderClarification &&
                            f.Forderungsversion.Bewilligungspflicht &&
                            f.Forderungsversion.Nachweispflicht &&
                            f.Status != KundendokumentItemStatus.Removed &&
                            f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                            f.KundendokumentID == kdoc.KundendokumentID &&
                            f.StandortObjekt.StandortID == standortId
                        ).Count();

                    nichtBearbeitetResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                (f.Erfuellung == KundendokumentErfuellung.NotDefined || f.Erfuellung == null || f.Erfuellung == KundendokumentErfuellung.NotEdited) &&
                                f.Forderungsversion.Bewilligungspflicht &&
                                f.Forderungsversion.Nachweispflicht &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.KundendokumentID == kdoc.KundendokumentID &&
                                f.StandortObjekt.StandortID == standortId
                            ).Count();
                }
                else
                {
                    erfuelltResult += _genericKundendokumentForderungsversionenRepository.Get(
                    f =>
                        f.Relevant &&
                        f.Erfuellung == KundendokumentErfuellung.Yes &&
                        f.Forderungsversion.Bewilligungspflicht &&
                        f.Forderungsversion.Nachweispflicht &&
                        f.Status != KundendokumentItemStatus.Removed &&
                        f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                        f.KundendokumentID == kdoc.KundendokumentID
                    ).Count();

                    nErfuelltResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                f.Erfuellung == KundendokumentErfuellung.No &&
                                f.Forderungsversion.Bewilligungspflicht &&
                                f.Forderungsversion.Nachweispflicht &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.KundendokumentID == kdoc.KundendokumentID
                            ).Count();

                    inBearbeitungResult += _genericKundendokumentForderungsversionenRepository.Get(
                        f =>
                            f.Relevant &&
                            f.Erfuellung == KundendokumentErfuellung.UnderClarification &&
                            f.Forderungsversion.Bewilligungspflicht &&
                            f.Forderungsversion.Nachweispflicht &&
                            f.Status != KundendokumentItemStatus.Removed &&
                            f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                            f.KundendokumentID == kdoc.KundendokumentID
                        ).Count();

                    nichtBearbeitetResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                (f.Erfuellung == KundendokumentErfuellung.NotDefined || f.Erfuellung == null || f.Erfuellung == KundendokumentErfuellung.NotEdited) &&
                                f.Forderungsversion.Bewilligungspflicht &&
                                f.Forderungsversion.Nachweispflicht &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.KundendokumentID == kdoc.KundendokumentID
                            ).Count();
                }



            }



            list.Add(new { name = Resources.Properties.Resources.Chart_Label_Erfuellt, value = erfuelltResult });

            list.Add(new { name = Resources.Properties.Resources.Chart_Label_NichtErfuellt, value = nErfuelltResult });

            list.Add(new { name = Resources.Properties.Resources.Chart_Label_InAbklaeurung, value = inBearbeitungResult });

            list.Add(new { name = Resources.Properties.Resources.Chart_Label_NotEdited, value = nichtBearbeitetResult });

            return list;
        }

        public ArrayList GetAllRelevantKundendokumentForderungsversionViewModelsStObjCount(object id, int standortObjektId, int standortId = 0)
        {
            var list = new ArrayList();
            var kundeID = Convert.ToInt32(id);
            int erfuelltResult = 0;
            int nErfuelltResult = 0;
            int inBearbeitungResult = 0;
            int nichtBearbeitetResult = 0;

            //group by StandortID first otherwise OrderByDescending is useless
            //Fixed NEOS-310 Kundendokument is not shown when PublizierenAm = current day
            var currentDay = DateTime.Today.AddDays(1);
            var kundendokument = (from k in new GenericRepository<Kundendokument>(_context).Get().ToList()
                                  where k.KundeID == kundeID && k.Status == KundendokumentStatus.Approved && k.PublizierenAm < currentDay
                                  group k by k.StandortID into grp
                                  select grp.OrderByDescending(a => a.PublizierenAm).FirstOrDefault()).ToList();

            /*
            -	Es werden alle freigegebenen und publizierten (also jeweils nur die letzten) Kundendokumente über jeden Standort betrachtet
            -	Es werden alles Forderungen (in der letzten Version), welche:
                o	für den Kunden relevant sind
                o	nicht den Status entfernt oder ersetzt haben
            -	Dann werden Sie je nach Status gruppiert:
                o	erfüllt (Häkchen durch Kunde)
                o	nicht erfüllt (Häkchen durch Kunde)
                o	in Bearbeitung 

             */
            foreach (var kdoc in kundendokument)
            {

                if (standortId != 0)
                {

                    erfuelltResult += _genericKundendokumentForderungsversionenRepository.Get(
                        f =>
                            f.Relevant &&
                            f.Erfuellung == KundendokumentErfuellung.Yes &&
                            f.Status != KundendokumentItemStatus.Removed &&
                            f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                            f.KundendokumentID == kdoc.KundendokumentID &&
                            f.StandortObjektID == standortObjektId &&
                            f.StandortObjekt.StandortID == standortId
                        ).Count();

                    nErfuelltResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                f.Erfuellung == KundendokumentErfuellung.No &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.KundendokumentID == kdoc.KundendokumentID &&
                                f.StandortObjektID == standortObjektId &&
                                f.StandortObjekt.StandortID == standortId
                            ).Count();

                    inBearbeitungResult += _genericKundendokumentForderungsversionenRepository.Get(
                        f =>
                            f.Relevant && f.Erfuellung == KundendokumentErfuellung.UnderClarification &&
                            f.Status != KundendokumentItemStatus.Removed &&
                            f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                            f.KundendokumentID == kdoc.KundendokumentID &&
                            f.StandortObjektID == standortObjektId &&
                            f.StandortObjekt.StandortID == standortId
                        ).Count();

                    nichtBearbeitetResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                (f.Erfuellung == KundendokumentErfuellung.NotDefined || f.Erfuellung == null || f.Erfuellung == KundendokumentErfuellung.NotEdited) &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.KundendokumentID == kdoc.KundendokumentID &&
                                f.StandortObjektID == standortObjektId &&
                                f.StandortObjekt.StandortID == standortId
                            ).Count();
                }
                else
                {
                    erfuelltResult += _genericKundendokumentForderungsversionenRepository.Get(
                    f =>
                        f.Relevant &&
                        f.Erfuellung == KundendokumentErfuellung.Yes &&
                        f.Status != KundendokumentItemStatus.Removed &&
                        f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                        f.StandortObjektID == standortObjektId &&
                        f.KundendokumentID == kdoc.KundendokumentID
                    ).Count();

                    nErfuelltResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                f.Erfuellung == KundendokumentErfuellung.No &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.StandortObjektID == standortObjektId &&
                                f.KundendokumentID == kdoc.KundendokumentID
                            ).Count();

                    inBearbeitungResult += _genericKundendokumentForderungsversionenRepository.Get(
                        f =>
                            f.Relevant &&
                            f.Erfuellung == KundendokumentErfuellung.UnderClarification &&
                            f.Status != KundendokumentItemStatus.Removed &&
                            f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                            f.StandortObjektID == standortObjektId &&
                            f.KundendokumentID == kdoc.KundendokumentID
                        ).Count();

                    nichtBearbeitetResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                (f.Erfuellung == KundendokumentErfuellung.NotDefined || f.Erfuellung == null || f.Erfuellung == KundendokumentErfuellung.NotEdited) &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.StandortObjektID == standortObjektId &&
                                f.KundendokumentID == kdoc.KundendokumentID
                            ).Count();
                }



            }



            list.Add(new { name = Resources.Properties.Resources.Chart_Label_Erfuellt, value = erfuelltResult });

            list.Add(new { name = Resources.Properties.Resources.Chart_Label_NichtErfuellt, value = nErfuelltResult });

            list.Add(new { name = Resources.Properties.Resources.Chart_Label_InAbklaeurung, value = inBearbeitungResult });

            list.Add(new { name = Resources.Properties.Resources.Chart_Label_NotEdited, value = nichtBearbeitetResult });

            return list;
        }

        public ArrayList GetAllRelevantKundendokumentForderungsversionViewModelsRechtsbereichCount(object id, int rechtsbereichId, int standortId = 0)
        {
            var list = new ArrayList();
            var kundeID = Convert.ToInt32(id);
            int erfuelltResult = 0;
            int nErfuelltResult = 0;
            int inBearbeitungResult = 0;
            int nichtBearbeitetResult = 0;

            //group by StandortID first otherwise OrderByDescending is useless
            //Fixed NEOS-310 Kundendokument is not shown when PublizierenAm = current day
            var currentDay = DateTime.Today.AddDays(1);
            var kundendokument = (from k in new GenericRepository<Kundendokument>(_context).Get().ToList()
                                  where k.KundeID == kundeID && k.Status == KundendokumentStatus.Approved && k.PublizierenAm < currentDay
                                  group k by k.StandortID into grp
                                  select grp.OrderByDescending(a => a.PublizierenAm).FirstOrDefault()).ToList();

            Rechtsbereich rechtsbereich = new Rechtsbereich();
            if (rechtsbereichId != 0)
            {
                unitOfWork.RechtsbereichRepository.GetByID(rechtsbereichId);
            }
            /*
            -	Es werden alle freigegebenen und publizierten (also jeweils nur die letzten) Kundendokumente über jeden Standort betrachtet
            -	Es werden alles Forderungen (in der letzten Version), welche:
                o	für den Kunden relevant sind
                o	nicht den Status entfernt oder ersetzt haben
            -	Dann werden Sie je nach Status gruppiert:
                o	erfüllt (Häkchen durch Kunde)
                o	nicht erfüllt (Häkchen durch Kunde)
                o	in Bearbeitung 

             */
            foreach (var kdoc in kundendokument)
            {

                if (standortId != 0 && rechtsbereichId != 0)
                {

                    erfuelltResult += _genericKundendokumentForderungsversionenRepository.Get(
                        f =>
                            f.Relevant &&
                            f.Erfuellung == KundendokumentErfuellung.Yes &&
                            f.Status != KundendokumentItemStatus.Removed &&
                            f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                            f.KundendokumentID == kdoc.KundendokumentID &&
                            f.Forderungsversion.Rechtsbereiche.Any(r => r.RechtsbereichID == rechtsbereichId) &&
                            f.StandortObjekt.StandortID == standortId
                        ).Count();

                    nErfuelltResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                f.Erfuellung == KundendokumentErfuellung.No &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.KundendokumentID == kdoc.KundendokumentID &&
                                f.Forderungsversion.Rechtsbereiche.Any(r => r.RechtsbereichID == rechtsbereichId) &&
                                f.StandortObjekt.StandortID == standortId
                            ).Count();

                    inBearbeitungResult += _genericKundendokumentForderungsversionenRepository.Get(
                        f =>
                            f.Relevant && f.Erfuellung == KundendokumentErfuellung.UnderClarification &&
                            f.Status != KundendokumentItemStatus.Removed &&
                            f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                            f.KundendokumentID == kdoc.KundendokumentID &&
                            f.Forderungsversion.Rechtsbereiche.Any(r => r.RechtsbereichID == rechtsbereichId) &&
                            f.StandortObjekt.StandortID == standortId
                        ).Count();

                    nichtBearbeitetResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                (f.Erfuellung == KundendokumentErfuellung.NotDefined || f.Erfuellung == null || f.Erfuellung == KundendokumentErfuellung.NotEdited) &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.KundendokumentID == kdoc.KundendokumentID &&
                                f.Forderungsversion.Rechtsbereiche.Any(r => r.RechtsbereichID == rechtsbereichId) &&
                                f.StandortObjekt.StandortID == standortId
                            ).Count();
                }
            }



            list.Add(new { name = Resources.Properties.Resources.Chart_Label_Erfuellt, value = erfuelltResult });

            list.Add(new { name = Resources.Properties.Resources.Chart_Label_NichtErfuellt, value = nErfuelltResult });

            list.Add(new { name = Resources.Properties.Resources.Chart_Label_InAbklaeurung, value = inBearbeitungResult });

            list.Add(new { name = Resources.Properties.Resources.Chart_Label_NotEdited, value = nichtBearbeitetResult });

            return list;
        }

        public ArrayList GetAllRelevantKundendokumentForderungsversionViewModelsShortcutCount(object id, int shortcutId, int standortId = 0)
        {
            var list = new ArrayList();
            var kundeID = Convert.ToInt32(id);
            int erfuelltResult = 0;
            int nErfuelltResult = 0;
            int inBearbeitungResult = 0;
            int nichtBearbeitetResult = 0;

            //group by StandortID first otherwise OrderByDescending is useless
            //Fixed NEOS-310 Kundendokument is not shown when PublizierenAm = current day
            var currentDay = DateTime.Today.AddDays(1);
            var kundendokument = (from k in new GenericRepository<Kundendokument>(_context).Get().ToList()
                                  where k.KundeID == kundeID && k.Status == KundendokumentStatus.Approved && k.PublizierenAm < currentDay
                                  group k by k.StandortID into grp
                                  select grp.OrderByDescending(a => a.PublizierenAm).FirstOrDefault()).ToList();

            /*
            -	Es werden alle freigegebenen und publizierten (also jeweils nur die letzten) Kundendokumente über jeden Standort betrachtet
            -	Es werden alles Forderungen (in der letzten Version), welche:
                o	für den Kunden relevant sind
                o	nicht den Status entfernt oder ersetzt haben
            -	Dann werden Sie je nach Status gruppiert:
                o	erfüllt (Häkchen durch Kunde)
                o	nicht erfüllt (Häkchen durch Kunde)
                o	in Bearbeitung 

             */
            foreach (var kdoc in kundendokument)
            {

                if (standortId != 0)
                {

                    erfuelltResult += _genericKundendokumentForderungsversionenRepository.Get(
                        f =>
                            f.Relevant &&
                            f.Erfuellung == KundendokumentErfuellung.Yes &&
                            f.Status != KundendokumentItemStatus.Removed &&
                            f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                            f.KundendokumentID == kdoc.KundendokumentID &&
                            f.Shortcut.ShortcutID == shortcutId &&
                            f.StandortObjekt.StandortID == standortId
                        ).Count();

                    nErfuelltResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                f.Erfuellung == KundendokumentErfuellung.No &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.KundendokumentID == kdoc.KundendokumentID &&
                            f.Shortcut.ShortcutID == shortcutId &&
                                f.StandortObjekt.StandortID == standortId
                            ).Count();

                    inBearbeitungResult += _genericKundendokumentForderungsversionenRepository.Get(
                        f =>
                            f.Relevant && f.Erfuellung == KundendokumentErfuellung.UnderClarification &&
                            f.Status != KundendokumentItemStatus.Removed &&
                            f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                            f.KundendokumentID == kdoc.KundendokumentID &&
                            f.Shortcut.ShortcutID == shortcutId &&
                            f.StandortObjekt.StandortID == standortId
                        ).Count();

                    nichtBearbeitetResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                (f.Erfuellung == KundendokumentErfuellung.NotDefined || f.Erfuellung == null || f.Erfuellung == KundendokumentErfuellung.NotEdited) &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.KundendokumentID == kdoc.KundendokumentID &&
                            f.Shortcut.ShortcutID == shortcutId &&
                                f.StandortObjekt.StandortID == standortId
                            ).Count();
                }
                else
                {
                    erfuelltResult += _genericKundendokumentForderungsversionenRepository.Get(
                    f =>
                        f.Relevant &&
                        f.Erfuellung == KundendokumentErfuellung.Yes &&
                        f.Status != KundendokumentItemStatus.Removed &&
                        f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                        f.Shortcut.ShortcutID == shortcutId &&
                        f.KundendokumentID == kdoc.KundendokumentID
                    ).Count();

                    nErfuelltResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                f.Erfuellung == KundendokumentErfuellung.No &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.Shortcut.ShortcutID == shortcutId &&
                                f.KundendokumentID == kdoc.KundendokumentID
                            ).Count();

                    inBearbeitungResult += _genericKundendokumentForderungsversionenRepository.Get(
                        f =>
                            f.Relevant &&
                            f.Erfuellung == KundendokumentErfuellung.UnderClarification &&
                            f.Status != KundendokumentItemStatus.Removed &&
                            f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                            f.Shortcut.ShortcutID == shortcutId &&
                            f.KundendokumentID == kdoc.KundendokumentID
                        ).Count();

                    nichtBearbeitetResult +=
                        _genericKundendokumentForderungsversionenRepository.Get(
                            f =>
                                f.Relevant &&
                                (f.Erfuellung == KundendokumentErfuellung.NotDefined || f.Erfuellung == null || f.Erfuellung == KundendokumentErfuellung.NotEdited) &&
                                f.Status != KundendokumentItemStatus.Removed &&
                                f.Status != KundendokumentItemStatus.Replaced && f.KundeID == kundeID &&
                                f.Shortcut.ShortcutID == shortcutId &&
                                f.KundendokumentID == kdoc.KundendokumentID
                            ).Count();
                }



            }



            list.Add(new { name = Resources.Properties.Resources.Chart_Label_Erfuellt, value = erfuelltResult });

            list.Add(new { name = Resources.Properties.Resources.Chart_Label_NichtErfuellt, value = nErfuelltResult });

            list.Add(new { name = Resources.Properties.Resources.Chart_Label_InAbklaeurung, value = inBearbeitungResult });

            list.Add(new { name = Resources.Properties.Resources.Chart_Label_NotEdited, value = nichtBearbeitetResult });

            return list;
        }

        /// <summary>
        /// Gets the view models by ids.
        /// </summary>
        /// <param name="ids">The ids.</param>
        /// <returns>Returns the view models.</returns>
        public List<Int32> GetIDs(ISet<int> ids)
        {
            var versionenIds = new List<Int32>();

            if (ids == null)
            {
                return versionenIds;
            }

            var version = (from f in _context.KundendokumentForderungsversionen where (ids.Contains(f.ForderungsversionID)) select f.Forderungsversion.ForderungID).ToList();

            return version;

        }

        /// <summary>
        /// Gets the view model by id.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>Returns the view models</returns>
        public KundendokumentForderungsversionViewModel GetViewModelByID(int id)
        {
            _genericKundendokumentForderungsversionenRepository.Get(null, null, "Forderungsversion").ToList();
            return GetViewModel(GetByID(id, "Forderungsversion"), _currentLang);
        }

        private KundendokumentForderungsversionViewModel GetViewModel(KundendokumentForderungsversion kundendokumentForderungsversion, int lang)
        {

            var standortObjekt = _genericStandortObjektRepository.Get(e => e.StandortObjektID == kundendokumentForderungsversion.StandortObjektID, null, "").FirstOrDefault();
            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;

            var viewModel = new KundendokumentForderungsversionViewModel
            {
                KundendokumentForderungsversionID = kundendokumentForderungsversion.KundendokumentForderungsversionID,
                KundendokumentID = kundendokumentForderungsversion.KundendokumentID,
                Kundenbezug = kundendokumentForderungsversion.Kundenbezug,
                StandortObjektID = kundendokumentForderungsversion.StandortObjektID,
                Status = kundendokumentForderungsversion.Status,
                Erfuellung = kundendokumentForderungsversion.Erfuellung.HasValue ? kundendokumentForderungsversion.Erfuellung.Value : KundendokumentErfuellung.NotEdited,
                LetzterPruefZeitpunkt = kundendokumentForderungsversion.LetztePruefungAm,
                NaechstePruefungAm = kundendokumentForderungsversion.NaechstePruefungAm,
                Pruefmethode = kundendokumentForderungsversion.Pruefmethode,
                Verantwortlich = kundendokumentForderungsversion.Verantwortlich,
                Ablageort = kundendokumentForderungsversion.Ablageort,
                QsFreigabe = kundendokumentForderungsversion.QsFreigabe,
                Kommentar = kundendokumentForderungsversion.Kommentar,
                ForderungsversionID = kundendokumentForderungsversion.ForderungsversionID,
                Spalte1 = kundendokumentForderungsversion.Spalte1,
                Spalte2 = kundendokumentForderungsversion.Spalte2,
                Spalte3 = kundendokumentForderungsversion.Spalte3,
                Spalte4 = kundendokumentForderungsversion.Spalte4,
                Spalte5 = kundendokumentForderungsversion.Spalte5,
                Spalte6 = kundendokumentForderungsversion.Spalte6,
                Spalte7 = kundendokumentForderungsversion.Spalte7,
                Spalte8 = kundendokumentForderungsversion.Spalte8,
                Spalte9 = kundendokumentForderungsversion.Spalte9,
                Spalte10 = kundendokumentForderungsversion.Spalte10,
                BearbeitetAm = kundendokumentForderungsversion.BearbeitetAm,
                BearbeitetVon = (kundendokumentForderungsversion.BearbeitetVon != null) ? kundendokumentForderungsversion.BearbeitetVon.FullName : null,
                ErstelltVon = (kundendokumentForderungsversion.ErstelltVon != null) ? kundendokumentForderungsversion.ErstelltVon.FullName : null,
                ErstelltAm = kundendokumentForderungsversion.ErstelltAm,
                ForderungID = kundendokumentForderungsversion.Forderungsversion != null ? kundendokumentForderungsversion.Forderungsversion.ForderungID : 0
            };

            //load erlass properties
            if (kundendokumentForderungsversion.Forderungsversion != null)
            {
                var sprachen = _context.Sprachen;

                if (kundendokumentForderungsversion.Forderungsversion.Erlassfassung != null)
                {
                    viewModel.ErlassID = kundendokumentForderungsversion.Forderungsversion.Erlassfassung.ErlassID;
                }

                viewModel.ErlassfassungID = kundendokumentForderungsversion.Forderungsversion.ErlassfassungID;
                viewModel.VersionsNummer = kundendokumentForderungsversion.Forderungsversion.VersionsNummer;
                viewModel.Bewilligungspflicht = kundendokumentForderungsversion.Forderungsversion.Bewilligungspflicht;
                viewModel.Nachweispflicht = kundendokumentForderungsversion.Forderungsversion.Nachweispflicht;
            }


            return viewModel;
        }

        public IQueryable<KundendokumentForderungsversion> GetKundendokumentForderungsversionenByShortcutId(int shortcutID)
        {
            return _genericKundendokumentForderungsversionenRepository.Get().Where(x => x.Shortcut.ShortcutID == shortcutID);
        }

        public IQueryable<KundendokumentForderungsversionViewModel> GetKundendokumentForderungsversionToUpdateShortcut(int kundendokumentID, int standortObjektID)
        {
            {
                var fordVersionen = (from x in _context.KundendokumentForderungsversionen
                                     where (x.KundendokumentID == kundendokumentID
                                              && x.QsFreigabe
                                              && x.StandortObjektID == standortObjektID
                                              && x.Relevant
                                              && (x.Status != KundendokumentItemStatus.Removed || x.Status != KundendokumentItemStatus.Replaced))
                                     select new KundendokumentForderungsversionViewModel()
                                     {
                                         KundendokumentForderungsversionID = x.KundendokumentForderungsversionID,
                                         KundendokumentID = x.KundendokumentID,
                                         StandortObjektID = x.StandortObjektID,
                                         ShortcutID = (x.Shortcut != null) ? (x.Shortcut.ShortcutID) : 1,
                                         Shortcut = (x.Shortcut != null) ? (x.Shortcut.Name) : "",
                                     });

                return fordVersionen;
            }
        }

        public KundendokumentForderungsversionViewModel GetKundendokumentForderungsversionByErlassfassung(int erlassfassungID, int kundendokumentID)
        {
            return (from x in _context.KundendokumentForderungsversionen
                    where x.KundendokumentID == kundendokumentID && x.Forderungsversion.ErlassfassungID == erlassfassungID
                    select new KundendokumentForderungsversionViewModel()
                    {
                        KundendokumentForderungsversionID = x.KundendokumentForderungsversionID,
                        LetzterPruefZeitpunkt = x.LetztePruefungAm,
                        NaechstePruefungAm = x.NaechstePruefungAm
                    }).First();
        }

        public KundendokumentForderungsversion GetKfvByErlassfassungID(int erlassfassungID, int kundendokumentID)
        {
            return (from x in _context.KundendokumentForderungsversionen
                    where x.KundendokumentID == kundendokumentID && x.Forderungsversion.ErlassfassungID == erlassfassungID
                    select x).First();
        }
    }
}