using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Xml.Linq;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Repositories
{
    public class KundendokumentChecklistRepository
    {
        private readonly NeoSysLCS_Dev _context;
        private readonly IGenericRepository<KundendokumentChecklist> _genericKundendokumentChecklistRepository;
        private readonly UnitOfWork _unitOfWork;
        private int _currentLang;

        /// <summary>
        /// Initializes a new instance.
        /// </summary>
        /// <param name="contextCandidate">The context candidate.</param>
        public KundendokumentChecklistRepository(NeoSysLCS_Dev context)
        {
            _context = context;
            _genericKundendokumentChecklistRepository = new GenericRepository<KundendokumentChecklist>(context);
            _unitOfWork = new UnitOfWork(context);

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
            _currentLang = sprache.SpracheID;
        }

      
        public void DeleteRange(IEnumerable<KundendokumentChecklist> entitiesToDelete)
        {
            _genericKundendokumentChecklistRepository.DeleteRange(entitiesToDelete);
        }

        public void Delete(int id)
        {
            _genericKundendokumentChecklistRepository.Delete(id);
        }

        public List<KundendokumentChecklist> GetKundendokumentChecklistsByKundendokument(int? kundendokumentID)
        {
            if (!kundendokumentID.HasValue)
            {
                return new List<KundendokumentChecklist>();
            }

            var kundendokumentChecklists =
                _genericKundendokumentChecklistRepository.Get(c => c.KundendokumentID == kundendokumentID,
                    null, "Checklist,Questions").ToList();

            return kundendokumentChecklists;
        }

        public KundendokumentChecklist GetByID(object id)
        {

            return _genericKundendokumentChecklistRepository.Get(c => c.KundendokumentChecklistID == (int)id,
                null, "Checklist,Questions").FirstOrDefault();
        }

        public KundendokumentChecklist GetBasedOnKundendokumentAndChecklistIds(int kundendokumentId, int checklistId)
        {
            return _genericKundendokumentChecklistRepository.Get(c => c.KundendokumentID == kundendokumentId && c.ChecklistID == checklistId).FirstOrDefault();
        }

        public IQueryable<KundendokumentChecklistViewModel> GetKundendokumentChecklistsViewModels(int? kundendokumentID, int spracheID)
        {
            int? kundenId = _genericKundendokumentChecklistRepository.GetKundeIdOfCurrentUser();
            var checkliste = (from x in _context.KundendokumentChecklist
                    join chkLst in _context.Checklist on x.ChecklistID equals  chkLst.ChecklistID
                    join erlass in _context.Erlasse on chkLst.ErlassID equals erlass.ErlassID
                    join herausgeber in _context.Herausgebers on erlass.HerausgeberId equals herausgeber.HerausgeberID
                    where (kundenId == null && x.KundendokumentID == kundendokumentID) || (x.KundeID == kundenId && x.KundendokumentID == kundendokumentID)
                    select new KundendokumentChecklistViewModel()
                    {
                        KundendokumentChecklistID = x.KundendokumentChecklistID,
                        checklistUebersetzungen = x.Translation,
                        KundendokumentID = x.KundendokumentID,
                        ErlassID = erlass.ErlassID,
                        ErlassfassungID = x.ErlassfassungID,
                        erlassUebersetzungen = erlass.Uebersetzung,
                        ErlassSrNummer = erlass.SrNummer ?? "",
                        HerausgeberID = herausgeber.HerausgeberID,
                        HerausgeberName = (spracheID == 1 ? (herausgeber.NameDE ?? herausgeber.NameFR ?? herausgeber.NameIT ?? herausgeber.NameEN) : null) ??
                                          (spracheID == 2 ? (herausgeber.NameFR ?? herausgeber.NameDE ?? herausgeber.NameIT ?? herausgeber.NameEN) : null) ??
                                          (spracheID == 3 ? (herausgeber.NameIT ?? herausgeber.NameDE ?? herausgeber.NameFR ?? herausgeber.NameEN) : null) ??
                                          (spracheID == 4 ? (herausgeber.NameEN ?? herausgeber.NameDE ?? herausgeber.NameFR ?? herausgeber.NameIT) : null),
                        Status = x.Status,
                        ErstelltAm = x.ErstelltAm,
                        ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                        ErstelltVonID = x.ErstelltVonID,
                        BearbeitetAm = x.BearbeitetAm,
                        BearbeitetVonID = x.BearbeitetVonID,
                        BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                    }).ToList();
            
            foreach(var chk in checkliste)
            {
               XDocument checklistUebersetzung = XDocument.Parse(chk.checklistUebersetzungen);
               chk.ChecklistTitle = XMLHelper.GetUebersetzungFromXmlField(checklistUebersetzung, "Title", spracheID);
               chk.ChecklistDescription = XMLHelper.GetUebersetzungFromXmlField(checklistUebersetzung, "Description", spracheID);
                
                XDocument uebersetzungErlass = XDocument.Parse(chk.erlassUebersetzungen);
                chk.ErlassTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Titel", spracheID);
                chk.ErlassQuelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Quelle", spracheID);

                try
                {
                    KundendokumentForderungsversionViewModel kfv = _unitOfWork.KundendokumentForderungsversionRepository.GetKundendokumentForderungsversionByErlassfassung(chk.ErlassfassungID, kundendokumentID.Value);
                    chk.NaechstePruefungAm = kfv.NaechstePruefungAm;
                    chk.LetzterPruefZeitpunkt = kfv.LetzterPruefZeitpunkt;
                    chk.KundendokumentForderungsversionID = kfv.KundendokumentForderungsversionID;
                }
                catch (Exception e) // catch exception if kfv is null
                {
                    var test = e.Message;
                }
            }
            return checkliste.AsQueryable();
        }

        public KundendokumentChecklistViewModel GetKundendokumentChecklistViewModelByChecklist(int kundendokumentChecklistId)
        {
            var checkliste = (from x in _context.KundendokumentChecklist
                              join chkLst in _context.Checklist on x.ChecklistID equals chkLst.ChecklistID
                              where x.KundendokumentChecklistID == kundendokumentChecklistId
                              select new KundendokumentChecklistViewModel()
                              {
                                  KundendokumentChecklistID = x.KundendokumentChecklistID,
                                  checklistUebersetzungen = x.Translation,
                                  KundendokumentID = x.KundendokumentID,
                                  ErlassID = chkLst.ErlassID,
                                  Status = x.Status,
                                  ErstelltAm = x.ErstelltAm,
                                  ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                                  ErstelltVonID = x.ErstelltVonID,
                                  BearbeitetAm = x.BearbeitetAm,
                                  BearbeitetVonID = x.BearbeitetVonID,
                                  BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                              }).ToList();

            return checkliste.First();
        }

        public IQueryable<KundendokumentChecklistQuestionViewModel> GetKundendokumentChecklistQuestionsViewModels(int kundendokumentChecklistID, int spracheID)
        {
            var kundendokumentChecklist = GetByID(kundendokumentChecklistID);
            var questions = (from q in kundendokumentChecklist.Questions
                select new KundendokumentChecklistQuestionViewModel()
                {
                    KundendokumentChecklistQuestionID = q.KundenDokumentChecklistQuestionID,
                    KundendokumentChecklistID = kundendokumentChecklistID,
                    SharedImage = q.SharedImage,
                    Translation = q.Translation,
                    Answer = q.Answer,
                    HeaderID = q.ChecklistHeaderID,
                    Numeration = q.Numeration,
                    ChecklistType = q.ChecklistType,
                }).ToList();

            foreach (var q in questions)
            {
                var translation = XDocument.Parse(q.Translation);
                q.Title = XMLHelper.GetUebersetzungFromXmlField(translation, "Title", spracheID);

                var headerTranslation = (from x in _context.ChecklistHeader
                                         where x.ChecklistHeaderID == q.HeaderID
                                         select x.Translation).First();

                var headerTranslationParsed = XDocument.Parse(headerTranslation);

                var headerId = "" + q.HeaderID;
                if (q.HeaderID < 10)
                {
                    headerId = "0" + q.HeaderID;
                }

                q.HeaderTitle = headerId + ": " + XMLHelper.GetUebersetzungFromXmlField(headerTranslationParsed, "Title", _currentLang);

                if (q.ChecklistType == "Checkpoint")
                {
                    q.Answer = KundendokumentQuestionAnswer.Checkpoint;
                }

                // get current massnahme for this kommentar to add the counter
                List<MassnahmeViewModel> massnahmeViewModels = _unitOfWork.MassnahmeRepository.GetByOriginID(q.KundendokumentChecklistQuestionID);
                q.MassnahmeNewCount = massnahmeViewModels.Where(x => x.Status == MassnahmeStatus.New).Count();
                q.MassnahmeInProgressCount = massnahmeViewModels.Where(x => x.Status == MassnahmeStatus.InProgress).Count();
                q.MassnahmeFinishedCount = massnahmeViewModels.Where(x => x.Status == MassnahmeStatus.Finished).Count();
            }

            return questions.AsQueryable();
        }

        public void Update(KundendokumentChecklist checklist)
        {
            _genericKundendokumentChecklistRepository.Update(checklist);
        }

        public void CheckIfAllChecklistAreFullfiled(int kundendokumentChecklistID)
        {
            var kundendokumentChecklist = GetByID(kundendokumentChecklistID);
            var questions = (from q in kundendokumentChecklist.Questions
                             select new KundendokumentChecklistQuestionViewModel()
                             {
                                 KundendokumentChecklistQuestionID = q.KundenDokumentChecklistQuestionID,
                                 KundendokumentChecklistID = kundendokumentChecklistID,
                                 Answer = q.Answer
                             }).ToList();

            bool allFullfilled = true;

            foreach (var q in questions)
            {
                if (q.Answer != KundendokumentQuestionAnswer.Yes)
                {
                    allFullfilled = false;
                    break;
                }
            }

            if (allFullfilled)
            {
                KundendokumentForderungsversionViewModel kfvViewModel = _unitOfWork.KundendokumentForderungsversionRepository.GetKundendokumentForderungsversionByErlassfassung(kundendokumentChecklist.ErlassfassungID, kundendokumentChecklist.KundendokumentID);
                KundendokumentForderungsversion kfv= _unitOfWork.KundendokumentForderungsversionRepository.GetByID(kfvViewModel.KundendokumentForderungsversionID);
                kfv.Erfuellung = KundendokumentErfuellung.Yes;
                _unitOfWork.KundendokumentForderungsversionRepository.Update(kfv);
            }
        }

        public KundendokumentChecklistViewModel GetKundendokumentChecklistViewModelById(int KundendokumentChecklistID)
        {
            var checkliste = (from x in _context.KundendokumentChecklist
                              join chkLst in _context.Checklist on x.ChecklistID equals chkLst.ChecklistID
                              join erlass in _context.Erlasse on chkLst.ErlassID equals erlass.ErlassID
                              where x.KundendokumentChecklistID == KundendokumentChecklistID
                              select new KundendokumentChecklistViewModel()
                              {
                                  KundendokumentChecklistID = x.KundendokumentChecklistID,
                                  KundendokumentID = x.KundendokumentID,
                                  ErlassID = erlass.ErlassID,
                                  erlassUebersetzungen = erlass.Uebersetzung,
                                  ErlassSrNummer = erlass.SrNummer ?? "",
                              }).ToList().First();

            return checkliste;
        }

    }
}