using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.Exceptions;
using NeoSysLCS.Repositories.Interfaces;
using NeoSysLCS.Repositories.ViewModels;


namespace NeoSysLCS.Repositories
{
    /// <summary>
    /// The repository for erlasstypen
    /// </summary>
    public class ErlasstypRepository : IReloadable<Erlasstyp>
    {
        private readonly IGenericRepository<Erlasstyp> _genericErlasstypRepository;
        private readonly IGenericRepository<Erlass> _genericErlassRepository;
        private readonly UnitOfWork _unitOfWork;
        private readonly NeoSysLCS_Dev _context;
        private int _currentLang;

        /// <summary>
        /// Initializes a new instance.
        /// </summary>
        /// <param name="contextCandidate">The context candidate.</param>
        public ErlasstypRepository(NeoSysLCS_Dev contextCandidate)
        {
            _context = contextCandidate;
            _genericErlasstypRepository = new GenericRepository<Erlasstyp>(contextCandidate);
            _genericErlassRepository = new GenericRepository<Erlass>(contextCandidate);
            _unitOfWork = new UnitOfWork(contextCandidate);

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
            _currentLang = sprache.SpracheID;
        }

        /// <summary>
        /// Gets the erlasstyp by id.
        /// </summary>
        /// <param name="id">The id.</param>
        /// <returns>Returns the erlasstyp.</returns>
        public Erlasstyp GetByID(object id)
        {
            return _genericErlasstypRepository.GetByID(id);
        }

        /// <summary>
        /// Inserts the erlasstyp from the specified view model.
        /// </summary>
        /// <param name="viewModel">The view model.</param>
        public void Insert(ErlasstypViewModel viewModel)
        {
            var erlasstyp = new Erlasstyp();
            erlasstyp.ErstelltAm = DateTime.Now;
            erlasstyp.ErstelltVonID = HttpContext.Current.User.Identity.GetUserId();

            if(_currentLang == 1)
            {
                erlasstyp.TitelDE = viewModel.Titel;
            }
            if (_currentLang == 2)
            {
                erlasstyp.TitelFR = viewModel.Titel;
            }
            if (_currentLang == 3)
            {
                erlasstyp.TitelIT = viewModel.Titel;
            }
            if (_currentLang == 4)
            {
                erlasstyp.TitelEN = viewModel.Titel;
            }

            _genericErlasstypRepository.Insert(erlasstyp);
        }

        /// <summary>
        /// Updates the erlasstyp from the specified view model.
        /// </summary>
        /// <param name="viewModel">The view model.</param>
        public void Update(ErlasstypViewModel viewModel)
        {
            Erlasstyp erlasstyp = _genericErlasstypRepository.GetByID(viewModel.ErlasstypID);
            erlasstyp.ErlasstypID = viewModel.ErlasstypID;
            erlasstyp.BearbeitetAm = DateTime.Now;
            erlasstyp.BearbeitetVonID = HttpContext.Current.User.Identity.GetUserId();

            if (_currentLang == 1)
            {
                erlasstyp.TitelDE = viewModel.Titel;
            }
            if (_currentLang == 2)
            {
                erlasstyp.TitelFR = viewModel.Titel;
            }
            if (_currentLang == 3)
            {
                erlasstyp.TitelIT = viewModel.Titel;
            }
            if (_currentLang == 4)
            {
                erlasstyp.TitelEN = viewModel.Titel;
            }

            _genericErlasstypRepository.Update(erlasstyp);
        }

        /// <summary>
        /// Deletes the erlasstyp by the specified if.
        /// </summary>
        /// <param name="id">The id.</param>
        /// <exception cref="NeoSysLCS.Repositories.Exceptions.HasRelationsException">Thrown when the Erlasstyp is still in use and connot be deleted </exception>
        public void Delete(int id)
        {
            if (_genericErlassRepository.Get(e => e.ErlasstypID == id, null, "").Any())
            {
                throw new HasRelationsException(Resources.Properties.Resources.Entitaet_Erlass_Plural, HasRelationsException.RelationType.UsedByEntity);
            }
            _genericErlasstypRepository.Delete(id);
        }

        /// <summary>
        /// Reloads the erlasstyp from the database.
        /// </summary>
        /// <param name="entityToReload">The entity to reload.</param>
        public void Reload(Erlasstyp entityToReload)
        {
            _genericErlasstypRepository.Reload(entityToReload);
        }

        /// <summary>
        /// Gets all erlasstyp view models.
        /// </summary>
        /// <returns>Returns the view models</returns>
        public IQueryable<ErlasstypViewModel> GetAllErlasstypViewModels()
        {
            return (from x in _context.Erlasstypen
                    select new ErlasstypViewModel()
                    {
                        ErlasstypID = x.ErlasstypID,
                        ErstelltAm = x.ErstelltAm,
                        ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                        ErstelltVonID = x.ErstelltVonID,
                        BearbeitetAm = x.BearbeitetAm,
                        BearbeitetVonID = x.BearbeitetVonID,
                        BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                        Titel = (_currentLang == 1 ? (x.TitelDE ?? x.TitelFR ?? x.TitelIT ?? x.TitelEN) : null) ??
                                     (_currentLang == 2 ? (x.TitelFR ?? x.TitelDE ?? x.TitelIT ?? x.TitelEN) : null) ??
                                     (_currentLang == 3 ? (x.TitelIT ?? x.TitelDE ?? x.TitelFR ?? x.TitelEN) : null) ??
                                     (_currentLang == 4 ? (x.TitelEN ?? x.TitelDE ?? x.TitelFR ?? x.TitelIT) : null),
                    }).OrderBy(et => et.Titel);
        }

        public IEnumerable<ErlasstypViewModel> GetErlassTypComboBox(int _currentLang)
        {
            return (from x in _context.Erlasstypen
                    select new ErlasstypViewModel()
                    {
                        ErlasstypID = x.ErlasstypID,
                        Titel = (_currentLang == 1 ? (x.TitelDE ?? x.TitelFR ?? x.TitelIT ?? x.TitelEN) : null) ??
                                     (_currentLang == 2 ? (x.TitelFR ?? x.TitelDE ?? x.TitelIT ?? x.TitelEN) : null) ??
                                     (_currentLang == 3 ? (x.TitelIT ?? x.TitelDE ?? x.TitelFR ?? x.TitelEN) : null) ??
                                     (_currentLang == 4 ? (x.TitelEN ?? x.TitelDE ?? x.TitelFR ?? x.TitelIT) : null)
                    }).ToList();
        }

        public IEnumerable<ErlasstypViewModel> GetErlasstypUebersetzungenByErlasstyp(int id)
        {
            List<ErlasstypViewModel> uebersetzungen = new List<ErlasstypViewModel>();

            uebersetzungen.Add((from x in _context.Erlasstypen where x.ErlasstypID == id select new ErlasstypViewModel() { ErlasstypID = id, ErlasstypUebersetzungID = "1_" + id, Titel = x.TitelDE ?? "", SpracheID = 1 }).FirstOrDefault());
            uebersetzungen.Add((from x in _context.Erlasstypen where x.ErlasstypID == id select new ErlasstypViewModel() { ErlasstypID = id, ErlasstypUebersetzungID = "2_" + id, Titel = x.TitelFR ?? "", SpracheID = 2 }).FirstOrDefault());
            uebersetzungen.Add((from x in _context.Erlasstypen where x.ErlasstypID == id select new ErlasstypViewModel() { ErlasstypID = id, ErlasstypUebersetzungID = "3_" + id, Titel = x.TitelIT ?? "", SpracheID = 3 }).FirstOrDefault());
            uebersetzungen.Add((from x in _context.Erlasstypen where x.ErlasstypID == id select new ErlasstypViewModel() { ErlasstypID = id, ErlasstypUebersetzungID = "4_" + id, Titel = x.TitelEN ?? "", SpracheID = 4 }).FirstOrDefault());

            return uebersetzungen;
        }

        public void UpdateErlasstypUebersetzung(ErlasstypViewModel viewModel)
        {
            Erlasstyp erlasstyp = _genericErlasstypRepository.GetByID(viewModel.ErlasstypID);

            if (viewModel.SpracheID == 1)
            {
                erlasstyp.TitelDE = viewModel.Titel;
            }
            if (viewModel.SpracheID == 2)
            {
                erlasstyp.TitelFR = viewModel.Titel;
            }
            if (viewModel.SpracheID == 3)
            {
                erlasstyp.TitelIT = viewModel.Titel;
            }
            if (viewModel.SpracheID == 4)
            {
                erlasstyp.TitelEN = viewModel.Titel;
            }

            _genericErlasstypRepository.Update(erlasstyp);
        }

    }
}