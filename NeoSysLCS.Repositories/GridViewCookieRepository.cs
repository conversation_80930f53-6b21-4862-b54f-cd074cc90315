using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.Exceptions;
using NeoSysLCS.Repositories.Interfaces;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Repositories
{
    public class GridViewCookieRepository
    {
        private readonly IGenericRepository<GridViewCookie> _genericGridViewCookieRepository;
        private readonly UnitOfWork _unitOfWork;
        private readonly NeoSysLCS_Dev _context;

        public GridViewCookieRepository(NeoSysLCS_Dev contextCandidate)
        {
            _context = contextCandidate;
            _genericGridViewCookieRepository = new GenericRepository<GridViewCookie>(contextCandidate);
            _unitOfWork = new UnitOfWork(contextCandidate);
        }

        public GridViewCookieViewModel GetViewModelByID(string id)
        {
            var cookie = _genericGridViewCookieRepository.GetByID(id);
            var viewModel = new GridViewCookieViewModel();
            if (cookie != null)
            {
                viewModel.GridViewCookieID = cookie.GridViewCookieID;
                viewModel.ForderungenLayout = cookie.ForderungenLayout;
                viewModel.ErlassLayout = cookie.ErlassLayout;
                viewModel.RemovedForderungenLayout = cookie.RemovedForderungenLayout;
                viewModel.StandortObjektLayout = cookie.StandortObjektLayout;
            }

            return viewModel;
        }

        public void Insert(string id, string grid, string layout)
        {
            GridViewCookie cookie = new GridViewCookie();
            cookie.GridViewCookieID = id;
            if (grid == "PortalKundendokumentForderungenGridView")
            {
                cookie.ForderungenLayout = layout;
            }
            else if (grid == "ErlassfassungenGridView")
            {
                cookie.ErlassLayout = layout;
            }
            else if (grid == "PortalKundendokumentRemovedForderungenGridView")
            {
                cookie.RemovedForderungenLayout = layout;
            }
            else if (grid == "KundendokumentStandortObjektGridView")
            {
                cookie.StandortObjektLayout = layout;
            }
            else if (grid == "KommentareGridView")
            {
                cookie.StandortObjektLayout = layout;
            }
            _genericGridViewCookieRepository.Insert(cookie);
            _unitOfWork.Save();

        }

        public void Update(string id, string grid, string layout)
        {
            GridViewCookie cookie = _genericGridViewCookieRepository.GetByID(id);
            if (grid == "PortalKundendokumentForderungenGridView")
            {
                cookie.ForderungenLayout = layout;
            }
            else if (grid == "ErlassfassungenGridView")
            {
                cookie.ErlassLayout = layout;
            }
            else if (grid == "PortalKundendokumentRemovedForderungenGridView")
            {
                cookie.RemovedForderungenLayout = layout;
            }
            else if (grid == "KundendokumentStandortObjektGridView")
            {
                cookie.StandortObjektLayout = layout;
            }
            else if (grid == "KommentareGridView")
            {
                cookie.StandortObjektLayout = layout;
            }
            else if (grid == "KonzernGridView")
            {
                cookie.KonzernLayout = layout;
            }
            _genericGridViewCookieRepository.Update(cookie);
        }

        public void DeleteCookie(string userId)
        {
            List<GridViewCookie> list = (from x in _context.GridViewCookie
                                        where x.GridViewCookieID.Contains(userId)
                                        select x).ToList();

            foreach (GridViewCookie cookie in list)
            {
                cookie.ForderungenLayout = null;
                cookie.ErlassLayout = null;
                cookie.RemovedForderungenLayout = null;
                cookie.StandortObjektLayout = null;
                cookie.KonzernLayout = null;
                _genericGridViewCookieRepository.Update(cookie);
                _context.SaveChanges();
            }           
        }
    }
}