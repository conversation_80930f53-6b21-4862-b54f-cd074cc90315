
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using NeoSysLCS.DomainModel.Models;


namespace NeoSysLCS.Repositories
{

    public class ChecklistQuestionRepository
    {
        private readonly NeoSysLCS_Dev _context;
        private readonly IGenericRepository<ChecklistQuestion> _genericChecklistQuestionRepository;
        private readonly UnitOfWork _unitOfWork;
        private int _currentLang;

        public ChecklistQuestionRepository(NeoSysLCS_Dev context)
        {
            _context = context;
            _genericChecklistQuestionRepository = new GenericRepository<ChecklistQuestion>(context);
            _unitOfWork = new UnitOfWork(context);
            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
            _currentLang = sprache.SpracheID;
        }


        public void Insert(List<ChecklistQuestion> questions)
        {
            foreach (var q in questions)
            {
                _genericChecklistQuestionRepository.Insert(q);
            }
        }

        public void Update(ChecklistQuestion question)
        {
            _genericChecklistQuestionRepository.Update(question);
        }


    }
}