using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;

namespace NeoSysLCS.Repositories
{
    interface IGenericRepository<TEntity> where TEntity : class
    {
        void Delete(object id);
        void Delete(TEntity entityToDelete);
        void DeleteRange(IEnumerable<TEntity> entitiesToDelete );
        IQueryable<TEntity> Get(Expression<Func<TEntity, bool>> filter = null, Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null, string includeProperties = "");
        TEntity GetByID(object id);
        void Insert(TEntity entity);
        void Update(TEntity entityToUpdate);
        void Reload(TEntity entityToReload);
        int? GetKundeIdOfCurrentUser();
    }
}
