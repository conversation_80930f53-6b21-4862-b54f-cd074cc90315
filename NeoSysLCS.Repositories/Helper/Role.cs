namespace NeoSysLCS.Repositories.Helper
{
    /// <summary>
    /// Contains all application roles used by LexPlus.
    /// 
    /// Specific information regarding the roles can be found in the documentation (Anwenderhandbuch).
    /// </summary>
    public static class Role
    {

        #region Neosys Roles

        public const string Admin = "ADMIN";
        public const string ProjectManager = "PROJECTMANAGER";
        public const string ObjectGuru = "OBJECTGURU";

        #endregion

        #region Customer Roles

        public const string SuperUser = "SUPERUSER";
        public const string EndUser = "ENDUSER";
        public const string ReadOnly = "READONLY";
        public const string Auditor = "AUDITOR";
        public const string Accountable = "ACCOUNTABLE";
        public const string Newsletter = "NEWSLETTER";

        #endregion

    }
}