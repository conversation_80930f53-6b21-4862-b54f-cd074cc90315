<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{C57A72DA-5AF6-42DE-86D3-437BAC506914}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>NeoSysLCS.Repositories</RootNamespace>
    <AssemblyName>NeoSysLCS.Repositories</AssemblyName>
    <TargetFrameworkVersion>v4.7</TargetFrameworkVersion>
    <UseIISExpress>true</UseIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\</SolutionDir>
    <RestorePackages>true</RestorePackages>
    <TargetFrameworkProfile />
    <UseGlobalApplicationHostFile />
    <Use64BitIISExpress />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>
    </DocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.Office.v24.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.RichEdit.v24.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Web.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=2.0.8.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.8\lib\net45-full\log4net.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Core, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.AspNet.Identity.Core.2.2.1\lib\net45\Microsoft.AspNet.Identity.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.EntityFramework">
      <HintPath>..\packages\Microsoft.AspNet.Identity.EntityFramework.2.2.1\lib\net45\Microsoft.AspNet.Identity.EntityFramework.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Owin, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.AspNet.Identity.Owin.2.2.1\lib\net45\Microsoft.AspNet.Identity.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Owin, Version=3.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.3.1.0\lib\net45\Microsoft.Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=3.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.3.1.0\lib\net45\Microsoft.Owin.Security.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Cookies, Version=3.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.Cookies.3.1.0\lib\net45\Microsoft.Owin.Security.Cookies.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.OAuth, Version=3.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.OAuth.3.1.0\lib\net45\Microsoft.Owin.Security.OAuth.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.10.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Owin, Version=1.0.0.0, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Linq.Dynamic, Version=1.0.6132.35681, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\System.Linq.Dynamic.1.0.7\lib\net40\System.Linq.Dynamic.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Mvc, Version=5.2.3.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.3\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="packages.config" />
    <None Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Web.config" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ChecklistHeaderRepository.cs" />
    <Compile Include="ConsultationRepository.cs" />
    <Compile Include="CortecTaskRepository.cs" />
    <Compile Include="DashboardRepository.cs" />
    <Compile Include="ChecklistRepository.cs" />
    <Compile Include="ChecklistQuestionRepository.cs" />
    <Compile Include="KundendokumentChecklistRepository.cs" />
    <Compile Include="KundeCortecRepository.cs" />
    <Compile Include="NewsletterRepository.cs" />
    <Compile Include="KundeSummaryRepositry.cs" />
    <Compile Include="Helper\XMLHelper.cs" />
    <Compile Include="EvaluationRepository.cs" />
    <Compile Include="CustomerNewsRepository.cs" />
    <Compile Include="MassnahmeRepository.cs" />
    <Compile Include="ObligationRepository.cs" />
    <Compile Include="ApplicationUserPrivacyPolicyRepository.cs" />
    <Compile Include="StandortBerichtRepository.cs" />
    <Compile Include="PrivacyPolicyRepository.cs" />
    <Compile Include="ToDoTaskRepository.cs" />
    <Compile Include="ToDoRepository.cs" />
    <Compile Include="LegalComplianceRepository.cs" />
    <Compile Include="OfferRepository.cs" />
    <Compile Include="StandortMenuUebersetzungRepository.cs" />
    <Compile Include="ViewModels\AllgemeineKundeninformationRepository.cs" />
    <Compile Include="FAQKategorieRepository.cs" />
    <Compile Include="FAQRepository.cs" />
    <Compile Include="GridViewCookieRepository.cs" />
    <Compile Include="Helper\EnumExtensionsViewModel.cs" />
    <Compile Include="KommentarRepository.cs" />
    <Compile Include="KonzernRepository.cs" />
    <Compile Include="KundendokumentErfuellungRepository.cs" />
    <Compile Include="ViewModels\ApplicationRoleViewModel.cs" />
    <Compile Include="ViewModels\ApplicationUserPrivacyPolicyViewModel.cs" />
    <Compile Include="ViewModels\ChecklistHeaderViewModel.cs" />
    <Compile Include="ViewModels\KundendokumentChecklistQuestionViewModel.cs" />
    <Compile Include="ViewModels\ChecklistQuestionViewModel.cs" />
    <Compile Include="ViewModels\ForderungsversionsCompareViewModel.cs" />
    <Compile Include="ViewModels\CustomerNewsViewModel.cs" />
    <Compile Include="ViewModels\DateRangePickerModel.cs" />
    <Compile Include="ViewModels\FAQViewModel.cs" />
    <Compile Include="ViewModels\FAQKategorieViewModel.cs" />
    <Compile Include="ViewModels\ConsultationViewModel.cs" />
    <Compile Include="ViewModels\KommentarNewsletterViewModel.cs" />
    <Compile Include="ViewModels\GesetzaenderungViewModel.cs" />
    <Compile Include="ViewModels\ApplicationUserNewsletterHistoryViewModel.cs" />
    <Compile Include="ViewModels\ChecklistViewModel.cs" />
    <Compile Include="ViewModels\KundendokumentChecklistViewModel.cs" />
    <Compile Include="ViewModels\KundendokumentSpaltenlabelViewModel.cs" />
    <Compile Include="ViewModels\KundendokumentSpaltenauswahlViewModel.cs" />
    <Compile Include="ViewModels\MassnahmeErfassenViewModel.cs" />
    <Compile Include="ViewModels\MassnahmeViewModel.cs" />
    <Compile Include="ViewModels\MasterKundendokumentErlassfassungViewModel.cs" />
    <Compile Include="ViewModels\MasterKundendokumentForderungsversionViewModel.cs" />
    <Compile Include="ViewModels\ObligationViewModel.cs" />
    <Compile Include="ViewModels\ShortcutErfassenViewModel.cs" />
    <Compile Include="ViewModels\StandortBerichtViewModel.cs" />
    <Compile Include="ViewModels\PrivacyPolicyViewModel.cs" />
    <Compile Include="ViewModels\SpracheViewModel.cs" />
    <Compile Include="ViewModels\KommentarViewModel.cs" />
    <Compile Include="ViewModels\KundendokumentErfuellungViewModel.cs" />
    <Compile Include="ShortcutRepository.cs" />
    <Compile Include="Exceptions\HasRelationsException.cs" />
    <Compile Include="Helper\CsvHelper.cs" />
    <Compile Include="Helper\Role.cs" />
    <Compile Include="HerausgeberRepository.cs" />
    <Compile Include="IndividuelleForderungRepository.cs" />
    <Compile Include="Interfaces\IReloadable.cs" />
    <Compile Include="KundendokumentErlassfassungenRepository.cs" />
    <Compile Include="KundendokumentPflichtRepository.cs" />
    <Compile Include="KundendokumentForderungsversionRepository.cs" />
    <Compile Include="KundendokumentRepository.cs" />
    <Compile Include="StandortObjektRepository.cs" />
    <Compile Include="StandortRepository.cs" />
    <Compile Include="UserRepository.cs" />
    <Compile Include="ForderungsversionRepository.cs" />
    <Compile Include="ErlassfassungRepository.cs" />
    <Compile Include="ErlassRepository.cs" />
    <Compile Include="IGenericRepository.cs" />
    <Compile Include="IUnitOfWork.cs" />
    <Compile Include="KontaktRepository.cs" />
    <Compile Include="KundeRepository.cs" />
    <Compile Include="ObjektkategorieRepository.cs" />
    <Compile Include="RechtsbereichRepository.cs" />
    <Compile Include="ErlasstypRepository.cs" />
    <Compile Include="PflichtRepository.cs" />
    <Compile Include="ArtikelRepository.cs" />
    <Compile Include="ObjektRepository.cs" />
    <Compile Include="UnitOfWork.cs" />
    <Compile Include="GenericRepository.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="UserStandortRoleRepository.cs" />
    <Compile Include="ViewModels\AccountViewModels.cs" />
    <Compile Include="ViewModels\AdminViewModel.cs" />
    <Compile Include="ViewModels\ArtikelViewModel.cs" />
    <Compile Include="ViewModels\GridViewCookieViewModel.cs" />
    <Compile Include="ViewModels\KonzernViewModel.cs" />
    <Compile Include="ViewModels\EvaluationViewModel.cs" />
    <Compile Include="ViewModels\ToDoViewModel.cs" />
    <Compile Include="ViewModels\KundeWithSummaryViewModel.cs" />
    <Compile Include="ViewModels\LegalComplianceViewModel.cs" />
    <Compile Include="ViewModels\NewStandortObjektViewModel.cs" />
    <Compile Include="ViewModels\BaseViewModel.cs" />
    <Compile Include="ViewModels\ErlassfassungViewModel.cs" />
    <Compile Include="ViewModels\ErlasstypViewModel.cs" />
    <Compile Include="ViewModels\ErlassViewModel.cs" />
    <Compile Include="ViewModels\HerausgeberViewModel.cs" />
    <Compile Include="ViewModels\KundendokumentErlassfassungViewModel.cs" />
    <Compile Include="ViewModels\KundendokumentStandortObjektForderungsversionViewModel.cs" />
    <Compile Include="ViewModels\KundendokumentForderungsversionViewModel.cs" />
    <Compile Include="ViewModels\KundendokumentStandortObjektPflichtViewModel.cs" />
    <Compile Include="ViewModels\KundendokumentViewModel.cs" />
    <Compile Include="ViewModels\ForderungsversionViewModel.cs" />
    <Compile Include="ViewModels\KontaktViewModel.cs" />
    <Compile Include="ViewModels\AuswertungForderungenViewModel.cs" />
    <Compile Include="ViewModels\Objekt2KundenAssignmentViewModel.cs" />
    <Compile Include="ViewModels\KundeViewModel.cs" />
    <Compile Include="ViewModels\ManageViewModels.cs" />
    <Compile Include="ViewModels\ObjektkategorieViewModel.cs" />
    <Compile Include="ViewModels\ObjektViewModel.cs" />
    <Compile Include="ViewModels\IndividuelleForderungViewModel.cs" />
    <Compile Include="ViewModels\KundendokumentPflichtViewModel.cs" />
    <Compile Include="ViewModels\OfferViewModel.cs" />
    <Compile Include="ViewModels\PflichtViewModel.cs" />
    <Compile Include="ViewModels\RechtsbereichViewModel.cs" />
    <Compile Include="ViewModels\ShortcutViewModel.cs" />
    <Compile Include="ViewModels\StandortObjektViewModel.cs" />
    <Compile Include="ViewModels\StandortViewModel.cs" />
    <Compile Include="ViewModels\UserStandortRoleViewModel.cs" />
    <Compile Include="ViewModels\DatePickerViewModel.cs" />
    <Compile Include="ViewModels\Validations\ErlassfassungInkrafttretungAttribute.cs" />
    <Compile Include="ViewModels\Validations\ForderungAufhebungAttribute.cs" />
    <Compile Include="ViewModels\Validations\ObjektNameUniqueAttribute.cs" />
    <Compile Include="ViewModels\YearViewModel.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\NeoSysLCS.DomainModel\NeoSysLCS.DomainModel.csproj">
      <Project>{ef2effe2-2175-41a2-ad56-d65721c68e5a}</Project>
      <Name>NeoSysLCS.DomainModel</Name>
    </ProjectReference>
    <ProjectReference Include="..\NeoSysLCS.Resources\NeoSysLCS.Resources.csproj">
      <Project>{4710c524-6cab-46ac-b206-561e7687a6b7}</Project>
      <Name>NeoSysLCS.Resources</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Test|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Test|x64'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>62741</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>http://localhost:62741/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Import Project="$(SolutionDir)\.nuget\NuGet.targets" Condition="Exists('$(SolutionDir)\.nuget\NuGet.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('$(SolutionDir)\.nuget\NuGet.targets')" Text="$([System.String]::Format('$(ErrorText)', '$(SolutionDir)\.nuget\NuGet.targets'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>