using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.ViewModels;
using System.Globalization;
using Microsoft.AspNet.Identity;
using System.Xml.Linq;
using NeoSysLCS.Repositories.Helper;

namespace NeoSysLCS.Repositories
{
    public class FAQRepository
    {
        private readonly NeoSysLCS_Dev _context;
        //private readonly UnitOfWork _unitOfWork;
        private readonly IGenericRepository<FAQ> _genericFAQRepository;
        private readonly IGenericRepository<FAQKategorie> _genericFAQKategorieRepository;
        private int _currentLang;

        public FAQRepository(NeoSysLCS_Dev contextCandidate)
        {
            _context = contextCandidate;
            _genericFAQRepository = new GenericRepository<FAQ>(contextCandidate);
            _genericFAQKategorieRepository = new GenericRepository<FAQKategorie>(contextCandidate);

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
            _currentLang = sprache.SpracheID;
        }

        public IQueryable<FAQViewModel> GetAllFAQViewModels()
        {
            var faq = (from x in _context.FAQ
                   select new FAQViewModel
                   {
                       FaqID = x.FaqID,
                       FAQKategorien = x.FAQKategorien.AsQueryable(),
                       Uebersetzung = x.Uebersetzung,
                       Status = x.Status,
                       Prio = x.Prio,
                   }).ToList();

            foreach (var frage in faq)
            {
                XDocument uebersetzung = XDocument.Parse(frage.Uebersetzung);

                frage.Frage = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Frage", _currentLang);
                frage.Quelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Quelle", _currentLang);
            }

            var query = faq.AsQueryable();
            return query;
        }

        public IQueryable<FAQViewModel> GetFAQViewModelsByFAQKategorie(string view)
        {
            var faq = (from x in _context.FAQ
                   where x.FAQKategorien.Any(fk => fk.Systemname == view)
                   orderby x.Prio
                   select new FAQViewModel
                   {
                       FaqID = x.FaqID,
                       FAQKategorien = x.FAQKategorien.AsQueryable(),
                       Uebersetzung = x.Uebersetzung,
                       Status = x.Status,
                       Prio = x.Prio,
                   }).ToList();

            foreach (var frage in faq)
            {
                XDocument uebersetzung = XDocument.Parse(frage.Uebersetzung);

                frage.Frage = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Frage", _currentLang);
                frage.Quelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Quelle", _currentLang);
            }

            var query = faq.AsQueryable();
            return query;
        }

        public void Insert(FAQViewModel viewModel)
        {
            FAQ faq = new FAQ();
            faq.FaqID = viewModel.FaqID;
            faq.Status = viewModel.Status;
            faq.ErstelltAm = DateTime.Now;
            faq.ErstelltVonID = HttpContext.Current.User.Identity.GetUserId();
            faq.Prio = viewModel.Prio;

            List<string> list = new List<string>();
            list.Add("Frage");
            list.Add("Quelle");

            XDocument uebersetzung = XMLHelper.CreateNewUebersetzung(list);

            Dictionary<string, string> elements = new Dictionary<string, string>();
            elements.Add("Frage", viewModel.Frage);
            elements.Add("Quelle", viewModel.Quelle);

            faq.Uebersetzung = XMLHelper.UpdateUebersetzung(uebersetzung, elements, _currentLang);

            _genericFAQRepository.Insert(faq);
        }

        public void Update(FAQViewModel viewModel)
        {
            FAQ faq = _genericFAQRepository.GetByID(viewModel.FaqID);
            faq.FaqID = viewModel.FaqID;
            faq.Status = viewModel.Status;
            faq.BearbeitetAm = DateTime.Now;
            faq.BearbeitetVonID = HttpContext.Current.User.Identity.GetUserId();
            faq.Prio = viewModel.Prio;

            var uebersetzung = XDocument.Parse(faq.Uebersetzung);

            Dictionary<string, string> elements = new Dictionary<string, string>();
            elements.Add("Frage", viewModel.Frage);
            elements.Add("Quelle", viewModel.Quelle);

            faq.Uebersetzung = XMLHelper.UpdateUebersetzung(uebersetzung, elements, _currentLang);

            _genericFAQRepository.Update(faq);
        }

        public void Delete(int id)
        {
            _genericFAQRepository.Delete(id);
        }

        public void SaveKategorieFAQ(string selectedIDs, int faqID)
        {
            FAQ faq = _genericFAQRepository.Get(f => f.FaqID == faqID, null, "FAQKategorien").FirstOrDefault();
            string[] ids = selectedIDs.Split(',');

            if (selectedIDs != "")
            {
                foreach (var id in ids)
                {
                    FAQKategorie obj = _genericFAQKategorieRepository.GetByID(Convert.ToInt32(id));
                    if (!faq.FAQKategorien.Contains(obj))
                    {
                        faq.FAQKategorien.Add(obj);
                    }
                }
            }

            foreach (var pfo in faq.FAQKategorien.ToArray())
            {
                if (pfo != null)
                {
                    if (!ids.ToList().Contains(pfo.FaqKategorieID.ToString()))
                    {
                        faq.FAQKategorien.Remove(pfo);
                    }
                }
            }
            if (faq.FAQKategorien.Count == 0)
            {
                faq.FAQKategorien = null;
            }

            _genericFAQRepository.Update(faq);
        }

        public IQueryable<FAQViewModel> GetFAQUebersetzungenByFAQ(int id)
        {
            FAQ FAQ = _genericFAQRepository.GetByID(id);

            List<int> sprachIds = _context.Sprachen.Select(x => x.SpracheID).ToList();
            List<FAQViewModel> uebersetzungen = new List<FAQViewModel>();
            XDocument xml = XDocument.Parse(FAQ.Uebersetzung);

            foreach (int sprachId in sprachIds)
            {
                FAQViewModel uebersetzung = new FAQViewModel();
                uebersetzung.FAQUebersetzungID = id.ToString() + "_" + sprachId.ToString();
                uebersetzung.Frage = XMLHelper.GetAllUebersetzungFromXmlField(xml, "Frage", sprachId);
                uebersetzung.Quelle = XMLHelper.GetAllUebersetzungFromXmlField(xml, "Quelle", sprachId);
                
                uebersetzung.SpracheID = sprachId;
                uebersetzung.FaqID = id;
                uebersetzungen.Add(uebersetzung);
            }

            return uebersetzungen.AsQueryable();
        }

        public void InsertUebersetzungFAQ(FAQViewModel uebersetzung, int faqID)
        {
            FAQ FAQ = _genericFAQRepository.GetByID(faqID);

            var xml = XDocument.Parse(FAQ.Uebersetzung);
            Dictionary<string, string> elements = new Dictionary<string, string>();
            elements.Add("Frage", uebersetzung.Frage);
            elements.Add("Quelle", uebersetzung.Quelle);

            FAQ.Uebersetzung = XMLHelper.UpdateUebersetzung(xml, elements, uebersetzung.SpracheID);

            _genericFAQRepository.Update(FAQ);
        }

        public void UpdateFAQUebersetzung(FAQViewModel viewModel)
        {
            FAQ faq = _genericFAQRepository.GetByID(viewModel.FaqID);

            var uebersetzung = XDocument.Parse(faq.Uebersetzung);

            Dictionary<string, string> elements = new Dictionary<string, string>();
            elements.Add("Frage", viewModel.Frage);
            elements.Add("Quelle", viewModel.Quelle);

            faq.Uebersetzung = XMLHelper.UpdateUebersetzung(uebersetzung, elements, viewModel.SpracheID);

            _genericFAQRepository.Update(faq);
        }

        public void UpdateUploadedFileUrl(int faqId, string field, string fileUrl, int languageId)
        {
            FAQ faq = _genericFAQRepository.GetByID(faqId);

            var uebersetzung = XDocument.Parse(faq.Uebersetzung);

            Dictionary<string, string> elements = new Dictionary<string, string>();
            elements.Add(field, fileUrl);

            faq.Uebersetzung = XMLHelper.UpdateUebersetzung(uebersetzung, elements, languageId != -1 ? languageId : _currentLang);

            _genericFAQRepository.Update(faq);
        }
    }
}