using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.ViewModels;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web;
using System.Xml.Linq;

namespace NeoSysLCS.Repositories
{
    public class KonzernRepository
    {
        private readonly IGenericRepository<KundendokumentForderungsversion> _genericKundendokumentForderungsversionenRepository;
        private readonly IGenericRepository<StandortObjekt> _genericStandortObjektRepository;
        private readonly IGenericRepository<Erlass> _genericErlassRepository;
        private readonly NeoSysLCS_Dev _context;
        private int _currentLang;

        public KonzernRepository(NeoSysLCS_Dev contextCandidate)
        {
            _context = contextCandidate;
            _genericKundendokumentForderungsversionenRepository = new GenericRepository<KundendokumentForderungsversion>(contextCandidate);
            _genericStandortObjektRepository = new GenericRepository<StandortObjekt>(contextCandidate);
            _genericErlassRepository = new GenericRepository<Erlass>(contextCandidate);
            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
            _currentLang = sprache.SpracheID;
        }

        public IQueryable<KonzernViewModel> GetAllKonzernViewModels(List<int> kundendokumentIds)
        {
            List<KonzernViewModel> viewModels = (from x in _context.KundendokumentForderungsversionen
                   from stdObj in _context.StandortObjekte.Where(curSta => curSta.StandortObjektID == x.StandortObjektID)
                   from fv in _context.Forderungsversionen.Where(curFV => curFV.ForderungsversionID == x.ForderungsversionID)
                   from f in _context.Forderungen.Where(curF => curF.ForderungID == fv.ForderungID)
                   from erlF in _context.Erlassfassungen.Where(curErlF => curErlF.ErlassfassungID == fv.ErlassfassungID)
                   from erl in _context.Erlasse.Where(curErl => curErl.ErlassID == erlF.ErlassID)
                   where (kundendokumentIds.Contains(x.KundendokumentID)
                            && x.QsFreigabe
                            && x.Relevant
                            && (x.Status != KundendokumentItemStatus.Removed || x.Status != KundendokumentItemStatus.Replaced))
                   select new KonzernViewModel()
                   {
                       KonzernID = fv.ForderungsversionID + "_" + stdObj.ObjektID,
                       KundendokumentForderungsversionID = x.KundendokumentForderungsversionID,
                       StandortObjektID = stdObj.ObjektID.HasValue ? stdObj.ObjektID.Value : stdObj.StandortObjektID,
                       //Erfuellung cannot be a nullable enum because of the comboboxfilter of the gridview...
                       Erfuellung = x.Erfuellung.HasValue ? (x.Erfuellung.Value == KundendokumentErfuellung.NotDefined ? KundendokumentErfuellung.NotEdited : x.Erfuellung.Value) : KundendokumentErfuellung.NotEdited,
                       ErstelltAm = x.ErstelltAm,
                       ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                       ErstelltVonID = x.ErstelltVonID,
                       BearbeitetAm = x.BearbeitetAm,
                       BearbeitetVonID = x.BearbeitetVonID,
                       BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                       StandortID = stdObj.StandortID.HasValue ? stdObj.StandortID.Value : 0,
                       stdObjUebersetzungen = stdObj.Uebersetzung,
                       erlUebersetzungen = erl.Uebersetzung,
                       Status = x.Status,
                       ErlassID = erl.ErlassID,
                       ArtikelNummer = (f.Artikel != null) ? f.Artikel.Nummer : null,
                       ArtikelQuelle = (f.Artikel != null) ?
                                     ((_currentLang == 1 ? (f.Artikel.QuelleDE ?? f.Artikel.QuelleFR ?? f.Artikel.QuelleIT ?? f.Artikel.QuelleEN) : null) ??
                                     (_currentLang == 2 ? (f.Artikel.QuelleFR ?? f.Artikel.QuelleDE ?? f.Artikel.QuelleIT ?? f.Artikel.QuelleEN) : null) ??
                                     (_currentLang == 3 ? (f.Artikel.QuelleIT ?? f.Artikel.QuelleDE ?? f.Artikel.QuelleFR ?? f.Artikel.QuelleEN) : null) ??
                                     (_currentLang == 4 ? (f.Artikel.QuelleEN ?? f.Artikel.QuelleDE ?? f.Artikel.QuelleFR ?? f.Artikel.QuelleIT) : null)) : null,
                       Rechtsbereiche = from tu in _context.Rechtsbereiche
                                        where tu.Standorte.Contains(stdObj.Standort) && tu.Forderungsversionen.Contains(fv)
                                        select new RechtsbereichViewModel()
                                        {
                                            Name = (_currentLang == 1 ? (tu.NameDE ?? tu.NameFR ?? tu.NameIT ?? tu.NameEN) : null) ??
                                                    (_currentLang == 2 ? (tu.NameFR ?? tu.NameDE ?? tu.NameIT ?? tu.NameEN) : null) ??
                                                    (_currentLang == 3 ? (tu.NameIT ?? tu.NameDE ?? tu.NameFR ?? tu.NameEN) : null) ??
                                                    (_currentLang == 4 ? (tu.NameEN ?? tu.NameDE ?? tu.NameFR ?? tu.NameIT) : null),
                                            RechtsbereichID = tu.RechtsbereichID,
                                        },
                   }).ToList();

            foreach (KonzernViewModel viewModel in viewModels)
            {
                XDocument uebersetzungStandortObjekt = XDocument.Parse(viewModel.stdObjUebersetzungen);
                viewModel.StandortObjektTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungStandortObjekt, "Name", _currentLang);

                XDocument uebersetzungErlass = XDocument.Parse(viewModel.erlUebersetzungen);
                viewModel.ErlassTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Titel", _currentLang);

                if (viewModel.StandortObjektID == 0)
                {
                    uebersetzungStandortObjekt = XDocument.Parse(viewModel.stdObjUebersetzungen);
                    viewModel.StandortObjektTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungStandortObjekt, "Name", _currentLang);

                    uebersetzungErlass = XDocument.Parse(viewModel.erlUebersetzungen);
                    viewModel.ErlassTitel = XMLHelper.GetUebersetzungFromXmlField(uebersetzungErlass, "Titel", _currentLang);

                }

            }

            var test = viewModels.Where(x => x.StandortObjektTitel == "Auto");

            return viewModels.AsQueryable();
        }

        public List<int> GetAllRechtsbereichIdsByKonzernViewModel(List<int> kundendokumentIds)
        {
            List<KonzernViewModel> rechtsbereiche = (from x in _context.KundendokumentForderungsversionen
                                                     from stdObj in _context.StandortObjekte.Where(curSta => curSta.StandortObjektID == x.StandortObjektID)
                                                     from fv in _context.Forderungsversionen.Where(curFV => curFV.ForderungsversionID == x.ForderungsversionID)
                                                     from f in _context.Forderungen.Where(curF => curF.ForderungID == fv.ForderungID)
                                                     from erlF in _context.Erlassfassungen.Where(curErlF => curErlF.ErlassfassungID == fv.ErlassfassungID)
                                                     from erl in _context.Erlasse.Where(curErl => curErl.ErlassID == erlF.ErlassID)
                                                     where kundendokumentIds.Contains(x.KundendokumentID)
                                                              && x.QsFreigabe
                                                              && x.Relevant
                                                              && (x.Status != KundendokumentItemStatus.Removed || x.Status != KundendokumentItemStatus.Replaced)
                                                     select new KonzernViewModel()
                                                     {
                                                         KonzernID = fv.ForderungsversionID + "_" + stdObj.ObjektID,
                                                         Rechtsbereiche = from tu in _context.Rechtsbereiche
                                                                          where tu.Standorte.Contains(stdObj.Standort) && tu.Forderungsversionen.Contains(fv)
                                                                          select new RechtsbereichViewModel()
                                                                          {
                                                                              Name = (_currentLang == 1 ? (tu.NameDE ?? tu.NameFR ?? tu.NameIT ?? tu.NameEN) : null) ??
                                                                                      (_currentLang == 2 ? (tu.NameFR ?? tu.NameDE ?? tu.NameIT ?? tu.NameEN) : null) ??
                                                                                      (_currentLang == 3 ? (tu.NameIT ?? tu.NameDE ?? tu.NameFR ?? tu.NameEN) : null) ??
                                                                                      (_currentLang == 4 ? (tu.NameEN ?? tu.NameDE ?? tu.NameFR ?? tu.NameIT) : null),
                                                                              RechtsbereichID = tu.RechtsbereichID,
                                                                          },
                                                     }).ToList();

            List<int> rechtsBereichIds = new List<int>();

            foreach (KonzernViewModel viewModel in rechtsbereiche)
            {
                foreach (RechtsbereichViewModel rechtsbereichViewModel in viewModel.Rechtsbereiche)
                {
                    rechtsBereichIds.Add(rechtsbereichViewModel.RechtsbereichID);
                }
            }

            return rechtsBereichIds;
        }
    }
}