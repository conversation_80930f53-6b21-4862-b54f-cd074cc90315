using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web;
using System.Xml.Linq;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.Exceptions;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.Interfaces;
using NeoSysLCS.Repositories.ViewModels;


namespace NeoSysLCS.Repositories
{
    /// <summary>
    /// The repository for objekte 
    /// </summary>
    public class ObjektRepository : IReloadable<Objekt>
    {
        private readonly IGenericRepository<Objekt> _genericObjektRepository;
        private readonly IGenericRepository<Forderungsversion> _genericForderungsverionenRepository;
        private readonly IGenericRepository<Pflicht> _genericPflichtRepository;
        private readonly IGenericRepository<StandortObjekt> _genericStandortobjektRepository;
        private readonly UnitOfWork _unitOfWork;
        private readonly NeoSysLCS_Dev _context;
        private int _currentLang;

        /// <summary>
        /// Initializes a new instance.
        /// </summary>
        /// <param name="contextCandidate">The context candidate.</param>
        public ObjektRepository(NeoSysLCS_Dev contextCandidate)
        {
            _context = contextCandidate;
            _genericObjektRepository = new GenericRepository<Objekt>(contextCandidate);
            _genericStandortobjektRepository = new GenericRepository<StandortObjekt>(contextCandidate);
            _genericForderungsverionenRepository = new GenericRepository<Forderungsversion>(contextCandidate);
            _genericPflichtRepository = new GenericRepository<Pflicht>(contextCandidate);
            _unitOfWork = new UnitOfWork(contextCandidate);

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
            _currentLang = sprache.SpracheID;

        }

        /// <summary>
        /// Gets the objekt by id.
        /// </summary>
        /// <param name="id">The id.</param>
        /// <returns>Returns the objekt.</returns>
        public Objekt GetByID(object id)
        {
            return _genericObjektRepository.GetByID(id);
        }

        /// <summary>
        /// Gets objekt view models by ID.
        /// </summary>
        /// <returns>Returns the viewmodel</returns>
        public ObjektViewModel GetObjektViewModelByID(object id)
        {
            var x = _genericObjektRepository.GetByID(id);
            var objekt = new ObjektViewModel
            {
                ObjektID = x.ObjektID,
                ObjektkategorieID = x.ObjektkategorieID,
                Freigabe = x.Freigabe,
                QsFreigabe = x.QsFreigabe,
                ErstelltAm = x.ErstelltAm,
                Uebersetzung = x.Uebersetzung,
                ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                ErstelltVonID = x.ErstelltVonID,
                BearbeitetAm = x.BearbeitetAm,
                BearbeitetVonID = x.BearbeitetVonID,
                BearbeitetVon = (x.BearbeitetVon != null)
                    ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname)
                    : null,
            };

            XDocument uebersetzung = XDocument.Parse(objekt.Uebersetzung);

            objekt.Name = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Name", _currentLang);
            objekt.Beschreibung = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Beschreibung", _currentLang);
            
            return objekt;
        }

        /// <summary>
        /// Inserts the objekt from the specified objekt view model.
        /// </summary>
        /// <param name="objektViewModel">The objekt view model.</param>
        public void Insert(ObjektViewModel objektViewModel)
        {
            var objekt = new Objekt();
            objekt.ObjektkategorieID = objektViewModel.ObjektkategorieID;
            //objekt.ObjektUebersetzungen.Add(uebersetzung);
            objekt.Freigabe = objektViewModel.Freigabe;
            objekt.QsFreigabe = objektViewModel.QsFreigabe;

            if (objektViewModel.Freigabe != null && objektViewModel.Freigabe.Value)
            {
                objekt.FreigabeAm = DateTime.Now;
                objekt.FreigabeVon = HttpContext.Current.User.Identity.GetUserId();
            }
            if (objektViewModel.QsFreigabe != null && objektViewModel.QsFreigabe.Value)
            {
                objekt.QsFreigabeAm = DateTime.Now;
                objekt.QsFreigabeVon = HttpContext.Current.User.Identity.GetUserId();
            }

            objekt.ErstelltAm = DateTime.Now;
            objekt.ErstelltVonID = HttpContext.Current.User.Identity.GetUserId();

            List<string> list = new List<string>();
            list.Add("Name");
            list.Add("Beschreibung");

            XDocument uebersetzung = XMLHelper.CreateNewUebersetzung(list);

            Dictionary<string, string> elements = new Dictionary<string, string>();
            elements.Add("Name", objektViewModel.Name);
            elements.Add("Beschreibung", objektViewModel.Beschreibung);

            objekt.Uebersetzung = XMLHelper.UpdateUebersetzung(uebersetzung, elements, _currentLang);
            
            _genericObjektRepository.Insert(objekt);
        }

        /// <summary>
        /// Updates the objekt from the specified view model.
        /// </summary>
        /// <param name="objektViewModel">The objekt view model.</param>
        /// <param name="userID">The user id.</param>
        public void Update(ObjektViewModel objektViewModel, string userID)
        {
            Objekt objekt = _genericObjektRepository.GetByID(objektViewModel.ObjektID);
            objekt.ObjektkategorieID = objektViewModel.ObjektkategorieID;
            objekt.BearbeitetAm = DateTime.Now;
            objekt.BearbeitetVonID = HttpContext.Current.User.Identity.GetUserId();

            var uebersetzung = XDocument.Parse(objekt.Uebersetzung);

            Dictionary<string, string> elements = new Dictionary<string, string>();
            elements.Add("Name", objektViewModel.Name);
            elements.Add("Beschreibung", objektViewModel.Beschreibung);

            objekt.Uebersetzung = XMLHelper.UpdateUebersetzung(uebersetzung, elements, _currentLang);
            

            if (objektViewModel.Freigabe == true)
            {
                objekt.SetFreigabe(true, userID);
            }
            else
            {
                objekt.SetFreigabe(false, null);
            }

            if (objektViewModel.QsFreigabe == true)
            {
                objekt.SetQsFreigabe(true, userID);
            }
            else
            {
                objekt.SetQsFreigabe(false, null);
            }

            _genericObjektRepository.Update(objekt);

        }


        /// <summary>
        /// Deletes the objekt by the specified objekt id.
        /// </summary>
        /// <param name="objektId">The objekt id.</param>
        /// <exception cref="NeoSysLCS.Repositories.Exceptions.HasRelationsException">Thrown when the objekt is still used in foderungen, pflichten or standortobjekte and 
        /// cannot be deleted </exception>
        public void Delete(int objektId)
        {
            var obj = _genericObjektRepository.Get(o => o.ObjektID == objektId, null, "Forderungsversionen,Pflichten").FirstOrDefault();
            if (obj != null && obj.Forderungsversionen.Count > 0)
            {
                throw new HasRelationsException(Resources.Properties.Resources.Entitaet_Forderung_Plural, HasRelationsException.RelationType.UsedByEntity);
            }

            if (obj != null && obj.Pflichten.Count > 0)
            {
                throw new HasRelationsException(Resources.Properties.Resources.Entitaet_Pflicht_Plural, HasRelationsException.RelationType.UsedByEntity);
            }

            if (new GenericRepository<StandortObjekt>(_context).Get(s => s.ObjektID == objektId).Any())
            {
                throw new HasRelationsException(Resources.Properties.Resources.Entitaet_StandortObjekt_Plural, HasRelationsException.RelationType.UsedByEntity);
            }

            _genericObjektRepository.Delete(objektId);
        }

        /// <summary>
        /// Reloads the specified entity from the database
        /// </summary>
        /// <param name="entityToReload">The entity to reload.</param>
        public void Reload(Objekt entityToReload)
        {
            _genericObjektRepository.Reload(entityToReload);
        }

        /// <summary>
        /// Gets all objekt view models.
        /// </summary>
        /// <returns>Returns the viewmodels</returns>
        public IQueryable<ObjektViewModel> GetAllObjektViewModels()
        {
            var objekte = (from x in _context.Objekte
                   select new ObjektViewModel
                   {
                       ObjektID = x.ObjektID,
                       ObjektkategorieID = x.ObjektkategorieID,
                       Freigabe = x.Freigabe,
                       QsFreigabe = x.QsFreigabe,
                       ErstelltAm = x.ErstelltAm,
                       Uebersetzung = x.Uebersetzung,
                       ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                       ErstelltVonID = x.ErstelltVonID,
                       BearbeitetAm = x.BearbeitetAm,
                       BearbeitetVonID = x.BearbeitetVonID,
                       BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                   }).ToList();

            foreach (var objekt in objekte)
            {
                XDocument uebersetzung = XDocument.Parse(objekt.Uebersetzung);

                objekt.Name = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Name", _currentLang);
                objekt.Beschreibung = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Beschreibung", _currentLang);
            }

            var query = objekte.AsQueryable();
            return query;
        }
        
        /// <summary>
        /// Gets the view models assigning objekts to kunden.
        /// </summary>
        /// <returns>Returns the view models</returns>
        public IQueryable<Objekt2KundenAssignmentViewModel> GetObjekt2KundenAssignmentViewModels()
        {
            return from x in _context.StandortObjekte
                   where(x.Archiviert == false || x.Archiviert.Equals(null))
                   select new Objekt2KundenAssignmentViewModel
                   {
                       ObjektID = x.ObjektID == null ? 0 : (int)x.ObjektID,
                       StandortID = x.StandortID == null ? 0 : (int)x.StandortID,
                       KundeID = x.KundeID == null ? 0 : (int)x.KundeID,

                   };
        }

        public IEnumerable<ObjektViewModel> GetObjektComboBox()
        {
            var objekte = (from x in _context.Objekte
                join tx in _context.StandortObjekte on x.ObjektID equals tx.ObjektID
                where (x.ObjektID == tx.ObjektID && (tx.Archiviert == false || tx.Archiviert.Equals(null)))
                select new ObjektViewModel()
                {
                    ObjektID = x.ObjektID,
                    Uebersetzung = x.Uebersetzung,
                    //Name = ((from tu in x.ObjektUebersetzungen
                    //         join sp in _context.Sprachen on tu.SpracheID equals sp.SpracheID
                    //         where
                    //             (tu.SpracheID == _currentLang && !string.IsNullOrEmpty(tu.Name))
                    //             ||
                    //             (!string.IsNullOrEmpty(tu.Name) &&
                    //              !(from tu2 in x.ObjektUebersetzungen
                    //                where tu2.SpracheID == _currentLang && !string.IsNullOrEmpty(tu2.Name)
                    //                select tu2).Any())
                    //         orderby sp.Reihenfolge
                    //         select tu.Name).FirstOrDefault() ?? Resources.Properties.Resources.Fehler_Keine_Uebersetzung),
                }).ToList();

            foreach (var objekt in objekte)
            {
                XDocument uebersetzung = XDocument.Parse(objekt.Uebersetzung);

                objekt.Name = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Name", _currentLang);
            }

            return objekte;
        }

        /// <summary>
        /// Saves the forderungen assignemnt of the specified objects.
        /// </summary>
        /// <param name="selectedIDs">The selected ids.</param>
        /// <param name="objektID">The objekt id.</param>
        public void SaveForderungen(string selectedIDs, string objektID)
        {
            int objektIDInt = Convert.ToInt32(objektID);

            Objekt objekt = _genericObjektRepository.Get(e => e.ObjektID == objektIDInt, null, "Forderungsversionen").FirstOrDefault();

            string[] ids = selectedIDs.Split(',');

            if (selectedIDs != "")
            {
                foreach (var id in ids)
                {
                    Forderungsversion forderungsversion = _genericForderungsverionenRepository.GetByID(Convert.ToInt32(id));
                    if (!objekt.Forderungsversionen.Contains(forderungsversion))
                    {
                        objekt.Forderungsversionen.Add(forderungsversion);
                    }
                }
            }
            foreach (var forderungsversion in objekt.Forderungsversionen.ToArray())
            {
                if (!ids.ToList().Contains(forderungsversion.ForderungsversionID.ToString()))
                {
                    objekt.Forderungsversionen.Remove(forderungsversion);
                }
            }
            if (objekt.Forderungsversionen.Count == 0)
            {
                objekt.Forderungsversionen = null;
            }
        }

        /// <summary>
        /// Saves the pflichten assignment of the specified objekt.
        /// </summary>
        /// <param name="selectedIDs">The selected ids.</param>
        /// <param name="objektID">The objekt id.</param>
        public void SavePflichten(string selectedIDs, string objektID)
        {
            int objektIDInt = Convert.ToInt32(objektID);

            Objekt objekt = _genericObjektRepository.Get(e => e.ObjektID == objektIDInt, null, "Pflichten").FirstOrDefault();

            string[] ids = selectedIDs.Split(',');

            if (selectedIDs != "")
            {
                foreach (var id in ids)
                {
                    Pflicht pflicht = _genericPflichtRepository.GetByID(Convert.ToInt32(id));
                    if (!objekt.Pflichten.Contains(pflicht))
                    {
                        objekt.Pflichten.Add(pflicht);
                    }
                }
            }
            foreach (var pflicht in objekt.Pflichten.ToArray())
            {
                if (!ids.ToList().Contains(pflicht.PflichtID.ToString()))
                {
                    objekt.Pflichten.Remove(pflicht);
                }
            }
            if (objekt.Pflichten.Count == 0)
            {
                objekt.Pflichten = null;
            }
        }


        /// <summary>
        /// Gets the objekte view models by forderung.
        /// </summary>
        /// <param name="forderungsversionId">The forderungsversion identifier.</param>
        /// <returns>Returns the view model</returns>
        public IQueryable<ObjektViewModel> GetByForderung(int forderungsversionId)
        {
            var objekte = (from x in _context.Objekte
                   where(x.Forderungsversionen.Any(f => f.ForderungsversionID == forderungsversionId))
                   select new ObjektViewModel
                   {
                       ObjektID = x.ObjektID,
                       ObjektkategorieID = x.ObjektkategorieID,
                       Freigabe = x.Freigabe,
                       QsFreigabe = x.QsFreigabe,
                       ErstelltAm = x.ErstelltAm,
                       ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                       ErstelltVonID = x.ErstelltVonID,
                       BearbeitetAm = x.BearbeitetAm,
                       BearbeitetVonID = x.BearbeitetVonID,
                       BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                       Uebersetzung = x.Uebersetzung,
                       //Name = ((from tu in x.ObjektUebersetzungen
                       //         join sp in _context.Sprachen on tu.SpracheID equals sp.SpracheID
                       //         where
                       //             (tu.SpracheID == _currentLang && !string.IsNullOrEmpty(tu.Name))
                       //             ||
                       //             (!string.IsNullOrEmpty(tu.Name) &&
                       //              !(from tu2 in x.ObjektUebersetzungen
                       //                where tu2.SpracheID == _currentLang && !string.IsNullOrEmpty(tu2.Name)
                       //                select tu2).Any())
                       //         orderby sp.Reihenfolge
                       //         select tu.Name).FirstOrDefault() ?? Resources.Properties.Resources.Fehler_Keine_Uebersetzung),
                       //Beschreibung = ((from tu in x.ObjektUebersetzungen
                       //                 join sp in _context.Sprachen on tu.SpracheID equals sp.SpracheID
                       //                 where
                       //                     (tu.SpracheID == _currentLang && !string.IsNullOrEmpty(tu.Beschreibung))
                       //                     ||
                       //                     (!string.IsNullOrEmpty(tu.Beschreibung) &&
                       //                      !(from tu2 in x.ObjektUebersetzungen
                       //                        where tu2.SpracheID == _currentLang && !string.IsNullOrEmpty(tu2.Beschreibung)
                       //                        select tu2).Any())
                       //                 orderby sp.Reihenfolge
                       //                 select tu.Beschreibung).FirstOrDefault() ?? ""),
                   }).ToList();

            foreach (var objekt in objekte)
            {
                XDocument uebersetzung = XDocument.Parse(objekt.Uebersetzung);

                objekt.Name = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Name", _currentLang);
                objekt.Beschreibung = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Beschreibung", _currentLang);
            }

            var query = objekte.AsQueryable();
            return query;
        }

        /// <summary>
        /// Gets the objekte view models by pflicht.
        /// </summary>
        /// <param name="pflichtId">The forderungsversion identifier.</param>
        /// <returns>Returns the view model</returns>
        public IQueryable<ObjektViewModel> GetByPflicht(int pflichtId)
        {
            var objekte = (from x in _context.Objekte
                   where(x.Pflichten.Any(p => p.PflichtID == pflichtId))
                   select new ObjektViewModel
                   {
                       ObjektID = x.ObjektID,
                       ObjektkategorieID = x.ObjektkategorieID,
                       Freigabe = x.Freigabe,
                       QsFreigabe = x.QsFreigabe,
                       ErstelltAm = x.ErstelltAm,
                       ErstelltVon = (x.ErstelltVon != null) ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname) : null,
                       ErstelltVonID = x.ErstelltVonID,
                       BearbeitetAm = x.BearbeitetAm,
                       BearbeitetVonID = x.BearbeitetVonID,
                       BearbeitetVon = (x.BearbeitetVon != null) ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname) : null,
                       Uebersetzung = x.Uebersetzung,
                       //Name = ((from tu in x.ObjektUebersetzungen
                       //         join sp in _context.Sprachen on tu.SpracheID equals sp.SpracheID
                       //         where
                       //             (tu.SpracheID == _currentLang && !string.IsNullOrEmpty(tu.Name))
                       //             ||
                       //             (!string.IsNullOrEmpty(tu.Name) &&
                       //              !(from tu2 in x.ObjektUebersetzungen
                       //                where tu2.SpracheID == _currentLang && !string.IsNullOrEmpty(tu2.Name)
                       //                select tu2).Any())
                       //         orderby sp.Reihenfolge
                       //         select tu.Name).FirstOrDefault() ?? Resources.Properties.Resources.Fehler_Keine_Uebersetzung),
                       //Beschreibung = ((from tu in x.ObjektUebersetzungen
                       //                 join sp in _context.Sprachen on tu.SpracheID equals sp.SpracheID
                       //                 where
                       //                     (tu.SpracheID == _currentLang && !string.IsNullOrEmpty(tu.Beschreibung))
                       //                     ||
                       //                     (!string.IsNullOrEmpty(tu.Beschreibung) &&
                       //                      !(from tu2 in x.ObjektUebersetzungen
                       //                        where tu2.SpracheID == _currentLang && !string.IsNullOrEmpty(tu2.Beschreibung)
                       //                        select tu2).Any())
                       //                 orderby sp.Reihenfolge
                       //                 select tu.Beschreibung).FirstOrDefault() ?? ""),
                   }).ToList();

            foreach (var objekt in objekte)
            {
                XDocument uebersetzung = XDocument.Parse(objekt.Uebersetzung);

                objekt.Name = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Name", _currentLang);
                objekt.Beschreibung = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Beschreibung", _currentLang);
            }

            var query = objekte.AsQueryable();
            return query;
        }

        public IQueryable<ObjektViewModel> GetAllObjektViewModelsByKommentar(int kommentarID)
        {
            var objekte =  (from x in _context.Objekte
                   where (x.Kommentare.Any(k => k.KommentarID == kommentarID))
                   select new ObjektViewModel()
                   {
                       ObjektID = x.ObjektID,
                       Uebersetzung = x.Uebersetzung,
                       //    Name = ((from tu in x.ObjektUebersetzungen
                       //              join sp in _context.Sprachen on tu.SpracheID equals sp.SpracheID
                       //              where
                       //                  (tu.SpracheID == _currentLang && !string.IsNullOrEmpty(tu.Name))
                       //                  ||
                       //                  (!string.IsNullOrEmpty(tu.Name) &&
                       //                   !(from tu2 in x.ObjektUebersetzungen
                       //                     where tu2.SpracheID == _currentLang && !string.IsNullOrEmpty(tu2.Name)
                       //                     select tu2).Any())
                       //              orderby sp.Reihenfolge
                       //              select tu.Name).FirstOrDefault() ?? Resources.Properties.Resources.Fehler_Keine_Uebersetzung)
                   }).ToList();

            foreach (var objekt in objekte)
            {
                XDocument uebersetzung = XDocument.Parse(objekt.Uebersetzung);

                objekt.Name = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Name", _currentLang);                
            }

            var query = objekte.AsQueryable();
            return query;
        }

        public IQueryable<ObjektViewModel> GetObjektUebersetzungenByObjekt(int id)
        {
            Objekt objekt = _genericObjektRepository.GetByID(id);

            List<int> sprachIds = _context.Sprachen.Select(x => x.SpracheID).ToList();
            List<ObjektViewModel> uebersetzungen = new List<ObjektViewModel>();
            XDocument xml = XDocument.Parse(objekt.Uebersetzung);

            foreach (int sprachId in sprachIds)
            {
                ObjektViewModel uebersetzung = new ObjektViewModel();
                uebersetzung.ObjektUebersetzungID = id.ToString() + "_" + sprachId.ToString();
                uebersetzung.Name = XMLHelper.GetAllUebersetzungFromXmlField(xml, "Name", sprachId);
                uebersetzung.Beschreibung = XMLHelper.GetAllUebersetzungFromXmlField(xml, "Beschreibung", sprachId);

                uebersetzung.SpracheID = sprachId;
                uebersetzung.ObjektID = id;
                uebersetzungen.Add(uebersetzung);
            }

            return uebersetzungen.AsQueryable();
        }

        public void InsertUebersetzungObjekt(ObjektViewModel uebersetzung, int ObjektID)
        {
            Objekt objekt = _genericObjektRepository.GetByID(ObjektID);

            var xml = XDocument.Parse(objekt.Uebersetzung);
            Dictionary<string, string> elements = new Dictionary<string, string>();
            elements.Add("Name", uebersetzung.Name);
            elements.Add("Beschreibung", uebersetzung.Beschreibung);

            objekt.Uebersetzung = XMLHelper.UpdateUebersetzung(xml, elements, uebersetzung.SpracheID);

            _genericObjektRepository.Update(objekt);
        }

        public void UpdateObjektUebersetzung(ObjektViewModel viewModel)
        {
            Objekt objekt = _genericObjektRepository.GetByID(viewModel.ObjektID);

            var uebersetzung = XDocument.Parse(objekt.Uebersetzung);

            Dictionary<string, string> elements = new Dictionary<string, string>();
            elements.Add("Name", viewModel.Name);
            elements.Add("Beschreibung", viewModel.Beschreibung);

            objekt.Uebersetzung = XMLHelper.UpdateUebersetzung(uebersetzung, elements, viewModel.SpracheID);

            _genericObjektRepository.Update(objekt);
        }

        public bool CheckIfNameAlreadyExists(int sprachId, int objektId, string name)
        {

            var objekte = (from x in _context.Objekte
                           where x.ObjektID != objektId
                           select new ObjektViewModel
                           {
                               ObjektID = x.ObjektID,
                               Uebersetzung = x.Uebersetzung
                           }).ToList();

            foreach (var objekt in objekte)
            {
                XDocument uebersetzung = XDocument.Parse(objekt.Uebersetzung);
                objekt.Name = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Name", sprachId);
            }

            return objekte.Where(e => e.Name == name).Any();

            //return _genericObjekteUebersetzungRepository.Get(
            //    e => e.Name.Equals(name, StringComparison.CurrentCulture) && e.ObjektID != objektId && e.SpracheID == sprachId).Any();
        }
    }
}