using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web.WebPages;
using System.Xml.Linq;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.ViewModels;
using Newtonsoft.Json.Converters;


namespace NeoSysLCS.Repositories
{
    public class ChecklistRepository
    {
        private readonly IGenericRepository<Checklist> _genericChecklistRepository;

        private readonly UnitOfWork _unitOfWork;
        private readonly NeoSysLCS_Dev _context;
        private readonly int _currentLang;


        public ChecklistRepository(NeoSysLCS_Dev context)
        {
            _context = context;
            _genericChecklistRepository = new GenericRepository<Checklist>(context);

            _unitOfWork = new UnitOfWork(context);

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            var sprache = _context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
            _currentLang = sprache.SpracheID;

        }

        public Checklist GetByID(object id)
        {
            return _genericChecklistRepository.GetByID(id);
        }

        public void Insert(Checklist checklist)
        {
            _genericChecklistRepository.Insert(checklist);
        }

        public void Update(Checklist checklist)
        {
            _genericChecklistRepository.Update(checklist);
        }

        public IQueryable<ChecklistViewModel> GetAllChecklistViewModels()
        {
            var checklists = (from c in _context.Checklist
                select new ChecklistViewModel()
                {
                    ChecklistID = c.ChecklistID,
                    ErlassID = c.ErlassID,
                    ErlassfassungID = c.ErlassfassungID.Value,
                    ErstelltAm = c.ErstelltAm,
                    BearbeitetAm = c.BearbeitetAm,
                    Questions = c.Questions.AsQueryable(),
                    QuestionCount = c.Questions.Count(),
                    Translation = c.Translation,
                    LastUpdatedOnChildItem = c.LastUpdatedOnChildItem,
                    ErlassUebersetzung = c.Erlass.Uebersetzung,
                    ErlassNummer = c.Erlass.SrNummer,
                }).ToList();

            foreach (var chk in checklists)
            {
                var translation = XDocument.Parse(chk.Translation);

                chk.Title = XMLHelper.GetUebersetzungFromXmlField(translation, "Title", _currentLang);
                chk.Description = XMLHelper.GetUebersetzungFromXmlField(translation, "Description", _currentLang);
                chk.LastUpdatedOnChildItemDateTime = chk.LastUpdatedOnChildItem == null || chk.LastUpdatedOnChildItem.IsEmpty()
                    ? DateTime.Now
                    : DateTime.ParseExact(chk.LastUpdatedOnChildItem, "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None);

                Erlass erlass = _unitOfWork.ErlassRepository.GetByID(chk.ErlassID);
                XDocument erlassUebersetzung = XDocument.Parse(chk.ErlassUebersetzung);
                chk.ErlassQuelle = XMLHelper.GetAllUebersetzungFromXmlField(erlassUebersetzung, "Quelle", _currentLang);

                Erlassfassung erlassfassung = _unitOfWork.ErlassfassungRepository.GetByID(chk.ErlassfassungID);
                chk.Inkrafttretung = erlassfassung.Inkrafttretung;
            }

            return checklists.AsQueryable();
        }

        public int GetKundendokumentChecklistIdByErlassId(int erlassId, int kundendokumentId)
        {
            List<int> erlassFassungIds = _unitOfWork.ErlassfassungRepository.GetAllErlassfassungViewModels(erlassId).Select(x => x.ErlassfassungID).ToList();
            var kundendokumentChecklists = _unitOfWork.kundendokumentChecklistRepository.GetKundendokumentChecklistsByKundendokument(kundendokumentId);
            var kundedokumentChecklist = kundendokumentChecklists.FirstOrDefault(chk => erlassFassungIds.Contains(chk.ErlassfassungID));
            if (kundedokumentChecklist != null)
            {
                return kundedokumentChecklist.KundendokumentChecklistID;
            }
            else
            {
                return 0;
            }            
        }

        public IQueryable<ChecklistQuestionViewModel> GetChecklistQuestions(int checklistID)
        {
            Checklist checklist = GetByID(checklistID);

            var questions = (from q in checklist.Questions
                select new ChecklistQuestionViewModel()
                {
                    ChecklistQuestionID = q.ChecklistQuestionID,
                    SharedImage = q.SharedImage,
                    Translation = q.Translation,
                    HeaderID = q.ChecklistHeaderID,
                    Numeration = q.Numeration,
                    ChecklistType = q.ChecklistType,
                }).ToList();

            foreach (var q in questions)
            {
                var translation = XDocument.Parse(q.Translation);

                q.Title = XMLHelper.GetUebersetzungFromXmlField(translation, "Title", _currentLang);

                var headerTranslation = (from x in _context.ChecklistHeader
                                         where x.ChecklistHeaderID == q.HeaderID
                                         select x.Translation).First();

                var headerTranslationParsed = XDocument.Parse(headerTranslation);
                var headerId = "" + q.HeaderID;
                if (q.HeaderID < 10)
                {
                    headerId = "0" + q.HeaderID;
                }
                q.HeaderTitle = headerId + ": " + XMLHelper.GetUebersetzungFromXmlField(headerTranslationParsed, "Title", _currentLang);
            }

            //var nums = "0123456789".ToCharArray();
            //return questions.OrderBy(x => x.Numeration.LastIndexOfAny(nums)).ThenBy(x => x.Numeration).AsQueryable();
            return questions.AsQueryable();
        }

        public void UpdateChecklistOnNewErlassfassung(int erlassID)
        {
            Checklist checklist = _genericChecklistRepository.Get(x => x.ErlassID == erlassID).FirstOrDefault();
            if (checklist != null)
            {
                int erlassfassungID = _unitOfWork.ErlassfassungRepository.getLatestErlassfassungIdByErlass(erlassID);
                checklist.ErlassfassungID = erlassfassungID;
                Update(checklist);
            }
        }
    }
}