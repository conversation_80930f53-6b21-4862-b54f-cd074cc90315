using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Xml.Linq;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Repositories
{

    public class CustomerNewsRepository
    {

        private readonly IGenericRepository<CustomerNews> _genericCustomerNewsRepository;
        private readonly NeoSysLCS_Dev _context;
        private readonly UnitOfWork _unitOfWork;
        private readonly int _currentLang;


        public static readonly Expression<Func<CustomerNews, bool>> AllNews = x => true;

        public static readonly Expression<Func<CustomerNews, bool>> NewsForToday = x =>
            DateTime.Now >= x.FromDate && DateTime.Now <= x.ToDate;

        public CustomerNewsRepository(NeoSysLCS_Dev context)
        {
            _context = context;
            _genericCustomerNewsRepository = new GenericRepository<CustomerNews>(context);
            _unitOfWork = new UnitOfWork(context);

            var isoLanguage = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache language = _context.Sprachen.FirstOrDefault(s => s.Lokalisierung == isoLanguage);
            if (language != null)
            {
                _currentLang = language.SpracheID;
            }
        }

        /// <summary>
        /// Gets the CustomerNews by id
        /// </summary>
        /// <param name="id">The id.</param>
        /// <returns>Returns theThe CustomerNews</returns>
        public CustomerNews GetById(object id)
        {
            return _genericCustomerNewsRepository.GetByID(id);
        }

        /// <summary>
        /// Gets a enumerable of customer news filtered by the specified filter
        /// </summary>
        /// <param name="filter">The filter.</param>
        /// <param name="orderBy">The order by.</param>
        /// <param name="includeProperties">The include properties.</param>
        /// <returns>Returns the CustomerNews</returns>
        public IEnumerable<CustomerNews> Get(Expression<Func<CustomerNews, bool>> filter = null, Func<IQueryable<CustomerNews>, IOrderedQueryable<CustomerNews>> orderBy = null, string includeProperties = "")
        {
            return _genericCustomerNewsRepository.Get(filter, orderBy, includeProperties);
        }


        public IQueryable<CustomerNewsViewModel> GetAllCustomerNewsViewModels(Expression<Func<CustomerNews, bool>> filter = null)
        {
            if (filter == null)
            {
                filter = AllNews;
            }

            var query = _context.CustomerNews
                .Where(filter)
                .Select(x => new CustomerNewsViewModel()
                {
                    CustomerNewsID = x.CustomerNewsID,
                    FromDate = x.FromDate,
                    ToDate = x.ToDate,
                    Translation = x.Translation
                }).ToList().OrderByDescending(x => x.FromDate);
            
            foreach (var news  in query)
            {
                XDocument translation = XDocument.Parse(news.Translation);
                news.Text = XMLHelper.GetUebersetzungFromXmlField(translation, "Text", _currentLang);
                news.Link = XMLHelper.GetUebersetzungFromXmlField(translation, "Link", _currentLang);
            }
            return query.AsQueryable();
        }

        /// <summary>
        /// Inserts the specified CustomerNews.
        /// </summary>
        /// <param name="viewModel">The entity.</param>
        public void Insert(CustomerNewsViewModel viewModel)
        {
            var entity = new CustomerNews
            {
                FromDate = viewModel.FromDate,
                ToDate = viewModel.ToDate
            };

            var list = new List<string>
            {
                "Text",
                "Link"
            };

            var translation = XMLHelper.CreateNewUebersetzung(list);
           
            var elements = new Dictionary<string, string>
            {
                { "Text", viewModel.Text },
                { "Link", viewModel.Link }
            };

            entity.Translation = XMLHelper.UpdateUebersetzung(translation, elements, _currentLang);

            _genericCustomerNewsRepository.Insert(entity);

        }

        /// <summary>
        /// Updates the customerNews from the specified view model.
        /// </summary>
        /// <param name="viewModel">The view model.</param>
        public void Update(CustomerNewsViewModel viewModel)
        {
            var entity = _genericCustomerNewsRepository.GetByID(viewModel.CustomerNewsID);
            entity.FromDate = viewModel.FromDate;
            entity.ToDate = viewModel.ToDate;

            var translation = XDocument.Parse(entity.Translation);
            var elements = new Dictionary<string, string>
            {
                { "Text", viewModel.Text },
                { "Link", viewModel.Link }
            };

            entity.Translation = XMLHelper.UpdateUebersetzung(translation, elements, _currentLang);

            _genericCustomerNewsRepository.Update(entity);

        }

        public void Delete(int customerNewsID)
        {
            _genericCustomerNewsRepository.Delete(customerNewsID);
        }


        public IQueryable<CustomerNewsViewModel> GetCustomerNewsTranslationsByNewsId(int customerNewsId)
        {
            var customerNews = _genericCustomerNewsRepository.GetByID(customerNewsId);

            var languageIds = _context.Sprachen.Select(x => x.SpracheID).ToList();
            var xml = XDocument.Parse(customerNews.Translation);

            var translations = languageIds.Select(languageId => new CustomerNewsViewModel
                {
                    TranslationID = customerNewsId.ToString() + "_" + languageId.ToString(),
                    Text = XMLHelper.GetAllUebersetzungFromXmlField(xml, "Text", languageId),
                    Link = XMLHelper.GetAllUebersetzungFromXmlField(xml, "Link", languageId),
                    LanguageID = languageId,
                    CustomerNewsID = customerNewsId
                })
                .ToList();

            return translations.AsQueryable();
        }


        public void UpdateCustomerNewsTranslation(CustomerNewsViewModel translation, int customerNewsId)
        {
            CustomerNews news = _genericCustomerNewsRepository.GetByID(customerNewsId);

            var xml = XDocument.Parse(news.Translation);
            Dictionary<string, string> elements = new Dictionary<string, string>();
            elements.Add("Text", translation.Text);
            elements.Add("Link", translation.Link);
            
            news.Translation= XMLHelper.UpdateUebersetzung(xml, elements, translation.LanguageID);

            _genericCustomerNewsRepository.Update(news);
        }

        public void UpdateUploadedFileUrl(int customerNewsId, string field, string fileUrl, int languageId)
        {
            CustomerNews news = _unitOfWork.CustomerNewsRepository.GetById(customerNewsId);

            var translation = XDocument.Parse(news.Translation);

            Dictionary<string, string> elements = new Dictionary<string, string>();
            elements.Add(field, fileUrl);

            news.Translation = XMLHelper.UpdateUebersetzung(translation, elements, languageId != -1 ? languageId : _currentLang);

            _genericCustomerNewsRepository.Update(news);

        }

    }
}