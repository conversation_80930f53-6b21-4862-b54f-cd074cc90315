using System.Linq;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories
{
    /// <summary>
    /// The repository for KindeCortec
    /// </summary>
    public class KundeCortecRepository
    {
        private readonly IGenericRepository<KundeCortec> _genericKundeCortecRepository;

        private readonly UnitOfWork _unitOfWork;
        private readonly NeoSysLCS_Dev _context;

        /// <summary>
        /// Initializes a new instance.
        /// </summary>
        /// <param name="contextCandidate">The context candidate.</param>
        public KundeCortecRepository(NeoSysLCS_Dev contextCandidate)
        {
            _context = contextCandidate;
            _unitOfWork = new UnitOfWork(contextCandidate);
            _genericKundeCortecRepository = new GenericRepository<KundeCortec>(contextCandidate);
        }

        public KundeCortec GetKundeCortecByAuftragNr(string auftragNr)
        {
            var customerCortec = (from ks in this._context.KundeCortec
                    where ks.AuftragNr == auftragNr
                    select ks).FirstOrDefault<KundeCortec>();
            if (customerCortec != null) {
                var customerCortecAddress = (from kc in this._context.KundeCortecCorrespondenceAddress
                                             where kc.KundeCortecID == customerCortec.KundeCortecID
                                             select kc).ToArray();
                customerCortec.AdrKorrespondenz = customerCortecAddress;
            }

            return customerCortec;
        }

        public void Insert(KundeCortec kundeCortec)
        {
            _genericKundeCortecRepository.Insert(kundeCortec);
        }

        public void Update(KundeCortec kundeCortec)
        {
            _genericKundeCortecRepository.Update(kundeCortec);
        }
    }
}





