using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.ViewModels;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web;

namespace NeoSysLCS.Repositories
{
    public class LegalComplianceRepository
    {

        private readonly IGenericRepository<LegalCompliance> _genericLegalComplianceRepository;
        private readonly UnitOfWork _unitOfWork;
        private readonly NeoSysLCS_Dev _context;

        /// <summary>
        /// Initializes a new instance.
        /// </summary>
        /// <param name="contextCandidate">The context candidate.</param>
        public LegalComplianceRepository(NeoSysLCS_Dev contextCandidate)
        {
            _genericLegalComplianceRepository = new GenericRepository<LegalCompliance>(contextCandidate);
            _unitOfWork = new UnitOfWork(contextCandidate);
            _context = contextCandidate;
        }

        public List<LegalComplianceViewModel> GetByStandortKundendokument(object id, DateTime startDate, DateTime endDate, int standortId = 0)
        {
            var list = new ArrayList();
            var kundeID = Convert.ToInt32(id);

            DateTime currentDayPlusOne = DateTime.Today.AddDays(1);
            List<Kundendokument> kundendokumente = new List<Kundendokument>();
            if (standortId == 0)
            {
                kundendokumente = (from k in new GenericRepository<Kundendokument>(_context).Get().ToList()
                                   where k.KundeID == kundeID && k.Status == KundendokumentStatus.Approved && k.PublizierenAm < currentDayPlusOne
                                   select k).ToList();
            }
            else
            {
                kundendokumente = (from k in new GenericRepository<Kundendokument>(_context).Get().ToList()
                                   where k.KundeID == kundeID && k.Status == KundendokumentStatus.Approved && k.PublizierenAm < currentDayPlusOne && k.StandortID == standortId
                                   select k).ToList();
            }

            Dictionary<string, int> monthDictionairy = new Dictionary<string, int>();
            monthDictionairy.Add(CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(1), 1);
            monthDictionairy.Add(CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(2), 2);
            monthDictionairy.Add(CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(3), 3);
            monthDictionairy.Add(CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(4), 4);
            monthDictionairy.Add(CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(5), 5);
            monthDictionairy.Add(CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(6), 6);
            monthDictionairy.Add(CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(7), 7);
            monthDictionairy.Add(CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(8), 8);
            monthDictionairy.Add(CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(9), 9);
            monthDictionairy.Add(CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(10), 10);
            monthDictionairy.Add(CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(11), 11);
            monthDictionairy.Add(CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(12), 12);

            List<LegalComplianceViewModel> legalComplianceViewModelList = new List<LegalComplianceViewModel>();
            DateTime newDate = new DateTime();
            if (endDate != newDate && startDate != newDate)
            {
                List<LegalCompliance> completeLegalComplianceList = new List<LegalCompliance>();
                foreach (Kundendokument kundendokument in kundendokumente)
                {
                    List<LegalCompliance> legalComplianceList = _genericLegalComplianceRepository.Get(x => x.KundendokumentID == kundendokument.KundendokumentID).ToList();
                    if (legalComplianceList.Any())
                    {
                        completeLegalComplianceList.AddRange(legalComplianceList);
                    }
                }

                List<LegalCompliance> rangeLegalComplianceList = new List<LegalCompliance>();
                rangeLegalComplianceList.AddRange(completeLegalComplianceList.Where(x => x.ErstelltAm.Value >= startDate && x.ErstelltAm.Value <= endDate));

                foreach (LegalCompliance legalCompliance in rangeLegalComplianceList)
                {
                    LegalComplianceViewModel legalComplianceErfuellt = new LegalComplianceViewModel();
                    legalComplianceErfuellt.Erfuellung = Resources.Properties.Resources.Chart_Label_Erfuellt;
                    legalComplianceErfuellt.Erfuellt = legalCompliance.Erfuellt;
                    legalComplianceErfuellt.ErstelltAm = legalCompliance.ErstelltAm.Value;
                    int month = legalCompliance.ErstelltAm.Value.Month;
                    legalComplianceErfuellt.Month = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(month) + " " + legalCompliance.ErstelltAm.Value.Year.ToString();

                    LegalComplianceViewModel legalComplianceNichtErfuellt = new LegalComplianceViewModel();
                    legalComplianceNichtErfuellt.Erfuellung = Resources.Properties.Resources.Chart_Label_NichtErfuellt;
                    legalComplianceNichtErfuellt.Erfuellt = legalCompliance.NichtErfuellt;
                    legalComplianceNichtErfuellt.ErstelltAm = legalCompliance.ErstelltAm.Value;
                    legalComplianceNichtErfuellt.Month = legalCompliance.ErstelltAm.Value.Month.ToString() + " " + legalCompliance.ErstelltAm.Value.Year.ToString();
                    legalComplianceNichtErfuellt.Month = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(month) + " " + legalCompliance.ErstelltAm.Value.Year.ToString();

                    LegalComplianceViewModel legalComplianceInAbklaerung = new LegalComplianceViewModel();
                    legalComplianceInAbklaerung.Erfuellung = Resources.Properties.Resources.Chart_Label_InAbklaeurung;
                    legalComplianceInAbklaerung.Erfuellt = legalCompliance.InAbklaerung;
                    legalComplianceInAbklaerung.ErstelltAm = legalCompliance.ErstelltAm.Value;
                    legalComplianceInAbklaerung.Month = legalCompliance.ErstelltAm.Value.Month.ToString() + " " + legalCompliance.ErstelltAm.Value.Year.ToString();
                    legalComplianceInAbklaerung.Month = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(month) + " " + legalCompliance.ErstelltAm.Value.Year.ToString();

                    LegalComplianceViewModel legalComplianceNichtBearbeitet = new LegalComplianceViewModel();
                    legalComplianceNichtBearbeitet.Erfuellung = Resources.Properties.Resources.Chart_Label_NotEdited;
                    legalComplianceNichtBearbeitet.Erfuellt = legalCompliance.NichtBearbeitet;
                    legalComplianceNichtBearbeitet.ErstelltAm = legalCompliance.ErstelltAm.Value;
                    legalComplianceNichtBearbeitet.Month = legalCompliance.ErstelltAm.Value.Month.ToString() + " " + legalCompliance.ErstelltAm.Value.Year.ToString();
                    legalComplianceNichtBearbeitet.Month = CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(month) + " " + legalCompliance.ErstelltAm.Value.Year.ToString();


                    legalComplianceViewModelList.Add(legalComplianceErfuellt);
                    legalComplianceViewModelList.Add(legalComplianceNichtErfuellt);
                    legalComplianceViewModelList.Add(legalComplianceInAbklaerung);
                    legalComplianceViewModelList.Add(legalComplianceNichtBearbeitet);
                }
            }

            return legalComplianceViewModelList.OrderBy(x => x.ErstelltAm).ToList();
        }

        public void CloneForStandort(int oldStandortId, int newKundendokumentId, int newStandortId)
        {
            var legalComplianceOfOriginalKundendokument =_genericLegalComplianceRepository.Get(x => x.StandortID == oldStandortId,
                orderBy: q => q.OrderByDescending(x => x.ErstelltAm)).FirstOrDefault();
            if (legalComplianceOfOriginalKundendokument != null)
            {
                var legalComplianceOfNewKundendokument = new LegalCompliance
                {
                    BearbeitetAm = legalComplianceOfOriginalKundendokument.BearbeitetAm,
                    BearbeitetVonID = legalComplianceOfOriginalKundendokument.BearbeitetVonID,
                    ErstelltAm = legalComplianceOfOriginalKundendokument.ErstelltAm,
                    ErstelltVonID = legalComplianceOfOriginalKundendokument.ErstelltVonID,
                    Erfuellt = legalComplianceOfOriginalKundendokument.Erfuellt,
                    NichtErfuellt = legalComplianceOfOriginalKundendokument.NichtErfuellt,
                    InAbklaerung = legalComplianceOfOriginalKundendokument.InAbklaerung,
                    NichtBearbeitet = legalComplianceOfOriginalKundendokument.NichtBearbeitet,
                    Total = legalComplianceOfOriginalKundendokument.Total,
                    KundendokumentID = newKundendokumentId,
                    StandortID = newStandortId
                };
                _genericLegalComplianceRepository.Insert(legalComplianceOfNewKundendokument);
                _context.SaveChanges();
            }
        }
    }
}