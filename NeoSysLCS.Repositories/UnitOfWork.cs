using System;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Repositories
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly NeoSysLCS_Dev _context;
        private bool _disposed = false;
        private ForderungsversionRepository _forderungRepository;
        private ArtikelRepository _artikelRepository;
        private HerausgeberRepository _herausgeberRepository;
        private ErlassRepository _erlassRepository;
        private ErlasstypRepository _erlasstypRepository;
        private ErlassfassungRepository _erlassversionRepository;
        private ObjektRepository _objektRepository;
        private PflichtRepository _pflichtRepository;
        private ObligationRepository _obligationRepository;
        private RechtsbereichRepository _rechtsbereichRepository;
        private GenericRepository<Sprache> _spracheRepository;
        private ObjektkategorieRepository _objektkategorieRepository;
        private KundeRepository _kundeRepository;
        private OfferRepository _offerRepository;
        private KontaktRepository _kontaktRepository;
        private UserRepository _userRepository;
        private StandortRepository _standortRepository;
        private IndividuelleForderungRepository _individuelleForderungRepository;
        private StandortObjektRepository _standortObjektRepository;
        private KundendokumentRepository _kundendokumentRepository;
        private KundendokumentForderungsversionRepository _kundendokumentForderungsversionRepository;
        private KundendokumentErlassfassungenRepository _kundendokumentErlassfassungRepository;
        private KundendokumentPflichtRepository _kundendokumentPflichtRepository;
        private AllgemeineKundeninformationRepository _allgemeineKundeninformationRepository;
        private GridViewCookieRepository _GridViewCookieViewRepository;
        private KonzernRepository _konzernRepository;
        private UserStandortRoleRepository _userStandortRoleRepository;
        private ShortcutRepository _shortcutRepository;
        private KundendokumentErfuellungRepository _kundendokumentErfuellungRepository;
        private FAQRepository _FAQRepository;
        private FAQKategorieRepository _FAQKategorieRepository;
        private KommentarRepository _KommentarRepository;
        private LegalComplianceRepository _LegalComplianceRepository;
        private StandortMenuUebersetzungRepository _StandortMenuUebersetzungRepository;
        private ToDoRepository _toDoRepository;
        private EvaluationRepository _evaluationRepository;
        private KundeSummaryRepository _kundeSummaryRepository;
        private CustomerNewsRepository _customerNewsRepository;
        private ConsultationRepository _consultationRepository;
        private DashboardRepository _dashboardRepository;
        private NewsletterRepository _newsletterRepository;
        private ChecklistRepository _checklistRepository;
        private ChecklistQuestionRepository _checklistQuestionRepository;
        private ChecklistHeaderRepository _checklistHeaderRepository;
        private KundendokumentChecklistRepository _kundendokumentChecklistRepository;
        private MassnahmeRepository _massnahmeRepository;
        private KundeCortecRepository _kundeCortecRepository;
        private ToDoTaskRepository _toDoTaskRepository;
        private PrivacyPolicyRepository _privacyPolicyRepository;
        private ApplicationUserPrivacyPolicyRepository _applicationUserPrivacyPolicyRepository;
        private StandortBerichtRepository _standortBerichtRepository;
        private CortecTaskRepository _cortecTaskRepository;
        public UnitOfWork()
        {
            _context = new NeoSysLCS_Dev();
        }

        public UnitOfWork(NeoSysLCS_Dev contextCandidate)
        {
            _context = contextCandidate;
        }

        public NeoSysLCS_Dev Context
        {
            get { return _context; }
        }

        public KontaktRepository KontaktRepository
        {
            get
            {
                return _kontaktRepository ?? (_kontaktRepository = new KontaktRepository(_context));
            }
        }

        public UserRepository UserRepository
        {
            get
            {
                return _userRepository ?? (_userRepository = new UserRepository(_context));
            }
        }

        public KundeRepository KundeRepository
        {
            get
            {
                return _kundeRepository ?? (_kundeRepository = new KundeRepository(_context));
            }
        }

        public OfferRepository OfferRepository
        {
            get
            {
                return _offerRepository ?? ( _offerRepository = new OfferRepository(_context));
            }
        }

        public StandortRepository StandortRepository
        {
            get
            {

                if (_standortRepository == null)
                {
                    _standortRepository = new StandortRepository(_context);
                }
                return _standortRepository;
            }
        }

        public ForderungsversionRepository ForderungsversionRepository
        {
            get
            {
                if (_forderungRepository == null)
                {
                    _forderungRepository = new ForderungsversionRepository(_context);
                }
                return _forderungRepository;
            }
        }

        public GenericRepository<Sprache> SpracheRepository
        {
            get
            {

                if (_spracheRepository == null)
                {
                    _spracheRepository = new GenericRepository<Sprache>(_context);
                }
                return _spracheRepository;
            }
        }

        public ObjektkategorieRepository ObjektkategorieRepository
        {
            get
            {
                if (_objektkategorieRepository == null)
                {
                    _objektkategorieRepository = new ObjektkategorieRepository(_context);
                }
                return _objektkategorieRepository;
            }
        }

        public ObjektRepository ObjektRepository
        {
            get
            {

                if (_objektRepository == null)
                {
                    _objektRepository = new ObjektRepository(_context);
                }
                return _objektRepository;
            }
        }

        public PflichtRepository PflichtRepository
        {
            get
            {

                if (_pflichtRepository == null)
                {
                    _pflichtRepository = new PflichtRepository(_context);
                }
                return _pflichtRepository;
            }
        }

        public ObligationRepository ObligationRepository
        {
            get
            {
                if (_obligationRepository == null)
                {
                    _obligationRepository = new ObligationRepository(_context);
                }

                return _obligationRepository;
            }
        }

        public RechtsbereichRepository RechtsbereichRepository
        {
            get
            {

                if (_rechtsbereichRepository == null)
                {
                    _rechtsbereichRepository = new RechtsbereichRepository(_context);
                }
                return _rechtsbereichRepository;
            }
        }

        public ArtikelRepository ArtikelRepository
        {
            get
            {

                if (_artikelRepository == null)
                {
                    _artikelRepository = new ArtikelRepository(_context);
                }
                return _artikelRepository;
            }
        }

        public HerausgeberRepository HerausgeberRepository
        {
            get
            {

                if (_herausgeberRepository == null)
                {
                    _herausgeberRepository = new HerausgeberRepository(_context);
                }
                return _herausgeberRepository;
            }
        }

        public ErlassRepository ErlassRepository
        {
            get
            {

                if (_erlassRepository == null)
                {
                    _erlassRepository = new ErlassRepository(_context);
                }
                return _erlassRepository;
            }
        }

        public ErlassfassungRepository ErlassfassungRepository
        {
            get
            {

                if (_erlassversionRepository == null)
                {
                    _erlassversionRepository = new ErlassfassungRepository(_context);
                }
                return _erlassversionRepository;
            }
        }

        public ErlasstypRepository ErlasstypRepository
        {
            get
            {

                if (_erlasstypRepository == null)
                {
                    _erlasstypRepository = new ErlasstypRepository(_context);
                }
                return _erlasstypRepository;
            }
        }

        public IndividuelleForderungRepository IndividuelleForderungRepository
        {
            get
            {

                if (_individuelleForderungRepository == null)
                {
                    _individuelleForderungRepository = new IndividuelleForderungRepository(_context);
                }
                return _individuelleForderungRepository;
            }
        }


        public StandortObjektRepository StandortObjektRepository
        {
            get
            {

                if (_standortObjektRepository == null)
                {
                    _standortObjektRepository = new StandortObjektRepository(_context);
                }
                return _standortObjektRepository;
            }
        }

        public KundendokumentRepository KundendokumentRepository
        {
            get
            {

                if (_kundendokumentRepository == null)
                {
                    _kundendokumentRepository = new KundendokumentRepository(_context);
                }
                return _kundendokumentRepository;
            }
        }

        public KundendokumentForderungsversionRepository KundendokumentForderungsversionRepository
        {
            get
            {

                if (_kundendokumentForderungsversionRepository == null)
                {
                    _kundendokumentForderungsversionRepository = new KundendokumentForderungsversionRepository(_context);
                }
                return _kundendokumentForderungsversionRepository;
            }
        }


        public KundendokumentErlassfassungenRepository KundendokumentErlassfassungRepository
        {
            get
            {

                if (_kundendokumentErlassfassungRepository == null)
                {
                    _kundendokumentErlassfassungRepository = new KundendokumentErlassfassungenRepository(_context);
                }
                return _kundendokumentErlassfassungRepository;
            }
        }



        public KundendokumentPflichtRepository KundendokumentPflichtRepository
        {
            get
            {

                if (_kundendokumentPflichtRepository == null)
                {
                    _kundendokumentPflichtRepository = new KundendokumentPflichtRepository(_context);
                }
                return _kundendokumentPflichtRepository;
            }
        }


        public AllgemeineKundeninformationRepository AllgemeineKundeninformationRepository
        {
            get
            {
                if (_allgemeineKundeninformationRepository == null)
                {
                    _allgemeineKundeninformationRepository = new AllgemeineKundeninformationRepository(_context);
                }
                return _allgemeineKundeninformationRepository;
            }
        }

        public GridViewCookieRepository GridViewCookieViewRepository
        {
            get
            {
                if (_GridViewCookieViewRepository == null)
                {
                    _GridViewCookieViewRepository = new GridViewCookieRepository(_context);
                }
                return _GridViewCookieViewRepository;
            }
        }

        public KonzernRepository KonzernRepository
        {
            get
            {
                if (_konzernRepository == null)
                {
                    _konzernRepository = new KonzernRepository(_context);
                }
                return _konzernRepository;
            }
        }

        public UserStandortRoleRepository UserStandortRoleRepository
        {
            get
            {
                if (_userStandortRoleRepository == null)
                {
                    _userStandortRoleRepository = new UserStandortRoleRepository(_context);
                }
                return _userStandortRoleRepository;
            }
        }

        public ShortcutRepository ShortcutRepository
        {
            get
            {
                if (_shortcutRepository == null)
                {
                    _shortcutRepository = new ShortcutRepository(_context);
                }
                return _shortcutRepository;
            }
        }

        public KundendokumentErfuellungRepository KundendokumentErfuellungRepository
        {
            get
            {
                if (_kundendokumentErfuellungRepository == null)
                {
                    _kundendokumentErfuellungRepository = new KundendokumentErfuellungRepository(_context);
                }
                return _kundendokumentErfuellungRepository;
            }
        }

        public FAQRepository FAQRepository
        {
            get
            {
                if (_FAQRepository == null)
                {
                    _FAQRepository = new FAQRepository(_context);
                }
                return _FAQRepository;
            }
        }

        public FAQKategorieRepository FAQKategorieRepository
        {
            get
            {
                if (_FAQKategorieRepository == null)
                {
                    _FAQKategorieRepository = new FAQKategorieRepository(_context);
                }
                return _FAQKategorieRepository;
            }
        }

        public KommentarRepository KommentarRepository
        {
            get
            {
                if (_KommentarRepository == null)
                {
                    _KommentarRepository = new KommentarRepository(_context);
                }
                return _KommentarRepository;
            }
        }

        public LegalComplianceRepository LegalComplianceRepository
        {
            get
            {
                if (_LegalComplianceRepository == null)
                {
                    _LegalComplianceRepository = new LegalComplianceRepository(_context);
                }
                return _LegalComplianceRepository;
            }
        }
        public StandortMenuUebersetzungRepository StandortMenuUebersetzungRepository
        {
            get
            {
                if (_StandortMenuUebersetzungRepository == null)
                {
                    _StandortMenuUebersetzungRepository = new StandortMenuUebersetzungRepository(_context);
                }
                return _StandortMenuUebersetzungRepository;
            }
        }

        public ToDoRepository TodoRepository
        {
            get
            {

                if (_toDoRepository == null)
                {
                    _toDoRepository = new ToDoRepository(_context);
                }
                return _toDoRepository;
            }
        }

        public EvaluationRepository EvaluationRepository
        {
            get
            {
                if (_evaluationRepository == null)
                {
                    _evaluationRepository = new EvaluationRepository(_context);
                }
                return _evaluationRepository;
            }
        }

        public KundeSummaryRepository KundeSummaryRepository
        {
            get
            {
                if (_kundeSummaryRepository == null)
                {
                    _kundeSummaryRepository = new KundeSummaryRepository(_context);
                }
                return _kundeSummaryRepository;
            }
        }

        public CustomerNewsRepository CustomerNewsRepository
        {
            get
            {
                if (_customerNewsRepository == null)
                {
                    _customerNewsRepository = new CustomerNewsRepository(_context);
                }
                return _customerNewsRepository;
            }
        }

        public ConsultationRepository ConsultationRepository
        {
            get
            {
                if (_consultationRepository == null)
                {
                    _consultationRepository = new ConsultationRepository(_context);
                }
                return _consultationRepository;
            }
        }

        public DashboardRepository DashboardRepository
        {
            get
            {
                if (_dashboardRepository == null)
                {
                    _dashboardRepository = new DashboardRepository(_context);
                }
                return _dashboardRepository;
            }
        }

        public NewsletterRepository NewsletterRepository
        {
            get
            {
                if (_newsletterRepository == null)
                {
                    _newsletterRepository = new NewsletterRepository(_context);
                }
                return _newsletterRepository;
            }
        }

        public ChecklistRepository ChecklistRepository
        {
            get
            {

                if (_checklistRepository == null)
                {
                    _checklistRepository = new ChecklistRepository(_context);
                }
                return _checklistRepository;
            }
        }

        public ChecklistQuestionRepository ChecklistQuestionRepository
        {
            get
            {

                if (_checklistQuestionRepository == null)
                {
                    _checklistQuestionRepository = new ChecklistQuestionRepository(_context);
                }

                return _checklistQuestionRepository;
            }
        }

        public ChecklistHeaderRepository ChecklistHeaderRepository
        {
            get
            {

                if (_checklistHeaderRepository == null)
                {
                    _checklistHeaderRepository = new ChecklistHeaderRepository(_context);
                }

                return _checklistHeaderRepository;
            }
        }

        public KundendokumentChecklistRepository kundendokumentChecklistRepository
        {
            get
            {

                if (_kundendokumentChecklistRepository == null)
                {
                    _kundendokumentChecklistRepository = new KundendokumentChecklistRepository(_context);
                }

                return _kundendokumentChecklistRepository;
            }
        }

        public MassnahmeRepository MassnahmeRepository
        {
            get
            {

                if (_massnahmeRepository == null)
                {
                    _massnahmeRepository = new MassnahmeRepository(_context);
                }

                return _massnahmeRepository;
            }
        }

        public KundeCortecRepository KundeCortecRepository
        {
            get
            {

                if (_kundeCortecRepository == null)
                {
                    _kundeCortecRepository = new KundeCortecRepository(_context);
                }

                return _kundeCortecRepository;
            }
        }

        public ToDoTaskRepository TodoTaskRepository
        {
            get
            {

                if (_toDoTaskRepository == null)
                {
                    _toDoTaskRepository = new ToDoTaskRepository(_context);
                }
                return _toDoTaskRepository;
            }
        }

        public PrivacyPolicyRepository PrivacyPolicyRepository
        {
            get
            {
                if (_privacyPolicyRepository == null)
                {
                    _privacyPolicyRepository = new PrivacyPolicyRepository(_context);
                }

                return _privacyPolicyRepository;
            }
        }

        public ApplicationUserPrivacyPolicyRepository ApplicationUserPrivacyPolicyRepository
        {
            get
            {
                if (_applicationUserPrivacyPolicyRepository == null)
                {
                    _applicationUserPrivacyPolicyRepository = new ApplicationUserPrivacyPolicyRepository(_context);
                }

                return _applicationUserPrivacyPolicyRepository;
            }
        }


        public CortecTaskRepository CortecTaskRepository
        {
            get
            {
                if (_cortecTaskRepository == null)
                {
                    _cortecTaskRepository = new CortecTaskRepository(_context);
                }
                return _cortecTaskRepository;
            }
        }

        public StandortBerichtRepository StandortBerichtRepository
        {
            get
            {
                if (_standortBerichtRepository == null)
                {
                    _standortBerichtRepository = new StandortBerichtRepository(_context);
                }
                return _standortBerichtRepository;
            }
        }


        public void Save()
        {
            _context.SaveChanges();
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _context.Dispose();
                }
            }
            _disposed = true;
        }


    }


}