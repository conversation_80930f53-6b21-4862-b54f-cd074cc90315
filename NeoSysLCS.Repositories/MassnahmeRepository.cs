using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Web.Mvc;
using System.Xml.Linq;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Repositories
{
    public class MassnahmeRepository
    {

        public static readonly MassnahmeStatus[] ActiveStatusArray = { MassnahmeStatus.New, MassnahmeStatus.InProgress };
        public static readonly MassnahmeStatus[] InactiveStatusArray = { MassnahmeStatus.Finished };

        private readonly IGenericRepository<Massnahme> _genericMassnahmeRepository;

        private readonly NeoSysLCS_Dev _context;
        private readonly int _currentLang;
        private readonly IUnitOfWork _unitOfWork;

        public MassnahmeRepository(NeoSysLCS_Dev context)
        {
            _context = context;
            _unitOfWork = new UnitOfWork();
            _genericMassnahmeRepository = new GenericRepository<Massnahme>(context);

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            var sprache = _context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
            _currentLang = sprache.SpracheID;
        }

        /// <summary>
        /// Gets a enumerable of Massnahmen filtered by the specified filter
        /// </summary>
        /// <param name="filter">The filter.</param>
        /// <param name="orderBy">The order by.</param>
        /// <param name="includeProperties">The include properties.</param>
        /// <returns>Returns the Massnahmen</returns>
        public IEnumerable<Massnahme> Get(Expression<Func<Massnahme, bool>> filter = null, Func<IQueryable<Massnahme>, IOrderedQueryable<Massnahme>> orderBy = null, string includeProperties = "")
        {
            return _genericMassnahmeRepository.Get(filter, orderBy, includeProperties);
        }

        /// <summary>
        /// Gets the Massnahme by id
        /// </summary>
        /// <param name="id">The id.</param>
        /// <returns>Returns theThe Massnahme</returns>
        public Massnahme GetByID(object id)
        {
            return _genericMassnahmeRepository.GetByID(id);
        }

        /// <summary>
        /// Inserts the specified Offer.
        /// </summary>
        /// <param name="entity">The entity.</param>
        public int Insert(MassnahmeViewModel viewModel)
        {
            int? kundenId = _genericMassnahmeRepository.GetKundeIdOfCurrentUser();

            int massnahmenIdsCount = (from x in _context.Massnahme where x.KundeID == kundenId.Value select x.KundeMassnahmeID).Count();

            int massnahmeCount = 0;
            if (massnahmenIdsCount > 0)
            {
                massnahmeCount = (from x in _context.Massnahme where x.KundeID == kundenId.Value select x.KundeMassnahmeID).Max();
            }
            else
            {
                massnahmeCount = 0;
            }

            Massnahme entity = new Massnahme();
            entity.Betreff = viewModel.Betreff;
            entity.Status = viewModel.Status;
            entity.Termin = viewModel.Termin;
            entity.ShortcutID = viewModel.ShortcutID;
            entity.StandortID = viewModel.StandortID;
            entity.KundeID = kundenId;
            entity.KundeMassnahmeID = massnahmeCount + 1;
            entity.ArtikelID = viewModel.AritkelID;
            entity.OriginID = viewModel.OriginID;
            entity.EmailNotification = viewModel.EmailNotification;

            var list = new List<string>
            {
                "Massnahme",
                "Bemerkung",
            };

            var uebersetzung = XMLHelper.CreateNewUebersetzung(list);
            var elements = new Dictionary<string, string>();
            elements.Add("Massnahme", viewModel.MassnahmeText);
            elements.Add("Bemerkung", viewModel.Bemerkung);

            entity.Uebersetzung = XMLHelper.UpdateUebersetzung(uebersetzung, elements, _currentLang);

            var linkElementDe = new Dictionary<string, string>();
            linkElementDe.Add("Link", viewModel.LinkDe);
            entity.Uebersetzung = XMLHelper.UpdateUebersetzung(uebersetzung, linkElementDe, 1);

            var linkElementFr = new Dictionary<string, string>();
            linkElementFr.Add("Link", viewModel.LinkFr);
            entity.Uebersetzung = XMLHelper.UpdateUebersetzung(uebersetzung, linkElementFr, 2);

            var linkElementIt = new Dictionary<string, string>();
            linkElementIt.Add("Link", viewModel.LinkIt);
            entity.Uebersetzung = XMLHelper.UpdateUebersetzung(uebersetzung, linkElementIt, 3);

            var linkElementEn = new Dictionary<string, string>();
            linkElementEn.Add("Link", viewModel.LinkEn);
            entity.Uebersetzung = XMLHelper.UpdateUebersetzung(uebersetzung, linkElementEn, 4);

            _genericMassnahmeRepository.Insert(entity);

            return entity.KundeMassnahmeID;
        }

        /// <summary>
        /// Insert Massnahme model.
        /// </summary>
        /// <param name="model">The model.</param>
        public void InsertModel(Massnahme model)
        {
            _genericMassnahmeRepository.Insert(model);
        }



        /// <summary>
        /// Updates the Massnahme from the specified view model.
        /// </summary>
        /// <param name="viewModel">The view model.</param>
        public void Update(MassnahmeViewModel viewModel)
        {
            Massnahme entity = _genericMassnahmeRepository.GetByID(viewModel.MassnahmeID);

           //entity.Verantwortlich = viewModel.Verantwortlich;
            entity.Status = viewModel.Status;
            entity.Termin = viewModel.Termin;
            //entity.StandortID = viewModel.StandortID;
            entity.ShortcutID = viewModel.ShortcutID;
            entity.BearbeitetAm = viewModel.BearbeitetAm;
            entity.BearbeitetVonID = viewModel.BearbeitetVonID;

            var uebersetzung = XDocument.Parse(entity.Uebersetzung);
            var elements = new Dictionary<string, string>();
            elements.Add("Massnahme", viewModel.MassnahmeText);
            elements.Add("Bemerkung", viewModel.Bemerkung);
            
            entity.Uebersetzung = XMLHelper.UpdateUebersetzung(uebersetzung, elements, _currentLang);

            _genericMassnahmeRepository.Update(entity);
        }

        public void RemoveShortcutRelation(Massnahme entity)
        {
            entity.Shortcut = null;
            _genericMassnahmeRepository.Update(entity);
        }

        //public void SetReminderSentToTrue(Massnahme massnahme)
        //{
        //    entity.ReminderSent = true;
        //    _genericMassnahmeRepository.Update(entity);
        //}

        /// <summary>
        /// Deletes the specified entity.
        /// </summary>
        /// <param name="entityToDelete">The entity to delete.</param>
        public void Delete(Massnahme entityToDelete)
        {
            _genericMassnahmeRepository.Delete(entityToDelete);
        }

        /// <summary>
        /// Gets all offer view models.
        /// </summary>
        /// <returns>Returns the view models</returns>
        public IQueryable<MassnahmeViewModel> GetMassnahmeViewModels(List<int> shortcutsIds = null, MassnahmeStatus[] statuses = null)
        {
            if (statuses == null)
            {
                statuses = ActiveStatusArray;
            }

            int? kundenId = _genericMassnahmeRepository.GetKundeIdOfCurrentUser();

            List<MassnahmeViewModel> massnahmen = new List<MassnahmeViewModel>();

            if (!shortcutsIds.Any())
            {
                massnahmen = (from x in _context.Massnahme
                              where statuses.Contains(x.Status) && x.KundeID == kundenId
                              select new MassnahmeViewModel()
                              {
                                  MassnahmeID = x.MassnahmeID,
                                  Betreff = x.Betreff,
                                  Uebersetzung = x.Uebersetzung,
                                  KundeMassnahmeID = x.KundeMassnahmeID,
                                  Status = x.Status,
                                  Termin = x.Termin,
                                  StandortID = x.StandortID,
                                  StandortName = (x.Standort != null) ? (x.Standort.Name) : null,
                                  ShortcutID = (x.Shortcut != null) ? (x.ShortcutID.HasValue ? x.ShortcutID.Value : 1) : 1,
                                  Shortcut = (x.Shortcut != null) ? (x.Shortcut.Name) : "",
                                  ErstelltAm = x.ErstelltAm,
                                  ErstelltVonID = x.ErstelltVonID,
                                  BearbeitetAm = x.BearbeitetAm,
                                  BearbeitetVonID = x.BearbeitetVonID,
                                  BearbeitetVon = (x.BearbeitetVon != null)
                                      ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname)
                                      : null,
                                  ErstelltVon = (x.ErstelltVon != null)
                                      ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname)
                                      : null,
                              }).ToList();
            }
            else
            {
                massnahmen = (from x in _context.Massnahme
                              where statuses.Contains(x.Status) && x.KundeID == kundenId && shortcutsIds.Contains(x.ShortcutID ?? 1)
                              select new MassnahmeViewModel()
                              {
                                  MassnahmeID = x.MassnahmeID,
                                  Betreff = x.Betreff,
                                  Uebersetzung = x.Uebersetzung,
                                  KundeMassnahmeID = x.KundeMassnahmeID,
                                  Status = x.Status,
                                  Termin = x.Termin,
                                  StandortID = x.StandortID,
                                  StandortName = (x.Standort != null) ? (x.Standort.Name) : null,
                                  ShortcutID = (x.Shortcut != null) ? (x.ShortcutID.HasValue ? x.ShortcutID.Value : 1) : 1, // Updated line
                                  Shortcut = (x.Shortcut != null) ? (x.Shortcut.Name) : "",
                                  ErstelltAm = x.ErstelltAm,
                                  ErstelltVonID = x.ErstelltVonID,
                                  BearbeitetAm = x.BearbeitetAm,
                                  BearbeitetVonID = x.BearbeitetVonID,
                                  BearbeitetVon = (x.BearbeitetVon != null)
                                      ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname)
                                      : null,
                                  ErstelltVon = (x.ErstelltVon != null)
                                      ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname)
                                      : null,
                              }).ToList();
            }

            foreach (var massnahme in massnahmen)
            {
                XDocument uebersetzung = XDocument.Parse(massnahme.Uebersetzung);
                massnahme.MassnahmeText = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Massnahme", _currentLang);
                massnahme.Bemerkung = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Bemerkung", _currentLang);
                massnahme.Link = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Link", _currentLang);
            }

            return massnahmen.AsQueryable();
        }


        public List<MassnahmeViewModel> GetMassnahmeViewModelsForReminderMail()
        {
            List<MassnahmeViewModel> massnahmen = (from x in _context.Massnahme
                                                   where ActiveStatusArray.Contains(x.Status) && !x.ReminderSent && x.Termin == DateTime.Today
                                                   select new MassnahmeViewModel()
                                                   {
                                                       MassnahmeID = x.MassnahmeID,
                                                       Betreff = x.Betreff,
                                                       Uebersetzung = x.Uebersetzung,
                                                       KundeMassnahmeID = x.KundeMassnahmeID,
                                                       Status = x.Status,
                                                       Termin = x.Termin,
                                                       StandortID = x.StandortID,
                                                       StandortName = (x.Standort != null) ? (x.Standort.Name) : null,
                                                       ShortcutID = (x.Shortcut != null) ? (x.Shortcut.ShortcutID) : 1,
                                                       Shortcut = (x.Shortcut != null) ? (x.Shortcut.Name) : "",
                                                       ErstelltAm = x.ErstelltAm,
                                                       ErstelltVonID = x.ErstelltVonID,
                                                       BearbeitetAm = x.BearbeitetAm,
                                                       BearbeitetVonID = x.BearbeitetVonID,
                                                       BearbeitetVon = (x.BearbeitetVon != null)
                                                           ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname)
                                                           : null,
                                                       ErstelltVon = (x.ErstelltVon != null)
                                                           ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname)
                                                           : null,
                                                       KundeID = x.KundeID.Value,
                                                       EmailNotification = x.EmailNotification
                                                   }).ToList();

            //foreach (var massnahme in massnahmen)
            //{
            //    XDocument uebersetzung = XDocument.Parse(massnahme.Uebersetzung);
            //    massnahme.MassnahmeText = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Massnahme", _currentLang);
            //    massnahme.Bemerkung = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Bemerkung", _currentLang);
            //    massnahme.Link = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Link", _currentLang);
            //}

            return massnahmen;
        }

        /// <summary>
        /// Gets the Massnahme uebersetzungen by id.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns>Returns the uebersetzungen</returns>
        public IQueryable<MassnahmeViewModel> GetMassnamhmeUebersetzungenByMassnahmeId(int id)
        {
            Massnahme massnahme = _genericMassnahmeRepository.GetByID(id);

            List<int> sprachIds = _context.Sprachen.Select(x => x.SpracheID).ToList();
            List<MassnahmeViewModel> uebersetzungen = new List<MassnahmeViewModel>();
            XDocument xml = XDocument.Parse(massnahme.Uebersetzung);

            int standortId = massnahme.StandortID.HasValue ? massnahme.StandortID.Value : 0;

            if (standortId != 0)
            {
                sprachIds = _unitOfWork.StandortRepository.GetByID(standortId).Sprachen.OrderBy(s => s.SpracheID).Select(x => x.SpracheID).ToList();
            }

            foreach (int sprachId in sprachIds)
            {
                MassnahmeViewModel uebersetzung = new MassnahmeViewModel();
                uebersetzung.MassnahmeUebersetzungID = id.ToString() + "_" + sprachId.ToString();
                uebersetzung.MassnahmeText = XMLHelper.GetUebersetzungBySpecificLanguageFromXmlField(xml, "Massnahme", sprachId);
                uebersetzung.Bemerkung = XMLHelper.GetUebersetzungBySpecificLanguageFromXmlField(xml, "Bemerkung", sprachId);
                uebersetzung.Link = XMLHelper.GetUebersetzungFromXmlField(xml, "Link", sprachId);
                uebersetzung.SpracheID = sprachId;
                uebersetzung.MassnahmeID = id;
                uebersetzungen.Add(uebersetzung);
            }

            return uebersetzungen.AsQueryable();
        }

        public MassnahmeViewModel GetMassnahme(int id)
        {
            Massnahme x = _genericMassnahmeRepository.GetByID(id);
            return new MassnahmeViewModel()
            {
                MassnahmeID = x.MassnahmeID,
                Betreff = x.Betreff,
                Uebersetzung = x.Uebersetzung,
                KundeMassnahmeID = x.KundeMassnahmeID,
                Status = x.Status,
                Termin = x.Termin,
                StandortID = x.StandortID,
                StandortName = (x.Standort != null) ? (x.Standort.Name) : null,
                ShortcutID = (x.Shortcut != null) ? (x.Shortcut.ShortcutID) : 1,
                Shortcut = (x.Shortcut != null) ? (x.Shortcut.Name) : "",
                ErstelltAm = x.ErstelltAm,
                ErstelltVonID = x.ErstelltVonID,
                BearbeitetAm = x.BearbeitetAm,
                BearbeitetVonID = x.BearbeitetVonID,
                BearbeitetVon = (x.BearbeitetVon != null)
                    ? (x.BearbeitetVon.Vorname + " " + x.BearbeitetVon.Nachname)
                    : null,
                ErstelltVon = (x.ErstelltVon != null)
                    ? (x.ErstelltVon.Vorname + " " + x.ErstelltVon.Nachname)
                    : null,
                KundeID = x.KundeID.Value
            };
        }


        public void InsertUebersetzungMassnahme(MassnahmeViewModel uebersetzung, int massnahmeID)
        {
            Massnahme massnahme = _genericMassnahmeRepository.GetByID(massnahmeID);

            var xml = XDocument.Parse(massnahme.Uebersetzung);
            Dictionary<string, string> elements = new Dictionary<string, string>();
            elements.Add("Massnahme", uebersetzung.MassnahmeText);
            elements.Add("Bemerkung", uebersetzung.Bemerkung);

            massnahme.Uebersetzung = XMLHelper.UpdateUebersetzung(xml, elements, uebersetzung.SpracheID);

            _genericMassnahmeRepository.Update(massnahme);
        }

        public void SaveMassnahmeShortcut(Int32 shortcutID, Int32 massnahmeID)
        {
            Massnahme massnahme = _genericMassnahmeRepository.Get(m => m.MassnahmeID == massnahmeID, null, "Shortcut").FirstOrDefault();
            massnahme.ShortcutID = shortcutID;

            _genericMassnahmeRepository.Update(massnahme);

        }

        // NEOS-705 Add additional filtering for KundeID for the dashboard otherwise also Massnahmen from other customer will be counted since the OriginID is the same
        public List<MassnahmeViewModel> GetByOriginID(int originID, int kundeId = 0)
        {
            IQueryable<MassnahmeViewModel> massnahmeViewModels = from x in _context.Massnahme
                                                                 where x.OriginID == originID && (kundeId == 0 || x.KundeID == kundeId)
                                                                 select new MassnahmeViewModel
                                                                 {
                                                                     MassnahmeID = x.MassnahmeID,
                                                                     OriginID = x.OriginID,
                                                                     Status = x.Status
                                                                 };
            return massnahmeViewModels.ToList();
        }

        public List<Massnahme> GetByShortcutID(Int32 shortcutID)
        {
            return _genericMassnahmeRepository.Get(m => m.ShortcutID == shortcutID, null, "Shortcut").ToList();
          
        }
    }
}