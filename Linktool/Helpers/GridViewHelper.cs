using System;
using System.Net;
using System.Web.UI.WebControls;
using DevExpress.Utils;
using DevExpress.Web;
using DevExpress.Web.Mvc;
using DevExpress.XtraPrinting;

namespace NeoSysLCS.Linktool.Helpers
{
    /// <summary>
    /// Helper class for general grid view actions
    /// </summary>
    public static class GridViewHelper
    {

        /// <summary>
        /// Applies the default settings to the grid view
        /// </summary>
        /// <param name="settings">The settings.</param>
        /// <param name="showCommandBar">if set to <c>true</c> [show command bar].</param>
        public static void ApplyDefaultSettings(GridViewSettings settings, Boolean showCommandBar = true)
        {
            if (settings != null)
            {

                settings.Width = Unit.Percentage(100);
                settings.Styles.AlternatingRow.Enabled = DefaultBoolean.True;

                //performance settings
                settings.EnablePagingGestures = AutoBoolean.False;
                settings.EnableCallbackAnimation = false;
                settings.SettingsPager.Mode = GridViewPagerMode.ShowPager;

                //Settings that you don't have to click several times to choose a CheckBox
                settings.SettingsEditing.BatchEditSettings.StartEditAction = GridViewBatchStartEditAction.Click;

                //settings.CommandColumn.SelectAllCheckboxMode = GridViewSelectAllCheckBoxMode.AllPages;

                settings.SettingsCommandButton.EditButton.RenderMode = GridCommandButtonRenderMode.Image;
                settings.SettingsCommandButton.EditButton.Image.Url = "/Content/images/edit-26.png";

                settings.SettingsCommandButton.DeleteButton.RenderMode = GridCommandButtonRenderMode.Image;
                settings.SettingsCommandButton.DeleteButton.Image.Url = "/Content/images/delete_sign-26.png";

                settings.SettingsCommandButton.NewButton.RenderMode = GridCommandButtonRenderMode.Image;
                settings.SettingsCommandButton.NewButton.Image.Url = "/Content/images/pencil-26.png";

                settings.SettingsCommandButton.UpdateButton.RenderMode = GridCommandButtonRenderMode.Image;
                settings.SettingsCommandButton.UpdateButton.Image.Url = "/Content/images/save-26.png";

                settings.SettingsCommandButton.CancelButton.RenderMode = GridCommandButtonRenderMode.Image;
                settings.SettingsCommandButton.CancelButton.Image.Url = "/Content/images/eraser-24.png";

                settings.SettingsPager.Visible = true;
                settings.Settings.ShowGroupPanel = true;
                settings.Settings.ShowGroupedColumns = true;
               
                settings.Settings.ShowFilterRow = true;
                settings.Settings.ShowFilterRowMenu = true;
                settings.Settings.ShowFilterBar = GridViewStatusBarMode.Auto;
                settings.Settings.ShowFilterRowMenuLikeItem = true;              

                settings.SettingsBehavior.EnableRowHotTrack = true;
                settings.SettingsBehavior.ConfirmDelete = false;
                settings.Settings.ShowFooter = true;
                settings.SettingsContextMenu.Enabled = false;
                settings.SettingsBehavior.EnableCustomizationWindow = true;

                settings.CommandColumn.ShowClearFilterButton = true;

                settings.SettingsPager.FirstPageButton.Visible = true;
                settings.SettingsPager.LastPageButton.Visible = true;
                settings.SettingsPager.PageSizeItemSettings.Visible = true;
                settings.SettingsPager.PageSizeItemSettings.Items = new string[] { "10", "20", "50", "100", "200", "250" };

                settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Enabled;
                //settings.SettingsPager.PageSizeItemSettings.Visible = true;

                settings.SettingsPager.ShowDefaultImages = true;
                settings.SettingsPager.AllButton.Visible = true;
                settings.SettingsPager.ShowSeparators = true;

                settings.SettingsBehavior.AllowSelectByRowClick = true;
                settings.ClientSideEvents.CallbackError = @"function(s,e){
                    " + "$('#'+ s.name).append('<div class=\"alert alert-danger\"> <a class=\"close\" data-dismiss=\"alert\">×</a>  ' + e.message + '</div>'); e.handled = true;" +
               "}";

                //tooltip
            }
        }

        /// <summary>
        /// Applies the default settings to uebersetzungs grids
        /// </summary>
        /// <param name="settings">The settings.</param>
        public static void ApplyUebersetzungsSettings(GridViewSettings settings)
        {
            settings.SettingsBehavior.AllowSort = false;
            settings.Settings.ShowFilterRow = false;
            settings.Width = Unit.Percentage(100);
            settings.Styles.AlternatingRow.Enabled = DefaultBoolean.True;
            settings.CommandColumn.Visible = true;
            settings.SettingsBehavior.EnableRowHotTrack = true;
            settings.SettingsBehavior.AllowSelectByRowClick = true;
        }

        public static void AddNewestFirstSortorderColumn(GridViewSettings settings, string fieldname)
        {
            settings.Columns.Add(column =>
            {
                column.FieldName = fieldname;
                column.Visible = false;
                column.SortDescending();
            });
        }

        /// <summary>
        /// Sets the validationsettings display on all specified columns
        /// </summary>
        /// <param name="settings">The settings.</param>
        /// <param name="display">The display.</param>
        public static void SetValidationSettingsOnColumns(GridViewSettings settings, Display display)
        {
            foreach (MVCxGridViewColumn column in settings.Columns)
            {
                var prop = (column.PropertiesEdit as EditProperties);
                if (prop != null)
                {
                    prop.ValidationSettings.Display = display;
                }
            }
        }

    }
}