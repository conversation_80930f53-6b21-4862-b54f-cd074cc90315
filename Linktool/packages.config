<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="3.5.0.2" targetFramework="net47" />
  <package id="EntityFramework" version="6.1.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.Identity.Core" version="2.2.1" targetFramework="net462" />
  <package id="Microsoft.AspNet.Identity.EntityFramework" version="2.2.1" targetFramework="net462" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.Razor" version="3.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebPages.Data" version="3.2.3" targetFramework="net452" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net452" />
  <package id="Newtonsoft.Json" version="10.0.3" targetFramework="net47" />
  <package id="WebGrease" version="1.6.0" targetFramework="net462" />
</packages>