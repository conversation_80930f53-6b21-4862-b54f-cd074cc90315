using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Expressions;
using System.Web;
using NeoSysLCS.Linktool.Models;
using NeoSysLCS.Repositories;

namespace NeoSysLCS.Linktool.Repositories
{
    /// <summary>
    /// Generic Repository 
    /// source: http://www.asp.net/mvc/tutorials/getting-started-with-ef-5-using-mvc-4/implementing-the-repository-and-unit-of-work-patterns-in-an-asp-net-mvc-application
    /// </summary>
    /// <typeparam name="TEntity">The type of the entity.</typeparam>
    public class GenericRepository<TEntity> : IGenericRepository<TEntity> where TEntity : class
    {
        internal NeoSysLCS_Dev _context;
        internal DbSet<TEntity> _dbSet;
        private const string LargeDatabaseDataContextKey = "NeoSysLCS_Dev";

        /// <summary>
        /// Constructs a new instance of the generic repository
        /// </summary>
        /// <param name="context">database context</param>
        public GenericRepository(NeoSysLCS_Dev context)
        {
            context.Database.CommandTimeout = 180;
            _context = context;


            _dbSet = context.Set<TEntity>();

        }

        

        /// <summary>
        /// Gets the entities
        /// </summary>
        /// <param name="filter">filter query</param>
        /// <param name="orderBy">order by</param>
        /// <param name="includeProperties">properties to include in query</param>
        /// <returns>Returns the entities</returns>
        public virtual IQueryable<TEntity> Get(
            Expression<Func<TEntity, bool>> filter = null,
            Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>> orderBy = null,
            string includeProperties = "")
        {

            /*get
            {
                if (HttpContext.Current.Items[LargeDatabaseDataContextKey] == null)
                    HttpContext.Current.Items[LargeDatabaseDataContextKey] = new NeoSysLCS_Dev();
                return (NeoSysLCS_Dev)HttpContext.Current.Items[LargeDatabaseDataCont extKey];
            }*/

            IQueryable<TEntity> query = _dbSet;

            //add filter to query
            if (filter != null)
            {
                query = query.Where(filter);
            }

            //add filter for KundeID if needed
            //if (typeof(TEntity).IsSubclassOf(typeof(KundenportalBaseModel)))
            //{
            //    var kundeId = GetKundeIdOfCurrentUser();

            //    if (kundeId != null)
            //    {
            //        _context.Kunden.Where(k => k.KundeID == kundeId);
            //    }
            //}

            //add erstellt von and bearbeitet von name/join on each query
            includeProperties = includeProperties + ",BearbeitetVon,ErstelltVon";

            //add included properties
            foreach (var includeProperty in includeProperties.Split
                (new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
            {
                query = query.Include(includeProperty);
            }

            //add orderby
            if (orderBy != null)
            {
                return orderBy(query).AsQueryable();
            }

            return query.AsQueryable();
        }

        /// <summary>
        /// Gets the entity by id
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>Returns the entity</returns>
        public virtual TEntity GetByID(object id)
        {
            var entity = _dbSet.Find(id);

            //var baseEntity = entity as KundenportalBaseModel;
            //if (baseEntity != null)
            //{
            //    var kundeId = GetKundeIdOfCurrentUser();
            //    if (kundeId != null && baseEntity.KundeID != kundeId)
            //    {
            //        return null;
            //    }

            //}
            return entity;
        }

        /// <summary>
        /// Inserts an Entity
        /// </summary>
        /// <param name="entity"></param>
        public virtual void Insert(TEntity entity)
        {

            //KundenportalBaseModel baseKundenportal = entity as KundenportalBaseModel;
            //if (baseKundenportal != null)
            //{
            //    //test if KundeID is set for the Filtering
            //    if (baseKundenportal.KundeID == null)
            //    {
            //        throw new Exception("KundenId not defined");
            //    }
            //    var kundeId = GetKundeIdOfCurrentUser();
            //    if (kundeId != null && baseKundenportal.KundeID != kundeId)
            //    {
            //        throw new Exception("The current User is not allowed to insert this entity");
            //    }
            //}

            ////set baee model flags
            //BaseModel baseModel = entity as BaseModel;
            //if (baseModel != null)
            //{
            //    baseModel.ErstelltAm = DateTime.Now;
            //    baseModel.ErstelltVonID = HttpContext.Current.User.Identity.GetUserId();
            //}
            _dbSet.Add(entity);
        }

        /// <summary>
        /// Deletes the entity with assigned id
        /// </summary>
        /// <param name="id">id</param>
        public virtual void Delete(object id)
        {

            TEntity entityToDelete = _dbSet.Find(id);
            Delete(entityToDelete);
        }

        /// <summary>
        /// Deletes the range.
        /// </summary>
        /// <param name="entitiesToDelete">The entities to delete.</param>
        public virtual void DeleteRange(IEnumerable<TEntity> entitiesToDelete)
        {
            _dbSet.RemoveRange(entitiesToDelete);
        }

        /// <summary>
        /// Deletes the entity
        /// </summary>
        /// <param name="entityToDelete">entity to delete</param>
        public virtual void Delete(TEntity entityToDelete)
        {
            //var baseEntity = entityToDelete as KundenportalBaseModel;
            //if (baseEntity != null)
            //{
            //    var kundeId = GetKundeIdOfCurrentUser();
            //    if (kundeId != null && baseEntity.KundeID != kundeId)
            //    {
            //        throw new Exception("The current User is not allowed to delete this entity");
            //    }
            //}

            if (_context.Entry(entityToDelete).State == EntityState.Detached)
            {
                _dbSet.Attach(entityToDelete);
            }
            _dbSet.Remove(entityToDelete);
        }

        /// <summary>
        /// Update the entity
        /// </summary>
        /// <param name="entityToUpdate">entity to update</param>
        public virtual void Update(TEntity entityToUpdate)
        {
            //var baseEntity = entityToUpdate as KundenportalBaseModel;
            //if (baseEntity != null)
            //{
            //    var kundeId = GetKundeIdOfCurrentUser();
            //    if (kundeId != null && baseEntity.KundeID != kundeId)
            //    {
            //        throw new Exception("The current User with ID (" +
            //            HttpContext.Current.User.Identity.GetUserId() + ") is not allowed to update this entity");
            //    }

            //}

            _dbSet.Attach(entityToUpdate);
            _context.Entry(entityToUpdate).State = EntityState.Modified;
            _context.SaveChanges();
        }

        /// <summary>
        /// reloads the entity from the database
        /// </summary>
        /// <param name="entityToReload">entity to reload</param>
        public void Reload(TEntity entityToReload)
        {
            _context.Entry(entityToReload).Reload();
        }

        public int? GetKundeIdOfCurrentUser()
        {
            throw new NotImplementedException();
        }
    }

    public class DBEntities
    {
    }
}