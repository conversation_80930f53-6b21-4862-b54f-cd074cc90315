using System.Web.Mvc;
using System.Web.Routing;

namespace NeoSysLCS.Linktool {
    public class RouteConfig {
        public static void RegisterRoutes(RouteCollection routes) {
            routes.IgnoreRoute("{resource}.axd/{*pathInfo}");
            routes.IgnoreRoute("{resource}.ashx/{*pathInfo}");

            routes.MapRoute(
                name: "Default", // Route name
                url: "{controller}/{action}/{id}", // URL with parameters
                defaults: new { controller = "Suvalink", action = "Index", id = UrlParameter.Optional } // Parameter defaults
            );

            routes.MapRoute(
                name: "Redirect Route", // Route name
                url: "{controller}/{action}/{id}", // URL with parameters
                defaults: new { controller = "Suvalink", action = "GetLink", id = "Link" } // Parameter defaults
            );
        }
    }
}