$(function () {
    $('#side-menu').metisMenu();
});

$(function () {
    $("#menu-toggle").click(function (e) {
        e.preventDefault();
        $('#page-wrapper').toggleClass("page-wrapper-toggle");
        $('.navbar-default').toggleClass("hidden");
        $('.slide-handle').toggleClass("fa-angle-double-right");
    });
    
});


function setHeartbeat() {
    setTimeout("heartbeat()", 900000); // every 15 min
}

function heartbeat() {
    $.ajax({
        url: "/Home/KeepSessionAlive",
        headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        },
        cache: false,
        success: function (data) {
            setHeartbeat();
        },
        contentType: "json",
        method: "GET",
        beforeSend: function (xhr) { xhr.setRequestHeader('Cache-Control', 'no-cache, no-store, must-revalidate'); xhr.setRequestHeader('Pragma', 'no-cache'); xhr.setRequestHeader('Expires', '0'); }
    });

}


//Loads the correct sidebar on window load,
//collapses the sidebar on window resize.
// Sets the min-height of #page-wrapper to window size
$(function() {
    $(window).bind("load resize", function() {
        topOffset = 50;
        width = (this.window.innerWidth > 0) ? this.window.innerWidth : this.screen.width;
        if (width < 768) {
            $('div.navbar-collapse').addClass('collapse');
            topOffset = 100; // 2-row-menu
        } else {
            $('div.navbar-collapse').removeClass('collapse');
        }
        height = (this.window.innerHeight > 0) ? this.window.innerHeight : this.screen.height;
        height = height - topOffset;
        if (height < 1) height = 1;
        if (height > topOffset) {
            $("#page-wrapper").css("min-height", (height - 40) + "px"); //minus footer height
        }
    });
});
