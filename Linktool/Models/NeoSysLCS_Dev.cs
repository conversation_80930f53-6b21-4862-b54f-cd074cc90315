using System;
using System.Data.Entity;
using System.Data.Entity.ModelConfiguration.Conventions;
using System.Data.Entity.SqlServer;
using Microsoft.AspNet.Identity.EntityFramework;

namespace NeoSysLCS.Linktool.Models
{
    public partial class NeoSysLCS_Dev : IdentityDbContext
    {
        static NeoSysLCS_Dev()
        {
            Database.SetInitializer<NeoSysLCS_Dev>(null);
        }

        public new class Configuration : DbConfiguration
        {
            public Configuration()
            {

                SetExecutionStrategy("System.Data.SqlClient",() => new SqlAzureExecutionStrategy(1, TimeSpan.FromSeconds(30))); 

                /*var transactionHandler = new CacheTransactionHandler(new InMemoryCache());

                AddInterceptor(transactionHandler);

                var cachingPolicy = new CachingPolicy();

                Loaded +=
                  (sender, args) => args.ReplaceService<DbProviderServices>(
                    (s, _) => new CachingProviderServices(s, transactionHandler,
                      cachingPolicy));
                 */
            }
        }


        public NeoSysLCS_Dev()
            : base("NeoSysLCS_Dev")
        {
            
        
        /*Configuration.AutoDetectChangesEnabled = false;
        Configuration.LazyLoadingEnabled = false;
        Configuration.ProxyCreationEnabled = false;*/
        //Configuration.EnsureTransactionsForFunctionsAndCommands = true;
    }
        public virtual IDbSet<Erlass> Erlasse { get; set; }
        public virtual IDbSet<Sprache> Sprachen { get; set; }
        public virtual IDbSet<Suvalink> Suvalink { get; set; }


        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {

            base.OnModelCreating(modelBuilder);
            modelBuilder.Conventions.Remove<PluralizingTableNameConvention>();
            modelBuilder.Conventions.Remove<ManyToManyCascadeDeleteConvention>();
            modelBuilder.Conventions.Remove<OneToManyCascadeDeleteConvention>();
        }

        public static NeoSysLCS_Dev Create()
        {
            return new NeoSysLCS_Dev();
        }

    }


}