.dxgvControl_ModernoNeosys,
.dxgvDisabled_ModernoNeosys
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
	background-color: #FFFFFF;
	cursor: default;
}
.dxgvDisabled_ModernoNeosys
{
	color: #A6A6A6;
}
.dxgvControl_ModernoNeosys a
{
	color: #465057;
	text-decoration: none;
}
.dxgvControl_ModernoNeosys a:hover
{
	color: #A6A497;
	text-decoration: none;

}
.dxgvDisabled_ModernoNeosys a
{
	color: #A6A6A6;
}
.dxgvLoadingPanel_ModernoNeosys
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
	background-color: White;
	border: 1px solid #cfcfcf;
	box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.1);
	-webkit-box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.1);
}
.dxgvLoadingPanel_ModernoNeosys td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 15px 34px 15px 20px;
}
.dxgvLoadingPanel_ModernoNeosys .dxlp-loadingImage
{
	background-image: url('../Web/Loading.gif');
	height: 40px;
	width: 40px;
}
.dxgvLoadingPanelStatusBar_ModernoNeosys .dxlp-loadingImage 
{
	background-image: url('gvLoadingOnStatusBar.gif');
	height: 16px;
	width: 16px;
}
.dxgvLoadingPanelStatusBar_ModernoNeosys
{
	background-color: transparent;
}
.dxgvLoadingPanelStatusBar_ModernoNeosys td
{
	white-space: nowrap;
	text-align: center;
	padding: 0px 2px;
}

.dxgvFilterPopupWindow_ModernoNeosys
{
	color: Black;
	border: 1px solid #A3C0E8;
}
.dxgvFilterPopupItemsArea_ModernoNeosys
{
	color: Black;
	background-color: #FFFFFF;
}
.dxgvFilterPopupButtonPanel_ModernoNeosys
{
	background-color: #ECF4FE;
	border: 1px solid #d1d1d1;
	color: #2C4D79;
}

.dxgvFilterPopupItem_ModernoNeosys td.dxgv,
.dxgvFilterPopupActiveItem_ModernoNeosys td.dxgv,
.dxgvFilterPopupSelectedItem_ModernoNeosys td.dxgv
{
	padding: 3px 2px 4px 4px;
	cursor: default;
	white-space: nowrap;
}
.dxgvFilterPopupActiveItem_ModernoNeosys
{
	background-color: #FFE7A2;
	color: Black;
}
.dxgvFilterPopupSelectedItem_ModernoNeosys
{
	color: Black;
	background-color: #FFBD69;
}
.dxgvControl_ModernoNeosys .dxeListBox_ModernoNeosys
{
	background-color: #FFFFFF;
}

.dxgvTable_ModernoNeosys
{
	background-color: #FFFFFF;
	border: Solid 1px #d1d1d1;
    border-top: 0;
    border-bottom: 0;
	border-collapse: separate!important;
	overflow: hidden;
}
.dxgvTable_ModernoNeosys .dxgvHEC
{
	background-color: #FFFFFF;
    border: 0;
	overflow: hidden;
}
div > .dxgvTable_ModernoNeosys
{
    border-right: 0;
    border-left: 0;
}
.dxgvControl_ModernoNeosys .dxgvHSDC
{
    border-right: Solid 1px #d1d1d1;
    border-left: Solid 1px #d1d1d1;
    border-top: Solid 1px #d1d1d1;
}
.dxgvControl_ModernoNeosys .dxgvHSDC .dxgvHeader_ModernoNeosys
{
	border-top: 0!important; 
}
.dxgvControl_ModernoNeosys .dxgvCSD .dxgvHeader_ModernoNeosys
{
    border-top: 0;
}
.dxgvControl_ModernoNeosys .dxgvFSDC
{
    border-right: Solid 1px #d1d1d1;
    border-left: Solid 1px #d1d1d1;
    border-bottom: Solid 1px #d1d1d1;
}
.dxgvControl_ModernoNeosys .dxgvFSDC .dxgvFooter_ModernoNeosys td.dxgv
{
    border-bottom: 0;
}
.dxgvControl_ModernoNeosys .dxgvCSD
{
    border: Solid 1px #d1d1d1;
	box-sizing: content-box;
}
.dxeDropDownWindow_ModernoNeosys .dxgvControl_ModernoNeosys .dxgvCSD,
.dxeDropDownWindow_ModernoNeosys .dxgvControl_ModernoNeosys .dxgvHSDC,
.dxeDropDownWindow_ModernoNeosys .dxgvControl_ModernoNeosys .dxgvFSDC
{
    border: 0;
}
.dxgvControl_ModernoNeosys .dxgvHSDC + .dxgvCSD 
{
    border-top: 0;
}
.dxgvControl_ModernoNeosys .dxgvFCSD
{
    border-right: Solid 1px #d1d1d1;
    border-left: Solid 1px #d1d1d1;
    border-bottom: Solid 1px #d1d1d1;
}

.dxgvDataRowAlt_ModernoNeosys
{
	background-color: #F4F1E3;
}
.dxgvFilterRow_ModernoNeosys
{
}
.dxgvEditForm_ModernoNeosys
{
}
.dxgvEditForm_ModernoNeosys td.dxgv
{
	border-bottom: 1px Solid #d1d1d1;
	padding: 10px 15px;
}
.dxgvEditForm_ModernoNeosys td.dxgvIndentCell
{
	border-top-width: 0px;
    border-right: 1px Solid #d1d1d1;
}
.dxgvSelectedRow_ModernoNeosys
{
	background-color: #f3f3f3;
}
.dxgvFocusedRow_ModernoNeosys
{
	background-color: #e5e5e5;
}
.dxgvSelectedRow_ModernoNeosys td.dxgvCommandColumn_ModernoNeosys a, 
.dxgvFocusedRow_ModernoNeosys td.dxgvCommandColumn_ModernoNeosys a
{
	color: #009C49;
}
.dxgvPreviewRow_ModernoNeosys
{
	background-color: white;
}
.dxgvDetailCell_ModernoNeosys,
.dxgvPreviewRow_ModernoNeosys td.dxgv,
.dxgvEmptyDataRow_ModernoNeosys td.dxgv
{
	padding: 15px 15px 15px 20px;
	border-bottom: 1px Solid #d1d1d1!important;
	border-top-width: 0;
	border-left-width: 0;
	border-right-width: 0;
}
.dxgvPreviewRow_ModernoNeosys td.dxgvIndentCell,
.dxgvDetailRow_ModernoNeosys td.dxgvIndentCell
{
	padding: 8px 10px;
	border-bottom: 1px Solid #d1d1d1;
    border-right: 1px Solid #d1d1d1;
}
.dxGridView_gvDetailExpandedButton_ModernoNeosys,
.dxGridView_gvDetailCollapsedButton_ModernoNeosys
{
    margin: 2px 0 -2px;
}
.dxgvEmptyDataRow_ModernoNeosys
{
	color: #4F4F4F;
}
.dxgvEmptyDataRow_ModernoNeosys td.dxgv
{
	text-align: center;
}

.dxgvEditFormDisplayRow_ModernoNeosys td.dxgv,
.dxgvInlineEditRow_ModernoNeosys td.dxgv,
.dxgvDataRow_ModernoNeosys td.dxgv,
.dxgvDataRowAlt_ModernoNeosys td.dxgv,
.dxgvSelectedRow_ModernoNeosys td.dxgv,
.dxgvFocusedRow_ModernoNeosys td.dxgv
{
	overflow: hidden;
	border-top-width: 0;
	border-left-width: 0;
	border-bottom: 1px Solid #d1d1d1;
	border-right: 1px Solid #d1d1d1;
	padding: 8px 10px 9px;
}
.dxgvEditFormDisplayRow_ModernoNeosys:last-child td.dxgv,
.dxgvInlineEditRow_ModernoNeosys:last-child td.dxgv,
.dxgvDataRow_ModernoNeosys:last-child td.dxgv,
.dxgvDataRowAlt_ModernoNeosys:last-child td.dxgv,
.dxgvSelectedRow_ModernoNeosys:last-child td.dxgv,
.dxgvFocusedRow_ModernoNeosys:last-child td.dxgv,
.dxgvDetailRow_ModernoNeosys:last-child td.dxgv
{
	border-bottom: 1px Solid #d1d1d1!important;
}
.dxgvEditFormDisplayRow_ModernoNeosys:first-child td.dxgv,
.dxgvInlineEditRow_ModernoNeosys:first-child td.dxgv,
.dxgvDataRow_ModernoNeosys:first-child td.dxgv,
.dxgvDataRowAlt_ModernoNeosys:first-child td.dxgv,
.dxgvSelectedRow_ModernoNeosys:first-child td.dxgv,
.dxgvFocusedRow_ModernoNeosys:first-child td.dxgv
{
	border-top: 1px Solid #d1d1d1!important;
}
.dxgvEditFormDisplayRow_ModernoNeosys td.dxgv:last-child,
.dxgvInlineEditRow_ModernoNeosys td.dxgv:last-child,
.dxgvDataRow_ModernoNeosys td.dxgv:last-child,
.dxgvDataRowAlt_ModernoNeosys td.dxgv:last-child,
.dxgvSelectedRow_ModernoNeosys td.dxgv:last-child,
.dxgvFocusedRow_ModernoNeosys td.dxgv:last-child
{
	border-right: 0!important;
}
.dxgvDataRow_ModernoNeosys .dxICheckBox_ModernoNeosys,
.dxgvDataRowAlt_ModernoNeosys .dxICheckBox_ModernoNeosys,
.dxgvInlineEditRow_ModernoNeosys .dxICheckBox_ModernoNeosys,
.dxgvSelectedRow_ModernoNeosys .dxICheckBox_ModernoNeosys,
.dxgvFocusedRow_ModernoNeosys .dxICheckBox_ModernoNeosys,
.dxgvDataRow_ModernoNeosys .dxeIRadioButton_ModernoNeosys,
.dxgvDataRowAlt_ModernoNeosys .dxeIRadioButton_ModernoNeosys,
.dxgvInlineEditRow_ModernoNeosys .dxeIRadioButton_ModernoNeosys,
.dxgvSelectedRow_ModernoNeosys .dxeIRadioButton_ModernoNeosys,
.dxgvFocusedRow_ModernoNeosys .dxeIRadioButton_ModernoNeosys
{
    margin: 4px 1px;
}
.dxgvCommandColumn_ModernoNeosys .dxICheckBox_ModernoNeosys,
.dxgvCommandColumn_ModernoNeosys .dxeIRadioButton_ModernoNeosys
{
    margin: -5px 1px -6px;
}
.dxgvCommandColumn_ModernoNeosys,
.dxgvEditFormDisplayRow_ModernoNeosys td.dxgvCommandColumn_ModernoNeosys,
.dxgvDataRow_ModernoNeosys td.dxgvCommandColumn_ModernoNeosys,
.dxgvDataRowAlt_ModernoNeosys td.dxgvCommandColumn_ModernoNeosys,
.dxgvSelectedRow_ModernoNeosys td.dxgvCommandColumn_ModernoNeosys,
.dxgvFocusedRow_ModernoNeosys td.dxgvCommandColumn_ModernoNeosys
{
	padding: 6px 10px 9px;
    white-space: nowrap;
}
.dxgvEditFormDisplayRow_ModernoNeosys
{
	background-color: #FFFFFF;
}
.dxgvEditFormDisplayRow_ModernoNeosys td.dxgv
{
}
.dxgvEditFormDisplayRow_ModernoNeosys td.dxgvIndentCell
{
	border-right: 1px Solid #d1d1d1;
	border-left: 1px Solid #d1d1d1;
	border-top-width: 0px;
}
.dxgvEditingErrorRow_ModernoNeosys
{
	background-color: #F3D6D6;
	color: #BA1717;
}
.dxgvEditingErrorRow_ModernoNeosys td.dxgv
{
	white-space: pre-wrap;
	border-bottom: 1px Solid #A3C0E8;
	border-right-width: 0;
	border-top-width: 0;
	border-left-width: 0;
	padding: 6px 12px;
}

.dxgvInlineEditRow_ModernoNeosys td.dxgv
{
	padding: 2px 1px;
}
.dxgvFilterRow_ModernoNeosys td.dxgv
{
	border-bottom: 1px Solid #d1d1d1;
	border-right-width: 0px;
	border-top-width: 0;
	border-left-width: 0;
	padding: 2px 1px;
	overflow: hidden;
}
.dxgvInlineEditRow_ModernoNeosys .dxeTextBox_ModernoNeosys td.dxic,
.dxgvFilterRow_ModernoNeosys .dxeTextBox_ModernoNeosys td.dxic
{
	padding: 4px 2px 4px 7px!important;
}
.dxgvInlineEditRow_ModernoNeosys .dxeButtonEdit_ModernoNeosys td.dxic,
.dxgvFilterRow_ModernoNeosys .dxeButtonEdit_ModernoNeosys td.dxic 
{
	padding: 4px 2px 4px 7px!important;
}
.dxgvInlineEditRow_ModernoNeosys .dxeButtonEditButton_ModernoNeosys,
.dxgvFilterRow_ModernoNeosys .dxeButtonEditButton_ModernoNeosys 
{
    padding: 10px 10px;
}
.dxgvInlineEditRow_ModernoNeosys .dxeSpinIncButton_ModernoNeosys, 
.dxgvInlineEditRow_ModernoNeosys .dxeSpinDecButton_ModernoNeosys,
.dxgvFilterRow_ModernoNeosys .dxeSpinIncButton_ModernoNeosys, 
.dxgvFilterRow_ModernoNeosys .dxeSpinDecButton_ModernoNeosys
{
    padding: 4px 10px;
}
.dxgvInlineEditRow_ModernoNeosys .dxeSpinLargeIncButton_ModernoNeosys,
.dxgvInlineEditRow_ModernoNeosys .dxeSpinLargeDecButton_ModernoNeosys,
.dxgvFilterRow_ModernoNeosys .dxeSpinLargeIncButton_ModernoNeosys, 
.dxgvFilterRow_ModernoNeosys .dxeSpinLargeDecButton_ModernoNeosys
{
    padding: 9px 10px;
}
.dxgvGroupRow_ModernoNeosys
{
    color: #7e7e7e;
}
.dxgvFocusedGroupRow_ModernoNeosys
{
	background-color: #e5e5e5;
}
.dxgvGroupRow_ModernoNeosys td.dxgv,
.dxgvFocusedGroupRow_ModernoNeosys td.dxgv
{
	border: 0;
    border-bottom: 1px Solid #d1d1d1;
	vertical-align: middle;
	white-space: nowrap;
	padding: 8px 10px 9px;
}
.dxgvGroupRow_ModernoNeosys:last-child td.dxgv,
.dxgvFocusedGroupRow_ModernoNeosys:last-child td.dxgv
{
    border-bottom: 1px Solid #d1d1d1!important;
}
.dxGridView_gvExpandedButton_ModernoNeosys,
.dxGridView_gvCollapsedButton_ModernoNeosys 
{
    margin: 2px 0 -2px;
}
.dxgvFocusedGroupRow_ModernoNeosys td.dxgvIndentCell,
.dxgvFocusedRow_ModernoNeosys td.dxgvIndentCell,
.dxgvSelectedRow_ModernoNeosys td.dxgvIndentCell
{
	border-top-width: 0px;
	border-right: 1px Solid #d1d1d1;
}

.dxgvHeaderPanel_ModernoNeosys
{
	color: #979797;
	white-space: nowrap;
	border-top: 1px Solid #d1d1d1;
    border-bottom: 1px Solid #d1d1d1;
	padding: 8px 4px 7px 6px;
}

.dxgvMSDraggable .dxgvHeader_ModernoNeosys,
.dxgvMSDraggable.dxgvGroupPanel_ModernoNeosys
{
    -ms-touch-action: pinch-zoom;
}
.dxgvHeader_ModernoNeosys
{
	cursor: pointer;
	white-space: nowrap;
	padding: 7px 10px 6px;
	border: 1px Solid #d1d1d1;
	background: #e3e3e3;
	overflow: hidden;
	font-weight: normal;
	text-align: left;
}
tr:first-child > .dxgvHeader_ModernoNeosys
{
	border-top-width: 1px!important; 
}
.dxgvHeader_ModernoNeosys a,
.dxgvHeader_ModernoNeosys a:hover
{
	color: #a6a497;
}

.dxgvHeader_ModernoNeosys,
.dxgvHeader_ModernoNeosys table
{
    color: #7e7e7e;
}
.dxgvHeader_ModernoNeosys td
{
	white-space: nowrap;
}
.dxgvCustomization_ModernoNeosys, 
.dxgvPopupEditForm_ModernoNeosys
{
	padding: 0;
	margin: 0;
}
.dxgvGroupPanel_ModernoNeosys
{
	color: #979797;
	white-space: nowrap;
	border-top: 1px Solid #d1d1d1;
	padding: 16px 4px 16px 6px;
}
.dxgvFooter_ModernoNeosys
{
	background-color: #fafafa;
	white-space: nowrap;
}
.dxgvFooter_ModernoNeosys td.dxgv
{
	padding: 12px 10px;
	border-right-width: 0px;
	border-bottom: 1px Solid #d1d1d1!important;
}
.dxgvFSDC .dxgvFooter_ModernoNeosys td.dxgv,
.dxgvCSD .dxgvFooter_ModernoNeosys td.dxgv
{
	border-bottom: 0!important;
}
.dxgvGroupFooter_ModernoNeosys
{
	white-space: nowrap;
}
.dxgvGroupFooter_ModernoNeosys td.dxgv
{
	padding: 8px 10px 9px;
	border-bottom: 1px Solid #d1d1d1;
	border-right-width: 0px;
}

.dxgvInlineEditRow_ModernoNeosys td.dxgvIndentCell,
.dxgvDataRow_ModernoNeosys td.dxgvIndentCell,
.dxgvGroupRow_ModernoNeosys td.dxgvIndentCell,
.dxgvGroupFooter_ModernoNeosys td.dxgvIndentCell
{
	border-top-width: 0px;
	border-right: 1px Solid #d1d1d1;
}

.dxgvTitlePanel_ModernoNeosys,
.dxgvTable_ModernoNeosys caption
{
	font-weight: normal;
    padding: 10px 0px 10px;
	text-align: center;
}
.dxgvLoadingDiv_ModernoNeosys
{
	background-color: Gray;
	opacity: 0.01;
	filter: progid:DXImageTransform.Microsoft.Alpha(Style=0, Opacity=1);
}
.dxgvPagerBottomPanel_ModernoNeosys + .dxgvStatusBar_ModernoNeosys
{
	border-top: 1px Solid #d1d1d1;
}
.dxgvStatusBar_ModernoNeosys tr.dxgv
{
	height: 20px;
}
.dxgvStatusBar_ModernoNeosys tr.dxgv > td
{
	padding: 13px 5px;
}
.dxgvCommandColumn_ModernoNeosys a
{
	margin: 0px 5px 0px 0px;
	vertical-align: middle;
}
.dxgvCommandColumnItem_ModernoNeosys
{
}
.dxgvEditFormTable_ModernoNeosys
{
	margin: 10px 0px;
	padding: 0px;
}
.dxgvEditFormCaption_ModernoNeosys
{
	padding: 4px 4px 4px 8px;
	white-space: nowrap;
}

.dxgvInlineEditCell_ModernoNeosys
{
	padding: 4px;
}
.dxgvEditFormCell_ModernoNeosys
{
	padding: 4px;
	border-width: 0;
}

.dxgvFilterBar_ModernoNeosys
{
	border-top: 1px solid #d1d1d1;
}
.dxgvFilterBar_ModernoNeosys a
{
	color: #009C49;
	text-decoration: underline;
}
.dxgvFilterBar_ModernoNeosys a:hover
{
	color: #2b2b2b;
}
.dxgvFilterBarCheckBoxCell_ModernoNeosys
{
	padding: 0 7px 0 3px;
}
.dxgvFilterBarImageCell_ModernoNeosys
{
	padding: 0 1px 0 3px;
	cursor: pointer;
}
.dxgvFilterBarExpressionCell_ModernoNeosys
{
	padding: 8px 5px 10px 0;
	white-space: nowrap;
}
.dxgvFilterBarClearButtonCell_ModernoNeosys
{
	padding: 5px 6px 8px;
}
.dxgvFilterBuilderMainArea_ModernoNeosys
{
	background: white none;
	padding: 6px 2px;
} 
.dxgvFilterBuilderButtonArea_ModernoNeosys
{
	background-color: white;
	border-top: 1px solid #d1d1d1;
	padding: 12px;
	white-space: nowrap;
}

.dxgvPagerBottomPanel_ModernoNeosys, 
.dxgvPagerTopPanel_ModernoNeosys
{
	padding: 4px 0;
}

.dxgvDataRowHover_ModernoNeosys
{
	background: #009C49;
	color: #141414;
}
.dxgvDataRowHover_ModernoNeosys a,
.dxgvDataRowHover_ModernoNeosys td.dxgvCommandColumn_ModernoNeosys a
{
	color: white;
}

.dxgvControl_ModernoNeosys .dxpLite_ModernoNeosys
{
    padding: 6px 6px 6px 0;
}

.dxgvControl_ModernoNeosys .dxgvHFSAC
{
    padding: 10px 0 0 9px;
}
.dxgvControl_ModernoNeosys .dxgvHFSC
{
    padding: 10px 0 6px;
}
.dxgvControl_ModernoNeosys .dxgvHFSC div
{
    height: 1px;
    background: #ececec;
}
.dxgvControl_ModernoNeosys div[id$='DXEPLPC']
{
    height: 60px;
}

/* Removes flicking in iOS Safari*/
.dxgvTable_ModernoNeosys
{
	-webkit-tap-highlight-color: rgba(0,0,0,0);
}

.dxgvControl_ModernoNeosys td.dxgvBatchEditCell_ModernoNeosys
{
    padding: 0;
}
.dxgvControl_ModernoNeosys td.dxgvBatchEditModifiedCell_ModernoNeosys
{
    background: #d7f9c7!important;
}
.dxgvControl_ModernoNeosys .dxgvErrorCell
{
    padding-left: 5px;
    width: 1px;
}
.dxgvControl_ModernoNeosys .dxgvErrorCell img
{
    margin: -2px 0;
}
.dxgvStatusBar_ModernoNeosys .dxgvCommandColumn_ModernoNeosys a
{
    margin-right: 10px;
}

.dxgvControl_ModernoNeosys td.dxgvBatchEditCell_ModernoNeosys,
.dxgvControl_ModernoNeosys td.dxgvBatchEditModifiedCell_ModernoNeosys
{
    border-bottom: 0!important;
}

.dxmLite_ModernoNeosys.dxm-ltr .dxgvContextMenu_ModernoNeosys.dxm-popup .dxm-content {
    padding-top: 4px;
    padding-bottom: 4px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxgvContextMenu_ModernoNeosys.dxm-popup .dxm-image,
.dxgvContextMenu_ModernoNeosys.dxm-popup .dxm-image {
	margin-top: -2px;
    margin-bottom: -3px;
	margin-left: -1px;
    border: 1px solid transparent;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxgvContextMenu_ModernoNeosys.dxm-popup .dxm-image.dxgvcm-chk,
.dxgvContextMenu_ModernoNeosys.dxm-popup .dxm-image.dxgvcm-chk {
	background-color: #dcdcdc;
	border-color: #c2c2c2;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxgvContextMenu_ModernoNeosys.dxm-popup .dxm-image {
	margin-right: 11px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxgvContextMenu_ModernoNeosys.dxm-popup .dxm-image.dxWeb_mSubMenuItem_ModernoNeosys {
	margin-left: 1px;
	margin-right: 13px;
}
.dxmLite_ModernoNeosys.dxm-ltr .dxgvContextMenu_ModernoNeosys.dxm-popup .dxm-image.dxWeb_mSubMenuItemChecked_ModernoNeosys {
	margin-left: -1px;
	margin-right: 11px;
}
