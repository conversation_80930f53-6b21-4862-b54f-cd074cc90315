/* ----------------- Main ----------------- */
/* Loading panel */
.dxheLoadingDiv_ModernoNeosys
{
    background: white;
    opacity: 0.85;
    filter: alpha(opacity=85);
}
.dxheLoadingPanel_ModernoNeosys
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
	background-color: White;
	border: 1px solid #cfcfcf;
	box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.1);
	-webkit-box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.1);
}
.dxheLoadingPanel_ModernoNeosys td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 15px 34px 15px 20px;
}
.dxheLoadingPanel_ModernoNeosys .dxlp-loadingImage
{
	background-image: url('../Web/Loading.gif');
	height: 40px;
	width: 40px;
}

.dxheControl_ModernoNeosys
{
    border: Solid 1px #d1d1d1;
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
}

/* Area */
.dxheContentArea_ModernoNeosys
{
    padding: 0;
}
.dxheErrorFrame_ModernoNeosys
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
    color: #D00707;
    background-color: #FBC7C7;
    border-bottom: solid 1px #DEC0C0;
}
.dxheErrorFrame_ModernoNeosys .dxhe 
{
    padding: 5px;
}
.dxheErrorFrame_ModernoNeosys .dxhe td 
{
    padding: 0;
}
.dxheErrorFrame_ModernoNeosys .dxheErrorFrameCloseButton_ModernoNeosys 
{
    cursor: pointer;
}
.dxheViewArea_ModernoNeosys,
.dxheViewArea_ModernoNeosys.dxeMemo_ModernoNeosys
{
    border: 0;
}
.dxheContentArea_ModernoNeosys td.dxheViewArea_ModernoNeosys 
{
    line-height: 0;
}
.dxheHtmlViewEdit_ModernoNeosys,
.dxheDesignViewArea_ModernoNeosys,
.dxhePreviewArea_ModernoNeosys 
{
    margin: 0px;
    background-color: #FFFFFF;
    background-image: none;
	font: 12px Arial, Helvetica, sans-serif;
	color: #2B2B2B;
}
.dxheContentArea_ModernoNeosys .dxeMemo_ModernoNeosys,
.dxheContentArea_ModernoNeosys .dxeMemoEditArea_ModernoNeosys
{
    background-color: white;
}
td.dxheHtmlViewEdit_ModernoNeosys,
body.dxheDesignViewArea_ModernoNeosys,
body.dxhePreviewArea_ModernoNeosys
{
    padding: 4px 4px 0 4px;
}
.dxheControl_ModernoNeosys.dxhe-rtl td.dxheHtmlViewEdit_ModernoNeosys,
.dxheControl_ModernoNeosys.dxhe-rtl .dxeMemo_ModernoNeosys td
{
	padding: 0 4px 0 0;
}
@media screen and (-webkit-min-device-pixel-ratio:0)
{
    .dxheControl_ModernoNeosys.dxhe-rtl td.dxheHtmlViewEdit_ModernoNeosys,
    .dxheControl_ModernoNeosys.dxhe-rtl .dxeMemo_ModernoNeosys td
    {
	    padding: 0 0 0 4px;
    }    
}
.dxheControl_ModernoNeosys .dxeMemo_ModernoNeosys textarea 
{
	resize:none;
}
/* Element appearance in DesignView */
body.dxheDesignViewArea_ModernoNeosys table.dxEmptyBorderTable,
body.dxheDesignViewArea_ModernoNeosys table.dxEmptyBorderTable td {
    border:1px dotted gray;
}
@media print 
{
	body.dxheDesignViewArea_ModernoNeosys table.dxEmptyBorderTable,
	body.dxheDesignViewArea_ModernoNeosys table.dxEmptyBorderTable td {
		border:0px;
	}
}
body.dxheDesignViewArea_ModernoNeosys pre
{
    word-wrap: break-word;
    white-space: pre-wrap;
}
/* Status Bar */
.dxheStatusBar_ModernoNeosys 
{
}
.dxheStatusBarTab_ModernoNeosys 
{    
}
.dxHtmlEditor_heSizeGrip_ModernoNeosys
{
    cursor: se-resize;
}
.dxHtmlEditor_heSizeGripRtl_ModernoNeosys
{
    cursor: ne-resize;
}
.dxheSizeGripContainer_ModernoNeosys 
{
    width: 100%;
    text-align: right;
    font-size: 0;
    margin-top: -18px;
}
.dxheControl_ModernoNeosys.dxhe-rtl .dxheSizeGripContainer_ModernoNeosys
{
	text-align: left;
}
.dxheControl_ModernoNeosys .dxtcLite_ModernoNeosys .dxtc-leftIndent
{
	width: 0!important;
}
.dxheControl_ModernoNeosys .dxtcLite_ModernoNeosys > .dxtc-stripContainer.dxtc-strip
{
	background: #fafafa;
	padding: 0!important;
}
.dxheControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-tab,
.dxheControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-activeTab,
.dxheControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-bottom.dxtc-noSpacing > .dxtc-stripContainer .dxtc-activeTab.dxtc-lead,
.dxheControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-leftIndent,
.dxheControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-spacer,
.dxheControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-rightIndent,
.dxheControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-sbWrapper,
.dxheControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-sbIndent,
.dxheControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-sbSpacer
{
    border-top: 1px solid #d1d1d1!important;
}
.dxheControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-tab
{
}
.dxheControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-tab .dxtc-link 
{
	padding: 7px 15px 5px 16px;
}

.dxheControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-activeTab
{
    border-top: 1px solid transparent!important;
}
.dxheControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-activeTab .dxtc-link 
{
	padding: 7px 14px 5px 15px;
}
.dxheControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-bottom > .dxtc-stripContainer .dxtc-activeTab.dxtc-lead
{
    border-left: 1px solid transparent!important;
}
.dxheControl_ModernoNeosys.dxhe-rtl .dxtcLite_ModernoNeosys.dxtc-bottom .dxtc-stripContainer .dxtc-activeTab.dxtc-lead
{
    border-left: 1px solid #D1D1D1 !important;
    border-right: 1px solid transparent!important;
}
.dxheControl_ModernoNeosys .dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-tab
{
	background: transparent;
}
.dxheControl_ModernoNeosys .dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-tabHover
{
	background: #009C49;
}
.dxheControl_ModernoNeosys .dxtcLite_ModernoNeosys > .dxtc-stripContainer .dxtc-activeTab
{
	background-color: white!important;
	border: 1px solid #d1d1d1;
	border-top: 0!important;
}
.dxheControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-bottom > ul
{
    border: 0!important;
}
/* ----------------- Dialog Forms ----------------- */
.dxpcLite_ModernoNeosys .dxheDlgFooter,
.dxpcLite_ModernoNeosys .dxheCustomDialog_ModernoNeosys .dxhecd-Buttons
{
	background-color: #fafafa;
	border-top: 1px #d1d1d1 solid;
	padding: 16px 11px;
	text-align: right;
}
.dxpcLite_ModernoNeosys.dxRtl .dxheDlgFooter,
.dxpcLite_ModernoNeosys.dxRtl .dxheCustomDialog_ModernoNeosys .dxhecd-Buttons
{
    text-align: left;
}
.dxpcLite_ModernoNeosys .dxheDlgFooterBtn
{
    width: 100px;
    margin-left: 10px;
}
.dxpcLite_ModernoNeosys.dxRtl .dxheDlgFooterBtn
{
    margin-right: 10px;
    margin-left: 0;
}
.dxpcLite_ModernoNeosys .dxheDlgInsertImageForm .dxheDlgImagePreviewCell
{
    color: #737373;
    border: dashed 1px #cac8c8;
}
.dxpcLite_ModernoNeosys .dxheDlgPasteFromWordForm .dxheDlgPasteContainerCell td
{
	border: Solid 1px #d1d1d1;
}
.dxpcLite_ModernoNeosys .dxheDlgInsertImageForm .dxheDlgRadionButtonTable
{
    width: 360px;
}
.dxpcLite_ModernoNeosys .dxheDlgInsertImageForm .dxheDlgFromTheWeb
{
    width: 360px;
}
.dxpcLite_ModernoNeosys .dxheDlgInsertImageForm .dxheDlgWrapTextCell
{
    width: 180px;
}
.dxpcLite_ModernoNeosys .dxheDlgInsertTableForm .dxheDlgCaptionCell,
.dxpcLite_ModernoNeosys .dxheDlgInsertLinkForm .dxheDlgCaptionCell,
.dxpcLite_ModernoNeosys .dxheDlgTableColumnPropertiesForm .dxheDlgCaptionCell
{
    padding-top: 9px;
}
.dxpcLite_ModernoNeosys .dxheDlgInsertImageForm .dxheDlgImagePropertiesForm .dxheDlgImagePropertiesCell .dxeTextBox_ModernoNeosys,
.dxpcLite_ModernoNeosys .dxheDlgInsertImageForm .dxheDlgImagePropertiesForm .dxheDlgImagePropertiesCell .dxeButtonEdit_ModernoNeosys  
{ 
    width: 200px!important; 
}
.dxpcLite_ModernoNeosys .dxheDlgInsertTableForm .dxrpControl_ModernoNeosys > tbody > tr > td > table,
.dxpcLite_ModernoNeosys .dxheDlgTableColumnPropertiesForm .dxrpControl_ModernoNeosys > tbody > tr > td > table
{
    width: 480px!important;
}
.dxpcLite_ModernoNeosys .dxheDlgInsertImageForm .dxheDlgSpinEdit,
.dxpcLite_ModernoNeosys .dxheDlgInsertTableForm .dxheDlgSpinEdit,
.dxpcLite_ModernoNeosys .dxheDlgTableColumnPropertiesForm .dxheDlgSpinEdit
{
    width: 70px!important;
}
.dxpcLite_ModernoNeosys .dxheDlgInsertImageForm .dxheDlgColorEdit,
.dxpcLite_ModernoNeosys .dxheDlgInsertTableForm .dxheDlgColorEdit,
.dxpcLite_ModernoNeosys .dxheDlgTableColumnPropertiesForm .dxheDlgColorEdit
{
    width: 140px!important;
}
.dxpcLite_ModernoNeosys .dxheDlgInsertTableForm .dxheDlgSizeComboBox,
.dxpcLite_ModernoNeosys .dxheDlgTableColumnPropertiesForm .dxheDlgSizeComboBox
{
    width: 170px!important;
}
.dxpcLite_ModernoNeosys .dxheDlgInsertTableForm .dxheDlgSizeTypeComboBox,
.dxpcLite_ModernoNeosys .dxheDlgTableColumnPropertiesForm .dxheDlgSizeTypeComboBox
{
    width: 60px!important;
}
.dxpcLite_ModernoNeosys .dxheDlgInsertTableForm .dxheDlgAlignComboBox,
.dxpcLite_ModernoNeosys .dxheDlgTableColumnPropertiesForm .dxheDlgAlignComboBox
{
    width: 100px!important;
}
.dxpcLite_ModernoNeosys .dxheDlgInsertTableForm .dxheDlgHeaderComboBox,
.dxpcLite_ModernoNeosys .dxheDlgTableColumnPropertiesForm .dxheDlgHeaderComboBox
{
    width: 120px!important;
}
/*----------------- Toolbars -----------------*/
.dxtbSpacing_ModernoNeosys
{
	height: 1px;
}
.dxtbControl_ModernoNeosys 
{
	color: #2B2B2B;
	background-color: #fafafa;
    border-bottom: Solid 1px #d1d1d1;
}
.dxtbControl_ModernoNeosys td.dxmMenu_ModernoNeosys
{
	border: 0px;
}
.dxtbComboBoxMenuItem_ModernoNeosys
{
    padding: 3px 2px;
}
.dxheControl_ModernoNeosys .dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys > .dxtc-stripContainer.dxtc-strip
{
    padding-top: 4px !important;
}
.dxheControl_ModernoNeosys .dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-leftIndent
{
    width: 4px !important;
}
.dxheControl_ModernoNeosys .dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top > .dxtc-stripContainer .dxtc-tab,
.dxheControl_ModernoNeosys .dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top > .dxtc-stripContainer .dxtc-activeTab,
.dxheControl_ModernoNeosys .dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top.dxtc-noSpacing > .dxtc-stripContainer .dxtc-activeTab.dxtc-lead,
.dxheControl_ModernoNeosys .dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top > .dxtc-stripContainer .dxtc-leftIndent,
.dxheControl_ModernoNeosys .dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top > .dxtc-stripContainer .dxtc-spacer,
.dxheControl_ModernoNeosys .dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top > .dxtc-stripContainer .dxtc-rightIndent
{
    border-bottom: 1px solid #d1d1d1!important;
}
.dxheControl_ModernoNeosys .dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top > .dxtc-stripContainer .dxtc-activeTab
{
    border-bottom: 1px solid transparent!important;
    border-top: 1px solid #d1d1d1!important;
}
.dxheControl_ModernoNeosys.dxhe-rtl .dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-stripContainer .dxtc-activeTab.dxtc-lead
{
    border-left: 1px solid #D1D1D1 !important;
    border-right: 1px solid transparent!important;
}
.dxheControl_ModernoNeosys .dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-strip.dxtc-stripContainer .dxtc-tab
{
    border-top: 1px solid transparent!important;
    border-left: 1px solid transparent!important;
    border-right: 1px solid transparent!important;
}
.dxheControl_ModernoNeosys .dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-strip.dxtc-stripContainer .dxtc-tab.dxr-inactiveTab,
.dxheControl_ModernoNeosys .dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-strip.dxtc-stripContainer .dxtc-activeTab.dxr-inactiveTab
{
    border-top: 0 !important;
    border-left: 0 !important;
    border-right: 0 !important;
}
.dxheControl_ModernoNeosys .dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top .dxtc-strip.dxtc-stripContainer .dxtc-spacer
{
    width: 2px;
    margin-left: -1px;
}
.dxheControl_ModernoNeosys .dxrControl_ModernoNeosys .dxtcLite_ModernoNeosys.dxtc-top > ul
{
    border: 0!important;
}
.dxheControl_ModernoNeosys .dxrControl_ModernoNeosys .dxr-minBtn
{
    margin: 1px 5px 0 0;
}
.dxheControl_ModernoNeosys .dxtbControl_ModernoNeosys.dxtbr_ModernoNeosys,
.dxheControl_ModernoNeosys .dxrControl_ModernoNeosys .dxpc-mainDiv.dxr-minPopup
{
    border: 0;
}
.dxheControl_ModernoNeosys .dxrControl_ModernoNeosys .dxr-tabContent
{
    border-width: 0 0 1px 0;
    border-color: #D1D1D1;
}

/* Toolbars Lightweight Mode */
.dxtbControl_ModernoNeosys .dxmLite_ModernoNeosys .dxm-main.dxmtb
{
	padding: 3px 2px;
    border-width: 0px;
}
.dxtbControl_ModernoNeosys .dxmLite_ModernoNeosys .dxmtb.dxm-horizontal .dxm-separator
{
	padding: 1px 3px!important;
}
.dxtbControl_ModernoNeosys .dxmLite_ModernoNeosys .dxm-horizontal.dxmtb .dxtb-comboBoxMenuItem > .dxeButtonEditSys,
.dxtbControl_ModernoNeosys .dxmLite_ModernoNeosys .dxm-horizontal.dxmtb .dxtb-comboBoxMenuItem > .dxeTextBoxSys
{
    margin: 0;
}
.dxmLite_ModernoNeosys .dxhetipControl_ModernoNeosys img
{
	vertical-align: top;
	margin-top: 3px;
}
.dxtbControl_ModernoNeosys .dxm-horizontal.dxmtb .dxm-dropDownMode .dxtcbControl_ModernoNeosys > img
{
    margin-top: -1px;
}
.dxtbControl_ModernoNeosys .dxm-horizontal.dxmtb .dxm-dropDownMode .dxtcbColorDiv_ModernoNeosys
{
    margin-top: -1px;
    margin-bottom: -2px;
}
.dxtbControl_ModernoNeosys .dxmLite_ModernoNeosys .dxm-horizontal.dxmtb .dxm-image-l .dxm-item.dxtb-cddi .dxm-content, 
.dxtbControl_ModernoNeosys .dxmLite_ModernoNeosys .dxm-horizontal.dxmtb .dxm-image-r .dxm-item.dxtb-cddi .dxm-content
{
	padding-top: 2px;
	padding-bottom: 3px;
}

/* ToolbarColorButton */
.dxtcbControl_ModernoNeosys { }
.dxtcbColorDiv_ModernoNeosys
{
	width: 16px;
	height: 4px;
	font-size: 0;
	background-color: Red;
}

/*----------------- RoundPanel -----------------*/
.dxheRP.dxrpControl_ModernoNeosys .dxrpcontent
{
    padding: 9px 10px 10px 10px !important;
}


