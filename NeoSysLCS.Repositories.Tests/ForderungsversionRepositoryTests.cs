using System;
using System.Collections.Generic;
using System.Data.Entity;
using Moq;
using System.Linq;
using NeoSysLCS.DomainModel.Models;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Repositories;

namespace NeoSysLCS.Repositories.Tests
{
    [TestClass()]
    public class ForderungsversionRepositoryTests
    {
        [TestMethod()]
        public void GetForderungsversionenTest()
        {
            var objekt1 = new Objekt { ObjektID = 1 }; // at Standort
            var objekt2 = new Objekt { ObjektID = 2 }; // at Standort
            var objekt3 = new Objekt { ObjektID = 3 }; // not at Standort
            var objekt4 = new Objekt { ObjektID = 4 }; // not at Standort

            var rechtsbereich1 = new Rechtsbereich() { RechtsbereichID = 1 };
            var rechtsbereich2 = new Rechtsbereich() { RechtsbereichID = 2 };
            var rechtsbereich3 = new Rechtsbereich() { RechtsbereichID = 3 };

            var herausgeber1 = new Herausgeber() { HerausgeberID = 1 };
            var herausgeber2 = new Herausgeber() { HerausgeberID = 2 };
            var herausgeber3 = new Herausgeber() { HerausgeberID = 3 };

            var erlass1 = new Erlass() { ErlassID = 1, Herausgeber = herausgeber1 };
            var erlass2 = new Erlass() { ErlassID = 2, Herausgeber = herausgeber2 };
            var erlass3 = new Erlass() { ErlassID = 3, Herausgeber = herausgeber3 };

            var standortObjektData = new List<StandortObjekt>
            {
                new StandortObjekt {StandortObjektID = 1, Objekt = objekt1, StandortID = 1},
                new StandortObjekt {StandortObjektID = 11, Objekt = objekt1, StandortID = 1},
                new StandortObjekt {StandortObjektID = 2, Objekt = objekt2, StandortID = 1},
                new StandortObjekt {StandortObjektID = 3, Objekt = objekt3, StandortID = 2},
                new StandortObjekt {StandortObjektID = 4, Objekt = objekt4, StandortID = 2}
            }.AsQueryable();

            var standortObjekteSet = new Mock<DbSet<StandortObjekt>>();
            standortObjekteSet.As<IQueryable<StandortObjekt>>().Setup(m => m.Provider).Returns(standortObjektData.Provider);
            standortObjekteSet.As<IQueryable<StandortObjekt>>().Setup(m => m.Expression).Returns(standortObjektData.Expression);
            standortObjekteSet.As<IQueryable<StandortObjekt>>().Setup(m => m.ElementType).Returns(standortObjektData.ElementType);
            standortObjekteSet.As<IQueryable<StandortObjekt>>().Setup(m => m.GetEnumerator()).Returns(standortObjektData.GetEnumerator());

            var standortData = new List<Standort>()
            {
                new Standort()
                {
                    StandortID = 1,
                    Name = "Teststandort",
                    Rechtsbereiche = new List<Rechtsbereich>() { rechtsbereich1, rechtsbereich2 },
                    Herausgeber = new List<Herausgeber>() { herausgeber1, herausgeber2 }
                }
            }.AsQueryable();

            var standorteSet = new Mock<DbSet<Standort>>();
            standorteSet.As<IQueryable<Standort>>().Setup(m => m.Provider).Returns(standortData.Provider);
            standorteSet.As<IQueryable<Standort>>().Setup(m => m.Expression).Returns(standortData.Expression);
            standorteSet.As<IQueryable<Standort>>().Setup(m => m.ElementType).Returns(standortData.ElementType);
            standorteSet.As<IQueryable<Standort>>().Setup(m => m.GetEnumerator()).Returns(standortData.GetEnumerator());


            var forderungsversionenData
                = new List<Forderungsversion> 
            {
                new Forderungsversion()
                {
                    // Rechtsbereich doesn't match Standort
                    ForderungsversionID = 1,
                    Inkrafttretung = DateTime.Now.AddDays(-1),
                    Rechtsbereiche = new List<Rechtsbereich>(){ rechtsbereich3 },
                    Erlassfassung = new Erlassfassung( ) { Erlass = erlass1 },
                    Objekte = new List<Objekt> { objekt1 },
                    QsFreigabe = true,
                    Forderung = new Forderung() { ForderungID = 1 } // not relevant but used to assemble viewmodel
                },
                new Forderungsversion()
                {
                    // themenbereich doesn't match Standort
                    ForderungsversionID = 2,
                    Inkrafttretung = DateTime.Now.AddDays(-1),
                    Rechtsbereiche = new List<Rechtsbereich>(){ rechtsbereich1 },
                    Erlassfassung = new Erlassfassung( ) { Erlass = erlass3 },
                    Objekte = new List<Objekt> { objekt1 },
                    QsFreigabe = true,
                    Forderung = new Forderung() { ForderungID = 1 } // not relevant but used to assemble viewmodel
                },
                new Forderungsversion()
                {
                    // not valid anymore
                    ForderungsversionID = 3,
                    Inkrafttretung = DateTime.Now.AddYears(-1),
                    Aufhebung = DateTime.Now.AddDays(-1),
                    Rechtsbereiche = new List<Rechtsbereich>() {rechtsbereich1, rechtsbereich2},
                    Erlassfassung = new Erlassfassung( ) { Erlass = erlass1 },
                    Objekte = new List<Objekt> { objekt1 },
                    QsFreigabe = true,
                    Forderung = new Forderung() { ForderungID = 1 } // not relevant but used to assemble viewmodel
                },

                 new Forderungsversion()
                {
                    // valid till tomorrow
                    ForderungsversionID = 4,
                    Inkrafttretung = DateTime.Now.AddYears(-1),
                    Aufhebung = DateTime.Now.AddDays(1),
                    Rechtsbereiche = new List<Rechtsbereich>() {rechtsbereich1, rechtsbereich2},
                    Erlassfassung = new Erlassfassung( ) { Erlass = erlass1 },
                    Objekte = new List<Objekt> { objekt2 },
                    QsFreigabe = true,
                    Forderung = new Forderung() { ForderungID = 1 } // not relevant but used to assemble viewmodel
                },
                new Forderungsversion()
                {
                    // valid till tomorrow
                    ForderungsversionID = 5,
                    Inkrafttretung = DateTime.Now.AddYears(-1),
                    Aufhebung = DateTime.Now.AddDays(100),
                    Rechtsbereiche = new List<Rechtsbereich>() {rechtsbereich2},
                    Erlassfassung = new Erlassfassung( ) { Erlass = erlass2 },
                    Objekte = new List<Objekt> { objekt1 },
                    QsFreigabe = true,
                    Forderung = new Forderung() { ForderungID = 1 } // not relevant but used to assemble viewmodel
                },
                new Forderungsversion()
                {
                    // valid for the time being
                    ForderungsversionID = 6,
                    Inkrafttretung = DateTime.Now.AddYears(-1),
                    Aufhebung = null,
                    Rechtsbereiche = new List<Rechtsbereich>() {rechtsbereich2},
                    Erlassfassung = new Erlassfassung( ) { Erlass = erlass2 },
                    Objekte = new List<Objekt> { objekt3, objekt2 },
                    QsFreigabe = true,
                    Forderung = new Forderung() { ForderungID = 1 } // not relevant but used to assemble viewmodel
                },
                new Forderungsversion()
                {
                    // not valid, objekt not at standort
                    ForderungsversionID = 6,
                    Inkrafttretung = DateTime.Now.AddYears(-1),
                    Aufhebung = null,
                    Rechtsbereiche = new List<Rechtsbereich>() {rechtsbereich2},
                    Erlassfassung = new Erlassfassung( ) { Erlass = erlass2 },
                    Objekte = new List<Objekt> { objekt3 },
                    QsFreigabe = true,
                    Forderung = new Forderung() { ForderungID = 1 } // not relevant but used to assemble viewmodel
                }, new Forderungsversion()
                {
                    // not valid, quality assurance boolean (qsfreigabe) is false
                    ForderungsversionID = 7,
                    Inkrafttretung = DateTime.Now.AddYears(-1),
                    Aufhebung = null,
                    Rechtsbereiche = new List<Rechtsbereich>() {rechtsbereich2},
                    Erlassfassung = new Erlassfassung( ) { Erlass = erlass2 },
                    Objekte = new List<Objekt> { objekt3, objekt2 },
                    QsFreigabe = false,
                    Forderung = new Forderung() { ForderungID = 1 } // not relevant but used to assemble viewmodel
                },
            }.AsQueryable();

            var forderungsversionenSet = new Mock<DbSet<Forderungsversion>>();
            forderungsversionenSet.As<IQueryable<Forderungsversion>>().Setup(m => m.Provider).Returns(forderungsversionenData.Provider);
            forderungsversionenSet.As<IQueryable<Forderungsversion>>().Setup(m => m.Expression).Returns(forderungsversionenData.Expression);
            forderungsversionenSet.As<IQueryable<Forderungsversion>>().Setup(m => m.ElementType).Returns(forderungsversionenData.ElementType);
            forderungsversionenSet.As<IQueryable<Forderungsversion>>().Setup(m => m.GetEnumerator()).Returns(forderungsversionenData.GetEnumerator());

            var sprachenData = new List<Sprache>
            {
                new Sprache{SpracheID = 1, Name = "Deutsch", Lokalisierung = "de"}
            }.AsQueryable();

            var sprachenSet = new Mock<DbSet<Sprache>>();
            sprachenSet.As<IQueryable<Sprache>>().Setup(m => m.Provider).Returns(sprachenData.Provider);
            sprachenSet.As<IQueryable<Sprache>>().Setup(m => m.Expression).Returns(sprachenData.Expression);
            sprachenSet.As<IQueryable<Sprache>>().Setup(m => m.ElementType).Returns(sprachenData.ElementType);
            sprachenSet.As<IQueryable<Sprache>>().Setup(m => m.GetEnumerator()).Returns(sprachenData.GetEnumerator());

            var mockContext = new Mock<NeoSysLCS_Dev>();
            mockContext.Setup(c => c.Set<Forderungsversion>()).Returns(forderungsversionenSet.Object);
            mockContext.Setup(c => c.Set<Standort>()).Returns(standorteSet.Object);
            mockContext.Setup(c => c.StandortObjekte).Returns(standortObjekteSet.Object);
            mockContext.Setup(c => c.Sprachen).Returns(sprachenSet.Object);

            // actual testing
            var repo = new ForderungsversionRepository(mockContext.Object);
            IEnumerable<KundendokumentForderungsversionViewModel> viewModels = repo.GetAllFeasibleForderungViewModelsForStandort(1);

            // verification
            Assert.AreEqual(3, viewModels.Count());
            Assert.AreEqual(4, viewModels.ElementAt(0).ForderungsversionID);
            Assert.AreEqual(5, viewModels.ElementAt(1).ForderungsversionID);
            Assert.AreEqual(6, viewModels.ElementAt(2).ForderungsversionID);
        }

        [TestMethod()]
        public void GetStandortObjektForderungsversionViewModelsByStandortTest()
        {
            var erlasfassung = new Erlassfassung
            {
                ErlassfassungID = 1,
                ErlassID = 10
            };

            var forderungsversion1 = new Forderungsversion()
            {
                ForderungsversionID = 1,
                Erlassfassung = erlasfassung,
            };

            var forderungsversion2 = new Forderungsversion()
            {
                ForderungsversionID = 2,
                Erlassfassung = erlasfassung,
            };

            var forderungsversion3 = new Forderungsversion()
            {
                ForderungsversionID = 3,
                Erlassfassung = erlasfassung,
            };


            var objekt1 = new Objekt { ObjektID = 1, Forderungsversionen = new List<Forderungsversion> { forderungsversion1, forderungsversion2, forderungsversion3 } };
            var objekt2 = new Objekt { ObjektID = 2, Forderungsversionen = new List<Forderungsversion>() };
            var objekt3 = new Objekt { ObjektID = 3, Forderungsversionen = new List<Forderungsversion> { forderungsversion3 } };
            var objekt4 = new Objekt { ObjektID = 4, Forderungsversionen = new List<Forderungsversion> { forderungsversion1 } };




            var standortObjektData = new List<StandortObjekt>()
            {
                new StandortObjekt()
                {
                    StandortObjektID = 1,
                    StandortID = 1,
                    Objekt = objekt1
                },
                new StandortObjekt()
                {
                    StandortObjektID = 11,
                    StandortID = 1,
                    Objekt = objekt1
                },
                new StandortObjekt()
                {
                    StandortObjektID = 2,
                    StandortID = 1,
                    Objekt = objekt2
                },
                new StandortObjekt()
                {
                    StandortObjektID = 3,
                    StandortID = 1,
                    Objekt = objekt3
                },
                 new StandortObjekt()
                {
                    StandortObjektID = 4,
                    StandortID = 1,
                    Objekt = objekt4
                }
            }.AsQueryable();


            var StandortObjektSet = new Mock<DbSet<StandortObjekt>>();
            StandortObjektSet.As<IQueryable<StandortObjekt>>().Setup(m => m.Provider).Returns(standortObjektData.Provider);
            StandortObjektSet.As<IQueryable<StandortObjekt>>().Setup(m => m.Expression).Returns(standortObjektData.Expression);
            StandortObjektSet.As<IQueryable<StandortObjekt>>().Setup(m => m.ElementType).Returns(standortObjektData.ElementType);
            StandortObjektSet.As<IQueryable<StandortObjekt>>().Setup(m => m.GetEnumerator()).Returns(standortObjektData.GetEnumerator());

            var sprachenData = new List<Sprache>
            {
                new Sprache{SpracheID = 1, Name = "Deutsch", Lokalisierung = "de"}
            }.AsQueryable();

            var sprachenSet = new Mock<DbSet<Sprache>>();
            sprachenSet.As<IQueryable<Sprache>>().Setup(m => m.Provider).Returns(sprachenData.Provider);
            sprachenSet.As<IQueryable<Sprache>>().Setup(m => m.Expression).Returns(sprachenData.Expression);
            sprachenSet.As<IQueryable<Sprache>>().Setup(m => m.ElementType).Returns(sprachenData.ElementType);
            sprachenSet.As<IQueryable<Sprache>>().Setup(m => m.GetEnumerator()).Returns(sprachenData.GetEnumerator());

            var mockContext = new Mock<NeoSysLCS_Dev>();
            mockContext.Setup(c => c.Set<StandortObjekt>()).Returns(StandortObjektSet.Object);
            mockContext.Setup(c => c.Sprachen).Returns(sprachenSet.Object);

            // call source under test
            var repo = new ForderungsversionRepository(mockContext.Object);
            var viewModelsReturned = repo.GetSelectedStandortObjektForderungsversionViewModelsByStandort(1, new HashSet<string> { "1_1", "1_2" });

            var viewModelsReturnedArray = viewModelsReturned.ToArray();


            Assert.AreEqual(5, viewModelsReturned.Count());
            Assert.AreEqual(1, viewModelsReturnedArray[0].StandortObjektID);
            Assert.AreEqual(1, viewModelsReturnedArray[0].ForderungsversionID);
            Assert.AreEqual("Hallo", viewModelsReturnedArray[0].Beschreibung);
            Assert.AreEqual(1, viewModelsReturnedArray[1].StandortObjektID);
            Assert.AreEqual(2, viewModelsReturnedArray[1].ForderungsversionID);
            Assert.AreEqual("KEINE DEUTSCHE ÜBERSETZUNG VORHANDEN!", viewModelsReturnedArray[1].Beschreibung);
            Assert.AreEqual(11, viewModelsReturnedArray[2].StandortObjektID);
            Assert.AreEqual(1, viewModelsReturnedArray[2].ForderungsversionID);
            Assert.AreEqual("Hallo", viewModelsReturnedArray[2].Beschreibung);
            Assert.AreEqual(11, viewModelsReturnedArray[3].StandortObjektID);
            Assert.AreEqual(2, viewModelsReturnedArray[3].ForderungsversionID);
            Assert.AreEqual("KEINE DEUTSCHE ÜBERSETZUNG VORHANDEN!", viewModelsReturnedArray[3].Beschreibung);
            Assert.AreEqual(4, viewModelsReturnedArray[4].StandortObjektID);
            Assert.AreEqual(1, viewModelsReturnedArray[4].ForderungsversionID);
            Assert.AreEqual("Hallo", viewModelsReturnedArray[4].Beschreibung);

        }
    }
}
