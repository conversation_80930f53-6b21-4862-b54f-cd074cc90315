<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Castle.Core" version="4.1.1" targetFramework="net47" />
  <package id="developwithpassion.specifications.fakeiteasy" version="0.6.0" targetFramework="net45" />
  <package id="EntityFramework" version="6.1.3" targetFramework="net47" />
  <package id="FakeItEasy" version="4.0.0" targetFramework="net47" />
  <package id="HtmlAgilityPack" version="1.5.1" targetFramework="net47" />
  <package id="Machine.Fakes" version="2.8.0" targetFramework="net462" />
  <package id="Machine.Fakes.FakeItEasy" version="2.8.0" targetFramework="net462" />
  <package id="Machine.Specifications" version="0.11.0" targetFramework="net451" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.3" targetFramework="net451" />
  <package id="Microsoft.AspNet.Razor" version="3.2.3" targetFramework="net451" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.3" targetFramework="net451" />
  <package id="Microsoft.Web.Infrastructure" version="1.0.0.0" targetFramework="net45" />
  <package id="Newtonsoft.Json" version="10.0.3" targetFramework="net47" />
  <package id="PDFsharp-MigraDoc-gdi" version="1.50.5147" targetFramework="net47" />
  <package id="Postal.Mvc5" version="1.2.0" targetFramework="net45" />
  <package id="RazorEngine" version="3.10.0" targetFramework="net47" />
</packages>