<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AccountController_PwdResetLogin_User_doesn_t_exist_or_Password_is_wrong" xml:space="preserve">
    <value>The user doesn't exist or the password is incorrect.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Alle_Standorte" xml:space="preserve">
    <value>All</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Allgemein_AenderungenSpeichern" xml:space="preserve">
    <value>Save changes</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Allgemein_Beschreibung" xml:space="preserve">
    <value>Description</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Allgemein_CollapseAllRows" xml:space="preserve">
    <value>Collapse all</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Allgemein_DatenGespeichert" xml:space="preserve">
    <value>The data have been saved successfully.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Allgemein_ExpandAllRows" xml:space="preserve">
    <value>Expand all</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Allgemein_hier" xml:space="preserve">
    <value>here</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Allgemein_InternerKommentar" xml:space="preserve">
    <value>Internal commentary</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Allgemein_Neue_Version" xml:space="preserve">
    <value>New version</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Allgemein_Validierung" xml:space="preserve">
    <value>The following validation errors occurred:</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Allgemein_Version_Plural" xml:space="preserve">
    <value>Versions</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Allgemein_Version_Singular" xml:space="preserve">
    <value>Version</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Allgemein_Zuordnungen" xml:space="preserve">
    <value>Assignments</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Allgmein_Einfügen" xml:space="preserve">
    <value>Paste</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Benutzer_bearbeiten" xml:space="preserve">
    <value>Edit user</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Button_Abbrechen" xml:space="preserve">
    <value>Cancel</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Button_Passwort_Reset" xml:space="preserve">
    <value>The password has been reset successfully</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Button_Schliessen" xml:space="preserve">
    <value>Close</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Button_Speichern" xml:space="preserve">
    <value>Save  </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Button_Zuordnung_speichern" xml:space="preserve">
    <value>Save assignment</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="ChangePasswordByAdmin_AdminPW" xml:space="preserve">
    <value>For safety reasons please re-enter your password.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="ChangePasswordByAdmin_Header" xml:space="preserve">
    <value>Change password for</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="ChangePasswordAutomatic" xml:space="preserve">
    <value>Reset password automatically</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="ChangePasswordManually" xml:space="preserve">
    <value>Reset password manually</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="ChangePasswordByAdmin_loginError" xml:space="preserve">
    <value>The password is incorrect.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="ChangePasswordByAdmin_notInRole" xml:space="preserve">
    <value>You are not authorised to change this password.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="ChangePasswordByAdmin_PasswordError" xml:space="preserve">
    <value>The user password could not be changed.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="ChangePasswordByAdmin_UserNew" xml:space="preserve">
    <value>New user password</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="ChangePasswordByAdmin_UserRepeat" xml:space="preserve">
    <value>Repeat new user password</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="ChangePasswordViewModel_ConfirmPassword_DisplayName" xml:space="preserve">
    <value>Confirm new password</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="ChangePasswordViewModel_NewPassword_DisplayName" xml:space="preserve">
    <value>New password</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="ChangePasswordViewModel_NewPassword_ErrorZahl" xml:space="preserve">
    <value>The new password must include at least one number (0-9).</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="ChangePasswordViewModel_NewPassword_ErrorZeichen" xml:space="preserve">
    <value>The new password must include at least 6 characters.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="ChangePasswordViewModel_NewPassword_Grossbuchstaben" xml:space="preserve">
    <value>The new password must include at least one capital letter (A-Z).</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="ChangePasswordViewModel_OldPassword_DisplayName" xml:space="preserve">
    <value>Please enter your old password.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="ChangePasswordView_Button_ChangePassword" xml:space="preserve">
    <value>Edit password</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="ChangePasswordView_Title" xml:space="preserve">
    <value>Edit password</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Chart_Anzahl_Forderungen" xml:space="preserve">
    <value>Number of requirements</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Chart_Keine_Datensaetze" xml:space="preserve">
    <value>No data available to display</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Chart_Label_Erfuellt" xml:space="preserve">
    <value>Fullfilled</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Chart_Label_InAbklaeurung" xml:space="preserve">
    <value>Under assessment</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Chart_Label_NichtErfuellt" xml:space="preserve">
    <value>Not fulfilled</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Chart_Label_NotEdited" xml:space="preserve">
    <value>Not edited</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Column_Kunde" xml:space="preserve">
    <value>Client</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Column_Sprache" xml:space="preserve">
    <value>Language</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Colunm_Name" xml:space="preserve">
    <value>Name</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Confirm_Email" xml:space="preserve">
    <value>Confirm email address</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Cookies_geloescht" xml:space="preserve">
    <value>The cookies were deleted.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Cookies_zuruecksetzen" xml:space="preserve">
    <value>Delete cookies</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="DeleteCookiesByAdmin_Header" xml:space="preserve">
    <value>Delete cookies for</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="DeleteCookiesView_Button_DeleteCookies" xml:space="preserve">
    <value>Delete cookies</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Delete_Filter" xml:space="preserve">
    <value>Remove filters</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Dropdown_Bewilligung" xml:space="preserve">
    <value>Authorisation</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Dropdown_KeineBewilligung" xml:space="preserve">
    <value>Not authorised</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Dropdown_Nachweis" xml:space="preserve">
    <value>Proof</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Dropown_KeinNachweis" xml:space="preserve">
    <value>No proof</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Email_Label_Email" xml:space="preserve">
    <value>E-mail</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Artikel_Plural" xml:space="preserve">
    <value>Articles</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Artikel_Singular" xml:space="preserve">
    <value>Article</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Base_BearbeitetAm" xml:space="preserve">
    <value>Edited on</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Base_BearbeitetVon" xml:space="preserve">
    <value>Edited by</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Base_ErstelltAm" xml:space="preserve">
    <value>Created on</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Base_ErstelltVon" xml:space="preserve">
    <value>Created by</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlassfassung_Beschluss" xml:space="preserve">
    <value>Modification of</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlassfassung_BetroffenKommentar" xml:space="preserve">
    <value>Commentary "Applicable"</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlassfassung_Inkrafttretung" xml:space="preserve">
    <value>Effective</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlassfassung_NichtBetroffenKommentar" xml:space="preserve">
    <value>Commentary "Not applicable"</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlassfassung_PLFreigabe" xml:space="preserve">
    <value>Project manager validation</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlassfassung_Plural" xml:space="preserve">
    <value>Versions of the legislative act</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlassfassung_Quelle" xml:space="preserve">
    <value>Source</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlassfassung_Singular" xml:space="preserve">
    <value>Version  </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlassfassung_Status" xml:space="preserve">
    <value>Status</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlasstyp_Singular" xml:space="preserve">
    <value>Type of legislative act</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_Abkuerzung" xml:space="preserve">
    <value>Abbreviation</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_Anwendungsbereiche" xml:space="preserve">
    <value>Areas of application</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_Aufhebung" xml:space="preserve">
    <value>Repeal</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_Bemerkung" xml:space="preserve">
    <value>Note</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_Durchfuehrung" xml:space="preserve">
    <value>Executed by</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_Erscheinungsort" xml:space="preserve">
    <value>Place of publication</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_ErschienenAm" xml:space="preserve">
    <value>Published on</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_Ersetzt" xml:space="preserve">
    <value>Replaced by</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_GesetzlicheAenderung" xml:space="preserve">
    <value>Last legislative change</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_Kerninhalte" xml:space="preserve">
    <value>Contents</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_NoRelevance" xml:space="preserve">
    <value>No relevant legislative changes</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_Norm" xml:space="preserve">
    <value>The standard is</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_Plural" xml:space="preserve">
    <value>Legislative acts</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_Quelle" xml:space="preserve">
    <value>Source</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_Quellenbezeichnung" xml:space="preserve">
    <value>Source reference</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_Rechtsnormen" xml:space="preserve">
    <value>Other applicable legal standards</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_ReviewDate" xml:space="preserve">
    <value>Date of review</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_Sachgebiete" xml:space="preserve">
    <value>Areas</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_Seit" xml:space="preserve">
    <value>Since</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_Singular" xml:space="preserve">
    <value>Legislative act</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_SrNummer" xml:space="preserve">
    <value>No.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_Stammfassung" xml:space="preserve">
    <value>Original version of the law</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_Titel" xml:space="preserve">
    <value>Title</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Erlass_VerantwortlicheStellen" xml:space="preserve">
    <value>Department responsible</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Forderungsversion_Aufhebung" xml:space="preserve">
    <value>Repeal</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Forderungsversion_Beschreibung" xml:space="preserve">
    <value>Description</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Forderungsversion_Bewilligungspflicht_Kurz" xml:space="preserve">
    <value>Authorisation</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Forderungsversion_Changed" xml:space="preserve">
    <value>edited</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Forderungsversion_Inkrafttretung" xml:space="preserve">
    <value>Coming into force</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Forderungsversion_InternerKommentar" xml:space="preserve">
    <value>Internal commentary</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Forderungsversion_Nachweispflicht_Kurz" xml:space="preserve">
    <value>Proof</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Forderungsversion_QsFreigabe" xml:space="preserve">
    <value>QA validation</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Forderungsversion_Status" xml:space="preserve">
    <value>Status</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Forderungsversion_Version" xml:space="preserve">
    <value>Version</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Forderung_AktuelleHandlungsprio" xml:space="preserve">
    <value>Current priority for action</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Forderung_Anwendungsbereich" xml:space="preserve">
    <value>Area of application</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Forderung_Handlungsprio" xml:space="preserve">
    <value>Priority for action</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Forderung_Plural" xml:space="preserve">
    <value>Requirements</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Forderung_Singular" xml:space="preserve">
    <value>Requirement</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Forderung_StatusReview" xml:space="preserve">
    <value>Status of review</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Herausgeber_Singular" xml:space="preserve">
    <value>Editor</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_IndividuelleForderung_Beschreibung" xml:space="preserve">
    <value>Description</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_IndividuelleForderung_GueltigBis" xml:space="preserve">
    <value>Effective until</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_IndividuelleForderung_GueltigVon" xml:space="preserve">
    <value>Effective from</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_IndividuelleForderung_Kommentar" xml:space="preserve">
    <value>Commentary</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_IndividuelleForderung_Plural" xml:space="preserve">
    <value>Individual requirements</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_IndividuelleForderung_Singular" xml:space="preserve">
    <value>Individual requirement</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_IndividuelleForderung_Standortobjekt" xml:space="preserve">
    <value>Facility / activity</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Kontakt_Plural" xml:space="preserve">
    <value>Contacts</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Kundenbezug_Name" xml:space="preserve">
    <value>Name</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Kundenbezug_Plural" xml:space="preserve">
    <value>Client references</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Kundenbezug_Singular" xml:space="preserve">
    <value>Client reference </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentErlassfassung_Betroffen" xml:space="preserve">
    <value>Applicable</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentErlassfassung_NichtBetroffen" xml:space="preserve">
    <value>Not applicable</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentErlassfassung_QSFreigabe" xml:space="preserve">
    <value>PM validation</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentErlassfassung_Status" xml:space="preserve">
    <value>Status</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentForderungsversion_Ablageort" xml:space="preserve">
    <value>Document location</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentForderungsversion_Erfuellung" xml:space="preserve">
    <value>Fulfilment</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentForderungsversion_Kommentar" xml:space="preserve">
    <value>Commentary</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt" xml:space="preserve">
    <value>Last check on</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentForderungsversion_NaechstePruefungAm" xml:space="preserve">
    <value>Next check on</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentForderungsversion_Pruefmethode" xml:space="preserve">
    <value>Checking method</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentForderungsversion_QsFreigabe" xml:space="preserve">
    <value>PM validation</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentForderungsversion_Relevant" xml:space="preserve">
    <value>Relevant</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentForderungsversion_Spalte1" xml:space="preserve">
    <value>Column 1</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentForderungsversion_Spalte10" xml:space="preserve">
    <value>Column 10</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentForderungsversion_Spalte2" xml:space="preserve">
    <value>Column 2</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentForderungsversion_Spalte3" xml:space="preserve">
    <value>Column 3</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentForderungsversion_Spalte4" xml:space="preserve">
    <value>Column 4</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentForderungsversion_Spalte5" xml:space="preserve">
    <value>Column 5</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentForderungsversion_Spalte6" xml:space="preserve">
    <value>Column 6</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentForderungsversion_Spalte7" xml:space="preserve">
    <value>Column 7</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentForderungsversion_Spalte8" xml:space="preserve">
    <value>Column 8</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentForderungsversion_Spalte9" xml:space="preserve">
    <value>Column 9</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentForderungsversion_Status" xml:space="preserve">
    <value>Status</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentForderungsversion_Verantwortlich" xml:space="preserve">
    <value>Responsible</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentIndividuelleForderung_Ablageort" xml:space="preserve">
    <value>Document location</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentIndividuelleForderung_ErfuelltDurch" xml:space="preserve">
    <value>Fulfilled by</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentIndividuelleForderung_Erfuellung" xml:space="preserve">
    <value>Fulfilment</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentIndividuelleForderung_Erfuellungszeitpunkt" xml:space="preserve">
    <value>Fulfilment on</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentIndividuelleForderung_LetzterPruefungszeitpunkt" xml:space="preserve">
    <value>Last check on</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentIndividuelleForderung_NaechstePruefungAm" xml:space="preserve">
    <value>Next check on</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentIndividuelleForderung_Status" xml:space="preserve">
    <value>Status</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentIndividuelleForderung_Verantwortlich" xml:space="preserve">
    <value>Responsible</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentPflicht_Ablageort" xml:space="preserve">
    <value>Document location</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentPflicht_ErfuelltDurch" xml:space="preserve">
    <value>Fulfilled by</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentPflicht_Erfuellung" xml:space="preserve">
    <value>Fulfilment</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentPflicht_Erfuellungszeitpunkt" xml:space="preserve">
    <value>Fulfilment on</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentPflicht_Kommentar" xml:space="preserve">
    <value>Commentary</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentPflicht_QsFreigabe" xml:space="preserve">
    <value>PM validation</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentPflicht_Relevant" xml:space="preserve">
    <value>Relevant</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentPflicht_Status" xml:space="preserve">
    <value>Status</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_KundendokumentPflicht_Verantwortlich" xml:space="preserve">
    <value>Responsible</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Kundendokument_Freigabe" xml:space="preserve">
    <value>Validation for the client</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Kundendokument_Plural" xml:space="preserve">
    <value>Client documents</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Kundendokument_PublizierenAm" xml:space="preserve">
    <value>Publish on</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Kundendokument_QsFreigabe" xml:space="preserve">
    <value>QA validation</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Kundendokument_QsKommentar" xml:space="preserve">
    <value>Commentary</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Kundendokument_Quentic" xml:space="preserve">
    <value>Quentic_Export</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Kundendokument_Singular" xml:space="preserve">
    <value>Client document</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Kundendokument_Status" xml:space="preserve">
    <value>Status</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Kundendokument_TabRechtsnormen" xml:space="preserve">
    <value>Legal standards</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Kundendokument_TabRechtspflichten" xml:space="preserve">
    <value>Legal obligations</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Kunde_LetzteAktualisierung" xml:space="preserve">
    <value>Last update</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Kunde_Singular" xml:space="preserve">
    <value>Client</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_LockoutEndDate_Singular" xml:space="preserve">
    <value>Active</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Objektkategorie_Name" xml:space="preserve">
    <value>Name</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Objektkategorie_Parent" xml:space="preserve">
    <value>Parent category</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Objektkategorie_Plural" xml:space="preserve">
    <value>Object categories</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Objektkategorie_Singular" xml:space="preserve">
    <value>Object category</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Objekt_Beschreibung" xml:space="preserve">
    <value>Description</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Objekt_Freigabe" xml:space="preserve">
    <value>Validation</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Objekt_FreigabeQS" xml:space="preserve">
    <value>QA validation</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Objekt_Name" xml:space="preserve">
    <value>Name</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Objekt_Plural" xml:space="preserve">
    <value>Objects</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Objekt_Singular" xml:space="preserve">
    <value>Object </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Pflicht_Beschreibung" xml:space="preserve">
    <value>Description</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Pflicht_Erfuellung" xml:space="preserve">
    <value>Fulfilment</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Pflicht_GueltigBis" xml:space="preserve">
    <value>Effective til</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Pflicht_GueltigVon" xml:space="preserve">
    <value>Effective from</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Pflicht_Kommentar" xml:space="preserve">
    <value>Commentary</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Pflicht_Plural" xml:space="preserve">
    <value>Obligations</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Pflicht_QsFreigabe" xml:space="preserve">
    <value>QA validation</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Pflicht_Singular" xml:space="preserve">
    <value>Obligation  </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Rechtsbereich_Plural" xml:space="preserve">
    <value>Legal areas</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Sprache_Singular" xml:space="preserve">
    <value>Language</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_StandortObjekt_Plural" xml:space="preserve">
    <value>Site objects</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_StandortObjekt_Singular" xml:space="preserve">
    <value>Site object </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Standort_Plural" xml:space="preserve">
    <value>Sites</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Standort_Singular" xml:space="preserve">
    <value>Site</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_User_ConfirmPassword" xml:space="preserve">
    <value>Please re-enter password</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_User_Email" xml:space="preserve">
    <value>E-mail</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_User_Nachname" xml:space="preserve">
    <value>Name</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_User_Password" xml:space="preserve">
    <value>Password</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_User_UserName" xml:space="preserve">
    <value>User name</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_User_Vorname" xml:space="preserve">
    <value>First name</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_FAQStatus_Edited" xml:space="preserve">
    <value>Edited</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_FAQStatus_New" xml:space="preserve">
    <value>New</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_FAQStatus_Unchanged" xml:space="preserve">
    <value>Unchanged</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KommentarStatus_Abgeschlossen" xml:space="preserve">
    <value>Finished</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KommentarStatus_FreigabeQS" xml:space="preserve">
    <value>QA validation</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KommentarStatus_InBearbeitung" xml:space="preserve">
    <value>In progress by PM</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KommentarStatus_Neu" xml:space="preserve">
    <value>New</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KommentarStatus_Uebersetzen" xml:space="preserve">
    <value>Translate</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentErfuellung_No" xml:space="preserve">
    <value>No</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentErfuellung_None" xml:space="preserve">
    <value />
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentErfuellung_NotDefined" xml:space="preserve">
    <value />
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentErfuellung_NotEdited" xml:space="preserve">
    <value>Not edited</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentErfuellung_NotExisting" xml:space="preserve">
    <value>not available</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentErfuellung_NotRelevant" xml:space="preserve">
    <value>Not relevant</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentErfuellung_UnderClarification" xml:space="preserve">
    <value>Under assessment</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentErfuellung_Yes" xml:space="preserve">
    <value>Yes</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentItemStatus_Blank" xml:space="preserve">
    <value />
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentItemStatus_Existing" xml:space="preserve">
    <value>Existing</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentItemStatus_New" xml:space="preserve">
    <value>New</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentItemStatus_NewVersion" xml:space="preserve">
    <value>New version</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentItemStatus_OldVersion" xml:space="preserve">
    <value>Old version</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentItemStatus_Removed" xml:space="preserve">
    <value>Removed</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentItemStatus_Replaced" xml:space="preserve">
    <value>Replaced</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentItemStatus_Status" xml:space="preserve">
    <value>Status</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentItemStatus_ToExamine" xml:space="preserve">
    <value>To be checked</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentItemStatus_Unchanged" xml:space="preserve">
    <value>Unchanged</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentStatus_Approved" xml:space="preserve">
    <value>Validated for the client</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentStatus_Created" xml:space="preserve">
    <value>Created</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentStatus_InQs" xml:space="preserve">
    <value>Validated for the QA</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentStatus_QsApproved" xml:space="preserve">
    <value>QA validation granted</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Erlassnummer" xml:space="preserve">
    <value>No. </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Error_Navigation_zurueck" xml:space="preserve">
    <value>Back to the portal</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Export_Popup_1" xml:space="preserve">
    <value>Please note that the document once exported cannot be re-imported.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Export_Popup_2" xml:space="preserve">
    <value>For any questions, please contact your Neosys representative or go to </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="FAQ_Link" xml:space="preserve">
    <value>https://www.neosys.ch/services/compliance-management/lexplus/faq-lexplus/?lang=en</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Fehler_BatchEditDeleteFailed" xml:space="preserve">
    <value>The data could not be deleted.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Fehler_BatchEditInsertUpdateFailed" xml:space="preserve">
    <value>The data could not be saved.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Fehler_EntitaetEnthältNoch" xml:space="preserve">
    <value>This entry still contains «##ENTITYNAME##»</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Fehler_EntitaetHasChildren" xml:space="preserve">
    <value>There are still existing subcategories. </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Fehler_EntitaetIsNotNewest" xml:space="preserve">
    <value>Only the last version can be deleted. </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Fehler_EntitaetWirdNochVerwendet" xml:space="preserve">
    <value>This entry is currently used in «##ENTITYNAME##». </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Fehler_FehlendeWerte" xml:space="preserve">
    <value>Please complete all fields.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Fehler_Import_ErfuellungNotReadable" xml:space="preserve">
    <value>Invalid value "##value##" in the Fulfilment column</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Fehler_Import_ForderungGeneralError" xml:space="preserve">
    <value>The requirement ID ##ID## caused an error: ##error##</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Fehler_Import_ForderungNotFromKunde" xml:space="preserve">
    <value>The requirement ID ##ID## could not be found.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Fehler_Import_ForderungNotInKundendokument" xml:space="preserve">
    <value>The requirement ID ##ID## could not be found in the current client document.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Fehler_Import_IndividuelleForderungGeneralError" xml:space="preserve">
    <value>The individual requirement ID ##ID## caused an error: ##error##</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Fehler_Import_IndividuelleForderungNotFromKunde" xml:space="preserve">
    <value>The individual requirement ID ##ID## could not be found.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Fehler_Import_IndividuelleForderungNotInStandort" xml:space="preserve">
    <value>The individual requirement ID ##ID## could not be found in the current site.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Fehler_Import_NumberOfColumns" xml:space="preserve">
    <value>The number of columns is incorrect.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Fehler_Import_PflichtGeneralError" xml:space="preserve">
    <value>The obligation ID ##ID## caused an error: ##error##</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Fehler_Import_PflichtNotInKundendokument" xml:space="preserve">
    <value>The obligation ID ##ID## could not be found in the current client document.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Fehler_Import_PlichtNotFromKunde" xml:space="preserve">
    <value>The obligation ID ##ID## could not be found.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Fehler_Keine_Uebersetzung" xml:space="preserve">
    <value>---</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Fehler_NeuerAlsInkrafttretungsdatum" xml:space="preserve">
    <value>Must be after the effective date!</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Fehler_NeuerOderGleichBeschlussdatum" xml:space="preserve">
    <value>Must be on or after the resolution date!</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Filter_Standortobjekt" xml:space="preserve">
    <value>Filter for site object</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="General_Error" xml:space="preserve">
    <value>An error occurred</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="HTTP_Error_403" xml:space="preserve">
    <value>HTTP error 403 - Forbidden: Access denied</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="HTTP_Error_404" xml:space="preserve">
    <value>HTTP error 404 - The web page cannot be found. </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="HTTP_Error_500" xml:space="preserve">
    <value>HTTP error 500 - An internal server error occurred. </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="ISL_Link" xml:space="preserve">
    <value>https://www.neosys.ch/dienstleistungen/compliance-management/lexplus/lexplus-helpdesk/</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Kundendokument_Delete_Are_you_Sure" xml:space="preserve">
    <value>Do you really want to delete the client document?</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Kundendokument_importieren" xml:space="preserve">
    <value>Import client document</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Kundendokument_Link_Kundendokument_oeffnen" xml:space="preserve">
    <value>Open legislation document</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Kundendokument_waehlen_fuer_import" xml:space="preserve">
    <value>Please select a client document (*.xlsx) to import:</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="KundeNeosys_delete" xml:space="preserve">
    <value>Neosys cannot be deleted!</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Kunden_Delete_Are_you_Sure" xml:space="preserve">
    <value>This operation will delete all logins, client documents and associated data, are you sure you want to proceed?</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Kunden_Kundenname_erforderlich" xml:space="preserve">
    <value>The client name must be indicated</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="KundeStandort_Kopieren" xml:space="preserve">
    <value>Copy site</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Label_Aktuelles_Kundendokument" xml:space="preserve">
    <value>Current legislation document</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Label_Rollen" xml:space="preserve">
    <value>Roles</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Link_bearbeiten" xml:space="preserve">
    <value>edit</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Login_Begruessung" xml:space="preserve">
    <value>Welcome</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Login_Bitte_Einloggen" xml:space="preserve">
    <value>Please log in</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Login_Error_allgemein" xml:space="preserve">
    <value>Invalid login</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Login_Error_Email" xml:space="preserve">
    <value>Invalid e-mail address</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Login_Error_Passwort" xml:space="preserve">
    <value>Invalid password</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Login_Forgot_Password" xml:space="preserve">
    <value>Forgot password?</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Login_Password_Passwort" xml:space="preserve">
    <value>Password</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Login_RememberMe_Eingeloggt_bleiben" xml:space="preserve">
    <value>Stay logged in?</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Menu_Auswertung" xml:space="preserve">
    <value>Assessment</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Menu_Auswertungen" xml:space="preserve">
    <value>Assessments</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Menu_Benutzerprofil" xml:space="preserve">
    <value>User profile</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Menu_Diagramme" xml:space="preserve">
    <value>Diagrams</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Menu_FAQ" xml:space="preserve">
    <value>FAQ</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Menu_ISL" xml:space="preserve">
    <value>Help desk</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Menu_Masteransicht" xml:space="preserve">
    <value>Master view</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Menu_Support" xml:space="preserve">
    <value>SECURIX Support</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Nachname" xml:space="preserve">
    <value>Name</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Navigation_Weiter" xml:space="preserve">
    <value>Next  </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Navigation_Zurueck" xml:space="preserve">
    <value>Back  </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Neuen_Benutzer_hinzufügen" xml:space="preserve">
    <value>Add new user</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Old_Erlassfassung_Kommentar" xml:space="preserve">
    <value>This commentary is no longer available online.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Password_Reset_Forgot" xml:space="preserve">
    <value>Forgot password?</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Passwort_generiert" xml:space="preserve">
    <value>A new password has been generated and sent to the user.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Passwort_Richtlinie" xml:space="preserve">
    <value>Guideline: The password must at least include 6 characters, 1 capital letter (A-Z) and 1 number (0-9).</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Passwort_wurde_geaendert" xml:space="preserve">
    <value>The password has been changed.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Passwort_wurde_gesetzt" xml:space="preserve">
    <value>The password has been set.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Passwort_wurde_zurueckgesetzt" xml:space="preserve">
    <value>The password has been reset.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Passwort_zuruecksetzen" xml:space="preserve">
    <value>Reset password</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Popup_Ausgewaehlt" xml:space="preserve">
    <value>Selected</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Quentic_Export_AktHandlungsprio" xml:space="preserve">
    <value>Current priority for action</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Quentic_Export_Andwendungsbereich" xml:space="preserve">
    <value>Area of application</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Quentic_Export_ArtNorm" xml:space="preserve">
    <value>Type of standard</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Quentic_Export_Bezeichnung" xml:space="preserve">
    <value>Name</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Quentic_Export_DurchgefuehrtVon" xml:space="preserve">
    <value>Review executed by</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Quentic_Export_Handlungsprio" xml:space="preserve">
    <value>Priority for action</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Quentic_Export_Kurzbezeichnung" xml:space="preserve">
    <value>Abbreviation</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Quentic_Export_Kurzzeichen" xml:space="preserve">
    <value>Legal abbreviation</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Quentic_Export_NoRelevance" xml:space="preserve">
    <value>No relevant changes</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Quentic_Export_Validity" xml:space="preserve">
    <value>Validity</value>
  </data>
  <data name="Quentic_Export_Rechtsebene" xml:space="preserve">
    <value>Legal level</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Quentic_Export_Rechtspflicht" xml:space="preserve">
    <value>Legal obligation</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Quentic_Export_RelevantChange" xml:space="preserve">
    <value>No relevant changes</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Quentic_Export_ReviewBemerkung" xml:space="preserve">
    <value>Review notes</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Quentic_Export_SpezRechtsbezug" xml:space="preserve">
    <value>Specific legal reference</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Quentic_Export_StatusReview" xml:space="preserve">
    <value>Status of review</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="ResetPasswordViewModel_ConfirmEmail_CompareErrorMessage" xml:space="preserve">
    <value>The e-mail addresses do not match.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="SetPasswordViewModel_ConfirmPassword_CompareErrorMessage" xml:space="preserve">
    <value>The passwords do not match.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="SetPasswordViewModel_ConfirmPassword_DisplayName" xml:space="preserve">
    <value>Repeat password</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="SetPasswordViewModel_NewPassword_DisplayName" xml:space="preserve">
    <value>New password</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="SetPasswordViewModel_NewPassword_ErrorMessage" xml:space="preserve">
    <value>The {0} must include at least {2} characters.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="SetPasswordViewModel_ValidEmail_ErrorMessage" xml:space="preserve">
    <value>This e-mail adress is invalid.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Standortverwaltung" xml:space="preserve">
    <value>Site administration</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Titel_Benutzerverwaltung" xml:space="preserve">
    <value>User administration</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Validation_ObjektNameUnique_Error" xml:space="preserve">
    <value>An object with this name already exists.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Validation_ObjektNameUnique_Null" xml:space="preserve">
    <value>The object name must be indicated.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Verwaltung" xml:space="preserve">
    <value>Administration</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Administration_Title" xml:space="preserve">
    <value>Administration</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Titel_Kundendokument_Singular" xml:space="preserve">
    <value>legislation document</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_AGB_Link" xml:space="preserve">
    <value>https://www.neosys.ch/en/general-terms-and-conditions.html</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Artikel_DeleteText" xml:space="preserve">
    <value>The following articles were marked for deletion:</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Auswertungen_Bewilligungspflichtig" xml:space="preserve">
    <value>Requires authorisation</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Auswertungen_Nachweispflichtig" xml:space="preserve">
    <value>Requires proof</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Auswertungen_No_Verantwortlichkeiten" xml:space="preserve">
    <value>No responsibilities have been defined.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Auswertungen_Tab_Verantwortlichkeiten" xml:space="preserve">
    <value>Responsibilities</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Benutzerverwaltung_Inaktive_ausblenden" xml:space="preserve">
    <value>Hide inactive users</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Benutzerverwaltung_Inaktive_einblenden" xml:space="preserve">
    <value>Show inactive users</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Breadcrumb_Home" xml:space="preserve">
    <value>Home</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Breadcrumb_KundendokumenteVonStandort" xml:space="preserve">
    <value>##KUNDE## - Client documents from site ##STANDORT##</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Breadcrumb_Kundenübersicht" xml:space="preserve">
    <value>Client overview</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Breadcrumb_PortalKundenDokument" xml:space="preserve">
    <value>##STANDORT## - legislation document of ##PUBLIZIEREN_AM##</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Breadcrumb_PortalKundenDokumentEmpty" xml:space="preserve">
    <value>##STANDORT## - legislation document  </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Breadcrumb_QsFreigabe" xml:space="preserve">
    <value>QA validation for client document from ##PUBLIZIEREN_AM##</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Breadcrumb_Standorte" xml:space="preserve">
    <value>Sites</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Breadcrumb_StandortObjekte" xml:space="preserve">
    <value>##KUNDE## - Objects from site ##STANDORT##</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Erlasse_DeleteText" xml:space="preserve">
    <value>The following legislative acts have been marked for deletion:</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Erlassfassungen_DeleteText" xml:space="preserve">
    <value>The following legislative act versions have been marked for deletion:</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Erlasstypen_DeleteText" xml:space="preserve">
    <value>The following legislative act types have been marked for deletion:</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Footer_Version" xml:space="preserve">
    <value>Version</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Forderungen_Definition" xml:space="preserve">
    <value>Definition</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Forderungen_DeleteText" xml:space="preserve">
    <value>The following obligations have been marked for deletion:</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Herausgeber_DeleteText" xml:space="preserve">
    <value>The following editors have been marked for deletion:</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Impressum_EntwickeltDurch" xml:space="preserve">
    <value>Developed by</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Impressum_Telefon" xml:space="preserve">
    <value>Telephone</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Impressum_Title" xml:space="preserve">
    <value>Impressum</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_IndividuelleForderungen_DeleteText" xml:space="preserve">
    <value>The following individual requirements have been marked for deletion:</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Konzern_Choose_Standort" xml:space="preserve">
    <value>Please select the requested</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Konzern_Choose_StandortMax" xml:space="preserve">
    <value>(max. 10)</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Konzern_Save" xml:space="preserve">
    <value>Save selection</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Konzern_Title" xml:space="preserve">
    <value>Overview</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Kundenbezuege_DeleteText" xml:space="preserve">
    <value>The following client references have been marked for deletion:</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_KundendokumentForderungen_Dialog" xml:space="preserve">
    <value>Customisation of the view</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_KundendokumentQs_AlreadyApproved" xml:space="preserve">
    <value>The document has already been validated and was published on ##PUBLIZIEREN_AM## .</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_KundendokumentQS_AlreadyQsFreigabe" xml:space="preserve">
    <value>QA validation has already been granted.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_KundendokumentQs_ApprovalNotYetPossible" xml:space="preserve">
    <value>The document can not be published yet, it is still being validated by QA.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_KundendokumentQs_KundendokumentFreigaben" xml:space="preserve">
    <value>Validate client document</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_KundendokumentQs_NoKundendokumentAvailable" xml:space="preserve">
    <value>There are no client documents available for this site.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_KundendokumentQs_QsFreigabe" xml:space="preserve">
    <value>QA validation</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_KundendokumentQs_QSFreigabeNotYetPossible" xml:space="preserve">
    <value>The QA validation can only be granted when all requirements, obligations and legislative act versions have been accepted.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_KundendokumentQs_RemovedForderungen" xml:space="preserve">
    <value>Removed requirements</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_KundendokumentQs_RemovedIndividuelleForderung" xml:space="preserve">
    <value>Removed individual requirements</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_KundendokumentQS_RemovedItems" xml:space="preserve">
    <value>Removed items</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_KundendokumentQs_RemovedPflichten" xml:space="preserve">
    <value>Removed obligations</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_KundendokumentQs_TabErlassfassungen" xml:space="preserve">
    <value>List of laws</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_KundendokumentQs_TabForderungen" xml:space="preserve">
    <value>Requirements</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_KundendokumentQs_TabFreigabe" xml:space="preserve">
    <value>Validation and publication</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_KundendokumentQs_TabPflichten" xml:space="preserve">
    <value>Obligations</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_KundendokumentQs_TabQsFreigabe" xml:space="preserve">
    <value>QA</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_KundendokumentUpdate_Error_InitialKundendokumentAlreadyExists" xml:space="preserve">
    <value>Insertion error: Client document already exists for this site!</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_KundendokumentUpdate_Error_MissingPredecessor" xml:space="preserve">
    <value>Insertion error: There is no validated client document yet for this site!</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_KundendokumentUpdate_Error_WrongPredecessor" xml:space="preserve">
    <value>Insertion error: The version of client document you are trying to update is not the latest version!</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Kundendokument_Button_Aktualisieren" xml:space="preserve">
    <value>Update client document</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Kundendokument_Button_Erstellen" xml:space="preserve">
    <value>Create client document</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Kundendokument_Current" xml:space="preserve">
    <value>Current legislation document </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Kundendokument_ErlassfassungenWaehlen" xml:space="preserve">
    <value>Please select the legislative acts which must contain the commentary "Applicable".</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Kundendokument_Erlassfassung_Kommentar" xml:space="preserve">
    <value>Commentary</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Kundendokument_Erlassfassung_NeosysKommentar" xml:space="preserve">
    <value>Neosys commentary</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Kundendokument_IndividuelleForderungen_Explanation" xml:space="preserve">
    <value>e.g. agreements on objectives, directives, additional requirements associated with validations etc.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Kundendokument_KundendokumentAktualisieren" xml:space="preserve">
    <value>Would you like to update the client document?</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Kundendokument_NochKeinKundendokument" xml:space="preserve">
    <value>No client document exists for this site. </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Kundendokument_Old" xml:space="preserve">
    <value>Archived legislation document</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Kundendokument_QsFreigabeAusstehen" xml:space="preserve">
    <value>The client document has not been validated for the client yet, so it is not possible to update the document.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Kundendokument_Status" xml:space="preserve">
    <value>Status</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Kundendokument_TabErlassfassungen" xml:space="preserve">
    <value>List of laws</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Kundendokument_TabForderungen" xml:space="preserve">
    <value>Requirements</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Kundendokument_TabIndividuelleForderungen" xml:space="preserve">
    <value>Individual requirements  </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Kundendokument_TabPflichten" xml:space="preserve">
    <value>Obligations</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Kundenportal_Kundenbezug" xml:space="preserve">
    <value>Detail </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Label_Logout" xml:space="preserve">
    <value>Logout</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_LegalCompliance_ChooseStandort" xml:space="preserve">
    <value>Selection</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_LegalCompliance_ChooseZeitraum" xml:space="preserve">
    <value>Selection of period</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_LegalCompliance_SaveSelection" xml:space="preserve">
    <value>Save selection</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Lockout_Message" xml:space="preserve">
    <value>This account has been blocked. Please contact your administrator.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Lockout_Title" xml:space="preserve">
    <value>Blocked account</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Masteransicht_Hint" xml:space="preserve">
    <value>Select the columns that will be displayed to the user.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Objekte_DeleteText" xml:space="preserve">
    <value>The following objects have been marked for deletion:</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Pflichten_DeleteText" xml:space="preserve">
    <value>The following obligations have been marked for deletion:</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_PortalDashboard_Erfüllungsgrad" xml:space="preserve">
    <value>Degree of fulfilment</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_PortalDashboard_NeosysNews" xml:space="preserve">
    <value>Neosys news, tips &amp; tricks</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Rechtsbereiche_DeleteText" xml:space="preserve">
    <value>The following legal areas have been marked for deletion:</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Rollenverwaltung_Titel" xml:space="preserve">
    <value>Role administration</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Spalten_Beschriftungen" xml:space="preserve">
    <value>Column headers</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Standorte_Write_Zugriff" xml:space="preserve">
    <value>Sites: (with writing access)</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Standortobjekte_DeleteText" xml:space="preserve">
    <value>The following site objects have been marked for deletion:</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Standortobjekte_Hint" xml:space="preserve">
    <value>The site objects can be renamed here.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Standort_Name" xml:space="preserve">
    <value>Name  </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Tab_AdditionalData" xml:space="preserve">
    <value>Additional data</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Tab_CompareForderung" xml:space="preserve">
    <value>Comparison with previous version</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Tab_ErlassDetail" xml:space="preserve">
    <value>Legislative act</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Tab_ErlassfassungDetail" xml:space="preserve">
    <value>Version of legislative act</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Tab_ForderungDetail" xml:space="preserve">
    <value>Requirement  </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Tab_Kundendokument_Archiv" xml:space="preserve">
    <value>Legislation documents</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Tab_Uebersicht" xml:space="preserve">
    <value>Overview</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Title_Erlasse_Uebersicht" xml:space="preserve">
    <value>Updated legislative acts </value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Title_Kundendokumente_Uebersicht" xml:space="preserve">
    <value>Overview of legislation documents</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Title_Kunden_Portal_Dashboard" xml:space="preserve">
    <value>##KUNDE## - Dashboard</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Title_Neosys_Portal_Dashboard" xml:space="preserve">
    <value>Neosys Portal - Dashboard</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_UserAdmin_Hint" xml:space="preserve">
    <value>Please save the access rights before creating the user.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="XLSX_Export_Format" xml:space="preserve">
    <value>Export as XLSX</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="PDF_Export_Format" xml:space="preserve">
    <value>Export as PDF</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="XSL_Export_Export" xml:space="preserve">
    <value>Export</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Compare_Forderung" xml:space="preserve">
    <value>Requirement of</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_News_FromDate" xml:space="preserve">
    <value>Date</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_News_News" xml:space="preserve">
    <value>News</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_News_Link" xml:space="preserve">
    <value>Link</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Allgemein_Mitgeandert" xml:space="preserve">
    <value>Also changed</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Allgemein_Button_Massnahme" xml:space="preserve">
    <value>Create a measure</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Popup_Massnahme_erstellt" xml:space="preserve">
    <value>Measure created</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Kommentar_Quelle" xml:space="preserve">
    <value>Source</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Dashboard_Tab_Gesetzesaenderungen" xml:space="preserve">
    <value>Amendment of the legislative act</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Dashboard_Tab_Kommentare" xml:space="preserve">
    <value>Comments</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Dashboard_Tab_Vernehmlassungen" xml:space="preserve">
    <value>Consultations</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Kommentar_ShortText" xml:space="preserve">
    <value>Brief information</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Consultation_Titel" xml:space="preserve">
    <value>Consultations</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Consultation_Phase" xml:space="preserve">
    <value>Phase</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_ConsultationStatus_Completed" xml:space="preserve">
    <value>Completed</value>
  </data>
  <data name="Enum_ConsultationStatus_Ongoing" xml:space="preserve">
    <value>Ongoing</value>
  </data>
  <data name="Enum_ConsultationStatus_Planned" xml:space="preserve">
    <value>Planned</value>
  </data>
  <data name="Entitaet_Consultation_EntryDate" xml:space="preserve">
    <value>Registration</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Consultation_OpenedDate" xml:space="preserve">
    <value>Opening</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Consultation_Deadline" xml:space="preserve">
    <value>Deadline</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Consultation_CompletedDate" xml:space="preserve">
    <value>Completed</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Kundendokument_Tab_SUVA" xml:space="preserve">
    <value>SUVA checklists</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_ChecklistQuestion_Abschnitt" xml:space="preserve">
    <value>Section</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enitaet_ChecklistQuestion_Nr" xml:space="preserve">
    <value>Number</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enitaet_ChecklistQuestion_Titel" xml:space="preserve">
    <value>Description</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enitaet_ChecklistQuestion_Antwort" xml:space="preserve">
    <value>Response</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enitaet_ChecklistQuestion_Image" xml:space="preserve">
    <value>Picture</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Massnahme_Singular" xml:space="preserve">
    <value>Measure</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Massnahme_Plural" xml:space="preserve">
    <value>Measures</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Massnahme_Created" xml:space="preserve">
    <value>Created on</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Massnahme_Betreff" xml:space="preserve">
    <value>Reference</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Massnahme_Bemerkung" xml:space="preserve">
    <value>Comment</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Massnahme_Status" xml:space="preserve">
    <value>Status</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Massnahme_Termin" xml:space="preserve">
    <value>Deadline</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_MassnahmeStatus_Finished" xml:space="preserve">
    <value>Completed</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_MassnahmeStatus_InProgress" xml:space="preserve">
    <value>In progress</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_MassnahmeStatus_New" xml:space="preserve">
    <value>New</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Downloadbereich_Title" xml:space="preserve">
    <value>Download area</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_All_Documents_Title" xml:space="preserve">
    <value>All legislation documents</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Newsletter_Title" xml:space="preserve">
    <value>Newsletter</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Bericht_Title" xml:space="preserve">
    <value>Reports</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Label_From" xml:space="preserve">
    <value>From</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Label_To" xml:space="preserve">
    <value>To</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_User_NewsletterPeriod" xml:space="preserve">
    <value>Newsletter delivery period</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_User_NewsletterPeriodMonat" xml:space="preserve">
    <value>Month</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_User_NewsletterPeriodMonate" xml:space="preserve">
    <value>Months</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundeStatus_Newsletter" xml:space="preserve">
    <value>Newsletter</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entität_Pflicht_Plural" xml:space="preserve">
    <value>Duties</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entität_Pflicht_Name" xml:space="preserve">
    <value>Name</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entität_Pflicht_Inkfraftretung" xml:space="preserve">
    <value>Entry into force</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entität_Pflicht_Zuordnungen" xml:space="preserve">
    <value>Assignment</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Newsletter_Header" xml:space="preserve">
    <value>Newsletter legal reforms</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Newsletter_Title" xml:space="preserve">
    <value>Legal reforms</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Newsletter_Erlassaenderungen" xml:space="preserve">
    <value>Amendments of the legislative act</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Newsletter_Additional_Erlassaenderungen" xml:space="preserve">
    <value>Further amended decrees in your customer document</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Newsletter_SUVA" xml:space="preserve">
    <value>Amended SUVA checklists</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Newsletter_Weiterlesen" xml:space="preserve">
    <value>READ MORE</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Shortcut_User" xml:space="preserve">
    <value>Users</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Shortcut_Name" xml:space="preserve">
    <value>Function</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Dashboard_Erfuellungsgrad" xml:space="preserve">
    <value>Degree of fulfilment all locations</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Bericht_ReportDate" xml:space="preserve">
    <value>Report date</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Bericht_Name" xml:space="preserve">
    <value>Report date</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="View_Newsletter_DateSent" xml:space="preserve">
    <value>Dispatch date</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentItemStatus_No" xml:space="preserve">
    <value>No</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentItemStatus_Partially" xml:space="preserve">
    <value>Partly</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Enum_KundendokumentItemStatus_Yes" xml:space="preserve">
    <value>Yes</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_PrivacyPolicy_Accept_Title" xml:space="preserve">
    <value>Before logging in, please confirm that you have read and acknowledged the following documents</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Entitaet_Massnahme_EmailNotification" xml:space="preserve">
    <value>Email notification</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="Evaluations_Offer_Vol_Existing_Customer_Tab_Angenommen_Label" xml:space="preserve">
    <value>Current offer volume with status Accepted: </value>
  </data>
  <data name="Evaluations_Offer_Vol_Existing_Customer_Tab_Versendet_Label" xml:space="preserve">
    <value>Current offer volume with status Sent: </value>
  </data>
  <data name="View_Masterdokument_Title" xml:space="preserve">
    <value>Master document</value>
  </data>
  <data name="UploadErrorExcel" xml:space="preserve">
    <value>Error in your Excel file. Fix it and upload again.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="UploadExcelSuccess" xml:space="preserve">
    <value>Upload done successfully.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="DateErr" xml:space="preserve">
    <value>Date format must be MM/DD/YYYY.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="FormatErr" xml:space="preserve">
    <value>Format must be 000-0000.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="LettersErr" xml:space="preserve">
    <value>Only letters must be used.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="NumbersErr" xml:space="preserve">
    <value>Only numbers must be separated by commas.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="UploadFile" xml:space="preserve">
    <value>File upload</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="ValueErr" xml:space="preserve">
    <value>Error occurred. Please fix the value.</value>
    <comment>uebersetzt</comment>
  </data>
  <data name="XErr" xml:space="preserve">
    <value>Only 'x' is allowed.</value>
    <comment>uebersetzt</comment>
  </data>
	<data name="Column" xml:space="preserve">
    <value>Column</value>
    <comment>uebersetzt</comment>
  </data>
	<data name="InvalidData" xml:space="preserve">
    <value>Invalid data</value>
    <comment>uebersetzt</comment>
  </data>
	<data name="Row" xml:space="preserve">
    <value>Row</value>
    <comment>uebersetzt</comment>
  </data>
</root>