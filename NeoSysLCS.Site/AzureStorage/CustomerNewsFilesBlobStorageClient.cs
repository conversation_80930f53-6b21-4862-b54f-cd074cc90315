using Microsoft.Azure.Storage.Blob;

namespace NeoSysLCS.Site.AzureStorage
{

    public class CustomerNewsFilesBlobStorageClient : BlobStorageClientBase
    {
        private readonly int _newsId;

        public CustomerNewsFilesBlobStorageClient(CloudBlobContainer container, int newsId) : base(container)
        {
            _newsId = newsId;
        }

        protected override string GetFolderName()
        {
            return _newsId.ToString();
        }
    }
}
