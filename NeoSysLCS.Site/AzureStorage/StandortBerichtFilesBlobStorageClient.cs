using Microsoft.Azure.Storage.Blob;

namespace NeoSysLCS.Site.AzureStorage
{

    public class StandortBerichtFilesBlobStorageClient : BlobStorageClientBase
    {
        private readonly int _standortId;

        public StandortBerichtFilesBlobStorageClient(CloudBlobContainer container, int standortId) : base(container)
        {
            _standortId = standortId;
        }

        protected override string GetFolderName()
        {
            return _standortId.ToString();
        }
    }
}
