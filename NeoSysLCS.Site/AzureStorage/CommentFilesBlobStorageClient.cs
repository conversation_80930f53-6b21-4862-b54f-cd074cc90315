using Microsoft.Azure.Storage.Blob;

namespace NeoSysLCS.Site.AzureStorage
{

    public class CommentFilesBlobStorageClient : BlobStorageClientBase
    {
        private readonly int _commentId;

        public CommentFilesBlobStorageClient(CloudBlobContainer container, int commentId) : base(container)
        {
            _commentId = commentId;
        }

        protected override string GetFolderName()
        {
            return _commentId.ToString();
        }
    }
}
