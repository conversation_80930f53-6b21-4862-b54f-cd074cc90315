namespace NeoSysLCS.Site.AzureStorage
{
    public interface IBlobStorageClientFactory
    {
        IBlobStorageClient CreateFaqFilesBlobStorageClient(int faqId);

        IBlobStorageClient CreateCommentFilesBlobStorageClient(int commentId);

        IBlobStorageClient CreateCustomerNewsFilesBlobStorageClient(int customerNewsId);

        IBlobStorageClient CreateStandortBerichtFilesBlobStorageClient(int standortId);

        IBlobStorageClient CreateUserNewsletterBlobStorageClient(string userId);

        IBlobStorageClient createPrivacyPolicyBlobStorageClient(int privacyPolicyId);

        IBlobStorageClient CreateStandortBerichteFilesBlobStorageClient(int standortBerichtId);

    }
}
