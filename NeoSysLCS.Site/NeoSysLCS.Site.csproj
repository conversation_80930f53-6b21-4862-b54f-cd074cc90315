<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{0023603A-D69D-4BEF-BB19-4849CF30B723}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>NeoSysLCS.Site</RootNamespace>
    <AssemblyName>NeoSysLCS.Site</AssemblyName>
    <TargetFrameworkVersion>v4.7</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <IISExpressSSLPort>44300</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication>enabled</IISExpressAnonymousAuthentication>
    <IISExpressWindowsAuthentication>disabled</IISExpressWindowsAuthentication>
    <IISExpressUseClassicPipelineMode>false</IISExpressUseClassicPipelineMode>
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\</SolutionDir>
    <RestorePackages>true</RestorePackages>
    <TargetFrameworkProfile />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <Use64BitIISExpress />
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <GenerateBindingRedirectsOutputType>true</GenerateBindingRedirectsOutputType>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <FilesToIncludeForPublish>OnlyFilesToRunTheApp</FilesToIncludeForPublish>
    <PublishDatabaseSettings>
      <Objects>
        <ObjectGroup Name="NeoSysLCS_Dev-Deployment" Order="1">
          <Destination Path="" />
          <Object Type="DbFullSql">
            <PreSource Path="Data Source=localhost\SQLExpress%3bInitial Catalog=NeoSysLCS_Dev%3bIntegrated Security=True" ScriptSchema="True" ScriptData="True" CopyAllFullTextCatalogs="False" DriDefaults="True" />
            <Source Path="obj\Debug\AutoScripts\NeoSysLCS_Dev-Deployment_SchemaAndData.sql" Transacted="True" />
          </Object>
        </ObjectGroup>
      </Objects>
    </PublishDatabaseSettings>
    <ExcludeGeneratedDebugSymbol>false</ExcludeGeneratedDebugSymbol>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <ExcludeFilesFromDeployment>Error.log;local.config</ExcludeFilesFromDeployment>
    <PublishDatabaseSettings>
      <Objects>
      </Objects>
    </PublishDatabaseSettings>
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.Charts.v24.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.TreeMap.v24.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Xpo.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraTreeMap.v24.1.UI, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraTreeMap.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Web.Resources.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Drawing.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Data.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.ExpressApp.Web.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.ExpressApp.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.Mvvm.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.PivotGrid.v24.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.RichEdit.v24.1.Export, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Printing.v24.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Office.v24.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.RichEdit.v24.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.SpellChecker.v24.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Spreadsheet.v24.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Data.Desktop.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Utils.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Utils.v24.1.UI, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxGauges.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxHtmlEditor.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxPivotGrid.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxScheduler.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxSpellChecker.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.DataVisualization.v24.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxSpreadsheet.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxThemes.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxGantt.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxTreeList.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Web.Mvc5.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.Web.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraCharts.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraCharts.v24.1.Web, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraGauges.v24.1.Presets, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraPivotGrid.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraPrinting.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraScheduler.v24.1.Core.Desktop, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DevExpress.XtraScheduler.v24.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <Private>True</Private>
    </Reference>
    <Reference Include="DocumentFormat.OpenXml, Version=2.7.2.0, Culture=neutral, PublicKeyToken=8fb06cb64d019a17, processorArchitecture=MSIL">
      <HintPath>..\packages\DocumentFormat.OpenXml.2.7.2\lib\net46\DocumentFormat.OpenXml.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="FluentSecurity">
      <HintPath>..\packages\FluentSecurity.2.1.0\lib\net40\FluentSecurity.dll</HintPath>
    </Reference>
    <Reference Include="Hangfire.Core, Version=1.7.28.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Hangfire.Core.1.7.28\lib\net46\Hangfire.Core.dll</HintPath>
    </Reference>
    <Reference Include="Hangfire.SqlServer, Version=1.7.28.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Hangfire.SqlServer.1.7.28\lib\net45\Hangfire.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="HtmlAgilityPack, Version=1.5.1.0, Culture=neutral, PublicKeyToken=bd319b19eaf3b43a, processorArchitecture=MSIL">
      <HintPath>..\packages\HtmlAgilityPack.1.5.1\lib\Net45\HtmlAgilityPack.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="log4net, Version=2.0.8.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.8\lib\net45-full\log4net.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Core, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.AspNet.Identity.Core.2.2.1\lib\net45\Microsoft.AspNet.Identity.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.EntityFramework">
      <HintPath>..\packages\Microsoft.AspNet.Identity.EntityFramework.2.2.1\lib\net45\Microsoft.AspNet.Identity.EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Owin, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Owin.2.2.1\lib\net45\Microsoft.AspNet.Identity.Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Azure.KeyVault.Core, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.Azure.KeyVault.Core.2.0.4\lib\net45\Microsoft.Azure.KeyVault.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.Storage.Blob, Version=11.1.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.Storage.Blob.11.1.7\lib\net452\Microsoft.Azure.Storage.Blob.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Azure.Storage.Common, Version=11.1.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Azure.Storage.Common.11.1.7\lib\net452\Microsoft.Azure.Storage.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=1.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.1.1.1\lib\net461\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Data.Edm, Version=5.8.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.Edm.5.8.2\lib\net40\Microsoft.Data.Edm.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Data.OData, Version=5.8.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.OData.5.8.2\lib\net40\Microsoft.Data.OData.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Data.Services.Client, Version=5.8.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.Services.Client.5.8.2\lib\net40\Microsoft.Data.Services.Client.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.Excel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <EmbedInteropTypes>True</EmbedInteropTypes>
      <HintPath>..\packages\Microsoft.Office.Interop.Excel.15.0.4795.1000\lib\net20\Microsoft.Office.Interop.Excel.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.3.1.0\lib\net45\Microsoft.Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.SystemWeb, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Host.SystemWeb.3.1.0\lib\net45\Microsoft.Owin.Host.SystemWeb.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.3.1.0\lib\net45\Microsoft.Owin.Security.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Cookies, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.Cookies.3.1.0\lib\net45\Microsoft.Owin.Security.Cookies.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.OAuth, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.OAuth.3.1.0\lib\net45\Microsoft.Owin.Security.OAuth.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Win32.Primitives.4.3.0\lib\net46\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="MigraDoc.DocumentObjectModel-gdi, Version=1.50.5147.0, Culture=neutral, PublicKeyToken=f94615aa0424f9eb, processorArchitecture=MSIL">
      <HintPath>..\packages\PDFsharp-MigraDoc-gdi.1.50.5147\lib\net20\MigraDoc.DocumentObjectModel-gdi.dll</HintPath>
    </Reference>
    <Reference Include="MigraDoc.Rendering-gdi, Version=1.50.5147.0, Culture=neutral, PublicKeyToken=f94615aa0424f9eb, processorArchitecture=MSIL">
      <HintPath>..\packages\PDFsharp-MigraDoc-gdi.1.50.5147\lib\net20\MigraDoc.Rendering-gdi.dll</HintPath>
    </Reference>
    <Reference Include="MigraDoc.RtfRendering-gdi, Version=1.50.5147.0, Culture=neutral, PublicKeyToken=f94615aa0424f9eb, processorArchitecture=MSIL">
      <HintPath>..\packages\PDFsharp-MigraDoc-gdi.1.50.5147\lib\net20\MigraDoc.RtfRendering-gdi.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.10.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="PdfSharp-gdi, Version=1.50.5147.0, Culture=neutral, PublicKeyToken=f94615aa0424f9eb, processorArchitecture=MSIL">
      <HintPath>..\packages\PDFsharp-MigraDoc-gdi.1.50.5147\lib\net20\PdfSharp-gdi.dll</HintPath>
    </Reference>
    <Reference Include="PdfSharp.Charting-gdi, Version=1.50.5147.0, Culture=neutral, PublicKeyToken=f94615aa0424f9eb, processorArchitecture=MSIL">
      <HintPath>..\packages\PDFsharp-MigraDoc-gdi.1.50.5147\lib\net20\PdfSharp.Charting-gdi.dll</HintPath>
    </Reference>
    <Reference Include="Postal, Version=1.0.0.0, Culture=neutral, PublicKeyToken=45719375b8b4d528, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Postal.Mvc5.1.2.0\lib\net45\Postal.dll</HintPath>
    </Reference>
    <Reference Include="RazorEngine, Version=3.10.0.0, Culture=neutral, PublicKeyToken=9ee697374c7e744a, processorArchitecture=MSIL">
      <HintPath>..\packages\RazorEngine.3.10.0\lib\net45\RazorEngine.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.AppContext, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.AppContext.4.3.0\lib\net463\System.AppContext.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.Console, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Console.4.3.0\lib\net46\System.Console.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.Services.Client" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=4.0.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.4.6.0\lib\net46\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.Tracing.4.3.0\lib\net462\System.Diagnostics.Tracing.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Globalization.Calendars, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Globalization.Calendars.4.3.0\lib\net46\System.Globalization.Calendars.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Compression.4.3.0\lib\net46\System.IO.Compression.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.IO.Compression.ZipFile, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Compression.ZipFile.4.3.0\lib\net46\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.4.3.0\lib\net46\System.IO.FileSystem.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.Primitives.4.3.0\lib\net46\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Hashing, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Hashing.6.0.0\lib\net461\System.IO.Hashing.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Packaging, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Packaging.4.4.0\lib\net46\System.IO.Packaging.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Linq, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Linq.4.3.0\lib\net463\System.Linq.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Linq.Expressions.4.3.0\lib\net463\System.Linq.Expressions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.4\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory.Data, Version=1.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.Data.1.0.2\lib\net461\System.Memory.Data.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.3\lib\net45\System.Net.Http.Formatting.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Sockets, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Sockets.4.3.0\lib\net46\System.Net.Sockets.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Reflection.4.3.0\lib\net462\System.Reflection.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.0\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.4.7.1\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.Extensions.4.3.0\lib\net462\System.Runtime.Extensions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.4.3.0\lib\net463\System.Runtime.InteropServices.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Spatial, Version=5.8.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Spatial.5.8.2\lib\net40\System.Spatial.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=4.0.5.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.4.7.2\lib\net461\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.4.7.2\lib\net461\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.RegularExpressions, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.RegularExpressions.4.3.0\lib\net463\System.Text.RegularExpressions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.3\lib\net45\System.Web.Http.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.3\lib\net45\System.Web.Http.WebHost.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.3\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.3\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Routing">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.3\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Web.Optimization">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Xml.ReaderWriter, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Xml.ReaderWriter.4.3.0\lib\net46\System.Xml.ReaderWriter.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="ThemeAssembly, Version=14.1.5.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\ThemeAssembly.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease, Version=1.6.5135.21930, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Owin">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\IdentityConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\SchedulerConfig.cs" />
    <Compile Include="App_Start\Startup.Auth.cs" />
    <Compile Include="Areas\Admin\Controllers\ChecklistController.cs" />
    <Compile Include="Areas\Admin\Controllers\EvaluationsController.cs" />
    <Compile Include="Areas\Admin\Controllers\AuswertungenController.cs" />
    <Compile Include="Areas\Admin\Controllers\FAQController.cs" />
    <Compile Include="Areas\Admin\Controllers\ForderungenController.cs" />
    <Compile Include="Areas\Admin\Controllers\ErlassfassungenController.cs" />
    <Compile Include="Areas\Admin\Controllers\ErlasseController.cs" />
    <Compile Include="Areas\Admin\Controllers\ErlassRechtsbereicheController.cs" />
    <Compile Include="Areas\Admin\Controllers\ForderungRechtsbereicheController.cs" />
    <Compile Include="Areas\Admin\Controllers\HerausgeberController.cs" />
    <Compile Include="Areas\Admin\Controllers\CustomerNewsController.cs" />
    <Compile Include="Areas\Admin\Controllers\ConsultationController.cs" />
    <Compile Include="Areas\Admin\Controllers\KommentarController.cs" />
    <Compile Include="Areas\Admin\Controllers\ConsultationErlasseController.cs" />
    <Compile Include="Areas\Admin\Controllers\ObligationController.cs" />
    <Compile Include="Areas\Admin\Controllers\StandortBerichteController.cs" />
    <Compile Include="Areas\Admin\Controllers\PrivacyPolicyController.cs" />
    <Compile Include="Areas\Portal\Controllers\MassnahmeShortcutController.cs" />
    <Compile Include="Areas\Admin\Controllers\ObligationForderungsversionsController.cs" />
    <Compile Include="Areas\Admin\Controllers\KommentarHauptErlassController.cs" />
    <Compile Include="Areas\Admin\Controllers\KommentarErlasseController.cs" />
    <Compile Include="Areas\Admin\Controllers\KommentarObjekteController.cs" />
    <Compile Include="Areas\Admin\Controllers\KontakteController.cs" />
    <Compile Include="Areas\Admin\Controllers\ObjektObligationController.cs" />
    <Compile Include="Areas\Admin\Controllers\ToDoListController.cs" />
    <Compile Include="Areas\Admin\Controllers\KundenOfferController.cs" />
    <Compile Include="Areas\Admin\Controllers\KundenController.cs" />
    <Compile Include="Areas\Admin\Controllers\KundendokumentUpdateController.cs" />
    <Compile Include="Areas\Admin\Controllers\KundendokumenteController.cs" />
    <Compile Include="Areas\Admin\Controllers\KundendokumenteQsController.cs" />
    <Compile Include="Areas\Admin\Controllers\KundeninformationController.cs" />
    <Compile Include="Areas\Portal\Controllers\MassnahmeController.cs" />
    <Compile Include="Areas\Portal\Controllers\LegalComplianceController.cs" />
    <Compile Include="Areas\Admin\Controllers\ObjektPflichtenController.cs" />
    <Compile Include="Areas\Admin\Controllers\ObjektForderungenController.cs" />
    <Compile Include="Areas\Admin\Controllers\ObjektkategorienController.cs" />
    <Compile Include="Areas\Admin\Controllers\ForderungObjekteController.cs" />
    <Compile Include="Areas\Admin\Controllers\RechtsbereicheController.cs" />
    <Compile Include="Areas\Admin\Controllers\ErlasstypenController.cs" />
    <Compile Include="Areas\Admin\Controllers\PflichtenController.cs" />
    <Compile Include="Areas\Admin\Controllers\StandortObjektController.cs" />
    <Compile Include="Areas\Admin\Controllers\ObjekteController.cs" />
    <Compile Include="Areas\Portal\Controllers\AdministrationController.cs" />
    <Compile Include="Areas\Portal\Controllers\AuswertungController.cs" />
    <Compile Include="Areas\Portal\Controllers\DatePickerController.cs" />
    <Compile Include="Areas\Portal\Controllers\FAQPortalController.cs" />
    <Compile Include="Areas\Portal\Controllers\MasterKundendokumentController.cs" />
    <Compile Include="Areas\Portal\Controllers\StandortAdministrationController.cs" />
    <Compile Include="Areas\Portal\Controllers\DashboardController.cs" />
    <Compile Include="Areas\Portal\Controllers\KonzernController.cs" />
    <Compile Include="Areas\Portal\Controllers\KundendokumentController.cs" />
    <Compile Include="Areas\Portal\Controllers\IndividuelleForderungenController.cs" />
    <Compile Include="Areas\Portal\Controllers\DownloadbereichController.cs" />
    <Compile Include="Areas\Portal\Controllers\StandorteController.cs" />
    <Compile Include="Areas\Portal\Controllers\KundendokumentStandortObjektController.cs" />
    <Compile Include="Areas\Portal\CustomerAreaRegistration.cs" />
    <Compile Include="Areas\Admin\Controllers\DashboardController.cs" />
    <Compile Include="Areas\Admin\NeoSysAreaRegistration.cs" />
    <Compile Include="Areas\Admin\Controllers\KommentarRechtsbereicheController.cs" />
    <Content Include="Areas\Admin\Views\Kunden\_KundenOfferExistingCustomersPartialView.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\_KundenCortecDetailsPopupContent.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\UploadStandortErlassImportPopupContent.cshtml" />
    <Content Include="bin\de\DevExpress.Blazor.Reporting.v24.1.Viewer.resources.dll" />
    <Content Include="bin\de\DevExpress.Blazor.RichEdit.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Blazor.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Charts.Designer.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Dashboard.v24.1.Core.resources.dll" />
    <Content Include="bin\de\DevExpress.Dashboard.v24.1.Web.resources.dll" />
    <Content Include="bin\de\DevExpress.Dashboard.v24.1.Win.resources.dll" />
    <Content Include="bin\de\DevExpress.Data.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.DataAccess.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.DataAccess.v24.1.UI.resources.dll" />
    <Content Include="bin\de\DevExpress.Diagram.v24.1.Core.resources.dll" />
    <Content Include="bin\de\DevExpress.Dialogs.v24.1.Core.resources.dll" />
    <Content Include="bin\de\DevExpress.EntityFrameworkCore.Security.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.AuditTrail.Xpo.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Blazor.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Chart.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.CloneObject.Xpo.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Dashboards.Blazor.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Dashboards.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Dashboards.Web.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Dashboards.Win.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.FileAttachment.Blazor.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.FileAttachment.Web.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.FileAttachment.Win.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.HtmlPropertyEditor.Win.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Kpi.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Maps.Web.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Notifications.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Notifications.Web.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Objects.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Office.Web.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Office.Win.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.PivotChart.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.PivotChart.Web.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.PivotChart.Win.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.PivotGrid.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.PivotGrid.Web.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Reports.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Reports.Web.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Reports.Win.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.ReportsV2.Blazor.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.ReportsV2.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.ReportsV2.Web.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.ReportsV2.Win.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Scheduler.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Scheduler.Web.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Scheduler.Win.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.ScriptRecorder.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.ScriptRecorder.Win.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Security.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Security.Xpo.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.StateMachine.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Validation.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Validation.Win.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.ViewVariantsModule.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Web.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Win.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Workflow.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.ExpressApp.Workflow.Win.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Map.v24.1.Core.resources.dll" />
    <Content Include="bin\de\DevExpress.Mvvm.v24.1.DataModel.resources.dll" />
    <Content Include="bin\de\DevExpress.Office.v24.1.Core.resources.dll" />
    <Content Include="bin\de\DevExpress.Pdf.v24.1.Core.resources.dll" />
    <Content Include="bin\de\DevExpress.Persistent.Base.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.PivotGrid.v24.1.Core.resources.dll" />
    <Content Include="bin\de\DevExpress.Printing.v24.1.Core.resources.dll" />
    <Content Include="bin\de\DevExpress.ReportServer.v24.1.Localization.resources.dll" />
    <Content Include="bin\de\DevExpress.RichEdit.v24.1.Core.resources.dll" />
    <Content Include="bin\de\DevExpress.Snap.v24.1.Core.resources.dll" />
    <Content Include="bin\de\DevExpress.Snap.v24.1.Extensions.resources.dll" />
    <Content Include="bin\de\DevExpress.Snap.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Sparkline.v24.1.Core.resources.dll" />
    <Content Include="bin\de\DevExpress.Spreadsheet.v24.1.Core.resources.dll" />
    <Content Include="bin\de\DevExpress.Utils.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Utils.v24.1.UI.resources.dll" />
    <Content Include="bin\de\DevExpress.Web.ASPxDiagram.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Web.ASPxGantt.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Web.ASPxHtmlEditor.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Web.ASPxRichEdit.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Web.ASPxScheduler.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Web.ASPxSpellChecker.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Web.ASPxSpreadsheet.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Web.ASPxTreeList.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Web.Resources.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Web.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Xpf.Charts.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Xpf.CodeView.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Xpf.Controls.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Xpf.Core.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Xpf.Docking.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Xpf.DocumentViewer.v24.1.Core.resources.dll" />
    <Content Include="bin\de\DevExpress.Xpf.Gantt.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Xpf.Gauges.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Xpf.Grid.v24.1.Core.resources.dll" />
    <Content Include="bin\de\DevExpress.Xpf.LayoutControl.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Xpf.NavBar.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Xpf.PdfViewer.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Xpf.Printing.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Xpf.PropertyGrid.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Xpf.ReportDesigner.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Xpf.Ribbon.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Xpf.RichEdit.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Xpf.Scheduler.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Xpf.Scheduling.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Xpf.SpellChecker.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Xpf.Spreadsheet.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.Xpo.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraBars.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraCharts.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraCharts.v24.1.Wizard.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraEditors.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraGantt.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraGauges.v24.1.Core.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraGauges.v24.1.Presets.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraGrid.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraLayout.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraNavBar.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraPdfViewer.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraPivotGrid.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraPrinting.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraReports.v24.1.Extensions.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraReports.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraReports.v24.1.Web.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraRichEdit.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraScheduler.v24.1.Core.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraScheduler.v24.1.Extensions.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraScheduler.v24.1.Reporting.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraScheduler.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraSpellChecker.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraSpreadsheet.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraTreeList.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraVerticalGrid.v24.1.resources.dll" />
    <Content Include="bin\de\DevExpress.XtraWizard.v24.1.resources.dll" />
    <Content Include="Content\images\archive.png" />
    <Content Include="Content\images\undo-unarchive.png" />
    <Content Include="Upload\PrivacyPolicy\DefaultPrivacyPolicy.html" />
    <Content Include="Views\Objekte\_ObjektForderungenExportPartial.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\_KundenStandortUebersetzungenPartialView.cshtml" />
    <Content Include="Areas\Admin\Views\Obligation\_ObligationUebersetzungenGridViewNew.cshtml" />
    <Content Include="Areas\Portal\Views\Auswertung\_PieChartForderungenBandN.cshtml" />
    <Content Include="Areas\Portal\Views\Auswertung\_PieChartForderungenStandortobjekte.cshtml" />
    <Content Include="Areas\Portal\Views\Auswertung\_PieChartForderungenN.cshtml" />
    <Compile Include="AzureStorage\BlobStorageClientBase.cs" />
    <Compile Include="AzureStorage\BlobStorageClientFactory.cs" />
    <Compile Include="AzureStorage\PrivacyPolicyBlobStorageClient.cs" />
    <Compile Include="AzureStorage\UserNewsletterBlobStorageClient.cs" />
    <Compile Include="AzureStorage\StandortBerichtFilesBlobStorageClient.cs" />
    <Compile Include="AzureStorage\CustomerNewsFilesBlobStorageClient.cs" />
    <Compile Include="AzureStorage\FaqFilesBlobStorageClient.cs" />
    <Compile Include="AzureStorage\IBlobStorageClient.cs" />
    <Compile Include="AzureStorage\IBlobStorageClientFactory.cs" />
    <Compile Include="AzureStorage\CommentFilesBlobStorageClient.cs" />
    <Compile Include="AzureStorage\UploadResult.cs" />
    <Compile Include="Controllers\AccountController.cs" />
    <Compile Include="Areas\Admin\Controllers\ArtikelController.cs" />
    <Compile Include="Controllers\BaseController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Controllers\ErlassImportController.cs" />
    <Compile Include="Controllers\KundendokumentImportExportController.cs" />
    <Compile Include="Controllers\ManageController.cs" />
    <Compile Include="Areas\Admin\Controllers\RolesAdminController.cs" />
    <Compile Include="Areas\Admin\Controllers\UsersAdminController.cs" />
    <Compile Include="Controllers\PrivacyPolicyConfirmationController.cs" />
    <Compile Include="Cortec\CortecClient.cs" />
    <Compile Include="Cortec\CortecClientDateFormatter.cs" />
    <Compile Include="Cortec\CortecService.cs" />
    <Compile Include="Cortec\CortecClientFactory.cs" />
    <Compile Include="Cortec\dtos\ToDoTaskRespDto.cs" />
    <Compile Include="Cortec\dtos\ToDoTaskDto.cs" />
    <Compile Include="Cortec\dtos\CustomerDto.cs" />
    <Compile Include="Cortec\ICortecClient.cs" />
    <Compile Include="Cortec\task\CortecTaskFactory.cs" />
    <Compile Include="Cortec\task\ICortecTaskCreatable.cs" />
    <Compile Include="Cortec\task\AbstractCortecTaskCreationStrategy.cs" />
    <Compile Include="Cortec\task\kommentar\TranslateKommentarStrategy.cs" />
    <Compile Include="Cortec\task\kommentar\QSKommentarStrategy.cs" />
    <Compile Include="Cortec\task\kommentar\NewKommentarStrategy.cs" />
    <Compile Include="Cortec\task\kommentar\KommentarAdapter.cs" />
    <Compile Include="Cortec\task\kunde\AktualisierungDocumentReceivedKundeStrategy.cs" />
    <Compile Include="Cortec\task\kunde\AktualisierungVersendetKundeStrategy.cs" />
    <Compile Include="Cortec\task\kunde\AktualisierungQSClosedKundeStrategy.cs" />
    <Compile Include="Cortec\task\kunde\AktualisierungInactiveKundeStrategy.cs" />
    <Compile Include="Cortec\task\kunde\AktualisierungQSKundeStrategy.cs" />
    <Compile Include="Cortec\task\kunde\AktualisierungInquirySentKundeStrategy.cs" />
    <Compile Include="Cortec\task\kunde\AktualisierungInquiryKundeStrategy.cs" />
    <Compile Include="Cortec\task\kunde\AnalyseInactiveKundeStrategy.cs" />
    <Compile Include="Cortec\task\kunde\AnalyseContractCreatedKundeStrategy.cs" />
    <Compile Include="Cortec\task\kunde\AnalyseCompletedKundeStrategy.cs" />
    <Compile Include="Cortec\task\kunde\AnalyseProcessKundeStrategy.cs" />
    <Compile Include="Cortec\task\kunde\AnalyseNewKundeStrategy.cs" />
    <Compile Include="Cortec\task\kunde\KundeAdapter.cs" />
    <Compile Include="Cortec\task\offer\ExistingCustomerAcceptedOfferStrategy.cs" />
    <Compile Include="Cortec\task\offer\ExistingCustomerContractCreatedOfferStrategy.cs" />
    <Compile Include="Cortec\task\offer\NewCustomerRejectedOfferStrategy.cs" />
    <Compile Include="Cortec\task\offer\NewCustomerSentOfferStrategy.cs" />
    <Compile Include="Cortec\task\offer\NewCustomerNewOfferStrategy.cs" />
    <Compile Include="Cortec\task\offer\NewCustomerQsOfferStrategy.cs" />
    <Compile Include="Cortec\task\offer\ExistingCustomerRejectedOfferStrategy.cs" />
    <Compile Include="Cortec\task\offer\ExistingCustomerCompletedOfferStrategy.cs" />
    <Compile Include="Cortec\task\offer\ExistingCustomerSentOfferStrategy.cs" />
    <Compile Include="Cortec\task\offer\ExistingCustomerQsOfferStrategy.cs" />
    <Compile Include="Cortec\task\offer\OfferAdapter.cs" />
    <Compile Include="Cortec\task\offer\ExistingCustomerNewOfferStrategy.cs" />
    <Compile Include="Filters\AuthFlagPolicy.cs" />
    <Compile Include="Filters\AuthFlagPolicyViolationHandler.cs" />
    <Compile Include="Filters\MyAdminUserPrincipal.cs" />
    <Compile Include="Filters\PwdResetAuthenticationAttribute.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="Helpers\CookieHelper.cs" />
    <Compile Include="Helpers\HangfireDashboardFilter.cs" />
    <Compile Include="Helpers\MassnahmeMailHelper.cs" />
    <Compile Include="Helpers\CultureHelper.cs" />
    <Compile Include="Helpers\EnumExtensions.cs" />
    <Compile Include="Helpers\ErlassfassungHelper.cs" />
    <Compile Include="Helpers\ErlassfassungExportHelper.cs" />
    <Compile Include="Helpers\ForderungExportHelper.cs" />
    <Compile Include="Helpers\GridViewUpdateHelper.cs" />
    <Compile Include="Helpers\HtmlEditorHelper.cs" />
    <Compile Include="Helpers\GridViewHelper.cs" />
    <Compile Include="Helpers\KundendokumentManager.cs" />
    <Compile Include="Helpers\LinkHelper.cs" />
    <Compile Include="Helpers\NewsletterTextHelper.cs" />
    <Compile Include="Helpers\PolicyViolationHandlerHelper.cs" />
    <Compile Include="Helpers\QuenticExportHelper.cs" />
    <Compile Include="Helpers\RequreSecureConnectionFilter.cs" />
    <Compile Include="Helpers\SessionHelper.cs" />
    <Compile Include="Helpers\KundendokumentData.cs" />
    <Compile Include="Helpers\SprachHelper.cs" />
    <Compile Include="Helpers\StringHelper.cs" />
    <Compile Include="Helpers\DateHelper.cs" />
    <Compile Include="Helpers\TranslationHelper.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Services\Newsletter\INewsletterEmailService.cs" />
    <Compile Include="Services\Newsletter\MockNewsletterEmailService.cs" />
    <Compile Include="Services\Newsletter\NewsletterEmailService.cs" />
    <Compile Include="Services\UserCreationService.cs" />
    <Compile Include="Startup.cs" />
    <Compile Include="Suva\ISuvaClient.cs" />
    <Compile Include="Suva\dtos\SuvaChecklistDto.cs" />
    <Compile Include="Suva\SuvaClient.cs" />
    <Compile Include="Suva\SuvaClientFactory.cs" />
    <Compile Include="Suva\dtos\SuvaChecklistDetailsDto.cs" />
    <Compile Include="Tasks\ErlassImportTask.cs" />
    <Compile Include="Tasks\CortecTask.cs" />
    <Compile Include="Tasks\CustomerStatusTask.cs" />
    <Compile Include="Tasks\MassnahmeTask.cs" />
    <Compile Include="Tasks\SuvaChecklistTask.cs" />
    <Compile Include="Tasks\NewsletterTask.cs" />
    <Compile Include="Tasks\KundeSummaryTask.cs" />
    <Compile Include="Tasks\EvaluationTask.cs" />
    <Compile Include="Tasks\UserPasswordResetTask.cs" />
    <Compile Include="Utilities\ExceptionUtility.cs" />
    <Compile Include="Utilities\Export\ExportAction.cs" />
    <Compile Include="Utilities\Export\ExportType.cs" />
    <Compile Include="Utilities\Export\ExportUtilities.cs" />
    <Compile Include="Utilities\Import\BaseImporter.cs" />
    <Compile Include="Utilities\Import\Erlass\BaseImporter.cs" />
    <Compile Include="Utilities\Import\Erlass\ArtikelImporter.cs" />
    <Compile Include="Utilities\Import\Erlass\ExcelFileValidator.cs" />
    <Compile Include="Utilities\Import\Erlass\MiscellaneousAssigner.cs" />
    <Compile Include="Utilities\Import\Erlass\ForderungsVersionImporter.cs" />
    <Compile Include="Utilities\Import\Erlass\ErlassFassungImporter.cs" />
    <Compile Include="Utilities\Import\Erlass\HerausgeberImporter.cs" />
    <Compile Include="Utilities\Import\Erlass\ErlassImporter.cs" />
    <Compile Include="Utilities\Import\Erlass\Importer.cs" />
    <Compile Include="Utilities\Import\KundendokumentIndividuelleForderungsversionImporter.cs" />
    <Compile Include="Utilities\Import\KundendokumentPflichtImporter.cs" />
    <Compile Include="Utilities\Import\KundendokumentForderungsversionImporter.cs" />
    <Compile Include="Utilities\Import\KundendokumentImporter.cs" />
    <Compile Include="Utilities\NavUtilities.cs" />
    <Content Include="agb_de.pdf" />
    <Content Include="agb_fr.pdf" />
    <Content Include="agb_it.pdf" />
    <Content Include="agb_en.pdf" />
    <None Include="App_offline\App_offline-template.htm" />
    <Content Include="Areas\Admin\Views\_ViewStart.cshtml" />
    <Content Include="bin\fr\DevExpress.Blazor.Reporting.v24.1.Viewer.resources.dll" />
    <Content Include="bin\fr\DevExpress.Blazor.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Charts.Designer.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Dashboard.v24.1.Core.resources.dll" />
    <Content Include="bin\fr\DevExpress.Dashboard.v24.1.Web.resources.dll" />
    <Content Include="bin\fr\DevExpress.Dashboard.v24.1.Win.resources.dll" />
    <Content Include="bin\fr\DevExpress.Data.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.DataAccess.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.DataAccess.v24.1.UI.resources.dll" />
    <Content Include="bin\fr\DevExpress.Diagram.v24.1.Core.resources.dll" />
    <Content Include="bin\fr\DevExpress.Dialogs.v24.1.Core.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Blazor.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Chart.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Dashboards.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Dashboards.Web.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Dashboards.Win.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.FileAttachment.Web.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.FileAttachment.Win.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.HtmlPropertyEditor.Win.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Kpi.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Maps.Web.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Notifications.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Notifications.Web.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Objects.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.PivotChart.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.PivotChart.Web.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.PivotChart.Win.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.PivotGrid.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.PivotGrid.Web.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Reports.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Reports.Web.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Reports.Win.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.ReportsV2.Blazor.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.ReportsV2.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.ReportsV2.Web.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.ReportsV2.Win.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Scheduler.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Scheduler.Web.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Scheduler.Win.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.ScriptRecorder.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Security.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.StateMachine.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Validation.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Validation.Win.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.ViewVariantsModule.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Web.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Win.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Workflow.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.ExpressApp.Workflow.Win.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Map.v24.1.Core.resources.dll" />
    <Content Include="bin\fr\DevExpress.Mvvm.v24.1.DataModel.resources.dll" />
    <Content Include="bin\fr\DevExpress.Office.v24.1.Core.resources.dll" />
    <Content Include="bin\fr\DevExpress.Pdf.v24.1.Core.resources.dll" />
    <Content Include="bin\fr\DevExpress.Persistent.Base.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.PivotGrid.v24.1.Core.resources.dll" />
    <Content Include="bin\fr\DevExpress.Printing.v24.1.Core.resources.dll" />
    <Content Include="bin\fr\DevExpress.ReportServer.v24.1.Localization.resources.dll" />
    <Content Include="bin\fr\DevExpress.RichEdit.v24.1.Core.resources.dll" />
    <Content Include="bin\fr\DevExpress.Snap.v24.1.Core.resources.dll" />
    <Content Include="bin\fr\DevExpress.Snap.v24.1.Extensions.resources.dll" />
    <Content Include="bin\fr\DevExpress.Snap.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Sparkline.v24.1.Core.resources.dll" />
    <Content Include="bin\fr\DevExpress.Spreadsheet.v24.1.Core.resources.dll" />
    <Content Include="bin\fr\DevExpress.Utils.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Utils.v24.1.UI.resources.dll" />
    <Content Include="bin\fr\DevExpress.Web.ASPxDiagram.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Web.ASPxGantt.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Web.ASPxHtmlEditor.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Web.ASPxRichEdit.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Web.ASPxScheduler.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Web.ASPxSpellChecker.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Web.ASPxSpreadsheet.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Web.ASPxTreeList.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Web.Resources.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Web.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Xpf.Charts.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Xpf.CodeView.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Xpf.Controls.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Xpf.Core.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Xpf.Docking.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Xpf.DocumentViewer.v24.1.Core.resources.dll" />
    <Content Include="bin\fr\DevExpress.Xpf.Gantt.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Xpf.Gauges.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Xpf.Grid.v24.1.Core.resources.dll" />
    <Content Include="bin\fr\DevExpress.Xpf.LayoutControl.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Xpf.NavBar.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Xpf.PdfViewer.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Xpf.Printing.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Xpf.PropertyGrid.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Xpf.ReportDesigner.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Xpf.Ribbon.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Xpf.RichEdit.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Xpf.Scheduler.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Xpf.Scheduling.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Xpf.SpellChecker.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Xpf.Spreadsheet.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.Xpo.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraBars.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraCharts.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraCharts.v24.1.Wizard.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraEditors.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraGantt.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraGauges.v24.1.Core.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraGauges.v24.1.Presets.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraGrid.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraLayout.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraNavBar.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraPdfViewer.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraPivotGrid.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraPrinting.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraReports.v24.1.Extensions.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraReports.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraReports.v24.1.Web.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraRichEdit.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraScheduler.v24.1.Core.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraScheduler.v24.1.Extensions.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraScheduler.v24.1.Reporting.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraScheduler.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraSpellChecker.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraSpreadsheet.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraTreeList.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraVerticalGrid.v24.1.resources.dll" />
    <Content Include="bin\fr\DevExpress.XtraWizard.v24.1.resources.dll" />
    <Content Include="bin\fr\Microsoft.AspNet.Identity.Core.resources.dll" />
    <Content Include="bin\fr\Microsoft.Data.Edm.resources.dll" />
    <Content Include="bin\fr\Microsoft.Data.OData.resources.dll" />
    <Content Include="bin\fr\Microsoft.Data.Services.Client.resources.dll" />
    <Content Include="bin\fr\NeoSysLCS.Resources.resources.dll" />
    <Content Include="bin\fr\System.Spatial.resources.dll" />
    <Content Include="Areas\Admin\Views\_ViewStart.cshtml" />
    <Content Include="bin\it\DevExpress.Blazor.Reporting.v24.1.Viewer.resources.dll" />
    <Content Include="bin\it\DevExpress.Blazor.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Charts.Designer.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Dashboard.v24.1.Core.resources.dll" />
    <Content Include="bin\it\DevExpress.Dashboard.v24.1.Web.resources.dll" />
    <Content Include="bin\it\DevExpress.Dashboard.v24.1.Win.resources.dll" />
    <Content Include="bin\it\DevExpress.Data.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.DataAccess.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.DataAccess.v24.1.UI.resources.dll" />
    <Content Include="bin\it\DevExpress.Diagram.v24.1.Core.resources.dll" />
    <Content Include="bin\it\DevExpress.Dialogs.v24.1.Core.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Blazor.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Chart.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Dashboards.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Dashboards.Web.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Dashboards.Win.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.FileAttachment.Web.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.FileAttachment.Win.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.HtmlPropertyEditor.Win.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Kpi.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Maps.Web.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Notifications.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Notifications.Web.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Objects.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.PivotChart.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.PivotChart.Web.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.PivotChart.Win.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.PivotGrid.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.PivotGrid.Web.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Reports.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Reports.Web.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Reports.Win.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.ReportsV2.Blazor.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.ReportsV2.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.ReportsV2.Web.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.ReportsV2.Win.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Scheduler.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Scheduler.Web.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Scheduler.Win.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.ScriptRecorder.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Security.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.StateMachine.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Validation.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Validation.Win.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.ViewVariantsModule.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Web.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Win.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Workflow.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.ExpressApp.Workflow.Win.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Map.v24.1.Core.resources.dll" />
    <Content Include="bin\it\DevExpress.Mvvm.v24.1.DataModel.resources.dll" />
    <Content Include="bin\it\DevExpress.Office.v24.1.Core.resources.dll" />
    <Content Include="bin\it\DevExpress.Pdf.v24.1.Core.resources.dll" />
    <Content Include="bin\it\DevExpress.Persistent.Base.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.PivotGrid.v24.1.Core.resources.dll" />
    <Content Include="bin\it\DevExpress.Printing.v24.1.Core.resources.dll" />
    <Content Include="bin\it\DevExpress.ReportServer.v24.1.Localization.resources.dll" />
    <Content Include="bin\it\DevExpress.RichEdit.v24.1.Core.resources.dll" />
    <Content Include="bin\it\DevExpress.Snap.v24.1.Core.resources.dll" />
    <Content Include="bin\it\DevExpress.Snap.v24.1.Extensions.resources.dll" />
    <Content Include="bin\it\DevExpress.Snap.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Sparkline.v24.1.Core.resources.dll" />
    <Content Include="bin\it\DevExpress.Spreadsheet.v24.1.Core.resources.dll" />
    <Content Include="bin\it\DevExpress.Utils.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Utils.v24.1.UI.resources.dll" />
    <Content Include="bin\it\DevExpress.Web.ASPxDiagram.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Web.ASPxGantt.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Web.ASPxHtmlEditor.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Web.ASPxRichEdit.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Web.ASPxScheduler.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Web.ASPxSpellChecker.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Web.ASPxSpreadsheet.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Web.ASPxTreeList.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Web.Resources.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Web.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Xpf.Charts.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Xpf.CodeView.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Xpf.Controls.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Xpf.Core.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Xpf.Docking.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Xpf.DocumentViewer.v24.1.Core.resources.dll" />
    <Content Include="bin\it\DevExpress.Xpf.Gantt.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Xpf.Gauges.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Xpf.Grid.v24.1.Core.resources.dll" />
    <Content Include="bin\it\DevExpress.Xpf.LayoutControl.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Xpf.NavBar.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Xpf.PdfViewer.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Xpf.Printing.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Xpf.PropertyGrid.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Xpf.ReportDesigner.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Xpf.Ribbon.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Xpf.RichEdit.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Xpf.Scheduler.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Xpf.Scheduling.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Xpf.SpellChecker.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Xpf.Spreadsheet.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.Xpo.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraBars.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraCharts.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraCharts.v24.1.Wizard.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraEditors.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraGantt.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraGauges.v24.1.Core.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraGauges.v24.1.Presets.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraGrid.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraLayout.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraNavBar.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraPdfViewer.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraPivotGrid.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraPrinting.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraReports.v24.1.Extensions.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraReports.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraReports.v24.1.Web.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraRichEdit.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraScheduler.v24.1.Core.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraScheduler.v24.1.Extensions.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraScheduler.v24.1.Reporting.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraScheduler.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraSpellChecker.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraSpreadsheet.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraTreeList.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraVerticalGrid.v24.1.resources.dll" />
    <Content Include="bin\it\DevExpress.XtraWizard.v24.1.resources.dll" />
    <Content Include="bin\it\Microsoft.AspNet.Identity.Core.resources.dll" />
    <Content Include="bin\it\Microsoft.Data.Edm.resources.dll" />
    <Content Include="bin\it\Microsoft.Data.OData.resources.dll" />
    <Content Include="bin\it\Microsoft.Data.Services.Client.resources.dll" />
    <Content Include="bin\it\NeoSysLCS.Resources.resources.dll" />
    <Content Include="bin\it\System.Spatial.resources.dll" />
    <Content Include="bin\ThemeAssembly.dll" />
    <Content Include="Content\bootstrap-theme.css" />
    <Content Include="Content\bootstrap-theme.min.css" />
    <Content Include="Content\bootstrap.css" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="Content\DevExpress.css" />
    <Content Include="Content\font-awesome.min.css" />
    <Content Include="Content\images\check-square-o_26.png" />
    <Content Include="Content\images\copy-24.png" />
    <Content Include="Content\images\delete_cookies.png" />
    <Content Include="Content\images\delete_sign-26.png" />
    <Content Include="Content\images\download_26.png" />
    <Content Include="Content\images\edit-26.png" />
    <Content Include="Content\images\eraser-24.png" />
    <Content Include="Content\images\file-download2.PNG" />
    <Content Include="Content\images\file-excel-o_26.png" />
    <Content Include="Content\images\folder-open-o_26.png" />
    <Content Include="Content\images\key_security-24.png" />
    <Content Include="Content\images\link.png" />
    <Content Include="Content\images\pencil-26.png" />
    <Content Include="Content\images\Question_Mark.png" />
    <Content Include="Content\images\save-26.png" />
    <Content Include="Content\images\sms-logo-small-footer.png" />
    <Content Include="Content\images\upload_26.png" />
    <Content Include="Content\plugins\dataTables\dataTables.bootstrap.css" />
    <Content Include="Content\plugins\metisMenu\metisMenu.min.css" />
    <Content Include="Content\sb-admin.css" />
    <Content Include="Error\403.html" />
    <Content Include="Error\404.html" />
    <Content Include="favicon.ico" />
    <Content Include="fonts\fontawesome-webfont.svg" />
    <Content Include="fonts\glyphicons-halflings-regular.svg" />
    <Content Include="LEXPLUS-CMYK.png" />
    <Content Include="logo-neosys.gif" />
    <Content Include="Areas\Admin\Views\UsersAdmin\_CallbackPanel.cshtml" />
    <Content Include="Areas\Admin\Views\Auswertungen\AktuelleForderungen.cshtml" />
    <Content Include="Areas\Admin\Views\Auswertungen\_AktuelleForderungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\StandortObjekt\_NewStandortObjektGridViewPopup.cshtml" />
    <Content Include="Areas\Portal\Views\Kundendokument\KundendokumentSpaltenlabelsGridView.cshtml" />
    <Content Include="robots.txt" />
    <Content Include="fonts\glyphicons-halflings-regular.woff2" />
    <Content Include="fonts\glyphicons-halflings-regular.woff" />
    <Content Include="fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="fonts\glyphicons-halflings-regular.eot" />
    <Content Include="Content\bootstrap-theme.min.css.map" />
    <Content Include="Content\bootstrap-theme.css.map" />
    <Content Include="Content\bootstrap.min.css.map" />
    <Content Include="Content\bootstrap.css.map" />
    <Content Include="Scripts\bootstrap.js" />
    <Content Include="Scripts\bootstrap.min.js" />
    <Content Include="Areas\Admin\Views\Herausgeber\_HerausgeberUebersetzungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\StandortObjekt\_StandortObjektUebersetzungenGridView.cshtml" />
    <Content Include="Areas\Portal\Views\Kundendokument\KundendokumentPageControlCallbacksPartial.cshtml" />
    <Content Include="Areas\Admin\Views\Dashboard\DashboardPageControlCallbacksPartial.cshtml" />
    <Content Include="Scripts\helpers\SingleSelectionHelper.js" />
    <Content Include="Scripts\jquery.unobtrusive-ajax.js" />
    <Content Include="Scripts\jquery.unobtrusive-ajax.min.js" />
    <Content Include="Scripts\jquery-3.1.1.js" />
    <Content Include="Scripts\jquery-3.1.1.min.js" />
    <Content Include="Scripts\jquery-2.1.1.js" />
    <Content Include="Scripts\jquery-2.1.1.min.js" />
    <Content Include="Areas\Admin\Views\Kunden\_KundenStandortMenuPartialView.cshtml" />
    <Content Include="Areas\Portal\Views\KundendokumentStandortObjekt\KundendokumentStandortObjektGridView.cshtml" />
    <Content Include="Areas\Portal\Views\KundendokumentStandortObjekt\StandortObjektDetailGridView.cshtml" />
    <Content Include="Areas\Portal\Views\Kundendokument\_NewStandortObjektGridViewPopup.cshtml" />
    <Content Include="Areas\Portal\Views\Kundendokument\_RowSelectionPartial.cshtml" />
    <Content Include="Areas\Portal\Views\Kundendokument\KundendokumentErlassfassungenGridView.cshtml" />
    <Content Include="Areas\Portal\Views\StandortAdministration\KundendokumentPageControlCallbacksPartial.cshtml" />
    <Content Include="Areas\Portal\Views\StandortAdministration\Index.cshtml" />
    <Content Include="Areas\Admin\Views\UsersAdmin\_DeleteCookiesByAdmin.cshtml" />
    <Content Include="Areas\Admin\Views\UsersAdmin\DeleteCookieConfirmation.cshtml" />
    <Content Include="Areas\Portal\Views\Konzern\KonzernGridView.cshtml" />
    <Content Include="Areas\Portal\Views\Konzern\Index.cshtml" />
    <Content Include="Areas\Admin\Views\UsersAdmin\_UserStandortGridView.cshtml" />
    <Content Include="Areas\Portal\Views\Konzern\KonzernIndex.cshtml" />
    <Content Include="Areas\Portal\Views\StandortAdministration\Shortcuts.cshtml" />
    <Content Include="Areas\Portal\Views\StandortAdministration\ShortcutUserPopupContent.cshtml" />
    <Content Include="Areas\Portal\Views\StandortAdministration\_RowSelectionPartialUser.cshtml" />
    <Content Include="Areas\Portal\Views\Auswertung\Index.cshtml" />
    <Content Include="Areas\Portal\Views\Auswertung\_PieChartForderungenFullfilment.cshtml" />
    <Content Include="Areas\Portal\Views\Auswertung\_PieChartForderungenB.cshtml" />
    <Content Include="Areas\Portal\Views\Auswertung\StandortAuswertung.cshtml" />
    <Content Include="Areas\Portal\Views\Auswertung\StandortIndex.cshtml" />
    <Content Include="Areas\Portal\Views\Auswertung\StandortPageControlCallbacksPartial.cshtml" />
    <Content Include="Areas\Portal\Views\Auswertung\_PieChartForderungenRechtsbereiche.cshtml" />
    <Content Include="Areas\Portal\Views\Auswertung\_PieChartForderungenShortcut.cshtml" />
    <Content Include="Areas\Portal\Views\StandortAdministration\KundendokumentMasterGridView.cshtml" />
    <Content Include="Areas\Admin\Views\FAQ\Index.cshtml" />
    <Content Include="Areas\Admin\Views\FAQ\_FAQGridView.cshtml" />
    <Content Include="Areas\Admin\Views\FAQ\_FAQUebersetzungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\FAQ\FAQKategoriePopupContent.cshtml" />
    <Content Include="Areas\Admin\Views\FAQ\_RowSelectionPartialKategorie.cshtml" />
    <Content Include="Areas\Portal\Views\Shared\_FAQPopup.cshtml" />
    <Content Include="Areas\Admin\Views\Kommentar\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Kommentar\_KommentareGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Kommentar\_KommentarUebersetzungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\KommentarObjekte\_KommentarObjektePartial.cshtml" />
    <Content Include="Areas\Admin\Views\KommentarObjekte\_ObjekteSelectionPartial.cshtml" />
    <Content Include="Areas\Admin\Views\KommentarObjekte\Index.cshtml" />
    <Content Include="Areas\Admin\Views\KommentarRechtsbereiche\_KommentarRechtsbereichePartial.cshtml" />
    <Content Include="Areas\Admin\Views\KommentarRechtsbereiche\_RechtsbereicheSelectionPartial.cshtml" />
    <Content Include="Areas\Admin\Views\KommentarRechtsbereiche\Index.cshtml" />
    <Content Include="Areas\Admin\Views\KommentarErlasse\_KommentarErlassePartial.cshtml" />
    <Content Include="Areas\Admin\Views\KommentarErlasse\_ErlasseSelectionPartial.cshtml" />
    <Content Include="Areas\Admin\Views\KommentarErlasse\Index.cshtml" />
    <Content Include="Areas\Portal\Views\Administration\AdministrationPageControllCallbacksPartial.cshtml" />
    <Content Include="Areas\Portal\Views\Administration\Index.cshtml" />
    <Content Include="Areas\Portal\Views\LegalCompliance\_LegalCompliance.cshtml" />
    <Content Include="Areas\Admin\Views\UsersAdmin\_ChangePasswordByAdminSelection.cshtml" />
    <Content Include="Areas\Admin\Views\UsersAdmin\_ChangePasswordByAdminAutomatic.cshtml" />
    <Content Include="Areas\Portal\Views\LegalCompliance\DatePicker.cshtml" />
    <Content Include="Areas\Portal\Views\Kundendokument\_ExportPartialPortalErlassfassung.cshtml" />
    <Content Include="Areas\Portal\Views\Konzern\_ExportPartialPortalKonzern.cshtml" />
    <Content Include="Areas\Admin\Views\ErlassRechtsbereiche\_ErlassRechtsbereichePartial.cshtml" />
    <Content Include="Areas\Admin\Views\ErlassRechtsbereiche\_RechtsbereichSelectionPartial.cshtml" />
    <Content Include="Areas\Admin\Views\ErlassRechtsbereiche\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\_KundenPageControlCallbacksPartial.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\_KundenAnalysePartialView.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\_KundenInaktivPartialView.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\_KundenAllPartialView.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\_KundenOfferPartialView.cshtml" />
    <Content Include="Areas\Admin\Views\ToDoList\Index.cshtml" />
    <Content Include="Areas\Admin\Views\ToDoList\_ToDoListGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Kommentar\KommentarUploadPopupContent.cshtml" />
    <Content Include="Areas\Admin\Views\Evaluations\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Evaluations\_EvaluationsPageControlCallbacksPartial.cshtml" />
    <Content Include="Areas\Admin\Views\Evaluations\_EvaluationsUpdatesPerMonthPartialView.cshtml" />
    <Content Include="Areas\Admin\Views\Evaluations\_EvaluationsDaysOfDelayPartialView.cshtml" />
    <Content Include="Areas\Admin\Views\Evaluations\_EvaluationsAmountOfDelayedUpdatesPartialView.cshtml" />
    <Content Include="Areas\Admin\Views\Evaluations\_EvaluationsOfferVolumesNewCustomerPartialView.cshtml" />
    <Content Include="Areas\Admin\Views\Evaluations\_EvaluationsOfferVolumesExistingCustomerPartialView.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\_KundenOfferInactivePartialView.cshtml" />
    <Content Include="NeoSysLCS.Site.wpp.targets" />
    <Content Include="Areas\Admin\Views\FAQ\FaqUploadPopupContent.cshtml" />
    <Content Include="Areas\Admin\Views\Kommentar\_KommentarPageControlCallbacksPartial.cshtml" />
    <Content Include="Areas\Portal\Views\Dashboard\_DashboardPageControlCallbacksPartial.cshtml" />
    <Content Include="Areas\Portal\Views\Dashboard\_ErfuellungsgradePartialView.cshtml" />
    <Content Include="Areas\Portal\Views\Dashboard\_AllgemeineNewsPartialView.cshtml" />
    <Content Include="Areas\Portal\Views\Dashboard\_VernehmlassungPartialView.cshtml" />
    <Content Include="Areas\Portal\Views\Dashboard\_GesetzesaenderungenPartialView.cshtml" />
    <Content Include="Areas\Portal\Views\Dashboard\_KommentarePartialView.cshtml" />
    <Content Include="Areas\Admin\Views\CustomerNews\_CustomerNewsGridView.cshtml" />
    <Content Include="Areas\Admin\Views\CustomerNews\Index.cshtml" />
    <Content Include="Areas\Admin\Views\CustomerNews\_CustomerNewsTranslationsGridView.cshtml" />
    <Content Include="Areas\Admin\Views\CustomerNews\CustomerUploadPopupContent.cshtml" />
    <Content Include="Areas\Admin\Views\Consultation\_ConsultationGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Consultation\_ConsultationUebersetzungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Consultation\Index.cshtml" />
    <Content Include="Areas\Admin\Views\ConsultationErlasse\_ErlasseSelectionPartial.cshtml" />
    <Content Include="Areas\Admin\Views\ConsultationErlasse\_ConsultationErlassePartial.cshtml" />
    <Content Include="Areas\Admin\Views\ConsultationErlasse\Index.cshtml" />
    <Content Include="Areas\Portal\Views\Downloadbereich\_AllDocumentsGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\_KundenNewsletterPartialView.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\UploadStandortBerichtPopupContent.cshtml" />
    <Content Include="Areas\Portal\Views\Downloadbereich\_NewsletterHistoryGridView.cshtml" />
    <Content Include="Areas\Portal\Views\Downloadbereich\AllDocuments.cshtml" />
    <Content Include="Areas\Portal\Views\Downloadbereich\StandorteMitBericht.cshtml" />
    <Content Include="Areas\Portal\Views\Standorte\StandorteMergedGridView.cshtml" />
    <Content Include="Areas\Portal\Views\Downloadbereich\_StandorteMitBerichtGridView.cshtml" />
    <Content Include="Areas\Portal\Views\Downloadbereich\NewsletterHistory.cshtml" />
    <Content Include="Areas\Admin\Views\Checklist\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Checklist\_ChecklistGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Checklist\_ChecklistQuestionsGridView.cshtml" />
    <Content Include="Areas\Portal\Views\Kundendokument\KundendokumentChecklisteGridView.cshtml" />
    <Content Include="Areas\Portal\Views\KundendokumentStandortObjekt\AllStandortObjekteGridView.cshtml" />
    <Content Include="Areas\Portal\Views\Kundendokument\KundendokumentChecklistQuestionsGridView.cshtml" />
    <Content Include="Areas\Admin\Views\KommentarHauptErlass\_HauptErlassSelectionPartial.cshtml" />
    <Content Include="Areas\Admin\Views\KommentarHauptErlass\_KommentarHauptErlassPartial.cshtml" />
    <Content Include="Areas\Admin\Views\KommentarHauptErlass\Index.cshtml" />
    <Content Include="Areas\Portal\Views\Massnahme\Index.cshtml" />
    <Content Include="Areas\Portal\Views\Massnahme\_NeuMassnahmenPartialView.cshtml" />
    <Content Include="Areas\Portal\Views\Massnahme\_ErledigtMassnahmenPartialView.cshtml" />
    <Content Include="Areas\Portal\Views\Massnahme\_MassnahmePageControlCallbacksPartial.cshtml" />
    <Content Include="Areas\Portal\Views\Shared\_MassnahmeErfassenPopup.cshtml" />
    <Content Include="Areas\Admin\Views\ObjektObligation\_ObligationGridView.cshtml" />
    <Content Include="Areas\Admin\Views\ObjektObligation\Index.cshtml" />
    <Content Include="Areas\Admin\Views\ObjektObligation\_ObligationUebersetzungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\ObligationForderungsversions\_ForderungsversionsSelectionPartial.cshtml" />
    <Content Include="Areas\Admin\Views\ObligationForderungsversions\_ObligationForderungsversionsPartial.cshtml" />
    <Content Include="Areas\Admin\Views\ObligationForderungsversions\Index.cshtml" />
    <Content Include="Areas\Portal\Views\Shared\_MassnahmeConfirmationPopup.cshtml" />
    <Content Include="Areas\Portal\Views\Massnahme\_MassnahmeUebersetzungenGridView.cshtml" />
    <Content Include="Areas\Portal\Views\MassnahmeShortcut\_ShortcutSelectionPartial.cshtml" />
    <Content Include="Areas\Portal\Views\MassnahmeShortcut\_MassnahmeShortcutPartial.cshtml" />
    <Content Include="Areas\Portal\Views\MassnahmeShortcut\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Obligation\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Obligation\_ObligationGridView.cshtml" />
    <Content Include="Areas\Portal\Views\Kundendokument\ForderungsversionChecklistGridView.cshtml" />
    <Content Include="Areas\Portal\Views\Shared\_MassnahmeErfassenMultiplePopup.cshtml" />
    <Content Include="Areas\Admin\Views\PrivacyPolicy\_PrivacyPolicyGridView.cshtml" />
    <Content Include="Areas\Admin\Views\PrivacyPolicy\_PrivacyPolicyUebersetzungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\PrivacyPolicy\PrivacyPolicyUploadPopupContent.cshtml" />
    <Content Include="Areas\Admin\Views\PrivacyPolicy\Index.cshtml" />
    <Content Include="Areas\Admin\Views\StandortBerichte\_StandortBerichteGridView.cshtml" />
    <Content Include="Areas\Admin\Views\StandortBerichte\_PrivacyPolicyUebersetzungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\StandortBerichte\Index.cshtml" />
    <Content Include="Areas\Admin\Views\StandortBerichte\StandortBerichteUploadPopupContent.cshtml" />
    <Content Include="Areas\Portal\Views\MasterKundendokument\_ExportPartialPortalForderungen.cshtml" />
    <Content Include="Areas\Portal\Views\MasterKundendokument\_ShortcutPopup.cshtml" />
    <Content Include="Areas\Portal\Views\MasterKundendokument\ForderungsversionAdditionalDataGridView.cshtml" />
    <Content Include="Areas\Portal\Views\MasterKundendokument\ForderungsversionChecklistGridView.cshtml" />
    <Content Include="Areas\Portal\Views\MasterKundendokument\ForderungsversionCompareDetailGridView.cshtml" />
    <Content Include="Areas\Portal\Views\MasterKundendokument\ForderungsversionDetailGridView.cshtml" />
    <Content Include="Areas\Portal\Views\MasterKundendokument\Index.cshtml" />
    <Content Include="Areas\Portal\Views\MasterKundendokument\KundendokumentArchivGridView.cshtml" />
    <Content Include="Areas\Portal\Views\MasterKundendokument\KundendokumentErlassfassungenGridView.cshtml" />
    <Content Include="Areas\Portal\Views\MasterKundendokument\KundendokumentForderungenGridView.cshtml" />
    <Content Include="Areas\Portal\Views\MasterKundendokument\KundendokumentPageControlCallbacksPartial.cshtml" />
    <Content Include="Areas\Portal\Views\MasterKundendokument\KundendokumentSpaltenlabelsGridView.cshtml" />
    <Content Include="Areas\Portal\Views\MasterKundendokument\_NewStandortObjektGridViewPopup.cshtml" />
    <Content Include="Areas\Portal\Views\MasterKundendokument\_RowSelectionPartial.cshtml" />
    <Content Include="Areas\Portal\Views\MasterKundendokument\_ExportPartialPortalErlassfassung.cshtml" />
    <Content Include="local.config" />
    <None Include="Properties\PublishProfiles\lcspreview - Web Deploy.pubxml" />
    <Content Include="Areas\Admin\Views\Kunden\_KundenStandortArchivePartialView.cshtml" />
    <None Include="Properties\PublishProfiles\lcsprod19 - Web Deploy.pubxml" />
    <None Include="Properties\PublishProfiles\SwisscomTest - Web Deploy.pubxml" />
    <None Include="Scripts\jquery.validate-vsdoc.js" />
    <Content Include="Scripts\jquery.validate.js" />
    <Content Include="Scripts\jquery.validate.min.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.min.js" />
    <Content Include="Static\ping.html" />
    <Content Include="Scripts\helpers\BatchEditDeleteHelper.js" />
    <Content Include="Scripts\helpers\SelectionHelper.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.bootstrap.js" />
    <Content Include="Scripts\jquery.validate.unobtrusive.bootstrap.min.js" />
    <Content Include="Scripts\modernizr-2.8.3.js" />
    <Content Include="Scripts\plugins\dataTables\dataTables.bootstrap.js" />
    <Content Include="Scripts\plugins\metisMenu\jquery.metisMenu.js" />
    <Content Include="Scripts\sb-admin.js" />
    <Content Include="Theme\ModernoNeosys\Chart\Loading.gif" />
    <Content Include="Theme\ModernoNeosys\Chart\styles.css" />
    <Content Include="Theme\ModernoNeosys\Editors\edtProgressPositionBack.png" />
    <Content Include="Theme\ModernoNeosys\Editors\edtTBDecBtn.png" />
    <Content Include="Theme\ModernoNeosys\Editors\edtTBDecBtnDisabled.png" />
    <Content Include="Theme\ModernoNeosys\Editors\edtTBDecBtnHover.png" />
    <Content Include="Theme\ModernoNeosys\Editors\edtTBDecBtnPressed.png" />
    <Content Include="Theme\ModernoNeosys\Editors\edtTBIncBtn.png" />
    <Content Include="Theme\ModernoNeosys\Editors\edtTBIncBtnDisabled.png" />
    <Content Include="Theme\ModernoNeosys\Editors\edtTBIncBtnHover.png" />
    <Content Include="Theme\ModernoNeosys\Editors\edtTBIncBtnPressed.png" />
    <Content Include="Theme\ModernoNeosys\Editors\edtTrackBarDoubleLargeTickH.gif" />
    <Content Include="Theme\ModernoNeosys\Editors\edtTrackBarDoubleLargeTickV.gif" />
    <Content Include="Theme\ModernoNeosys\Editors\edtTrackBarDoubleSmallTickH.gif" />
    <Content Include="Theme\ModernoNeosys\Editors\edtTrackBarDoubleSmallTickV.gif" />
    <Content Include="Theme\ModernoNeosys\Editors\edtTrackBarLargeTickH.gif" />
    <Content Include="Theme\ModernoNeosys\Editors\edtTrackBarLargeTickV.gif" />
    <Content Include="Theme\ModernoNeosys\Editors\edtTrackBarSmallTickH.gif" />
    <Content Include="Theme\ModernoNeosys\Editors\edtTrackBarSmallTickV.gif" />
    <Content Include="Theme\ModernoNeosys\Editors\sprite.css" />
    <Content Include="Theme\ModernoNeosys\Editors\sprite.png" />
    <Content Include="Theme\ModernoNeosys\Editors\styles.css" />
    <Content Include="Theme\ModernoNeosys\GridView\gvLoadingOnStatusBar.gif" />
    <Content Include="Theme\ModernoNeosys\GridView\sprite.css" />
    <Content Include="Theme\ModernoNeosys\GridView\sprite.png" />
    <Content Include="Theme\ModernoNeosys\GridView\spriteAccessible.css" />
    <Content Include="Theme\ModernoNeosys\GridView\spriteAccessible.png" />
    <Content Include="Theme\ModernoNeosys\GridView\styles.css" />
    <Content Include="Theme\ModernoNeosys\HtmlEditor\sprite.css" />
    <Content Include="Theme\ModernoNeosys\HtmlEditor\sprite.png" />
    <Content Include="Theme\ModernoNeosys\HtmlEditor\styles.css" />
    <Content Include="Theme\ModernoNeosys\PivotGrid\sprite.css" />
    <Content Include="Theme\ModernoNeosys\PivotGrid\sprite.png" />
    <Content Include="Theme\ModernoNeosys\PivotGrid\styles.css" />
    <Content Include="Theme\ModernoNeosys\PivotGrid\tvNodeLoading.gif" />
    <Content Include="Theme\ModernoNeosys\Scheduler\sprite.css" />
    <Content Include="Theme\ModernoNeosys\Scheduler\sprite.png" />
    <Content Include="Theme\ModernoNeosys\Scheduler\styles.css" />
    <Content Include="Theme\ModernoNeosys\SpellChecker\scErrorUnderline.gif" />
    <Content Include="Theme\ModernoNeosys\SpellChecker\styles.css" />
    <Content Include="Theme\ModernoNeosys\Spreadsheet\sprite.css" />
    <Content Include="Theme\ModernoNeosys\Spreadsheet\sprite.png" />
    <Content Include="Theme\ModernoNeosys\Spreadsheet\styles.css" />
    <Content Include="Theme\ModernoNeosys\Spreadsheet\TouchResize.png" />
    <Content Include="Theme\ModernoNeosys\TreeList\sprite.css" />
    <Content Include="Theme\ModernoNeosys\TreeList\sprite.png" />
    <Content Include="Theme\ModernoNeosys\TreeList\styles.css" />
    <Content Include="Theme\ModernoNeosys\Web\DocumentViewerSprite.css" />
    <Content Include="Theme\ModernoNeosys\Web\DocumentViewerSprite.png" />
    <Content Include="Theme\ModernoNeosys\Web\fmFileCheck.png" />
    <Content Include="Theme\ModernoNeosys\Web\HESprite.css" />
    <Content Include="Theme\ModernoNeosys\Web\HESprite.png" />
    <Content Include="Theme\ModernoNeosys\Web\igLoading.gif" />
    <Content Include="Theme\ModernoNeosys\Web\igNavBtnsBack.png" />
    <Content Include="Theme\ModernoNeosys\Web\isLoading.gif" />
    <Content Include="Theme\ModernoNeosys\Web\Loading.gif" />
    <Content Include="Theme\ModernoNeosys\Web\pcModalBack.gif" />
    <Content Include="Theme\ModernoNeosys\Web\rcMapImage.png" />
    <Content Include="Theme\ModernoNeosys\Web\splResizingPointer.gif" />
    <Content Include="Theme\ModernoNeosys\Web\sprite.css" />
    <Content Include="Theme\ModernoNeosys\Web\sprite.png" />
    <Content Include="Theme\ModernoNeosys\Web\SSSprite.css" />
    <Content Include="Theme\ModernoNeosys\Web\SSSprite.png" />
    <Content Include="Theme\ModernoNeosys\Web\styles.css" />
    <Content Include="Theme\ModernoNeosys\Web\tvNodeLoading.gif" />
    <Content Include="Theme\ModernoNeosys\XtraReports\pageBottomBorder.png" />
    <Content Include="Theme\ModernoNeosys\XtraReports\pageLeftBorder.png" />
    <Content Include="Theme\ModernoNeosys\XtraReports\pageRightBorder.png" />
    <Content Include="Theme\ModernoNeosys\XtraReports\pageTopBorder.png" />
    <Content Include="Theme\ModernoNeosys\XtraReports\sprite.css" />
    <Content Include="Theme\ModernoNeosys\XtraReports\sprite.png" />
    <Content Include="Theme\ModernoNeosys\XtraReports\styles.css" />
    <Content Include="Theme\ThemeAssembly.dll" />
    <Content Include="Upload\Default.html" />
    <Content Include="Upload\ErlassImport\DefaultErlass.html" />
    <Content Include="Upload\FAQ\DefaultFaq.html" />
    <Content Include="Upload\Kommentar\DefaultKommentar.html" />
    <Content Include="Upload\Newsletter\DefaultNewsletter.html" />
    <Content Include="Upload\StandortBericht\DefaultStandortBericht.html" />
    <Content Include="Views\Account\Login.cshtml" />
    <Content Include="fonts\FontAwesome.otf" />
    <Content Include="fonts\fontawesome-webfont.eot" />
    <Content Include="fonts\fontawesome-webfont.ttf" />
    <Content Include="fonts\fontawesome-webfont.woff" />
    <Content Include="Areas\Admin\Views\Herausgeber\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Objekte\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Objekte\ObjekteGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\_KundenAktualisierungPartialView.cshtml" />
    <Content Include="Areas\Admin\Views\Herausgeber\_HerausgeberGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Objekte\ObjektUebersetzungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\UsersAdmin\_UserAdminViewPartial.cshtml" />
    <Content Include="Areas\Admin\Views\RolesAdmin\_RolesAdminViewPartial.cshtml" />
    <Content Include="Areas\Admin\Views\Rechtsbereiche\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Rechtsbereiche\RechtsbereicheGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Rechtsbereiche\RechtsbereichUebersetzungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Erlasstypen\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Erlasstypen\ErlasstypenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Erlasstypen\ErlasstypUebersetzungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Erlasse\_ErlasseGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Erlasse\_ErlassUebersetzungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Erlasse\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Erlassfassungen\_ErlassfassungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Erlassfassungen\_ErlassfassungUebersetzungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Pflichten\_PflichtenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Pflichten\_PflichtUebersetzungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Pflichten\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Pflichten\_RowSelectionPartial.cshtml" />
    <Content Include="Areas\Admin\Views\Pflichten\_PflichtenObjektPartial.cshtml" />
    <Content Include="Areas\Admin\Views\Artikel\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Artikel\_ArtikelGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Artikel\_ArtikelUebersetzungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Erlassfassungen\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Objektkategorien\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Objektkategorien\ObjektkategorienTreeList.cshtml" />
    <Content Include="Areas\Admin\Views\Objektkategorien\ObjektkategorienGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Objektkategorien\ObjektkategorieUebersetzungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\_KundenStandortPartialView.cshtml" />
    <Content Include="Areas\Admin\Views\Forderungen\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Forderungen\_ForderungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Forderungen\_ForderungUebersetzungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Objektkategorien\ObjektkategorienNewForm.cshtml" />
    <Content Include="Areas\Admin\Views\ForderungObjekte\_ForderungObjektePartial.cshtml" />
    <Content Include="Areas\Admin\Views\ForderungObjekte\_ObjekteSelectionPartial.cshtml" />
    <Content Include="Areas\Admin\Views\ForderungObjekte\Index.cshtml" />
    <Content Include="Areas\Admin\Views\ForderungRechtsbereiche\_ForderungRechtsbereichePartial.cshtml" />
    <Content Include="Areas\Admin\Views\ForderungRechtsbereiche\_RechtsbereichSelectionPartial.cshtml" />
    <Content Include="Areas\Admin\Views\ForderungRechtsbereiche\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Forderungen\NewVersion.cshtml" />
    <Content Include="Areas\Admin\Views\Forderungen\_NewVersionPrevious.cshtml" />
    <Content Include="Areas\Admin\Views\Forderungen\_NewVersionNew.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\_KundenKontaktePartialView.cshtml" />
    <Content Include="Areas\Admin\Views\Rechtsbereiche\_RechtsbereichePartialLookupView.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\_RowSelectionPartial.cshtml" />
    <Content Include="Areas\Admin\Views\ObjektForderungen\_ForderungSelectionPartial.cshtml" />
    <Content Include="Areas\Admin\Views\ObjektForderungen\_ObjektForderungenPartial.cshtml" />
    <Content Include="Areas\Admin\Views\ObjektPflichten\_PflichtSelectionPartial.cshtml" />
    <Content Include="Areas\Admin\Views\ObjektPflichten\_ObjektPflichtenPartial.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\_KundenKontakteRechtsbereichePopupContent.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\_KundenStandorteKontaktePopupContent.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\_RowSelectionPartialKontakte.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\_KundenStandorteHerausgeberPopupContent.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\_RowSelectionPartialHerausgeber.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\_KundenStandorteSprachePopupContent.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\_RowSelectionPartialSprache.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\_RowSelectionPartialRechtsbereiche.cshtml" />
    <Content Include="Areas\Admin\Views\Kunden\_KundenStandorteRechtsbereichePopupContent.cshtml" />
    <Content Include="Areas\Admin\Views\StandortObjekt\Index.cshtml" />
    <Content Include="Areas\Admin\Views\StandortObjekt\_StandortObjektGridView.cshtml" />
    <Content Include="Areas\Portal\Views\IndividuelleForderungen\Index.cshtml" />
    <Content Include="Areas\Portal\Views\IndividuelleForderungen\IndividuelleForderungenGridView.cshtml" />
    <Content Include="Areas\Portal\Views\Standorte\Index.cshtml" />
    <Content Include="Areas\Portal\Views\Standorte\StandorteGridView.cshtml" />
    <Content Include="Areas\Portal\Views\_ViewStart.cshtml" />
    <Content Include="Areas\Portal\Views\Kundendokument\Index.cshtml" />
    <Content Include="Areas\Portal\Views\Kundendokument\KundendokumentForderungenGridView.cshtml" />
    <Content Include="Areas\Portal\Views\Kundendokument\KundendokumentArchivGridView.cshtml" />
    <Content Include="Areas\Portal\Views\Kundendokument\KundendokumentPflichtenGridView.cshtml" />
    <Content Include="Areas\Portal\Views\Kundendokument\ErlassDetailGridView.cshtml" />
    <Content Include="Areas\Portal\Views\Kundendokument\ErlassfassungDetailGridView.cshtml" />
    <Content Include="Areas\Portal\Views\Kundendokument\ForderungsversionDetailGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Kundendokumente\Index.cshtml" />
    <Content Include="Areas\Admin\Views\KundendokumenteQs\KundendokumenteQs.cshtml" />
    <Content Include="Areas\Admin\Views\Kundendokumente\_KundendokumenteGridView.cshtml" />
    <Content Include="Areas\Admin\Views\KundendokumenteQs\_KundendokumenteQsPageControl.cshtml" />
    <Content Include="Areas\Admin\Views\KundendokumenteQs\_KundendokumentForderungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\KundendokumenteQs\_KundendokumentFreigabePartial.cshtml" />
    <Content Include="Areas\Admin\Views\KundendokumenteQs\_KundendokumentPflichtenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\KundendokumenteQs\_KundendokumentQsFreigabePartial.cshtml" />
    <Content Include="Areas\Admin\Views\StandortObjekt\_RowSelectionPartial.cshtml" />
    <Content Include="Areas\Admin\Views\KundendokumentUpdate\_SelectErlassfassungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\KundendokumentUpdate\_SelectForderungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\KundendokumentUpdate\_SelectPflichtenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\KundendokumentUpdate\_StandortobjektForderungenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\KundendokumentUpdate\_StandortobjektPflichtenGridView.cshtml" />
    <Content Include="Areas\Admin\Views\KundendokumentUpdate\SelectErlassfassungen.cshtml" />
    <Content Include="Areas\Admin\Views\KundendokumentUpdate\SelectForderungen.cshtml" />
    <Content Include="Areas\Admin\Views\KundendokumentUpdate\SelectPflichten.cshtml" />
    <Content Include="Areas\Admin\Views\KundendokumentUpdate\StandortobjektForderungen.cshtml" />
    <Content Include="Areas\Admin\Views\KundendokumentUpdate\StandortobjektPflichten.cshtml" />
    <Content Include="Areas\Admin\Views\Dashboard\_KundeGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Dashboard\_ObjektGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Dashboard\_ErlassGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Dashboard\_StandortGridView.cshtml" />
    <Content Include="Areas\Admin\Views\KundendokumenteQs\_KundendokumentErlassfassungenGridView.cshtml" />
    <Content Include="Areas\Portal\Views\Kundendokument\ForderungsversionCompareDetailGridView.cshtml" />
    <Content Include="Areas\Admin\Views\UsersAdmin\_ChangePasswordByAdmin.cshtml" />
    <Content Include="Areas\Portal\Views\Standorte\StandortOverview.cshtml" />
    <Content Include="Areas\Portal\Views\Kundendokument\ForderungsversionAdditionalDataGridView.cshtml" />
    <Content Include="Areas\Admin\Views\KundendokumenteQs\_ErlassfassungPopup.cshtml" />
    <Content Include="Areas\Admin\Views\Auswertungen\Objekt2KundenAssignment.cshtml" />
    <Content Include="Areas\Admin\Views\Auswertungen\_Objekt2KundenAssignmentGridView.cshtml" />
    <Content Include="Areas\Admin\Views\Kundeninformation\Index.cshtml" />
    <Content Include="Areas\Admin\Views\Kundeninformation\_KundeninformationUebersetzungenGridView.cshtml" />
    <Content Include="Areas\Portal\Views\Dashboard\_PieChartForderungenFullfilment.cshtml" />
    <None Include="Properties\PublishProfiles\WiBint - Web Deploy.pubxml">
      <SubType>Designer</SubType>
    </None>
    <None Include="Properties\PublishProfiles\lcstest - Web Deploy.pubxml" />
    <Content Include="Views\Emails\newsletter-cover-image.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Views\Emails\newsletter-logo.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Views\Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Theme\ModernoNeosys\ASPxButton.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxButtonEdit.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxCalendar.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxCallbackPanel.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxCaptcha.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxCheckBox.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxCheckBoxList.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxCloudControl.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxColorEdit.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxComboBox.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxDataView.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxDateEdit.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxDateNavigator.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxDockPanel.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxDropDownEdit.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxFileManager.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxFilterControl.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxFormLayout.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxGridLookup.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxGridView.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxHeadline.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxHtmlEditor.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxHyperLink.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxImageGallery.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxImageSlider.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxImageZoom.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxImageZoomNavigator.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxLabel.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxListBox.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxLoadingPanel.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxMemo.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxMenu.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxNavBar.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxNewsControl.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxPageControl.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxPager.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxPivotGrid.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxPopupControl.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxPopupMenu.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxProgressBar.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxRadioButton.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxRadioButtonList.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxRatingControl.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxRibbon.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxRoundPanel.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxScheduler.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxSiteMapControl.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxSpellChecker.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxSpinEdit.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxSplitter.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxSpreadsheet.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxTabControl.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxTextBox.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxTimeEdit.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxTitleIndex.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxTokenBox.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxTrackBar.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxTreeList.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxTreeView.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxUploadControl.skin" />
    <Content Include="Theme\ModernoNeosys\ASPxValidationSummary.skin" />
    <Content Include="Theme\ModernoNeosys\MonthEdit.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxButton.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxButtonEdit.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxCalendar.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxCallbackPanel.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxCaptcha.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxChartControl.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxCheckBox.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxCheckBoxList.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxColorEdit.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxComboBox.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxDataView.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxDateEdit.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxDockPanel.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxDocumentViewer.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxDropDownEdit.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxFileManager.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxFormLayout.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxGridLookup.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxGridView.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxHtmlEditor.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxHyperLink.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxImageGallery.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxImageSlider.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxImageZoom.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxImageZoomNavigator.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxLabel.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxListBox.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxLoadingPanel.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxMemo.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxMenu.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxNavBar.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxPageControl.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxPivotGrid.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxPopupControl.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxPopupMenu.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxProgressBar.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxRadioButton.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxRadioButtonList.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxRatingControl.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxReportToolbar.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxReportViewer.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxRibbon.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxRoundPanel.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxScheduler.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxSpinEdit.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxSplitter.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxSpreadsheet.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxTabControl.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxTextBox.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxTimeEdit.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxTokenBox.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxTrackBar.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxTreeList.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxTreeView.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxUploadControl.skin" />
    <Content Include="Theme\ModernoNeosys\MVCxValidationSummary.skin" />
    <Content Include="Theme\ModernoNeosys\Parameters.config" />
    <Content Include="Theme\ModernoNeosys\RecurrenceTypeEdit.skin" />
    <Content Include="Theme\ModernoNeosys\ReportDocumentMap.skin" />
    <Content Include="Theme\ModernoNeosys\ReportParametersPanel.skin" />
    <Content Include="Theme\ModernoNeosys\ReportToolbar.skin" />
    <Content Include="Theme\ModernoNeosys\ReportViewer.skin" />
    <Content Include="Theme\ModernoNeosys\WebChartControl.skin" />
    <Content Include="Theme\ModernoNeosys\WeekDaysEdit.skin" />
    <Content Include="Theme\ModernoNeosys\WeekOfMonthEdit.skin" />
    <Content Include="Views\Emails\Web.config" />
    <Content Include="Views\Emails\RegEmail_DE.cshtml" />
    <Content Include="Views\Shared\Lockout.cshtml" />
    <Content Include="Views\KundendokumentImportExport\_ImportPartial.cshtml" />
    <Content Include="Views\KundendokumentImportExport\_ExportPartial.cshtml" />
    <Content Include="Scripts\jquery.validate.unobtrusive.bootstrap.min.js.map" />
    <Content Include="Views\Emails\_ViewStart.cshtml" />
    <Content Include="Areas\Admin\Views\UsersAdmin\ResetPasswordConfirmation.cshtml" />
    <Content Include="Views\Shared\_HtmlEditPopup.cshtml" />
    <Content Include="Views\Home\Impressum.cshtml" />
    <Content Include="Views\Home\HttpError404.cshtml" />
    <Content Include="Views\Home\HttpError403.cshtml" />
    <Content Include="Views\Home\HttpError500.cshtml" />
    <Content Include="Views\Home\GeneralError.cshtml" />
    <Content Include="Views\KundendokumentImportExport\_ExportPartialPortal.cshtml" />
    <Content Include="Views\Emails\PasswordReset_DE.cshtml" />
    <Content Include="Views\KundendokumentImportExport\_QuenticExportPartial.cshtml" />
    <Content Include="Views\Emails\RegEmail_FR.cshtml" />
    <Content Include="Views\Emails\RegEmail_IT.cshtml" />
    <Content Include="Views\Home\PasswordReset.cshtml" />
    <Content Include="Views\Home\PasswordResetSuccess.cshtml" />
    <Content Include="Areas\Portal\Views\Kundendokument\_ExportPartialPortalForderungen.cshtml" />
    <Content Include="Views\Emails\PasswordReset_FR.cshtml" />
    <Content Include="Views\Emails\PasswordReset_EN.cshtml" />
    <Content Include="Views\Emails\PasswordReset_IT.cshtml" />
    <Content Include="Views\Shared\_ReadOnlyPopup.cshtml" />
    <Content Include="Views\Emails\Newsletter_DE.cshtml" />
    <Content Include="Views\Emails\Newsletter_IT.cshtml" />
    <Content Include="Views\Emails\Newsletter_FR.cshtml" />
    <Content Include="Views\Emails\Newsletter_EN.cshtml" />
    <Content Include="Views\ErlassImport\_ImportPartial.cshtml" />
    <Content Include="Views\Emails\MassnahmeReminder_DE.cshtml" />
    <Content Include="Views\Emails\MassnahmeReminder_FR.cshtml" />
    <Content Include="Views\Emails\MassnahmeReminder_IT.cshtml" />
    <Content Include="Views\Emails\MassnahmeReminder_EN.cshtml" />
    <Content Include="Views\Emails\MassnahmeCreated_DE.cshtml" />
    <Content Include="Views\Emails\MassnahmeCreated_EN.cshtml" />
    <Content Include="Views\Emails\MassnahmeCreated_FR.cshtml" />
    <Content Include="Views\Emails\MassnahmeCreated_IT.cshtml" />
    <Content Include="Views\PrivacyPolicyConfirmation\_PrivacyPolicyConfirmationGridView.cshtml" />
    <Content Include="Views\PrivacyPolicyConfirmation\Index.cshtml" />
    <Content Include="Views\Emails\RegReminderBulkEmail_DE.cshtml" />
	<None Include="Web.lcspreview - Web Deploy.config">
		<DependentUpon>Web.config</DependentUpon>
	</None>
    <None Include="Web.lcsprod19 - Web Deploy.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.lcstest - Web Deploy.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
    <None Include="Web.WiBint - Web Deploy.config">
      <DependentUpon>Web.config</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Global.asax" />
    <Content Include="Content\Site.css" />
    <Content Include="Areas\Admin\Views\web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Areas\Portal\Views\web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Areas\Admin\Views\Dashboard\Index.cshtml" />
    <Content Include="Areas\Portal\Views\Dashboard\Index.cshtml" />
    <Content Include="Scripts\respond.js" />
    <Content Include="Scripts\respond.matchmedia.addListener.js" />
    <Content Include="Scripts\respond.matchmedia.addListener.min.js" />
    <Content Include="Scripts\respond.min.js" />
    <Content Include="Scripts\_references.js" />
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Shared\Error.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Views\Home\About.cshtml" />
    <Content Include="Views\Home\Contact.cshtml" />
    <Content Include="Views\Home\Index.cshtml" />
    <Content Include="Views\Shared\_LoginPartial.cshtml" />
    <Content Include="Views\Manage\ChangePassword.cshtml" />
    <Content Include="Views\Manage\Index.cshtml" />
    <Content Include="Areas\Admin\Views\RolesAdmin\Index.cshtml" />
    <Content Include="Areas\Admin\Views\UsersAdmin\Index.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="Areas\Admin\Models\" />
    <Folder Include="Areas\Admin\Views\Kontakte\" />
    <Folder Include="Areas\Admin\Views\Shared\" />
    <Folder Include="Areas\Portal\Models\" />
    <Folder Include="Areas\Portal\Views\Auswertung\Administration\" />
    <Folder Include="Views\Base\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="packages.config">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\NeoSysLCS.DomainModel\NeoSysLCS.DomainModel.csproj">
      <Project>{ef2effe2-2175-41a2-ad56-d65721c68e5a}</Project>
      <Name>NeoSysLCS.DomainModel</Name>
    </ProjectReference>
    <ProjectReference Include="..\NeoSysLCS.Repositories\NeoSysLCS.Repositories.csproj">
      <Project>{c57a72da-5af6-42de-86d3-437bac506914}</Project>
      <Name>NeoSysLCS.Repositories</Name>
    </ProjectReference>
    <ProjectReference Include="..\NeoSysLCS.Resources\NeoSysLCS.Resources.csproj">
      <Project>{4710c524-6cab-46ac-b206-561e7687a6b7}</Project>
      <Name>NeoSysLCS.Resources</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Test|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <FilesToIncludeForPublish>OnlyFilesToRunTheApp</FilesToIncludeForPublish>
    <PublishDatabaseSettings>
      <Objects>
        <ObjectGroup Name="NeoSysLCS_Dev-Deployment" Order="1">
          <Destination Path="" />
          <Object Type="DbFullSql">
            <PreSource Path="Data Source=localhost\SQLExpress%3bInitial Catalog=NeoSysLCS_Dev%3bIntegrated Security=True" ScriptSchema="True" ScriptData="False" CopyAllFullTextCatalogs="False" DriDefaults="True" />
            <Source Path="obj\Test\AutoScripts\NeoSysLCS_Dev-Deployment_SchemaOnly.sql" Transacted="True" />
          </Object>
        </ObjectGroup>
      </Objects>
    </PublishDatabaseSettings>
    <ExcludeGeneratedDebugSymbol>true</ExcludeGeneratedDebugSymbol>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>favicon.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Test|x64'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>65281</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>https://localhost:44300</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>http://lcstest.azurewebsites.net</CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Import Project="$(SolutionDir)\.nuget\NuGet.targets" Condition="Exists('$(SolutionDir)\.nuget\NuGet.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('$(SolutionDir)\.nuget\NuGet.targets')" Text="$([System.String]::Format('$(ErrorText)', '$(SolutionDir)\.nuget\NuGet.targets'))" />
    <Error Condition="!Exists('..\packages\NETStandard.Library.2.0.0\build\NETStandard.Library.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\NETStandard.Library.2.0.0\build\NETStandard.Library.targets'))" />
  </Target>
  <Import Project="..\packages\NETStandard.Library.2.0.0\build\NETStandard.Library.targets" Condition="Exists('..\packages\NETStandard.Library.2.0.0\build\NETStandard.Library.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
  <ItemGroup>
    <PackageReference Include="DevExpress.ExpressApp.CodeAnalysis" Version="24.1.6">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
    <PackageReference Include="EntityFramework">
      <Version>6.1.3</Version>
    </PackageReference>
  </ItemGroup>
</Project>
