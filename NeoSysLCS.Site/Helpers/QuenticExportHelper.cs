using DevExpress.Web.Mvc;
using NeoSysLCS.Repositories.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using NeoSysLCS.Resources.Properties;

namespace NeoSysLCS.Site.Helpers
{
    public class QuenticExportHelper
    {
        public static MVCxGridViewColumnCollection GetQuenticExportGridColumns(int kundeID, int standortId)
        {
            var columns = new MVCxGridViewColumnCollection();

            columns.Add(column =>
            {
                column.FieldName = "ErlassID";
                column.Caption = "ID";
            });

            columns.Add(column =>
            {
                column.FieldName = "ErlassSrNummer";
                column.Caption = Resources.Properties.Resources.Quentic_Export_Kurzzeichen;
            });

            columns.Add(column =>
            {
                column.FieldName = "ErlassAbkuerzung";
                column.Caption = Resources.Properties.Resources.Quentic_Export_Kurzbezeichnung;
            });
            columns.Add(column =>
            {
                column.FieldName = "ErlassTitel";
                column.Caption = Resources.Properties.Resources.Quentic_Export_Bezeichnung;
            });

            columns.Add(column =>
            {
                column.FieldName = "HerausgeberName";
                column.Caption = Resources.Properties.Resources.Quentic_Export_Rechtsebene;
            });

            columns.Add(column =>
            {
                column.FieldName = "Erlasstyp";
                column.Caption = Resources.Properties.Resources.Quentic_Export_ArtNorm;
            });

            columns.Add(column =>
            {
                column.FieldName = "Sachgebiete";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
            });

            columns.Add(column =>
            {
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), "Stellen");
            });

            columns.Add(column =>
            {
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), "Erscheinungsort");
            });

            columns.Add(column =>
            {
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), "Erscheinungsdatum");
            });

            columns.Add(column =>
            {
                column.FieldName = "Inkrafttretung";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
                column.PropertiesEdit.DisplayFormatString = "d";
            });

            columns.Add(column =>
            {
                column.FieldName = "LastChange";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
                column.PropertiesEdit.DisplayFormatString = "d";
            });

            columns.Add(column =>
            {
                column.FieldName = "Norm";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
            });

            columns.Add(column =>
            {
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), "NormSeit");
            });

            columns.Add(column =>
            {
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), "ErsetztDurch");
            });

            columns.Add(column =>
            {
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), "Bemerkung");
            });

            columns.Add(column =>
            {
                column.FieldName = "Kerninhalte";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
                column.ColumnType = MVCxGridViewColumnType.Memo;
                var memoProp = column.PropertiesEdit as DevExpress.Web.MemoProperties;
                if (memoProp != null)
                {
                    memoProp.EncodeHtml = false;
                }
            });

            columns.Add(column =>
            {
                column.Caption = Resources.Properties.Resources.Quentic_Export_Andwendungsbereich;
            });

            columns.Add(column =>
            {
                column.Caption = Resources.Properties.Resources.Entitaet_Erlass_Rechtsnormen;
            });

            columns.Add(column =>
            {
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), "Quellenbezeichnung");
            });

            columns.Add(column =>
            {
                column.FieldName = "Quelle";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
            });

            columns.Add(column =>
            {
                column.FieldName = "ReviewDatum";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
                column.PropertiesEdit.DisplayFormatString = "d";
            });

            columns.Add(column =>
            {
                column.FieldName = "NoRelevance";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
            });

            columns.Add(column =>
            {
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), "Durchfuehrung");
            });

            columns.Add(column =>
            {
                column.Caption = Resources.Properties.Resources.Quentic_Export_ReviewBemerkung;
            });

            columns.Add(column =>
            {
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), "Stammfassung");
            });


            return columns;
        }

        public static MVCxGridViewColumnCollection GetQuenticTwoExportGridColumns(int kundeID, int standortId)
        {
            var columns = new MVCxGridViewColumnCollection();

            columns.Add(column =>
            {
                // model value is Forderung (not working if fieldName is set to ForderungID)
                column.FieldName = "KundendokumentForderungsversionID";
                column.Caption = "ID";
            });

            columns.Add(column =>
            {
                column.FieldName = "RechtsnormID";
                column.Caption = "Rechtsnorm ID";
            });

            columns.Add(column =>
            {
                column.FieldName = "SrNummer";
                column.Caption = Resources.Properties.Resources.Entitaet_Kundendokument_TabRechtsnormen;
            });

            columns.Add(column =>
            {
                column.FieldName = "ArtikelNummer";
                column.Caption = Resources.Properties.Resources.Quentic_Export_SpezRechtsbezug;
            });
            columns.Add(column =>
            {
                column.Caption = Resources.Properties.Resources.Quentic_Export_Andwendungsbereich;
            });

            columns.Add(column =>
            {
                column.FieldName = "Beschreibung";
                column.Caption = Resources.Properties.Resources.Quentic_Export_Rechtspflicht;
                column.ColumnType = MVCxGridViewColumnType.Memo;
            });

            columns.Add(column =>
            {
                column.Caption = Resources.Properties.Resources.Entitaet_Erlass_VerantwortlicheStellen;
            });

            columns.Add(column =>
            {
                column.FieldName = "Handlungsprioritaet";
                column.Caption = Resources.Properties.Resources.Quentic_Export_Handlungsprio;
            });

            columns.Add(column =>
            {
                column.FieldName = "Rechtspflicht";
                column.Caption = Resources.Properties.Resources.Quentic_Export_Validity;
            });

            columns.Add(column =>
            {
                column.FieldName = "ReviewDatum";
                column.Caption = Resources.Properties.Resources.Entitaet_Erlass_ReviewDate;
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
                column.PropertiesEdit.DisplayFormatString = "d";
            });

            columns.Add(column =>
            {
                column.Caption = Resources.Properties.Resources.Quentic_Export_StatusReview;
            });

            columns.Add(column =>
            {
                column.Caption = Resources.Properties.Resources.Quentic_Export_AktHandlungsprio;
            });

            columns.Add(column =>
            {
                column.Caption = Resources.Properties.Resources.Quentic_Export_DurchgefuehrtVon;
            });

            columns.Add(column =>
            {
                column.Caption = Resources.Properties.Resources.Quentic_Export_ReviewBemerkung;
            });

            columns.Add(column =>
            {
                column.Caption = Resources.Properties.Resources.Quentic_Export_NoRelevance;
            });

            return columns;
        }
    }
}