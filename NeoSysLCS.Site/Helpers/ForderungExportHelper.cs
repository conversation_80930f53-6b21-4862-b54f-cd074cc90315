using DevExpress.Utils;
using DevExpress.Web;
using DevExpress.Web.Mvc;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Utilities.Export;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Drawing;

namespace NeoSysLCS.Site.Helpers
{
    public class ForderungExportHelper
    {
        public static GridViewSettings GetForderungenGridViewSettings(MVCxGridViewColumnCollection columns)
        {
            var settings = new GridViewSettings();
            settings.Name = "Forderungen";
            settings.KeyFieldName = "KundendokumentForderungsversionID";

            //make a list from the MCxGridViewColumnCollection otherwise settings.Columns.Add(col) won't work --> DevExpress 16.2.15
            var columnList = new List<MVCxGridViewColumn>();
            foreach (MVCxGridViewColumn column in columns)
            {
                columnList.Add(column);
            }

            foreach (MVCxGridViewColumn col in columnList)
            {
                settings.Columns.Add(col);
            }

            GridViewHelper.ApplyDefaultExportSettings(settings, ExportType.Xlsx);

            settings.SettingsExport.RenderBrick += (sender, e) =>
            {
                var column = e.Column as MVCxGridViewColumn;

                if (column != null && e.RowType == GridViewRowType.Header)
                {
                    e.BrickStyle.BackColor = System.Drawing.Color.FromArgb(255, 255, 153);
                    e.BrickStyle.ForeColor = Color.Black;
                    e.BrickStyle.Font = new System.Drawing.Font("Arial", 10);
                    e.BrickStyle.SetAlignment(DevExpress.Utils.HorzAlignment.Center, DevExpress.Utils.VertAlignment.Top);

                }

                if (column != null && column.FieldName == "Rechtsbereiche" && e.RowType == GridViewRowType.Data)
                {
                    var text = new List<string>();

                    var rechtsbereiche = e.Value as IQueryable<RechtsbereichViewModel>;

                    if (rechtsbereiche != null)
                    {
                        foreach (var rechtsbereich in rechtsbereiche)
                        {
                            text.Add(rechtsbereich.Name);
                        }
                    }

                    e.Text = string.Join(", ", text);
                    e.TextValue = string.Join(", ", text);
                }
                else if (column != null && column.FieldName == "RechtsbereicheFR" && e.RowType == GridViewRowType.Data)
                {
                    var text = new List<string>();

                    var rechtsbereiche = e.Value as IQueryable<RechtsbereichViewModel>;

                    if (rechtsbereiche != null)
                    {
                        foreach (var rechtsbereich in rechtsbereiche)
                        {
                            text.Add(rechtsbereich.Name);
                        }
                    }

                    e.Text = string.Join(", ", text);
                    e.TextValue = string.Join(", ", text);
                }
                else if (column != null && column.FieldName == "RechtsbereicheIT" && e.RowType == GridViewRowType.Data)
                {
                    var text = new List<string>();

                    var rechtsbereiche = e.Value as IQueryable<RechtsbereichViewModel>;

                    if (rechtsbereiche != null)
                    {
                        foreach (var rechtsbereich in rechtsbereiche)
                        {
                            text.Add(rechtsbereich.Name);
                        }
                    }

                    e.Text = string.Join(", ", text);
                    e.TextValue = string.Join(", ", text);
                }
                else if (column != null && column.FieldName == "RechtsbereicheEN" && e.RowType == GridViewRowType.Data)
                {
                    var text = new List<string>();

                    var rechtsbereiche = e.Value as IQueryable<RechtsbereichViewModel>;

                    if (rechtsbereiche != null)
                    {
                        foreach (var rechtsbereich in rechtsbereiche)
                        {
                            text.Add(rechtsbereich.Name);
                        }
                    }

                    e.Text = string.Join(", ", text);
                    e.TextValue = string.Join(", ", text);
                }
                else if (column != null && column.FieldName == "Erfuellung" && e.RowType == GridViewRowType.Data)
                {
                    if (e.Value is KundendokumentErfuellung status)
                    {
                        e.Text = status.GetTranslation();
                        e.TextValue = e.Text;
                    }
                }
                else if (column != null && column.FieldName == "ArtikelQuelle" && e.RowType == GridViewRowType.Data)
                {
                    if (e.Value != null)
                    {
                        var hyperlinkProperties = column.PropertiesEdit as HyperLinkProperties;
                        hyperlinkProperties.TextField = "ArtikelNummer";
                        hyperlinkProperties.TextFormatString = "'{0}";
                    }
                }
                else if (column != null && column.FieldName == "ArtikelQuelleFR" && e.RowType == GridViewRowType.Data)
                {
                    if (e.Value != null)
                    {
                        var hyperlinkProperties = column.PropertiesEdit as HyperLinkProperties;
                        hyperlinkProperties.TextField = "ArtikelNummer";
                        hyperlinkProperties.TextFormatString = "'{0}";
                    }
                }
                else if (column != null && column.FieldName == "ArtikelQuelleIT" && e.RowType == GridViewRowType.Data)
                {
                    if (e.Value != null)
                    {
                        var hyperlinkProperties = column.PropertiesEdit as HyperLinkProperties;
                        hyperlinkProperties.TextField = "ArtikelNummer";
                        hyperlinkProperties.TextFormatString = "'{0}";
                    }
                }
                else if (column != null && column.FieldName == "ArtikelQuelleEN" && e.RowType == GridViewRowType.Data)
                {
                    if (e.Value != null)
                    {
                        var hyperlinkProperties = column.PropertiesEdit as HyperLinkProperties;
                        hyperlinkProperties.TextField = "ArtikelNummer";
                        hyperlinkProperties.TextFormatString = "'{0}";
                    }
                }
                else if (column != null && column.FieldName == "ErlassQuelle" && e.RowType == GridViewRowType.Data)
                {
                    if (e.Value != null)
                    {
                        var hyperlinkProperties = column.PropertiesEdit as HyperLinkProperties;
                        hyperlinkProperties.TextField = "ErlassSrNummer";
                        hyperlinkProperties.TextFormatString = "'{0}";
                    }
                }
                else if (column != null && column.FieldName == "ErlassQuelleFR" && e.RowType == GridViewRowType.Data)
                {
                    if (e.Value != null)
                    {
                        var erlass = e.Value as string;
                        var hyperlinkProperties = column.PropertiesEdit as HyperLinkProperties;
                        hyperlinkProperties.TextField = "ErlassSrNummer";
                        hyperlinkProperties.TextFormatString = "'{0}";
                    }
                }
                else if (column != null && column.FieldName == "ErlassQuelleIT" && e.RowType == GridViewRowType.Data)
                {
                    if (e.Value != null)
                    {
                        var erlass = e.Value as string;
                        var hyperlinkProperties = column.PropertiesEdit as HyperLinkProperties;
                        hyperlinkProperties.TextField = "ErlassSrNummer";
                        hyperlinkProperties.TextFormatString = "'{0}";
                    }
                }
                else if (column != null && column.FieldName == "ErlassQuelleEN" && e.RowType == GridViewRowType.Data)
                {
                    if (e.Value != null)
                    {
                        var erlass = e.Value as string;
                        var hyperlinkProperties = column.PropertiesEdit as HyperLinkProperties;
                        hyperlinkProperties.TextField = "ErlassSrNummer";
                        hyperlinkProperties.TextFormatString = "'{0}";
                    }
                }
            };

            return settings;
        }

        public static MVCxGridViewColumnCollection GetKundendokumentForderungenExportGridColumns(int kundeID, int standortId, int kundendokumentID)
        {
            UnitOfWork _unitOfWork = new UnitOfWork();
            var columns = new MVCxGridViewColumnCollection();
            System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("NeoSysLCS.Resources.Properties.Resources", typeof(NeoSysLCS.Resources.Properties.Resources).Assembly);
            System.Globalization.CultureInfo info = new System.Globalization.CultureInfo("de");
            System.Globalization.CultureInfo infoFr = new System.Globalization.CultureInfo("fr");
            System.Globalization.CultureInfo infoIt = new System.Globalization.CultureInfo("it");
            System.Globalization.CultureInfo infoEn = new System.Globalization.CultureInfo("en");

            var standort = _unitOfWork.StandortRepository.GetByID(standortId);
            List<Int32> sprachIds = standort.Sprachen.Select(x => x.SpracheID).ToList();

            // get possible combinations of languages
            int spracheCount = sprachIds.Count();
            bool singleDe = false;
            bool singleFr = false;
            bool singleIt = false;
            bool singleEn = false;

            bool twoDeFr = false;
            bool twoDeIt = false;
            bool twoDeEn = false;
            bool twoFrIt = false;
            bool twoFrEn = false;
            bool twoItEn = false;

            bool threeDeFrIt = false;
            bool threeDeFrEn = false;
            bool threeDeItEn = false;
            bool threeFrItEn = false;

            bool all = false;

            if (spracheCount == 1)
            {
                singleDe = sprachIds.Contains(1) ? true : false;
                singleFr = sprachIds.Contains(2) ? true : false;
                singleIt = sprachIds.Contains(3) ? true : false;
                singleEn = sprachIds.Contains(4) ? true : false;
            }
            else if (spracheCount == 2)
            {
                twoDeFr = sprachIds.Contains(1) && sprachIds.Contains(2) ? true : false;
                twoDeIt = sprachIds.Contains(1) && sprachIds.Contains(3) ? true : false;
                twoDeEn = sprachIds.Contains(1) && sprachIds.Contains(4) ? true : false;
                twoFrIt = sprachIds.Contains(2) && sprachIds.Contains(3) ? true : false;
                twoFrEn = sprachIds.Contains(2) && sprachIds.Contains(4) ? true : false;
                twoItEn = sprachIds.Contains(3) && sprachIds.Contains(4) ? true : false;
            }
            else if (spracheCount == 3)
            {
                threeDeFrIt = sprachIds.Contains(1) && sprachIds.Contains(2) && sprachIds.Contains(3) ? true : false;
                threeDeFrEn = sprachIds.Contains(1) && sprachIds.Contains(2) && sprachIds.Contains(4) ? true : false;
                threeDeItEn = sprachIds.Contains(1) && sprachIds.Contains(3) && sprachIds.Contains(4) ? true : false;
                threeFrItEn = sprachIds.Contains(2) && sprachIds.Contains(3) && sprachIds.Contains(4) ? true : false;
            }

            columns.Add(column =>
            {
                column.FieldName = "KundendokumentForderungsversionID";
                column.Caption = "ID";
            });

            //if languages of client contain german
            if (sprachIds.Contains(1))
            {
                columns.Add(column =>
                {
                    column.FieldName = "Rechtsbereiche";
                    var titel = temp.GetString("Entitaet_Rechtsbereich_Plural", info);
                    column.Caption = titel;
                });

                columns.Add(column =>
                {
                    column.FieldName = "StandortObjektTitel";
                    var titel = temp.GetString("Entitaet_StandortObjekt_Singular", info);
                    column.Caption = titel;
                });

                columns.Add(column =>
                {
                    column.FieldName = "ErlassQuelle";
                    var titel = temp.GetString("Erlassnummer", info);
                    column.Caption = titel;
                    column.ColumnType = MVCxGridViewColumnType.HyperLink;
                    //var hyperlink = column.PropertiesEdit as HyperLinkProperties;
                });

                columns.Add(column =>
                {
                    column.FieldName = "ErlassTitel";
                    var titel = temp.GetString("Entitaet_Erlass_Singular", info);
                    column.Caption = titel;
                });

                columns.Add(column =>
                {
                    column.FieldName = "ArtikelQuelle";
                    var titel = temp.GetString("Entitaet_Artikel_Singular", info);
                    column.Caption = titel;
                    column.ColumnType = MVCxGridViewColumnType.HyperLink;
                    var hyperlink = column.PropertiesEdit as HyperLinkProperties;
                });

                columns.Add(column =>
                {
                    column.FieldName = "Beschreibung";
                    var titel = temp.GetString("Allgemein_Beschreibung", info);
                    column.Caption = titel;
                });

               /* columns.Add(column =>
                {
                    column.FieldName = "BearbeitetAm";
                    column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    var titel = temp.GetString("Entitaet_Erlassfassung_Beschluss", info);
                    column.Caption = titel;
                });*/
            }

            //if languages of client contain french
            if (sprachIds.Contains(2))
            {
                columns.Add(column =>
                {
                    column.FieldName = "RechtsbereicheFR";
                    var titel = temp.GetString("Entitaet_Rechtsbereich_Plural", infoFr);
                    column.Caption = titel;
                });

                columns.Add(column =>
                {
                    column.FieldName = "StandortObjektTitelFR";
                    var titel = temp.GetString("Entitaet_StandortObjekt_Singular", infoFr);
                    column.Caption = titel;
                });

                columns.Add(column =>
                {
                    column.FieldName = "ErlassQuelleFR";
                    var titel = temp.GetString("Erlassnummer", infoFr);
                    column.Caption = titel;
                    column.ColumnType = MVCxGridViewColumnType.HyperLink;
                    var hyperlink = column.PropertiesEdit as HyperLinkProperties;
                });

                columns.Add(column =>
                {
                    column.FieldName = "ErlassTitelFR";
                    var titel = temp.GetString("Entitaet_Erlass_Singular", infoFr);
                    column.Caption = titel;
                });

                columns.Add(column =>
                {
                    column.FieldName = "ArtikelQuelleFR";
                    var titel = temp.GetString("Entitaet_Artikel_Singular", infoFr);
                    column.Caption = titel;
                    column.ColumnType = MVCxGridViewColumnType.HyperLink;
                    var hyperlink = column.PropertiesEdit as HyperLinkProperties;
                });

                columns.Add(column =>
                {
                    column.FieldName = "BeschreibungFR";
                    var titel = temp.GetString("Allgemein_Beschreibung", infoFr);
                    column.Caption = titel;
                    column.ColumnType = MVCxGridViewColumnType.Memo;
                });

               /* columns.Add(column =>
                {
                    column.FieldName = "BearbeitetAm";
                    column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    var titel = temp.GetString("Entitaet_Erlassfassung_Beschluss", infoFr);
                    column.Caption = titel;
                });*/
            }

            //if languages of client contain italian
            if (sprachIds.Contains(3))
            {
                columns.Add(column =>
                {
                    column.FieldName = "RechtsbereicheIT";
                    var titel = temp.GetString("Entitaet_Rechtsbereich_Plural", infoIt);
                    column.Caption = titel;
                });

                columns.Add(column =>
                {
                    column.FieldName = "StandortObjektTitelIT";
                    var titel = temp.GetString("Entitaet_StandortObjekt_Singular", infoIt);
                    column.Caption = titel;
                });

                columns.Add(column =>
                {
                    column.FieldName = "ErlassQuelleIT";
                    var titel = temp.GetString("Entitaet_Erlass_SrNummer", infoIt);
                    column.Caption = titel;
                    column.ColumnType = MVCxGridViewColumnType.HyperLink;
                    var hyperlink = column.PropertiesEdit as HyperLinkProperties;
                });

                columns.Add(column =>
                {
                    column.FieldName = "ErlassTitelIT";
                    var titel = temp.GetString("Entitaet_Erlass_Singular", infoIt);
                    column.Caption = titel;
                });

                columns.Add(column =>
                {
                    column.FieldName = "ArtikelQuelleIT";
                    var titel = temp.GetString("Entitaet_Artikel_Singular", infoIt);
                    column.Caption = titel;
                    column.ColumnType = MVCxGridViewColumnType.HyperLink;
                    var hyperlink = column.PropertiesEdit as HyperLinkProperties;
                });

                columns.Add(column =>
                {
                    column.FieldName = "BeschreibungIT";
                    var titel = temp.GetString("Allgemein_Beschreibung", infoIt);
                    column.Caption = titel;
                    column.ColumnType = MVCxGridViewColumnType.Memo;
                });

               /* columns.Add(column =>
                {
                    column.FieldName = "BearbeitetAm";
                    column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    var titel = temp.GetString("Entitaet_Erlassfassung_Beschluss", infoIt);
                    column.Caption = titel;
                });*/
            }

            //if languages of client contain english
            if (sprachIds.Contains(4))
            {
                columns.Add(column =>
                {
                    column.FieldName = "RechtsbereicheEN";
                    var titel = temp.GetString("Entitaet_Rechtsbereich_Plural", infoEn);
                    column.Caption = titel;
                });

                columns.Add(column =>
                {
                    column.FieldName = "StandortObjektTitelEN";
                    var titel = temp.GetString("Entitaet_StandortObjekt_Singular", infoEn);
                    column.Caption = titel;
                });

                columns.Add(column =>
                {
                    column.FieldName = "ErlassQuelleEN";
                    var titel = temp.GetString("Entitaet_Erlass_SrNummer", infoEn);
                    column.Caption = titel;
                    column.ColumnType = MVCxGridViewColumnType.HyperLink;
                    var hyperlink = column.PropertiesEdit as HyperLinkProperties;
                });

                columns.Add(column =>
                {
                    column.FieldName = "ErlassTitelEN";
                    var titel = temp.GetString("Entitaet_Erlass_Singular", infoEn);
                    column.Caption = titel;
                });

                columns.Add(column =>
                {
                    column.FieldName = "ArtikelQuelleEN";
                    var titel = temp.GetString("Entitaet_Artikel_Singular", infoEn);
                    column.Caption = titel;
                    column.ColumnType = MVCxGridViewColumnType.HyperLink;
                    var hyperlink = column.PropertiesEdit as HyperLinkProperties;
                });

                columns.Add(column =>
                {
                    column.FieldName = "BeschreibungEN";
                    var titel = temp.GetString("Allgemein_Beschreibung", infoEn);
                    column.Caption = titel;
                    column.ColumnType = MVCxGridViewColumnType.Memo;
                });

             /*   columns.Add(column =>
                {
                    column.FieldName = "BearbeitetAm";
                    column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    var titel = temp.GetString("Entitaet_Erlassfassung_Beschluss", infoEn);
                    column.Caption = titel;
                });*/
            }

            if (spracheCount == 1)
            {
                //language german
                if (singleDe)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Bewilligungspflicht";
                        var titel = temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", info);
                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Nachweispflicht";
                        var titel = temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", info);
                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "ExportStatus";
                        var titel = temp.GetString("Entitaet_Forderungsversion_Changed", info);
                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Erfuellung";
                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", info);
                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Verantwortlich";
                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", info);
                        column.Caption = titel;
                    });
                }

                //language french
                if (singleFr)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Bewilligungspflicht";
                        var titel = temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoFr);
                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Nachweispflicht";
                        var titel = temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoFr);
                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "ExportStatus";
                        var titel = temp.GetString("Entitaet_Forderungsversion_Changed", infoFr);
                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Erfuellung";
                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoFr);
                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Verantwortlich";
                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoFr);
                        column.Caption = titel;
                    });
                }

                //language italian
                if (singleIt)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Bewilligungspflicht";
                        var titel = temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoIt);
                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Nachweispflicht";
                        var titel = temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoIt);
                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "ExportStatus";
                        var titel = temp.GetString("Entitaet_Forderungsversion_Changed", infoIt);
                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Erfuellung";
                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoIt);
                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Verantwortlich";
                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoIt);
                        column.Caption = titel;
                    });
                }

                if (singleEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Bewilligungspflicht";
                        var titel = temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoEn);
                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Nachweispflicht";
                        var titel = temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoEn);
                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "ExportStatus";
                        var titel = temp.GetString("Entitaet_Forderungsversion_Changed", infoEn);
                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Erfuellung";
                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoEn);
                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Verantwortlich";
                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoEn);
                        column.Caption = titel;
                    });
                }
            }

            if (spracheCount == 2)
            {
                //languages german and french
                if (twoDeFr)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Bewilligungspflicht";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoFr);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Nachweispflicht";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoFr);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "ExportStatus";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Changed", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Changed", infoFr);

                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Erfuellung";

                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoFr);

                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Verantwortlich";

                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoFr);

                        column.Caption = titel;
                    });
                }

                //languages german and italian
                if (twoDeIt)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Bewilligungspflicht";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoIt);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Nachweispflicht";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoIt);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "ExportStatus";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Changed", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Changed", infoIt);

                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Erfuellung";

                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoIt);

                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Verantwortlich";

                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoIt);

                        column.Caption = titel;
                    });
                }

                //languages german and english
                if (twoDeEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Bewilligungspflicht";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Nachweispflicht";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "ExportStatus";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Changed", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Changed", infoEn);

                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Erfuellung";

                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoEn);

                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Verantwortlich";

                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoEn);

                        column.Caption = titel;
                    });
                }

                //languages french and italian
                if (twoFrIt)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Bewilligungspflicht";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoFr)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoIt);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Nachweispflicht";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoFr)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoIt);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "ExportStatus";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Changed", infoFr)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Changed", infoIt);

                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Erfuellung";

                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoIt);

                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Verantwortlich";

                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoIt);

                        column.Caption = titel;
                    });
                }

                //languages french and english
                if (twoFrEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Bewilligungspflicht";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoFr)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Nachweispflicht";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoFr)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "ExportStatus";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Changed", infoFr)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Changed", infoEn);

                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Erfuellung";

                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoEn);

                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Verantwortlich";

                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoEn);

                        column.Caption = titel;
                    });
                }

                //languages italian and english
                if (twoItEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Bewilligungspflicht";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoIt)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Nachweispflicht";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoIt)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "ExportStatus";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Changed", infoIt)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Changed", infoEn);

                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Erfuellung";

                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoIt)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoEn);

                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Verantwortlich";

                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoIt)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoEn);

                        column.Caption = titel;
                    });
                }
            }

            if (spracheCount == 3)
            {
                //languages german, french and italian
                if (threeDeFrIt)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Bewilligungspflicht";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoFr)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoIt);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Nachweispflicht";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoFr)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoIt);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "ExportStatus";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Changed", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Changed", infoFr)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Changed", infoIt);

                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Erfuellung";

                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoIt);

                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Verantwortlich";

                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoIt);

                        column.Caption = titel;
                    });
                }

                //languages german, french and english
                if (threeDeFrEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Bewilligungspflicht";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoFr)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Nachweispflicht";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoFr)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "ExportStatus";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Changed", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Changed", infoFr)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Changed", infoEn);

                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Erfuellung";

                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoEn);

                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Verantwortlich";

                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoEn);

                        column.Caption = titel;
                    });
                }

                //languages german, italian and english
                if (threeDeItEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Bewilligungspflicht";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoIt)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Nachweispflicht";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoIt)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "ExportStatus";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Changed", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Changed", infoIt)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Changed", infoEn);

                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Erfuellung";

                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoIt)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoEn);

                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Verantwortlich";

                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoIt)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoEn);

                        column.Caption = titel;
                    });
                }

                //languages french, italian and english
                if (threeFrItEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Bewilligungspflicht";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoFr)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoIt)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Nachweispflicht";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoFr)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoIt)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "ExportStatus";

                        var titel = temp.GetString("Entitaet_Forderungsversion_Changed", infoFr)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Changed", infoIt)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Changed", infoEn);

                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Erfuellung";

                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoIt)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoEn);

                        column.Caption = titel;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Verantwortlich";

                        var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoIt)
                        + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoEn);

                        column.Caption = titel;
                    });
                }
            }

            if (spracheCount == 4)
            {
                columns.Add(column =>
                {
                    column.FieldName = "Bewilligungspflicht";

                    var titel = temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", info)
                    + "\n" + temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoFr)
                    + "\n" + temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoIt)
                    + "\n" + temp.GetString("Entitaet_Forderungsversion_Bewilligungspflicht_Kurz", infoEn);

                    column.Caption = titel;
                    column.ColumnType = MVCxGridViewColumnType.CheckBox;
                });

                columns.Add(column =>
                {
                    column.FieldName = "Nachweispflicht";

                    var titel = temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", info)
                    + "\n" + temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoFr)
                    + "\n" + temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoIt)
                    + "\n" + temp.GetString("Entitaet_Forderungsversion_Nachweispflicht_Kurz", infoEn);

                    column.Caption = titel;
                    column.ColumnType = MVCxGridViewColumnType.CheckBox;
                });

                columns.Add(column =>
                {
                    column.FieldName = "ExportStatus";

                    var titel = temp.GetString("Entitaet_Forderungsversion_Changed", info)
                    + "\n" + temp.GetString("Entitaet_Forderungsversion_Changed", infoFr)
                    + "\n" + temp.GetString("Entitaet_Forderungsversion_Changed", infoIt)
                    + "\n" + temp.GetString("Entitaet_Forderungsversion_Changed", infoEn);

                    column.Caption = titel;
                });

                columns.Add(column =>
                {
                    column.FieldName = "Erfuellung";

                    var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", info)
                    + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoFr)
                    + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoIt)
                    + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Erfuellung", infoEn); ;

                    column.Caption = titel;
                });

                columns.Add(column =>
                {
                    column.FieldName = "Verantwortlich";

                    var titel = temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", info)
                    + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoFr)
                    + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoIt)
                    + "\n" + temp.GetString("Entitaet_KundendokumentIndividuelleForderung_Verantwortlich", infoEn); ;

                    column.Caption = titel;
                });
            }

            //one language if clause
            if (spracheCount == 1)
            {
                //german
                if (singleDe)
                {                    
                    columns.Add(column =>
                    {
                        column.FieldName = "LetzterPruefZeitpunkt";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", info);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "NaechstePruefungAm";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", info);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Ablageort";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", info);

                        column.Caption = titel;
                        column.HeaderStyle.Wrap = DefaultBoolean.True;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Kommentar";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", info);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.Memo;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Pruefmethode";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", info);

                        column.Caption = titel;
                    });
                }

                //french
                if (singleFr)
                {                    
                    columns.Add(column =>
                    {
                        column.FieldName = "LetzterPruefZeitpunkt";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoFr);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "NaechstePruefungAm";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoFr);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Ablageort";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoFr);

                        column.Caption = titel;
                        column.HeaderStyle.Wrap = DefaultBoolean.True;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Kommentar";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoFr);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.Memo;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Pruefmethode";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoFr);

                        column.Caption = titel;
                    });
                }

                //italian
                if (singleIt)
                {                   
                    columns.Add(column =>
                    {
                        column.FieldName = "LetzterPruefZeitpunkt";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoIt);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "NaechstePruefungAm";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoIt);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Ablageort";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoIt);

                        column.Caption = titel;
                        column.HeaderStyle.Wrap = DefaultBoolean.True;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Kommentar";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoIt);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.Memo;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Pruefmethode";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoIt);

                        column.Caption = titel;
                    });
                }

                // english
                if (singleEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "LetzterPruefZeitpunkt";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "NaechstePruefungAm";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Ablageort";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoEn);

                        column.Caption = titel;
                        column.HeaderStyle.Wrap = DefaultBoolean.True;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Kommentar";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.Memo;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Pruefmethode";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoEn);

                        column.Caption = titel;
                    });
                }
            }

            //two languages if clause
            if (spracheCount == 2)
            {
                //german and french
                if (twoDeFr)
                {                    
                    columns.Add(column =>
                    {
                        column.FieldName = "LetzterPruefZeitpunkt";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoFr);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "NaechstePruefungAm";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoFr);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Ablageort";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoFr);

                        column.Caption = titel;
                        column.HeaderStyle.Wrap = DefaultBoolean.True;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Kommentar";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoFr);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.Memo;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Pruefmethode";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoFr);

                        column.Caption = titel;
                    });

                    
                }

                //german and italian
                if (twoDeIt)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "LetzterPruefZeitpunkt";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoIt);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "NaechstePruefungAm";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoIt);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Ablageort";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoIt);

                        column.Caption = titel;
                        column.HeaderStyle.Wrap = DefaultBoolean.True;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Kommentar";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoIt);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.Memo;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Pruefmethode";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoIt);

                        column.Caption = titel;
                    });
                }

                //german and english
                if (twoDeEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "LetzterPruefZeitpunkt";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "NaechstePruefungAm";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Ablageort";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoEn);

                        column.Caption = titel;
                        column.HeaderStyle.Wrap = DefaultBoolean.True;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Kommentar";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.Memo;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Pruefmethode";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoEn);

                        column.Caption = titel;
                    });
                }

                //french and italian
                if (twoFrIt)
                {                    
                    columns.Add(column =>
                    {
                        column.FieldName = "LetzterPruefZeitpunkt";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoIt);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "NaechstePruefungAm";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoIt);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Ablageort";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoIt);

                        column.Caption = titel;
                        column.HeaderStyle.Wrap = DefaultBoolean.True;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Kommentar";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoIt);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.Memo;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Pruefmethode";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoIt);

                        column.Caption = titel;
                    });
                }

                //french and english
                if (twoFrEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "LetzterPruefZeitpunkt";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "NaechstePruefungAm";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Ablageort";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoEn);

                        column.Caption = titel;
                        column.HeaderStyle.Wrap = DefaultBoolean.True;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Kommentar";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.Memo;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Pruefmethode";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoEn);

                        column.Caption = titel;
                    });
                }

                //italian and english
                if (twoItEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "LetzterPruefZeitpunkt";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoIt)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "NaechstePruefungAm";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoIt)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Ablageort";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoIt)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoEn);

                        column.Caption = titel;
                        column.HeaderStyle.Wrap = DefaultBoolean.True;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Kommentar";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoIt)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.Memo;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Pruefmethode";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoIt)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoEn);

                        column.Caption = titel;
                    });
                }
            }

            //german, french and italian
            if (spracheCount == 3)
            {
                // german, french and italian
                if (threeDeFrIt)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "LetzterPruefZeitpunkt";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoIt);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "NaechstePruefungAm";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoIt);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Ablageort";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoIt);

                        column.Caption = titel;
                        column.HeaderStyle.Wrap = DefaultBoolean.True;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Kommentar";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoIt);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.Memo;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Pruefmethode";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoIt);

                        column.Caption = titel;
                    });                    
                }

                // german, french and english
                if (threeDeFrEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "LetzterPruefZeitpunkt";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "NaechstePruefungAm";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Ablageort";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoEn);

                        column.Caption = titel;
                        column.HeaderStyle.Wrap = DefaultBoolean.True;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Kommentar";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.Memo;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Pruefmethode";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoEn);

                        column.Caption = titel;
                    });
                }

                // german, italian and english
                if (threeDeItEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "LetzterPruefZeitpunkt";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoIt)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "NaechstePruefungAm";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoIt)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Ablageort";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoIt)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoEn);

                        column.Caption = titel;
                        column.HeaderStyle.Wrap = DefaultBoolean.True;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Kommentar";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoIt)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.Memo;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Pruefmethode";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", info)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoIt)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoEn);

                        column.Caption = titel;
                    });
                }

                // french, italian and english
                if (threeFrItEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "LetzterPruefZeitpunkt";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoIt)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "NaechstePruefungAm";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoIt)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Ablageort";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoIt)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoEn);

                        column.Caption = titel;
                        column.HeaderStyle.Wrap = DefaultBoolean.True;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Kommentar";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoIt)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoEn);

                        column.Caption = titel;
                        column.ColumnType = MVCxGridViewColumnType.Memo;
                    });

                    columns.Add(column =>
                    {
                        column.FieldName = "Pruefmethode";

                        var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoFr)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoIt)
                        + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoEn);

                        column.Caption = titel;
                    });
                }
            }

            if (spracheCount == 4)
            {
                columns.Add(column =>
                {
                    column.FieldName = "LetzterPruefZeitpunkt";

                    var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", info)
                    + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoFr)
                    + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoIt)
                    + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_LetzterPruefungszeitpunkt", infoEn);

                    column.Caption = titel;
                    column.ColumnType = MVCxGridViewColumnType.DateEdit;
                });

                columns.Add(column =>
                {
                    column.FieldName = "NaechstePruefungAm";

                    var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", info)
                    + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoFr)
                    + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoIt)
                    + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_NaechstePruefungAm", infoEn);

                    column.Caption = titel;
                    column.ColumnType = MVCxGridViewColumnType.DateEdit;
                });

                columns.Add(column =>
                {
                    column.FieldName = "Ablageort";

                    var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", info)
                    + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoFr)
                    + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoIt)
                    + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Ablageort", infoEn);

                    column.Caption = titel;
                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                });

                columns.Add(column =>
                {
                    column.FieldName = "Kommentar";

                    var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", info)
                    + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoFr)
                    + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoIt)
                    + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Kommentar", infoEn);

                    column.Caption = titel;
                    column.ColumnType = MVCxGridViewColumnType.Memo;
                });

                columns.Add(column =>
                {
                    column.FieldName = "Pruefmethode";

                    var titel = temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", info)
                    + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoFr)
                    + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoIt)
                    + "\n" + temp.GetString("Entitaet_KundendokumentForderungsversion_Pruefmethode", infoEn);

                    column.Caption = titel;
                });
            }

            var labels = _unitOfWork.KundendokumentRepository.GetByID(kundendokumentID, "KundendokumentSpaltenlabel").KundendokumentSpaltenlabel;

            columns.Add(column =>
            {
                column.FieldName = "Spalte1";
                column.Caption = (labels == null || labels.LabelSpalte1 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte1;
            });
            columns.Add(column =>
            {
                column.FieldName = "Spalte2";
                column.Caption = (labels == null || labels.LabelSpalte2 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte2;
            });
            columns.Add(column =>
            {
                column.FieldName = "Spalte3";
                column.Caption = (labels == null || labels.LabelSpalte3 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte3;
            });
            columns.Add(column =>
            {
                column.FieldName = "Spalte4";
                column.Caption = (labels == null || labels.LabelSpalte4 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte4;
            });
            columns.Add(column =>
            {
                column.FieldName = "Spalte5";
                column.Caption = (labels == null || labels.LabelSpalte5 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte5;
            });
            columns.Add(column =>
            {
                column.FieldName = "Spalte6";
                column.Caption = (labels == null || labels.LabelSpalte6 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte6;
            });
            columns.Add(column =>
            {
                column.FieldName = "Spalte7";
                column.Caption = (labels == null || labels.LabelSpalte7 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte7;
            });
            columns.Add(column =>
            {
                column.FieldName = "Spalte8";
                column.Caption = (labels == null || labels.LabelSpalte8 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte8;
            });
            columns.Add(column =>
            {
                column.FieldName = "Spalte9";
                column.Caption = (labels == null || labels.LabelSpalte9 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte9;
            });
            columns.Add(column =>
            {
                column.FieldName = "Spalte10";
                column.Caption = (labels == null || labels.LabelSpalte10 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte10;
            });

            //one language if clause
          /*  if (spracheCount == 1)
            {
                //german
                if (singleDe)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "BearbeitetAm";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                        var titel = temp.GetString("Entitaet_Erlassfassung_Beschluss", info);
                        column.Caption = titel;
                    });
                }

                //french
                if (singleFr)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "BearbeitetAm";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                        var titel = temp.GetString("Entitaet_Erlassfassung_Beschluss", infoFr);
                        column.Caption = titel;
                    });
                }

                //italian
                if (singleIt)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "BearbeitetAm";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                        var titel = temp.GetString("Entitaet_Erlassfassung_Beschluss", infoIt);
                        column.Caption = titel;
                    });
                }

                // english
                if (singleEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "BearbeitetAm";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                        var titel = temp.GetString("Entitaet_Erlassfassung_Beschluss", infoEn);
                        column.Caption = titel;
                    });
                }
            }*/

            //two languages if clause
            if (spracheCount == 2)
            {
                //german and french
               /* if (twoDeFr)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "BearbeitetAm";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                        var titel = temp.GetString("Entitaet_Erlassfassung_Beschluss", info)
                                    + "\n" + temp.GetString("Entitaet_Erlassfassung_Beschluss", infoFr);
                        column.Caption = titel;
                    });
                }*/

                //german and italian
               /* if (twoDeIt)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "BearbeitetAm";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                        var titel = temp.GetString("Entitaet_Erlassfassung_Beschluss", info)
                                    + "\n" + temp.GetString("Entitaet_Erlassfassung_Beschluss", infoIt);
                        column.Caption = titel;
                    });
                }*/

                //german and english
              /*  if (twoDeEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "BearbeitetAm";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                        var titel = temp.GetString("Entitaet_Erlassfassung_Beschluss", info)
                                    + "\n" + temp.GetString("Entitaet_Erlassfassung_Beschluss", infoEn);
                        column.Caption = titel;
                    });
                }*/

                //french and italian
              /*  if (twoFrIt)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "BearbeitetAm";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                        var titel = temp.GetString("Entitaet_Erlassfassung_Beschluss", infoFr)
                                    + "\n" + temp.GetString("Entitaet_Erlassfassung_Beschluss", infoIt);
                        column.Caption = titel;
                    });
                }*/

                //french and english
              /*  if (twoFrEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "BearbeitetAm";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                        var titel = temp.GetString("Entitaet_Erlassfassung_Beschluss", infoFr)
                                    + "\n" + temp.GetString("Entitaet_Erlassfassung_Beschluss", infoEn);
                        column.Caption = titel;
                    });
                }*/

                //italian and english
              /*  if (twoItEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "BearbeitetAm";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                        var titel = temp.GetString("Entitaet_Erlassfassung_Beschluss", infoIt)
                                    + "\n" + temp.GetString("Entitaet_Erlassfassung_Beschluss", infoEn);
                        column.Caption = titel;
                    });
                }*/
            }

            //german, french and italian
            if (spracheCount == 3)
            {
                // german, french and italian
               /* if (threeDeFrIt)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "BearbeitetAm";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                        var titel = temp.GetString("Entitaet_Erlassfassung_Beschluss", info)
                                    + "\n" + temp.GetString("Entitaet_Erlassfassung_Beschluss", infoFr)
                                    + "\n" + temp.GetString("Entitaet_Erlassfassung_Beschluss", infoIt);
                        column.Caption = titel;
                    });
                }*/

                // german, french and english
               /* if (threeDeFrEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "BearbeitetAm";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                        var titel = temp.GetString("Entitaet_Erlassfassung_Beschluss", info)
                                    + "\n" + temp.GetString("Entitaet_Erlassfassung_Beschluss", infoFr)
                                    + "\n" + temp.GetString("Entitaet_Erlassfassung_Beschluss", infoEn);
                        column.Caption = titel;
                    });
                }*/

                // german, italian and english
              /*  if (threeDeItEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "BearbeitetAm";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                        var titel = temp.GetString("Entitaet_Erlassfassung_Beschluss", info)
                                    + "\n" + temp.GetString("Entitaet_Erlassfassung_Beschluss", infoIt)
                                    + "\n" + temp.GetString("Entitaet_Erlassfassung_Beschluss", infoEn);
                        column.Caption = titel;
                    });
                }*/

                // french, italian and english
              /*  if (threeFrItEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "BearbeitetAm";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;
                        var titel = temp.GetString("Entitaet_Erlassfassung_Beschluss", infoFr)
                                    + "\n" + temp.GetString("Entitaet_Erlassfassung_Beschluss", infoIt)
                                    + "\n" + temp.GetString("Entitaet_Erlassfassung_Beschluss", infoEn);
                        column.Caption = titel;
                    });
                }*/
            }

           /* if (spracheCount == 4)
            {
                columns.Add(column =>
                {
                    column.FieldName = "BearbeitetAm";
                    column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    var titel = temp.GetString("Entitaet_Erlassfassung_Beschluss", info)
                                + "\n" + temp.GetString("Entitaet_Erlassfassung_Beschluss", infoFr)
                                + "\n" + temp.GetString("Entitaet_Erlassfassung_Beschluss", infoIt)
                                + "\n" + temp.GetString("Entitaet_Erlassfassung_Beschluss", infoEn);
                    column.Caption = titel;
                });
            }*/

            if (spracheCount == 1)
            {
                //german
                if (singleDe)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Inkrafttretung";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;

                        var titel = temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", info);

                        column.Caption = titel;
                    });
                }

                if (singleFr)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Inkrafttretung";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;

                        var titel = temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoFr);

                        column.Caption = titel;
                    });
                }

                if (singleIt)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Inkrafttretung";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;

                        var titel = temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoIt);

                        column.Caption = titel;
                    });
                }

                if (singleEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Inkrafttretung";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;

                        var titel = temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoEn);

                        column.Caption = titel;
                    });
                }
            }

            if (sprachIds.Count == 2)
            {
                //german and french
                if (twoDeFr)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Inkrafttretung";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;

                        var titel = temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoFr);

                        column.Caption = titel;
                    });
                }

                if (twoDeIt)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Inkrafttretung";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;

                        var titel = temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoIt);

                        column.Caption = titel;
                    });
                }

                if (twoDeEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Inkrafttretung";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;

                        var titel = temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoEn);

                        column.Caption = titel;
                    });
                }

                if (twoFrIt)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Inkrafttretung";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;

                        var titel = temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoFr)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoIt);

                        column.Caption = titel;
                    });
                }

                if (twoFrEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Inkrafttretung";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;

                        var titel = temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoFr)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoEn);

                        column.Caption = titel;
                    });
                }

                if (twoItEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Inkrafttretung";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;

                        var titel = temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoIt)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoEn);

                        column.Caption = titel;
                    });
                }
            }

            if (sprachIds.Count == 3)
            {
                if (threeDeFrIt)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Inkrafttretung";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;

                        var titel = temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoFr)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoIt);

                        column.Caption = titel;
                    });
                }

                if (threeDeFrEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Inkrafttretung";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;

                        var titel = temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoFr)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoEn);

                        column.Caption = titel;
                    });
                }

                if (threeDeItEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Inkrafttretung";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;

                        var titel = temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", info)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoIt)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoEn);

                        column.Caption = titel;
                    });
                }

                if (threeFrItEn)
                {
                    columns.Add(column =>
                    {
                        column.FieldName = "Inkrafttretung";
                        column.ColumnType = MVCxGridViewColumnType.DateEdit;

                        var titel = temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoFr)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoIt)
                        + "\n" + temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoEn);

                        column.Caption = titel;
                    });
                }
            }

            if (sprachIds.Count == 4)
            {
                columns.Add(column =>
                {
                    column.FieldName = "Inkrafttretung";
                    column.ColumnType = MVCxGridViewColumnType.DateEdit;

                    var titel = temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", info)
                    + "\n" + temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoFr)
                    + "\n" + temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoIt)
                    + "\n" + temp.GetString("Entitaet_Forderungsversion_Inkrafttretung", infoEn);

                    column.Caption = titel;
                });
            }

            return columns;
        }
    }
}