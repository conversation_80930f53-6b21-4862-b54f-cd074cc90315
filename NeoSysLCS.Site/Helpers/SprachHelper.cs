using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.EntityFramework;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Site.Controllers;

namespace NeoSysLCS.Site.Helpers
{
    public class SprachHelper : BaseController
    {
        private ApplicationUserManager usrManager;
        private int SpracheID;

        public int GetCurrentSprachID()
        {
            //usrManager = HttpContext.GetOwinContext().GetUserManager<ApplicationUserManager>();
            usrManager = new ApplicationUserManager(new UserStore<ApplicationUser>(new NeoSysLCS_Dev()));
            ApplicationUser usr = usrManager.FindById(System.Web.HttpContext.Current.User.Identity.GetUserId());
            if (usr.SpracheID != 0)
            {
                SpracheID = (int) usr.SpracheID;
            }
            else
            {
                SpracheID = 1;
            }

            return SpracheID;
        }
    }
}