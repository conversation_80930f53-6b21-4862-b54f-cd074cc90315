using System.Collections.Generic;
using System.Linq;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;

namespace NeoSysLCS.Site.Helpers
{
    public static class KundendokumentManager
    {

        /// <summary>
        /// Deletes the child elements of a kundendokument.
        /// </summary>
        /// <param name="unitOfWork">The unit of work.</param>
        /// <param name="kundendokument">The kundendokument.</param>
        public static void DeleteChildElementsOfKundendokument(IUnitOfWork unitOfWork, Kundendokument kundendokument)
        {
            //Delete all items from Kundendokument
            unitOfWork.KundendokumentForderungsversionRepository.DeleteRange(kundendokument.KundendokumentForderungen);
            //Die Pflichten werden im Moment nich genutzt-- > Schritt wird übersprungen //Patrick <PERSON>, 26.06.2017
            //unitOfWork.KundendokumentPflichtRepository.DeleteRange(kundendokument.KundendokumentPflichten);
            unitOfWork.KundendokumentErlassfassungRepository.DeleteRange(kundendokument.KundendokumentErlassfassungen);

            unitOfWork.kundendokumentChecklistRepository.DeleteRange(kundendokument.KundendokumentChecklists);
            
        }
    }
}