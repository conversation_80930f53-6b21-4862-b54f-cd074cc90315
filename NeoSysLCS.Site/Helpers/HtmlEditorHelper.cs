using System.Web.UI.WebControls;
using DevExpress.Web.ASPxHtmlEditor;
using DevExpress.Web.Mvc;

namespace NeoSysLCS.Site.Helpers
{
    /// <summary>
    /// Helper for html editors
    /// </summary>
    public static class HtmlEditorHelper
    {

        /// <summary>
        /// Applies the default settings.
        /// </summary>
        /// <param name="editorSettings">The editor settings.</param>
        public static void ApplyHtmlEditorSettings(HtmlEditorSettings editorSettings)
        {
            editorSettings.Width = Unit.Percentage(150);
            editorSettings.Height = Unit.Empty;

            editorSettings.Settings.AllowHtmlView = false;
            editorSettings.Settings.AllowPreview = false;
            editorSettings.Toolbars.Add(toolbar =>
            {

                toolbar.Items.Add(new ToolbarUndoButton());
                toolbar.Items.Add(new ToolbarRedoButton());

                toolbar.Items.Add(new ToolbarBoldButton(true));
                toolbar.Items.Add(new ToolbarItalicButton());
                toolbar.Items.Add(new ToolbarUnderlineButton());
                toolbar.Items.Add(new ToolbarStrikethroughButton());
                toolbar.Items.Add(new ToolbarSubscriptButton());
                toolbar.Items.Add(new ToolbarSuperscriptButton());
                toolbar.Items.Add<ToolbarCustomCssEdit>(edit =>
                {
                    edit.DefaultCaption = "Format";
                    edit.Items.Add("Überschrift 1", "H1", "");
                    edit.Items.Add("Überschrift 2", "H2", "");
                    edit.Items.Add("Überschrift 3", "H3", "");
                    edit.Items.Add("Standard", "p", "");
                    edit.Width = 80;
                });

                toolbar.Items.Add<ToolbarFontSizeEdit>(fontSizeItem =>
                {
                    fontSizeItem.DefaultCaption = "Schriftgrösse";
                    fontSizeItem.ToolTip = "Schriftgrösse";
                    fontSizeItem.Items.Add("8pt", "1");
                    fontSizeItem.Items.Add("10pt", "2");
                    fontSizeItem.Items.Add("12pt", "3");
                    fontSizeItem.Items.Add("14pt", "4");
                    fontSizeItem.Items.Add("18pt", "5");
                    fontSizeItem.Items.Add("24pt", "6");
                });

                toolbar.Items.Add(new ToolbarBackColorButton(true));
                toolbar.Items.Add(new ToolbarFontColorButton());


                toolbar.Items.Add(new ToolbarInsertOrderedListButton(true));
                toolbar.Items.Add(new ToolbarInsertUnorderedListButton());
                toolbar.Items.Add(new ToolbarRemoveFormatButton());

            });
        }

    }
}