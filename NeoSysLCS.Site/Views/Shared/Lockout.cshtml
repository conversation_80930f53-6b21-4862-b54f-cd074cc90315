@model System.Web.Mvc.HandleErrorInfo

@using NeoSysLCS.Resources.Properties;

@{
    ViewBag.Title = Resources.View_Lockout_Title;
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}

<hgroup>
    <div class="alert alert-danger alert-dismissible" role="alert">
        <button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
        @Resources.View_Lockout_Message
    </div>
</hgroup>

