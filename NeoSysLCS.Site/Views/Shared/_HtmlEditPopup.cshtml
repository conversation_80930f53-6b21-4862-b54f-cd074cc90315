@using System.Web.UI.WebControls
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers

@* Model defines whether is is editable  *@
@model bool

<script type="text/javascript">
    //<![CDATA[
    function openPopupReadonly(grid, fieldname, index, title) {

        //set size
        setPopupWidth();

        //set title of popup
        popupHtmlEditor.SetHeaderText(title);

        //get value of field over callback
        grid.GetRowValues(index, fieldname, openPopupOnGetRowValues);
    }

    function openPopupOnBatchEditBegin(s, e) {
        //setsize
        setPopupWidth();

        //set title of popup
        popupHtmlEditor.SetHeaderText(s.GetHeader(e.focusedColumn.index).textContent);

        //get value of current cell
        var value = s.batchEditApi.GetCellValue(e.visibleIndex, e.focusedColumn.fieldName);

        //set value to htmleditor
        if (value != null) {
            htmlEditor.SetHtml(value);
        } else {
            htmlEditor.SetHtml("");
        }

        //save current infos
        $('#hfIndex').val(e.visibleIndex);
        $('#hfFieldname').val(e.focusedColumn.fieldName);
        $('#hfGrid').val(s.name);

        //show popup
        popupHtmlEditor.Show();

        //cancel event
        e.cancel = true;

    }

    function setPopupWidth() {
        //set height
        if (window.innerHeight < 520) {
            popupHtmlEditor.SetHeight(window.innerHeight - 50);
            $('#popupContent').height(popupHtmlEditor.GetHeight() - 130);
        } else {
            popupHtmlEditor.SetHeight(520);
            $('#popupContent').height(390);
        }

        //set width
        if (window.innerWidth < 650) {
            popupHtmlEditor.SetWidth(window.innerWidth - 50);
        } else {
            popupHtmlEditor.SetWidth(650);
        }
    }
    
    function openPopupOnGetRowValues(value) {
        //set value to htmlEditor if not null
        if (value != null) {
            $('#popupContent').html(value);
        } else {
            $('#popupContent').html(value);
        }

        //show popup
        popupHtmlEditor.Show();
    }

    function saveEditor() {
        //get values from hidden fields
        var value = htmlEditor.GetHtml();
        var fieldname = $('#hfFieldname').val();
        var index = $('#hfIndex').val();
        var grid = eval($('#hfGrid').val());

        //set value to grid
        grid.batchEditApi.SetCellValue(index, fieldname, value);

        //reset html on editor to emptystring (null doesn't work in IE)
        htmlEditor.SetHtml("");

        //hide popup
        popupHtmlEditor.Hide();
    }
    // ]]>
</script>

@Html.DevExpress().PopupControl(
    settings =>
    {
        settings.Name = "popupHtmlEditor";
        settings.Modal = true;
        settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
        settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
        settings.ScrollBars = ScrollBars.Auto;
        settings.AllowResize = true;
        settings.AllowDragging = true;

        if (Model)
        {
            settings.ClientSideEvents.Shown = @"function(s,e){
                htmlEditor.Focus();
            }";
        }
        settings.SetContent(() =>
        {

            ViewContext.Writer.Write("<input type='hidden' id='hfIndex' />");
            ViewContext.Writer.Write("<input type='hidden' id='hfFieldname' />");
            ViewContext.Writer.Write("<input type='hidden' id='hfGrid' />");

            if (Model)
            {
                Html.DevExpress().HtmlEditor(editorSettings =>
                {
                    editorSettings.Name = "htmlEditor";
                    HtmlEditorHelper.ApplyHtmlEditorSettings(editorSettings);
                    editorSettings.Height = Unit.Pixel(400);
                    editorSettings.Width = Unit.Pixel(600);
                    editorSettings.SettingsHtmlEditing.EnterMode = HtmlEditorEnterMode.BR;
                }).GetHtml();
                ViewContext.Writer.Write("<div class='popupButtons'>");
                Html.DevExpress().Button(
                    buttonSettings =>
                    {
                        buttonSettings.Name = "btnUpdateHtmlPopup";
                        buttonSettings.ControlStyle.CssClass = "button";
                        buttonSettings.Width = 80;
                        buttonSettings.Text = "OK";
                        buttonSettings.ClientSideEvents.Click = @"function(s,e){
                    saveEditor();
                }";
                    }).GetHtml();
            }
            else
            {
                ViewContext.Writer.Write("<div id='popupContent' style=''></div>");
                ViewContext.Writer.Write("<div class='popupButtons'>");
            }

            Html.DevExpress().Button(
                buttonSettings =>
                {
                    buttonSettings.Name = "btnCancelHtmlPopup";
                    buttonSettings.ControlStyle.CssClass = "button";
                    buttonSettings.Width = 80;
                    buttonSettings.Text = Resources.Navigation_Zurueck;
                    buttonSettings.ClientSideEvents.Click = "function(s, e){ popupHtmlEditor.Hide(); }";
                }
            ).GetHtml();

            ViewContext.Writer.Write("</div>");
        });

    }
).GetHtml()

