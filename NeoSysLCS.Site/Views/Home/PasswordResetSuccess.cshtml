@using System.Web.Mvc.Html
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Resources.Properties
@model NeoSysLCS.Repositories.ViewModels.ChangePasswordViewModel

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title - NeoSysLCS</title>
    @Styles.Render("~/Content/css")
    @Scripts.Render("~/bundles/modernizr")
    @Scripts.Render("~/Scripts/helpers/SelectionHelper.js")

    @Html.DevExpress().GetStyleSheets(
        new StyleSheet { ExtensionSuite = ExtensionSuite.NavigationAndLayout },
        new StyleSheet { ExtensionSuite = ExtensionSuite.GridView },
        new StyleSheet { ExtensionSuite = ExtensionSuite.TreeList },
        new StyleSheet { ExtensionSuite = ExtensionSuite.HtmlEditor }
    )

    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")
    <script src="@Url.Content("~/Scripts/jquery.unobtrusive-ajax.min.js")" type="text/javascript"></script>


    <!-- The DevExpress ASP.NET MVC Extensions' scripts -->
    @Html.DevExpress().GetScripts(
        new Script { ExtensionSuite = ExtensionSuite.NavigationAndLayout },
        new Script { ExtensionSuite = ExtensionSuite.GridView }
    )
</head>

</body>
    <div class="container">
        <div class="row">
            <div class="col-md-6 col-md-offset-3">
                <div class="login-panel panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">Passwort zurücksetzen</h3>
                    </div>
                    <div class="panel-body">
                        @using (Html.BeginForm("Login", "Account", new { ReturnUrl = ViewBag.ReturnUrl }, FormMethod.Get, new { role = "form" }))
                        {

                            <div class="form-group">
                                <div class="col-md-offset-0 col-md-12">
                                    <input type="submit" value="@Resources.Button_Passwort_Reset" class="btn btn-lg btn-success btn-block" />
                                </div>
                            </div>
                         }

                    </div>
                </div>
            </div>
        </div>
    </div>
</body>