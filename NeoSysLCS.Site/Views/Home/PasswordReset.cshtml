@using System.Web.Mvc.Html
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Resources.Properties
@model NeoSysLCS.Repositories.ViewModels.ChangePasswordViewModel

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title - NeoSysLCS</title>
    @Styles.Render("~/Content/css")
    @Scripts.Render("~/bundles/modernizr")
    @Scripts.Render("~/Scripts/helpers/SelectionHelper.js")

    @Html.DevExpress().GetStyleSheets(
        new StyleSheet { ExtensionSuite = ExtensionSuite.NavigationAndLayout },
        new StyleSheet { ExtensionSuite = ExtensionSuite.GridView },
        new StyleSheet { ExtensionSuite = ExtensionSuite.TreeList },
        new StyleSheet { ExtensionSuite = ExtensionSuite.HtmlEditor }
    )

    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")
    <script src="@Url.Content("~/Scripts/jquery.unobtrusive-ajax.min.js")" type="text/javascript"></script>


    <!-- The DevExpress ASP.NET MVC Extensions' scripts -->
    @Html.DevExpress().GetScripts(
        new Script { ExtensionSuite = ExtensionSuite.NavigationAndLayout },
        new Script { ExtensionSuite = ExtensionSuite.GridView }
    )
</head>

</body>
    <div class="container">
        <div class="row">
            <div class="col-md-4 col-md-offset-4">
                <div class="login-panel panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">@Resources.Passwort_zuruecksetzen</h3>
                    </div>
                    <div class="panel-body">
                        @using (Html.BeginForm("PasswordForgot", "Home", FormMethod.Post))
                         {
                        <fieldset class="divPwdChange">
                            <div class="form-group" style="margin-bottom: 10px;">
                                <div class="input-group">
                                    <span class="input-group-addon"><i class="fa fa-envelope-o fa-fw fa-lg"></i></span>
                                    @Html.TextBoxFor(m => m.Email, new { @class = "form-control", @autofocus = true, @placeholder = "E-Mail" })

                                    @*@Html.ValidationMessageFor(m => m.Email, "", new { @class = "text-danger" })*@
                                </div>
                            </div>
                            @*<div class="form-group">
            @Html.Label(Resources.Email_Label_Email, new { @class = "col-md-2 control-label" })
            <div class="col-md-10">
                @Html.TextBoxFor(m => m.Email, new { @class = "form-control", @style = "width:250px" })
            </div>
        </div>*@

                            <div class="form-group" style="margin-bottom: 10px;">
                                <div class="input-group">
                                    <span class="input-group-addon"><i class="fa fa-envelope-o fa-fw fa-lg"></i></span>
                                    @Html.TextBoxFor(m => m.confirmEmail, new { @class = "form-control", @autofocus = true, @placeholder = "E-Mail bestätigen" })                                  
                                </div>
                                <div style="margin-left: 52px;">
                                    @Html.ValidationMessageFor(m => m.confirmEmail, "", new { @class = "text-danger" })
                                </div>
                            </div>
                            @*<div class="form-group">
                                @Html.Label(Resources.Confirm_Email, new { @class = "col-md-2 control-label" })
                                <div class="col-md-10" style="padding-top: 5px; margin-top: 25px;">
                                    @Html.TextBoxFor(m => m.confirmEmail, new { @class = "form-control", @style = "width:250px" })
                                    @Html.ValidationMessageFor(m => m.Email, "", new { @class = "text-danger" })
                                </div>
                            </div>*@
                        </fieldset>

                            <div class="form-group">
                                <div class="col-md-offset-1 col-md-10">
                                    <input type="submit" value="@Resources.Passwort_zuruecksetzen" class="btn btn-lg btn-success btn-block" />
                                </div>
                            </div>
                         }

                    </div>
                </div>
            </div>
        </div>
    </div>
</body>