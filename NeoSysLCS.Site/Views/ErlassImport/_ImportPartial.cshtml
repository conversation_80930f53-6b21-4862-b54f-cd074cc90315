@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Utilities.Import.Erlass

@model  int


        @{
            Html.DevExpress().Button(settings =>
            {
                settings.Name = "btnImport_" + Model;
                settings.Images.Image.Url = "~/Content/images/download_26.png";
                settings.ToolTip = Resources.Erlass_Importieren;
                settings.Text = " ";
                settings.RenderMode = ButtonRenderMode.Link;
                settings.ClientSideEvents.Click = "function(s,e){ popupImport_" + Model + ".Show()}";
            }).Render();
            
            Html.DevExpress().PopupControl(settings =>
            {
                settings.Modal = true;
                settings.Name = "popupImport_" + Model;
                settings.HeaderText = Resources.Erlass_Importieren;
                settings.ShowCloseButton = true;
                settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
                settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
                settings.ClientSideEvents.Closing = " function(s, e) {$('#uploadMessage_" + Model + "').html('');}";

                settings.SetContent(() =>
                {
                    using (Html.BeginForm("Import", "ErlassImport", new { Area = "", id = Model }, FormMethod.Post, new { @class = "popupImport"}))
                    {
                        ViewContext.Writer.Write("<p>" + Resources.Erlass_waehlen_fuer_import + "</p>");
                        Html.DevExpress().UploadControl(upload =>
                        {
                            upload.Name = "fileUploadControl_" + Model;
                            upload.ShowAddRemoveButtons = false;
                            upload.ShowProgressPanel = true;
                            upload.ShowUploadButton = true;
                            upload.UploadButton.Image.Url = "~/Content/images/download_26.png";
                            upload.AddUploadButtonsHorizontalPosition = AddUploadButtonsHorizontalPosition.Right;
                            upload.ValidationSettings.Assign(Importer.ValidationSettings);
                            upload.ClientSideEvents.FileUploadComplete = "function(s, e) {" +
                                                                            "if (e.isValid) {" +
                                                                                "$('#uploadMessage_" + Model + "').html('<div class=\"alert alert-success\">' + e.callbackData + '</div>');" +
                                                                            "} else {" +
                                                                                "$('#uploadMessage_" + Model + "').html('<div class=\"alert alert-danger\">' + e.callbackData + '</div>');" +
                                                                            "}" +
                                                                         "}";
                        }).Render();
                        ViewContext.Writer.Write("<div id='uploadMessage_" + Model +"'></div>");

                    }
                });
            }).Render();
        }
