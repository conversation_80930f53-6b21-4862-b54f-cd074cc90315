using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using System.Net.Http.Headers;

namespace NeoSysLCS.Site.Suva
{
    class SuvaClient : ISuvaClient
    {


        private readonly HttpClient _httpClient = new HttpClient();
        private readonly string _checkListurl;


        public SuvaClient(string checkListUrl, string token)
        {
            _checkListurl = checkListUrl;
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        }

        public async Task<List<SuvaChecklistDto>> GetChecklists()
        {
            HttpResponseMessage response = await _httpClient.GetAsync($"{_checkListurl}/GetChecklists");
            response.EnsureSuccessStatusCode();
            
            return  await response.Content.ReadAsAsync<List<SuvaChecklistDto>>();
        }


        public async Task<SuvaChecklistDetailsDto> GetChecklistDetails(string id)
        {
            HttpResponseMessage response = await _httpClient.GetAsync($"{_checkListurl}/GetChecklist/{id}");
            if (response.IsSuccessStatusCode)
            {
                return await response.Content.ReadAsAsync<SuvaChecklistDetailsDto>();
            }

            return await Task.FromResult<SuvaChecklistDetailsDto>(null);
        }


    }
}
