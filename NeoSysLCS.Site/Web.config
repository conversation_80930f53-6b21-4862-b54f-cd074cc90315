<?xml version="1.0"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  http://go.microsoft.com/fwlink/?LinkId=301880
  -->
<configuration>
  <configSections>
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
    <sectionGroup name="devExpress">
      <section name="themes" type="DevExpress.Web.ThemesConfigurationSection, DevExpress.Web.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false"/>
      <section name="compression" type="DevExpress.Web.CompressionConfigurationSection, DevExpress.Web.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false"/>
      <section name="settings" type="DevExpress.Web.SettingsConfigurationSection, DevExpress.Web.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false"/>
      <section name="errors" type="DevExpress.Web.ErrorsConfigurationSection, DevExpress.Web.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false"/>
      <section name="resources" type="DevExpress.Web.ResourcesConfigurationSection, DevExpress.Web.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false"/>
    </sectionGroup>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net"/>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
  </configSections>
  <log4net>
    <root>
      <level value="ALL"/>
      <appender-ref ref="RollingLogFileAppenderError"/>
    </root>
    <appender name="RollingLogFileAppenderError" type="log4net.Appender.RollingFileAppender">
      <file value="C:\neosys-logs\neosys.log"/>
      <appendToFile value="true"/>
      <rollingStyle value="Composite"/>
      <datePattern value="yyyyMMdd"/>
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock"/>
      <maxSizeRollBackups value="10"/>
      <maximumFileSize value="10MB"/>
      <maxDateRollBackups value="60"/>
      <staticLogFileName value="true"/>
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%-5p %d %5rms %-22.22c{1} %-18.18M - %m%n"/>
      </layout>
    </appender>
  </log4net>
  <connectionStrings>
    <add name="NeoSysLCS_Dev" connectionString="Data Source=ICT00722;Initial Catalog=NeoSysLCS_Prod;Integrated Security=True;MultipleActiveResultSets=True" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <appSettings>
    <add key="owin:AppStartup" value="NeoSysLCS.Site.Startup,NeoSysLCS.Site" />
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <add key="Environment" value="Debug" />
    <add value="true" key="DXEnableCallbackCompression" />
    <add value="true" key="DXEnableResourceCompression" />
    <add value="true" key="DXEnableHtmlCompression" />
    <add value="true" key="DXEnableResourceMerging" />
    <add value="true" key="DXEnableThemesAssembly" />
    <add key="vs:EnableBrowserLink" value="true" />
    <add key="AzureStorageConnectionString" value="DefaultEndpointsProtocol=https;AccountName=lexplustest;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
    <add key="FilePath" value="https://localhost:44300/Upload/" />
	<add key="CortecAPI" value="Placeholder" />
    <add key="CortecTaskAPI" value="Placeholder" />
    <add key="CortecTaskAPIKey" value="Placeholder" />
    <add key="CortecTaskCreationEnabled" value="false" />
    <add key="AvoidCortecTaskDuplicates" value="false" />
  </appSettings>
  <system.net>
	  <mailSettings>
		  <smtp deliveryMethod="SpecifiedPickupDirectory">
			  <specifiedPickupDirectory
                  pickupDirectoryLocation="c:\maildrop"/>
		  </smtp>
	  </mailSettings>
    <connectionManagement>
      <add address="*" maxconnection="200"/>
    </connectionManagement>
  </system.net>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.5.1" />
      </system.Web>
  -->
  <system.web>
    <trust level="Full"/>
    <customErrors mode="On"/>
    <authentication mode="Forms">
      <forms loginUrl="~/Account/Login" timeout="2880" requireSSL="true"/>
    </authentication>
    <compilation debug="true" numRecompilesBeforeAppRestart="1000" targetFramework="4.6.1">
      <assemblies>
        <add assembly="DevExpress.Data.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Drawing.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Web.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Web.ASPxHtmlEditor.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Web.ASPxSpellChecker.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Web.ASPxTreeList.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Web.ASPxGantt.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Web.ASPxThemes.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Web.ASPxPivotGrid.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Utils.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.RichEdit.v24.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.SpellChecker.v24.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Charts.v24.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.XtraCharts.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.XtraGauges.v24.1.Presets, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Web.ASPxGauges.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.XtraCharts.v24.1.Web, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Printing.v24.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.XtraPivotGrid.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.PivotGrid.v24.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.XtraScheduler.v24.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.XtraScheduler.v24.1.Core.Desktop, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Web.ASPxScheduler.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Spreadsheet.v24.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Web.ASPxSpreadsheet.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="DevExpress.Web.Mvc5.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="System.Web.Entity, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="System.Data.Entity, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="System.Web.Abstractions, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add assembly="System.Web.Routing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add assembly="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add assembly="System.Data.Entity, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089"/>
        <add assembly="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add assembly="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add assembly="DevExpress.Web.Resources.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="ThemeAssembly, Version=19.1.3.0, Culture=neutral, PublicKeyToken=null"/>
        <add assembly="DevExpress.RichEdit.v24.1.Export, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <add assembly="System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089"/>
      </assemblies>
    </compilation>
    <pages enableSessionState="ReadOnly" validateRequest="false" clientIDMode="AutoID">
      <namespaces>
        <add namespace="DevExpress.Utils"/>
        <add namespace="DevExpress.Web"/>
        <add namespace="DevExpress.Web.ASPxHtmlEditor"/>
        <add namespace="DevExpress.Web.ASPxSpellChecker"/>
        <add namespace="DevExpress.Web.ASPxThemes"/>
        <add namespace="DevExpress.Web.ASPxTreeList"/>
        <add namespace="DevExpress.XtraCharts"/>
        <add namespace="DevExpress.XtraCharts.Web"/>
        <add namespace="DevExpress.XtraPivotGrid"/>
        <add namespace="DevExpress.Data.PivotGrid"/>
        <add namespace="DevExpress.Web.ASPxPivotGrid"/>
        <add namespace="DevExpress.Web.Mvc"/>
        <add namespace="DevExpress.Web.Mvc.UI"/>
        <add namespace="DevExpress.XtraScheduler"/>
        <add namespace="DevExpress.XtraScheduler.Native"/>
        <add namespace="DevExpress.Web.ASPxScheduler"/>
        <add namespace="DevExpress.Web.ASPxSpreadsheet"/>
        <add namespace="DevExpress.Web.ASPxScheduler.Controls"/>
        <add namespace="DevExpress.Web.ASPxScheduler.Reporting"/>
      </namespaces>
    </pages>
    <httpHandlers>
      <add type="DevExpress.Web.ASPxUploadProgressHttpHandler, DevExpress.Web.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="ASPxUploadProgressHandlerPage.ashx" validate="false"/>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DX.ashx" validate="false"/>
      <add validate="false" verb="GET,POST" path="DXDD.axd" type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
      <add validate="false" verb="GET,POST" path="DXXCD.axd" type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
    </httpHandlers>
    <httpModules>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" name="ASPxHttpHandlerModule"/>
      <!--<add name="OutputCache" type="System.Web.Caching.OutputCacheModule" />-->
      <remove name="Session"/>
      <remove name="WindowsAuthentication"/>
      <remove name="PassportAuthentication"/>
      <remove name="AnonymousIdentification"/>
      <remove name="UrlAuthorization"/>
      <remove name="FileAuthorization"/>
    </httpModules>
    <globalization culture="" uiCulture=""/>
    <httpRuntime targetFramework="4.6.1" maxRequestLength="16384" requestValidationMode="2.0" executionTimeout="240" enableVersionHeader="false"/>
    <sessionState mode="InProc" timeout="1200"/>
    <!--<sessionState mode="StateServer" stateConnectionString="tcpip=localhost:42424" timeout="1200" />-->
    <httpCookies httpOnlyCookies="false" requireSSL="true"/>
    <machineKey validation="HMACSHA512"/>
    <trace enabled="false" pageOutput="false"/>
  </system.web>
  <system.webServer>
    <applicationInitialization doAppInitAfterRestart="true">
      <add initializationPage="/Global.asax"/>
    </applicationInitialization>
    <validation validateIntegratedModeConfiguration="false"/>
    <modules runAllManagedModulesForAllRequests="true">
      <remove name="FormsAuthenticationModule"/>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" name="ASPxHttpHandlerModule"/>
    </modules>
    <handlers>
      <add type="DevExpress.Web.ASPxUploadProgressHttpHandler, DevExpress.Web.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="ASPxUploadProgressHandlerPage.ashx" name="ASPxUploadProgressHandler" preCondition="integratedMode"/>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DX.ashx" name="ASPxHttpHandlerModule" preCondition="integratedMode"/>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0"/>
      <remove name="OPTIONSVerbHandler"/>
      <remove name="TRACEVerbHandler"/>
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0"/>
      <add name="ASPxChartDesignerHandlerModule" preCondition="integratedMode" verb="GET,POST" path="DXXCD.axd" type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v24.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
    </handlers>
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="40000000"/>
      </requestFiltering>
    </security>
    <httpProtocol>
      <customHeaders>
        <remove name="X-Powered-By"/>
      </customHeaders>
    </httpProtocol>
    <staticContent>
      <!--<clientCache cacheControlMode="UseMaxAge" cacheControlMaxAge="365:00:00" />-->
      <remove fileExtension=".js"/>
      <mimeMap fileExtension=".js" mimeType="text/javascript"/>
    </staticContent>
    <!--<caching enabled="true" enableKernelCache="true" maxCacheSize="0" maxResponseSize="1024000">
      <profiles>
        <add extension=".*" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" varyByQueryString="*" />
      </profiles>
    </caching>-->
    <!--<httpCompression directory="%SystemDrive%\inetpub\temp\IIS Temporary Compressed Files" minFileSizeForComp="1024" maxDiskSpaceUsage="1024">
      <scheme name="gzip" dll="%Windir%\system32\inetsrv\gzip.dll" staticCompressionLevel="9" />
      <dynamicTypes>
        <add mimeType="text/*" enabled="true" />
        <add mimeType="message/*" enabled="true" />
        <add mimeType="application/javascript" enabled="true" />
        <add mimeType="*/*" enabled="false" />
        <add mimeType="application/json" enabled="true" />
      </dynamicTypes>
      <staticTypes>
        <add mimeType="text/*" enabled="true" />
        <add mimeType="message/*" enabled="true" />
        <add mimeType="application/javascript" enabled="true" />
        <add mimeType="*/*" enabled="false" />
      </staticTypes>
    </httpCompression>-->
    <!--<urlCompression doDynamicCompression="true" doStaticCompression="true" dynamicCompressionBeforeCache="true" />-->
    <rewrite>
      <rules>
        <rule name="HTTP to HTTPS redirect" stopProcessing="true">
          <match url="(.*)"/>
          <conditions>
            <add input="{HTTPS}" pattern="off" ignoreCase="true"/>
          </conditions>
          <action type="Redirect" url="https://{HTTP_HOST}/{R:1}" redirectType="Permanent"/>
        </rule>
      </rules>
      <outboundRules>
        <rule name="Add Strict-Transport-Security when HTTPS" enabled="true">
          <match serverVariable="RESPONSE_Strict_Transport_Security" pattern=".*"/>
          <conditions>
            <add input="{HTTPS}" pattern="on" ignoreCase="true"/>
          </conditions>
          <action type="Rewrite" value="max-age=31536000"/>
        </rule>
      </outboundRules>
    </rewrite>
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="*******-*******" newVersion="*******"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-10.0.0.0" newVersion="10.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-3.1.0.0" newVersion="3.1.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-3.1.0.0" newVersion="3.1.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.Cookies" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-3.1.0.0" newVersion="3.1.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.OAuth" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-3.1.0.0" newVersion="3.1.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Data.Edm" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-5.8.1.0" newVersion="5.8.1.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Data.Services.Client" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-5.8.1.0" newVersion="5.8.1.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Data.OData" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-5.8.1.0" newVersion="5.8.1.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Heavysoft.LockFreeSessionState.Common" publicKeyToken="ea16f0ccebd288da" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="*******-*******" newVersion="*******"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="*******-*******" newVersion="*******"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="RazorEngine" publicKeyToken="9ee697374c7e744a" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-3.10.0.0" newVersion="3.10.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Heavysoft.LockFreeSessionState.Common" publicKeyToken="12eed1a4d3dc558d" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-1.3.0.0" newVersion="1.3.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Azure.KeyVault.Core" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-2.0.0.0" newVersion="2.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="DevExpress.Web.v15.2" publicKeyToken="b88d1754d700e49a" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-15.2.14.0" newVersion="15.2.14.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.1.1.3" newVersion="4.1.1.3"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.1.1.0" newVersion="4.1.1.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.Tracing" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.1.1.0" newVersion="4.1.1.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Reflection" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.1.1.0" newVersion="4.1.1.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.InteropServices" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.1.1.0" newVersion="4.1.1.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.0" newVersion="4.0.1.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.6.0" newVersion="4.0.6.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.1" newVersion="4.0.1.1"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.5.1" newVersion="4.0.5.1"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2"/>
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.SqlConnectionFactory, EntityFramework"/>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer"/>
    </providers>
  </entityFramework>
  <devExpress>
    <themes customThemeAssemblies="ThemeAssembly" theme="ModernoNeosys" enableThemesAssembly="true"/>
    <!--<themes enableThemesAssembly="true" styleSheetTheme="" theme="Moderno" customThemeAssemblies="" />-->
    <!-- htmlcompression disabled callback compression-->
    <compression enableCallbackCompression="true" enableResourceCompression="true" enableResourceMerging="true" enableHtmlCompression="false"/>
    <settings protectControlState="true" doctypeMode="Xhtml" rightToLeft="false" embedRequiredClientLibraries="false" checkReferencesToExternalScripts="true" accessibilityCompliant="false"/>
    <errors callbackErrorRedirectUrl="/Home/Error"/>
  </devExpress>
</configuration>