@using System.Globalization
@using System.Linq
@using System.Web.UI.WebControls
@using DevExpress.Data
@using NeoSysLCS.DomainModel.Models
@using System.Xml.Linq;
@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Site.Helpers

@functions{
    bool IsSelectValue(int value)
    {
        /*foreach (var interest in Model.Objekte)
        {
            if (interest.Id == value)
                return true;
        }*/
        return true;
    }
}


@Html.DevExpress().GridView(
    settings =>
    {
        IUnitOfWork unitOfWork = new UnitOfWork();

        settings.Name = "gvRowForderungenSelection_" + ViewData["ObjektID"];
        GridViewHelper.ApplyDefaultSettings(settings);

        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        settings.KeyFieldName = "ForderungsversionID";
        settings.CallbackRouteValues = new { Controller = "ObjektForderungen", Action = "ForderungSelectionPartial", id = ViewData["ObjektID"] };

        settings.ControlStyle.CssClass = "grid";
        settings.SettingsCookies.StoreFiltering = false;
        settings.CommandColumn.Visible = true;
        settings.CommandColumn.ShowSelectCheckbox = true;
        settings.SettingsBehavior.AllowSelectByRowClick = false;
        settings.Settings.ShowGroupPanel = false;
        settings.CommandColumn.SetFilterTemplateContent("");
        settings.CommandColumn.Width = Unit.Pixel(50);

        if (User.IsInRole(Role.ObjectGuru))
        {
            settings.ClientSideEvents.Init = @"function(s, e){
            ForderungSelectionInit(" +
                                             ViewData["ObjektID"] + @",
                                   s,
                                   SelectedForderungenRows_" + ViewData["ObjektID"] + @",
                                   '#forderungenCount_" + ViewData["ObjektID"] + @"',
                                   '#forderungResult_" + ViewData["ObjektID"] + @"');
                                    }";
            settings.ClientSideEvents.SelectionChanged = @"function(s,e){
                ForderungSelectionChanged(" + ViewData["ObjektID"] + @", e);
            }";
        }
        //Columns
        settings.Columns.Add(column =>
        {

            column.Caption = "Erlass";
            column.FieldName = "ErlassID";
            //column.UnboundType = UnboundColumnType.String;
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            column.Settings.AllowHeaderFilter = DefaultBoolean.True;
            column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.CheckedList;

            NeoSysLCS_Dev context = new NeoSysLCS_Dev();
            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
            int _currentLang = sprache.SpracheID;

            List<ErlassViewModel> viewModels = (from e in context.Erlasse
                                                select new ErlassViewModel()
                                                {
                                                    ErlassID = e.ErlassID,
                                                    Uebersetzung = e.Uebersetzung
                                                }).ToList();

            foreach (ErlassViewModel viewModel in viewModels)
            {
                XDocument uebersetzung = XDocument.Parse(viewModel.Uebersetzung);
                viewModel.Titel = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Titel", _currentLang);
            }

            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
            comboBoxProperties.DataSource = viewModels.ToList();
            comboBoxProperties.TextField = "Titel";
            comboBoxProperties.ValueField = "ErlassID";
            comboBoxProperties.ValueType = typeof(int);

        });
        settings.Columns.Add(column =>
        {
            column.Visible = false;
            column.FieldName = "ErlassTitel";


        });
        settings.Columns.Add(column =>
        {
            column.Width = Unit.Pixel(50);
            column.Caption = "SR";
            column.FieldName = "ErlassSrNummer";

        });

        settings.Columns.Add(column =>
        {
            column.Width = Unit.Pixel(50);
            column.Caption = "Erlassfassung vom";
            column.FieldName = "Inkrafttretung";
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.Settings.AllowHeaderFilter = DefaultBoolean.True;
            column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.DateRangePicker;
        });
        settings.Columns.Add(column =>
        {
            column.Width = Unit.Pixel(50);
            column.Caption = "Version";
            column.FieldName = "VersionsNummer";

        });

        settings.Columns.Add(column =>
        {
            column.Width = Unit.Pixel(50);
            column.Caption = "Ausgew�hlt";
            column.FieldName = "isSelected";
            column.ColumnType = MVCxGridViewColumnType.CheckBox;
        });

        settings.Columns.Add(column =>
        {
            //column.Width = Unit.Percentage(40);
            column.Caption = "Forderung Beschreibung";
            column.FieldName = "Beschreibung";

            column.UnboundType = UnboundColumnType.String;
            column.PropertiesEdit.EncodeHtml = false;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "CurrentID";
            column.Visible = false;
            column.UnboundType = UnboundColumnType.Integer;
        });

        settings.CustomUnboundColumnData = (sender, e) =>
        {
            if (e.Column.FieldName == "BeschreibungShort")
            {
                e.Value = StringHelper.TruncateHtml((string)e.GetListSourceFieldValue("Beschreibung"), 150);
            }
            else if (e.Column.FieldName == "CurrentID")
            {
                e.Value = ViewData["ObjektID"];
            }
        };

        //setting preselected
        settings.PreRender = (sender, e) =>
        {
            MVCxGridView gridView = sender as MVCxGridView;
            if (gridView != null)
                foreach (var forderung in unitOfWork.ForderungsversionRepository.GetByObject(Convert.ToInt32(ViewData["ObjektID"])))
                {
                    gridView.Selection.SelectRowByKey(forderung.ForderungsversionID);
                }
        };

        settings.HtmlDataCellPrepared += (sender, e) =>
        {
            var aufhebung = Convert.ToDateTime(e.GetValue("Aufhebung"));
            if (aufhebung < DateTime.Today)
            {
                e.Cell.BackColor = System.Drawing.Color.Salmon;
            }
        };

    }).BindToLINQ(string.Empty, string.Empty, (s, e) =>
    {
        e.KeyExpression = "ForderungsversionID";
        var unitOfWork = new UnitOfWork();
        var query = unitOfWork.ForderungsversionRepository.GetAllForderungsversionViewModels(Convert.ToInt32(ViewData["ObjektID"]));
        e.QueryableSource = query;

    }).GetHtml()

