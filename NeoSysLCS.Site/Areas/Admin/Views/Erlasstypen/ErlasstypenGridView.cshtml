@using System.Web.UI.WebControls;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers

@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        settings.Name = "ErlasstypenGridView";
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        
        settings.KeyFieldName = "ErlasstypID";

        settings.CallbackRouteValues = new { Controller = "Erlasstypen", Action = "ErlasstypenGridView" };

        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "Erlasstypen", Action = "ErlasstypenGridViewBatchEditUpdate" };
        settings.SettingsEditing.BatchEditSettings.ShowConfirmOnLosingChanges = true;
        settings.SettingsBehavior.ConfirmDelete = false;

        if (User.IsInRole(Role.ProjectManager))
        {
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
            settings.CommandColumn.Visible = true;
            settings.CommandColumn.ShowNewButtonInHeader = true;
            settings.CommandColumn.ShowDeleteButton = true;
            settings.CommandColumn.ShowEditButton = false;
        }

        settings.Settings.ShowGroupPanel = false;
        settings.Width = Unit.Empty;

        //Columns
        settings.Columns.Add("Titel");

        settings.SettingsDetail.ShowDetailRow = true;
        settings.SetDetailRowTemplateContent(c =>
        {
            Html.RenderAction("ErlasstypUebersetzungenGridView", new { id = DataBinder.Eval(c.DataItem, "ErlasstypID") });
        });

        //GridViewHelper.AddNewestFirstSortorderColumn(settings, "ErlasstypID"); Repository sorting
        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        var errorList = ViewData["ErrorList"];

        if (errorList != null)
        {
            settings.Settings.ShowFooter = true;
            settings.SetFooterRowTemplateContent(c => Html.ViewContext.Writer.Write("Erlasstyp ist noch in Verwendung und kann nicht gel�scht werden"));
        }

    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "ErlasstypID";
}).GetHtml()

