@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories
@using NeoSysLCS.Resources.Properties

@model IQueryable<NeoSysLCS.Repositories.ViewModels.ObligationViewModel>




@{
    IUnitOfWork unitOfWork = new UnitOfWork();
    var objektId = Convert.ToInt32(ViewData["ObjektID"]);
    var objekt = unitOfWork.ObjektRepository.GetObjektViewModelByID(objektId);
    if (objekt != null)
    {
        ViewBag.Title = Resources.View_Breadcrumb_Obligations.Replace("##OBJEKT##", objekt.Name);
    }
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}

@section Scripts {
    @Scripts.Render("~/Scripts/helpers/BatchEditDeleteHelper.js")
}

@section breadcrumb{
    <a href="#" onclick="history.go(-1); return false;" title="Zurück"><i class="fa fa-arrow-left fa-lg"></i></a> |
    <span class="breadcrumb-noesys-navigation">
        <a href="/">Home</a>
        <span> > </span>
        @if (objekt != null)
        {
            @Html.ActionLink(Resources.Entitaet_Objekt_Singular, "Index", "Objekte", new { area = "Admin", expanded = objekt.ObjektID }, new { @class = "" })
        }
        else
        {
            @Html.ActionLink(Resources.Entitaet_Objekt_Singular, "Index", "Objekte", new { area = "Admin" }, new { @class = "" })
        }
        <span> > </span>
        <span>@ViewBag.Title</span>
    </span>

}

<script type="text/javascript">
    //<![CDATA[

    var objId, name, beschreibung;
    function ShowNewPopup(modal, nothing) {
        if (window.height < 800) {
            NewStandortobj.SetHeight(window.height - 50);
        } else {
            NewStandortobj.SetHeight(800);
        }

        if (window.width < 1250) {
            NewStandortobj.SetWidth(window.width - 50);
        } else {
            NewStandortobj.SetWidth(1250);
        }
        NewStandortobj.Show();
    }

    function OnBeginCallback(s, e) {
        //LoadingPanel.Show();
        Datenpanel.Hide();
    }

    function GetSelectedFieldValuesCallback(values) {

        var fields = new Array();

        //check if objID not null
        for (var j = 0; j < values.length; j++) {
            if (values[j][0] != null) {
                fields.push(values[j]);
            }
        }

        for (var j = 0; j < fields.length; j++) {
            StandortObjekt.AddNewRow();
            objId = fields[j][0];
            name = fields[j][1];
            beschreibung = fields[j][2];

            StandortObjekt.batchEditApi.EndEdit();
            StandortObjekt.batchEditApi.SetCellValue(-1 - j, 'ObjektID', objId);
            StandortObjekt.batchEditApi.SetCellValue(-1 - j, 'Name', name);
            StandortObjekt.batchEditApi.SetCellValue(-1 - j, 'Beschreibung', beschreibung);

        }
        //LoadingPanel.Show();
        StandortObjekt.UpdateEdit();
        //StandortObjekt.PerformCallback();
        Datenpanel.Hide();
        //NewStandortObjektGrid.UnSelectNode(); no uns elect possible

    }

    function onGetRowValues(Value) {
        alert(Value);
    }

    // ]]>
</script>

@Html.DevExpress().LoadingPanel(
        settings =>
        {
            settings.Name = "Datenpanel";
            settings.ShowImage = false;
            settings.Text = "Bitte warten Sie bis die Datenverarbeitung beendet ist.";
        }).GetHtml()

@Html.Partial("_ObligationGridView", Model)




