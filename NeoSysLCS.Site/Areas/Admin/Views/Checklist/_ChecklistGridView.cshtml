@using NeoSysLCS.Site.Areas.Admin.Controllers
@using NeoSysLCS.Site.Helpers
@using System.Web.UI.WebControls;
@using NeoSysLCS.Repositories.ViewModels
@using System.Xml.Linq
@using DevExpress.XtraGrid

@model IQueryable<NeoSysLCS.Repositories.ViewModels.ChecklistViewModel>

@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        settings.Name = ChecklistController.BaseGridViewSettings.GetGridViewName();
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;


        settings.SettingsResizing.ColumnResizeMode = ColumnResizeMode.NextColumn;

        settings.KeyFieldName = ChecklistController.BaseGridViewSettings.GetGridViewKeyFieldName();

        settings.CallbackRouteValues = new { Controller = "Checklist", Action = "ChecklistGridView" };


        foreach (MVCxGridViewColumn col in ChecklistController.BaseGridViewSettings.GetDataColumns(ViewContext, Model))
        {
            settings.Columns.Add(col);

            if (col.FieldName == "ErlassNummer")
            {
                col.SetDataItemTemplateContent(
                    container =>
                    {

                        var url = DataBinder.Eval(container.DataItem, "ErlassQuelle");
                        var nr = DataBinder.Eval(container.DataItem, "ErlassNummer");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, nr, false);
                        ViewContext.Writer.Write(htmlLink);
                    });
            }
        }

        GridViewHelper.AddNewestFirstSortorderColumn(settings, ChecklistController.BaseGridViewSettings.GetGridViewKeyFieldName());
        //GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        settings.SettingsDetail.ShowDetailRow = true;
        settings.SetDetailRowTemplateContent(c =>
        {
            Html.DevExpress().PageControl(pageControlSettings =>
            {
                pageControlSettings.Name = "ChecklistQuestionsPageControl_" + DataBinder.Eval(c.DataItem, "ChecklistID");
                pageControlSettings.Width = Unit.Percentage(100);

                pageControlSettings.TabPages.Add("Questions").SetContent(() =>
                {

                    Html.RenderAction("ChecklistQuestionsGridView", new
                    {
                        checklistID = DataBinder.Eval(c.DataItem, "ChecklistID")
                    });
                });

            }).Render();

        });

    });
}

@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = ChecklistController.BaseGridViewSettings.GetGridViewKeyFieldName();
}).GetHtml()

