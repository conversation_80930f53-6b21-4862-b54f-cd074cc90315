@using NeoSysLCS.Resources.Properties
@model IQueryable<NeoSysLCS.Repositories.ViewModels.RechtsbereichViewModel>

@section Scripts {
    @Scripts.Render("~/Scripts/helpers/BatchEditDeleteHelper.js")
}

@{
    ViewBag.Title = "Rechtsbereiche";
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}


@Html.Partial("RechtsbereicheGridView", Model)

<script type="text/javascript">
    var batchEditDeleteHelper = new BatchEditDeleteHelper("RechtsbereicheGridView", "RechtsbereichID", "Name", "@Resources.View_Rechtsbereiche_DeleteText");
</script>