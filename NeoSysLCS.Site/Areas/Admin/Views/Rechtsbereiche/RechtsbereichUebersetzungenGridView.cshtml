@using NeoSysLCS.DomainModel.Models;
@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers

@Html.DevExpress().GridView(
    settings =>
    {
        UnitOfWork unitOfWork = new UnitOfWork();

        GridViewHelper.ApplyUebersetzungsSettings(settings);

        settings.Name = "RechtsbereichUebersetzungenGridView_" + ViewData["RechtsbereichID"];
        settings.KeyFieldName = "RechtsbereichUebersetzungID";

        settings.CallbackRouteValues = new { Controller = "Rechtsbereiche", Action = "RechtsbereichUebersetzungenGridView", RechtsbereichID = ViewData["RechtsbereichID"] };

        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "Rechtsbereiche", Action = "RechtsbereichUebersetzungenGridViewBatchEditUpdate", RechtsbereichID = ViewData["RechtsbereichID"] };

        settings.CommandColumn.Visible = false;

        if (User.IsIn<PERSON>ole(Role.ProjectManager))
        {
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
        }
        //Columns
        settings.Columns.Add("Name");
        settings.Columns.Add(column =>
        {
            column.FieldName = "SpracheID";
            column.Caption = "Sprache";
            column.ReadOnly = true;
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

            comboBoxProperties.DataSource = unitOfWork.SpracheRepository.Get().ToList();
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueField = "SpracheID";
            comboBoxProperties.ValueType = typeof(int);
        });
        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
                currentGrid = s;
	        }";

        settings.ClientSideEvents.ClipboardCellPasting = @"function(s,e){
                e.cancel = currentGrid != s;
	        }";

    }).Bind(Model).GetHtml()