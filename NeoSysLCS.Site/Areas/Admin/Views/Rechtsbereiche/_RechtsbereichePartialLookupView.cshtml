@using System.Web.UI.WebControls
@{
    var lookupview = Html.DevExpress().GridLookup(settings =>
    {
       
        Random r = new Random();
        int n = r.Next();


        settings.Name = "RechtsbereicheLookup_" + ViewData["kontaktID"];
        settings.KeyFieldName = "RechtsbereichID";
        settings.CommandColumn.Visible = true;
        settings.CommandColumn.ShowSelectCheckbox = true;
        settings.Columns.Add("Name");
        settings.Properties.SelectionMode = DevExpress.Web.ASPxGridLookup.GridLookupSelectionMode.Multiple;
        settings.Properties.TextFormatString = "{0}";
        settings.Properties.MultiTextSeparator = ", ";
        settings.Properties.Width = Unit.Percentage(100);
        

        //settings.GridViewProperties.CallbackRouteValues = new {Controller = "Rechtsbereiche", Action = "MultiSelectPartial"};
        settings.GridViewProperties.Settings.ShowFilterRow = true;
        settings.GridViewProperties.Settings.ShowStatusBar = GridViewStatusBarMode.Visible;
        settings.GridViewProperties.SetStatusBarTemplateContent(c =>
        {
            ViewContext.Writer.Write("<div style=\"padding: 2px 8px 2px 0; float: right\">");
            @*Html.DevExpress().Button(
            btnSettings =>
            {
                btnSettings.Name = "btnClose" + ViewData["kontaktID"];
                btnSettings.UseSubmitBehavior = false;
                btnSettings.Text = "Close";
                btnSettings.ClientSideEvents.Click = "CloseGridLookup";
            }
            )
            .Render();
            *@
            ViewContext.Writer.Write("</div>");
        });

        settings.DataBound = (sender, e) =>
        {
            var gridLookup = (MVCxGridLookup) sender;
            gridLookup.GridView.Width = 350;
        };
    });
}

@lookupview.BindList(Model).GetHtml()