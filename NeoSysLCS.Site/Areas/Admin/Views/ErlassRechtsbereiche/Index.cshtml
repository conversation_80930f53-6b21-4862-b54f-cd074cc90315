@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.ViewModels

@section Scripts {
    @Scripts.Render("~/Scripts/jquery.unobtrusive-ajax.min.js")
    @Scripts.Render("~/Scripts/helpers/SelectionHelper.js")
}

@model IQueryable<RechtsbereichViewModel>

@{
    IUnitOfWork unitOfWork = new UnitOfWork();

    int erlassId = Convert.ToInt32(ViewData["ErlassID"]);
    ErlassViewModel erlassViewModel = unitOfWork.ErlassRepository.GetViewModelByID(erlassId);
    ViewBag.Title = "Rechtsbereichselektierung für Erlass "  + erlassViewModel.Titel + " (" + erlassViewModel.SrNummer + ")";
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}


@section breadcrumb{

    <a href="#" onclick="history.go(-1); return false;" title="Zurück"><i class="fa fa-arrow-left fa-lg"></i></a> |
    <span class="breadcrumb-noesys-navigation">
        <a href="/">Home</a>
        <a href="/">Home</a>
        <span> > </span>
        @Html.ActionLink("Erlasse", "Index", "Erlasse", new { area = "Admin" }, new { @class = "" })
        <span> > </span>
        <span>Rechtsbereichselektierung für Erlass «@erlassViewModel.Titel» (@erlassViewModel.SrNummer) </span>
    </span>
}


<script type="text/javascript">
    //<![CDATA[


    var selectionHelper;

    //grid init function
    function SelectionInit(itemId, grid, selectbox, countSelector, resultSelector) {
        if (selectionHelper == null) {
            selectionHelper = new SelectionHelper(
                '@Url.Action("SaveRechtsbereiche", "ErlassRechtsbereiche", new { Area = "Admin" })',
                "{0}"
            );
        }
        selectionHelper.AddSelection(itemId, grid, selectbox, countSelector, resultSelector);
    }
    //grid selection changed
    function SelectionChanged(itemId, e) {
        selectionHelper.SelectionChanged(itemId, "RechtsbereichID;Name", e);
    }
    //save button click
    function OnSubmitSelectionClick(itemId) {
        selectionHelper.OnSubmitClick(itemId,@ViewData["ErlassID"] );
    }
   
    // ]]>
</script>
@Html.Partial("_ErlassRechtsbereichePartial", Model)