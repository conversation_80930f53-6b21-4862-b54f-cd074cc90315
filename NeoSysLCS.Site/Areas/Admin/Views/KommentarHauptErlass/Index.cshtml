@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.ViewModels

@section Scripts {
    @Scripts.Render("~/Scripts/jquery.unobtrusive-ajax.min.js")
    @Scripts.Render("~/Scripts/helpers/SingleSelectionHelper.js")
}

@model IQueryable<ErlassViewModel>


@{
    IUnitOfWork unitOfWork = new UnitOfWork();
    ViewBag.Title = "HauptErlassselektierung";
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}


<script type="text/javascript">
    //<![CDATA[


    var selectionHelper;

    //grid init function
    function SelectionInit(itemId, grid, selectbox, countSelector, resultSelector) {
        if (selectionHelper == null) {
            selectionHelper = new SingleSelectionHelper(
                '@Url.Action("SaveKommentarHauptErlass", "KommentarHauptErlass", new { Area = "Admin" })',
                "{0}"
            );
        }
        selectionHelper.AddSelection(itemId, grid, selectbox, countSelector, resultSelector);
    }
    //grid selection changed
    function SelectionChanged(itemId, e) {
        selectionHelper.SelectionChanged(itemId, "ErlassID;Titel", e);
    }

    function EmptySelections() {
        selectionHelper.EmptySelections();
    }

    //save button click
    function OnSubmitSelectionClick(itemId) {
        selectionHelper.OnSubmitClick(itemId);
    }
   
    // ]]>
</script>
@Html.Partial("_KommentarHauptErlassPartial", Model)