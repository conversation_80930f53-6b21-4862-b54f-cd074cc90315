@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers


@{
    var grid = Html.DevExpress().GridView(
        settings =>
        {
            UnitOfWork unitOfWork = new UnitOfWork();

            GridViewHelper.ApplyDefaultSettings(settings);
            settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

            settings.Name = "gvRowSelection";
            settings.KeyFieldName = "ErlassID";

            settings.CallbackRouteValues = new { Controller = "KommentarHauptErlass", Action = "HauptErlassSelectionPartial", id = ViewData["KommentarID"] };
            settings.ControlStyle.CssClass = "grid";

            settings.SettingsCookies.StoreFiltering = false;
            settings.CommandColumn.Visible = true;
            settings.CommandColumn.ShowSelectCheckbox = true;
            settings.SettingsBehavior.AllowSelectByRowClick = false;
            settings.SettingsBehavior.AllowSelectSingleRowOnly = true;
            settings.Settings.ShowGroupPanel = false;

            MVCxGridViewColumn column = settings.Columns.Add("Titel");
            column.Caption = "Titel";

            MVCxGridViewColumn columnNumber = settings.Columns.Add("SrNummer");
            columnNumber.Caption = "Nummer";


            //setting preselected
            settings.PreRender = (sender, e) =>
            {
                MVCxGridView gridView = sender as MVCxGridView;
                if (gridView != null)
                {
                    Kommentar kom = unitOfWork.KommentarRepository.GetByID(Convert.ToInt32(ViewData["KommentarID"]));
                    gridView.Selection.SelectRowByKey(kom.HauptErlassID);
                }
            };

            settings.ClientSideEvents.Init = @"function(s, e){
            SelectionInit(" +
                                          ViewData["KommentarID"] + @",
                                   s,
                                   SelectedRows" + @",
                                   '#count" + @"',
                                   '#Productresult" + @"');
                                    }";
            settings.ClientSideEvents.SelectionChanged = @"function(s,e){
                SelectionChanged(" + ViewData["KommentarID"] + @", e);
            }";

        });
}

@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
    {
        var unitOfWork = new UnitOfWork();
        var query = unitOfWork.ErlassRepository.GetAllErlassViewModels();
        e.QueryableSource = query;
        e.KeyExpression = "ErlassID";
    }).GetHtml()