@using System.Web.UI.WebControls
@using DevExpress.Data
@using Microsoft.AspNet.Identity
@using Microsoft.AspNet.Identity.EntityFramework
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Areas.Admin.Controllers
@using NeoSysLCS.Site.Helpers

@model IQueryable<NeoSysLCS.Repositories.ViewModels.OfferViewModel>




@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        IUnitOfWork unitOfWork = new UnitOfWork();

        settings.Name = "OfferInactiveView";
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        SessionHelper sessionHelper = new SessionHelper();


        settings.KeyFieldName = "OfferID";

        settings.CallbackRouteValues = new { Controller = "KundenOffer", Action = "KundenOfferExistingCustomersPartialView", offerId = ViewData["OfferID"] };
        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "KundenOffer", Action = "KundenOfferExistingCustomersGridViewBatchEditUpdate" };

        settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
        settings.CommandColumn.Visible = true;
        settings.Settings.ShowGroupPanel = false;

        settings.SettingsPopup.HeaderFilter.Height = Unit.Pixel(440);
        settings.SettingsPopup.HeaderFilter.Width = Unit.Pixel(300);

        if (User.IsInRole(Role.Admin))
        {
            settings.CommandColumn.ShowNewButtonInHeader = false;
            settings.CommandColumn.ShowDeleteButton = true;

        }
        var detailsButton = new GridViewCommandColumnCustomButton();
        detailsButton.ID = "CustomerCorrtecDetails"; //todo Sprache
        detailsButton.Text = "<img title='Details' src='/Content/images/folder-open-o_26.png'>"; //todo Sprache
        
        settings.CommandColumn.CustomButtons.Add(detailsButton);
        settings.ClientSideEvents.BeginCallback = "OnBeginCallback";
        settings.ClientSideEvents.CustomButtonClick = "OnOffersCustomButtonClick";

        //Columns

        GridViewHelper.AddNewestFirstSortorderColumn(settings, "OfferID");

        foreach (MVCxGridViewColumn col in KundenOfferController.BaseGridViewSettings.GetDataColumnsExistingCustomer())
        {
            settings.Columns.Add(col);
        }

        settings.DataBound = (sender, e) => {  
            var gr = sender as MVCxGridView;  
            var column = gr.Columns["OfferVolume"] as MVCxGridViewColumn;
            System.Diagnostics.Debug.Assert(column != null, "column != null");
            column.ColumnType = MVCxGridViewColumnType.SpinEdit;
            (column.PropertiesEdit as SpinEditProperties).NumberType = SpinEditNumberType.Integer;
        };  

    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }


}
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "OfferID";
}).GetHtml()


