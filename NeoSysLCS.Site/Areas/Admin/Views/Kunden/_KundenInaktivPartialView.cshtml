@using System.Web.UI.WebControls
@using Microsoft.AspNet.Identity
@using Microsoft.AspNet.Identity.EntityFramework
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Areas.Admin.Controllers
@using NeoSysLCS.Site.Helpers

@model IQueryable<NeoSysLCS.Repositories.ViewModels.KundeViewModel>




@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        settings.Name = "KundenInaktivView";
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        SessionHelper sessionHelper = new SessionHelper();


        settings.KeyFieldName = "KundeID";

        settings.CallbackRouteValues = new { Controller = "Kunden", Action = "KundenInaktivPartialView", kundenId = ViewData["KundeID"] };
        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "Kunden", Action = "KundeInaktivGridViewBatchEditUpdate" };

        settings.CustomActionRouteValues = new { Controller = "Kunden", Action = "CopyKundeInaktivPartialView", key = ViewData["key"]};

        var copybtn = new GridViewCommandColumnCustomButton();
        copybtn.ID = "CopyCustomer"; //todo Sprache
        copybtn.Text = "<img title='Kunde kopieren' src='/Content/images/copy-24.png'>"; //todo Sprache

        var detailsButton = new GridViewCommandColumnCustomButton();
        detailsButton.ID = "CustomerCorrtecDetails"; //todo Sprache
        detailsButton.Text = "<img title='Details' src='/Content/images/folder-open-o_26.png'>"; //todo Sprache
        
        settings.CommandColumn.CustomButtons.Add(detailsButton);

        settings.ClientSideEvents.BeginCallback = "OnBeginCallback";
        settings.ClientSideEvents.CustomButtonClick = "OnCustomButtonClick";

        settings.CommandColumn.CustomButtons.Add(copybtn);

        settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
        settings.CommandColumn.Visible = true;
        settings.Settings.ShowGroupPanel = false;

        settings.SettingsPopup.HeaderFilter.Height = Unit.Pixel(440);
        settings.SettingsPopup.HeaderFilter.Width = Unit.Pixel(300);


        if (User.IsInRole(Role.Admin))
        {
            settings.CommandColumn.ShowDeleteButton = true;
        }

        //Columns

        GridViewHelper.AddNewestFirstSortorderColumn(settings, "KundeID");

        foreach (MVCxGridViewColumn col in KundenController.KundenGridViewSettings.GetDataColumns())
        {
            settings.Columns.Add(col);
        }

        settings.PreRender = (s, e) =>
        {
            MVCxGridView kgrid = (MVCxGridView)s;
            for (var x = 0; x < kgrid.VisibleRowCount; x++)
            {
                int kundeId = (int)kgrid.GetRowValues(x, "KundeID");
                if (Request.Params["expanded"] != null)
                {
                    int expanded = Convert.ToInt32(Request.Params["expanded"]);

                    ///foreach (string exp in expanded)
                    //{
                    if (expanded == kundeId)
                    {
                        kgrid.DetailRows.ExpandRow(x);
                    }
                    //}
                }
            }
        };

        settings.SettingsDetail.ShowDetailRow = true;
        settings.SetDetailRowTemplateContent(c =>
        {
            Html.DevExpress().PageControl(pageControlSettings =>
            {
                int kId = DataBinder.Eval(c.DataItem, "KundeID") == null ? Convert.ToInt32(Request.Params["expanded"]) : (int)DataBinder.Eval(c.DataItem, "KundeID");

                pageControlSettings.Name = "KundePageControl_" + kId;
                pageControlSettings.Width = Unit.Percentage(100);
                // TODO �bersetzung

                pageControlSettings.SaveStateToCookies = true;


                pageControlSettings.TabPages.Add("Standorte").SetContent(() =>
                {
                    Html.RenderAction("KundenStandortPartialView", new { kundeId = kId, parentTab = "KundenInaktivView" });
                });
                // TODO �bersetzung
                pageControlSettings.TabPages.Add("Kontakte").SetContent(() =>
                {
                    Html.RenderAction("KundenKontaktePartialView", new { kundeId = kId });
                });

                pageControlSettings.TabPages.Add("Menu Standort").SetContent(() =>
                {
                    Html.RenderAction("StandortMenuPartialView", new { kundeId = kId });
                });

            }).Render();
        });

    });
    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }


}
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "KundeID";
}).GetHtml()


