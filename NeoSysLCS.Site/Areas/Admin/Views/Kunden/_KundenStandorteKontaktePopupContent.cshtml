@using System.Web.UI.WebControls
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@model IEnumerable<NeoSysLCS.Repositories.ViewModels.KontaktViewModel>
@using NeoSysLCS.Resources.Properties

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title - NeoSysLCS</title>
    @Styles.Render("~/Content/css")
    @Scripts.Render("~/bundles/modernizr")
    @Scripts.Render("~/Scripts/helpers/SelectionHelper.js")

    @Html.DevExpress().GetStyleSheets(
        new StyleSheet { ExtensionSuite = ExtensionSuite.NavigationAndLayout },
        new StyleSheet { ExtensionSuite = ExtensionSuite.GridView },
        new StyleSheet { ExtensionSuite = ExtensionSuite.TreeList },
        new StyleSheet { ExtensionSuite = ExtensionSuite.HtmlEditor }
    )

    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")
    <script src="@Url.Content("~/Scripts/jquery.unobtrusive-ajax.min.js")" type="text/javascript"></script>


    <!-- The DevExpress ASP.NET MVC Extensions' scripts -->
    @Html.DevExpress().GetScripts(
        new Script { ExtensionSuite = ExtensionSuite.NavigationAndLayout },
        new Script { ExtensionSuite = ExtensionSuite.GridView }
    )

    <script type="text/javascript">
        //<![CDATA[
        var selectionHelper;

        //grid init function
        function SelectionInit(itemId, grid, selectbox, countSelector, resultSelector) {
            if (selectionHelper == null) {
                selectionHelper = new SelectionHelper(
                    '@Url.Action("SaveStandortKontakte", "Kunden", new { Area = "Admin" })',
                    "{0}"
                );
            }
            selectionHelper.AddSelection(itemId, grid, selectbox, countSelector, resultSelector);
        }
        //grid selection changed
        function SelectionChanged(itemId, e) {
            selectionHelper.SelectionChanged(itemId, "KontaktID;FullName", e);
        }
        //save button click
        function OnSubmitSelectionClick(itemId) {
            selectionHelper.OnSubmitClick(itemId);
        }
       
        // ]]>
    </script>


</head>
<body style="background-color: #fff;">

    @{
        UnitOfWork unitOfWork = new UnitOfWork();

        ViewContext.Writer.Write("<div>");
        ViewContext.Writer.Write("<div class=\"col-xs-4\">");
        Html.DevExpress().ListBox(
            boxSettings =>
            {
                boxSettings.Name = "SelectedRows";
                boxSettings.Properties.EnableItemsVirtualRendering = DefaultBoolean.False;
                boxSettings.Properties.EnableClientSideAPI = true;
                boxSettings.Height = 300;
                boxSettings.Width = Unit.Percentage(100);
                boxSettings.Properties.ValueField = "KontaktID";
                boxSettings.Properties.ValueType = typeof(int);

            }).GetHtml();
        ViewContext.Writer.Write("<div class='text'>" + Resources.Popup_Ausgewaehlt + ":&nbsp;<strong id='count'>0</strong></div>");
        ViewContext.Writer.Write("</div><div class=\"col-xs-8\">");

        Html.RenderPartial("_RowSelectionPartialKontakte", Model);

        ViewContext.Writer.Write("</div></div>");


        Html.DevExpress().Button(buttonSettings =>
        {
            buttonSettings.Name = "btnSubmit";
            buttonSettings.Text = Resources.Button_Zuordnung_speichern;

            buttonSettings.UseSubmitBehavior = true;
            buttonSettings.ControlStyle.CssClass = "btn pull-right btn-sm";
            buttonSettings.ClientSideEvents.Click = @"function(s,e){
                            OnSubmitSelectionClick(" + @ViewData["StandortID"] + @");
                        }";
        }).GetHtml();
        Html.DevExpress().Button(
            buttonSettings =>
            {
                buttonSettings.Name = "btnCancel";
                buttonSettings.ControlStyle.CssClass = "btn btn-sm";
                buttonSettings.Width = 80;
                buttonSettings.Text = Resources.Button_Abbrechen;
                buttonSettings.ClientSideEvents.Click = "function(s, e){ parent.pcModalMode_Kontakte" + ViewData["KundeID"] + ".Hide(); }";
            }
        )
        .Render();


        ViewContext.Writer.Write("<div id=\"Productresult\" class=\"pull-right label label-success\"></div>");



    }

</body>
</html>