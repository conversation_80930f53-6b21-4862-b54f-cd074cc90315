@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories.Helper

@{
    ViewBag.Title = "Kunden - Übersicht";
    Layout = "~/Views/Shared/_Layout.cshtml";
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}


<script type="text/javascript">
    //<![CDATA[

    function ShowDetailPopup(url, win) {
        modal = eval(win);

        if (window.height < 600) {
            modal.SetHeight(window.height - 50);
        } else {
            modal.SetHeight(600);
        }

        if (window.width < 800) {
            modal.SetWidth(window.width - 50);
        } else {
            modal.SetWidth(800);
        }

        modal.SetContentUrl(url);
        modal.Show();
    }

    function CloseDetailPopup(win) {
        modal = eval(win);
        modal.Hide();
    }

    var clickedRowKey;
    function OnBeginCallback(s, e) {
        if (e.command === "CUSTOMCALLBACK") {
            e.customArgs["key"] = clickedRowKey;
        }
    }
    function OnEndCallback(s, e) {
        //s.PerformCallback(); // This refreshes the grid
    }
    function OnCustomButtonClick(s, e) {
        clickedRowKey = s.GetRowKey(e.visibleIndex);
        if (e.buttonID === "CopyCustomer") {
            s.PerformCallback({ action: "copy", standortId: clickedRowKey });
        }
        else if (e.buttonID === "UnarchiveStandort") {
            if (confirm("Sind Sie sicher, dass Sie diesen Standort aus dem Archiv wiederherstellen möchten?")) {
                s.PerformCallback({ action: "unarchive", standortId: clickedRowKey });
            }
        }
        else if (e.buttonID === "Archive") {
            if (confirm("Sind Sie sicher, dass Sie den Standort archivieren möchten ?")) {
                s.PerformCallback({ action: "archive", standortId: clickedRowKey });
            }
        }
        else if (e.buttonID == "CustomerCorrtecDetails") {
            CustomerCorrtecDetails.SetContentUrl("/Admin/Kunden/KundeCorrtecDetails?key=" + clickedRowKey);

            if (window.height < 520) {
                CustomerCorrtecDetails.SetHeight(window.height - 50);
            } else {
                CustomerCorrtecDetails.SetHeight(520);
            }

            if (window.width < 650) {
                CustomerCorrtecDetails.SetWidth(window.width - 50);
            } else {
                CustomerCorrtecDetails.SetWidth(650);
            }
            CustomerCorrtecDetails.SetHeaderText("Kundendaten");
            CustomerCorrtecDetails.Show();
        }

    }

    function OnOffersCustomButtonClick(s, e) {
        clickedRowKey = s.GetRowKey(e.visibleIndex);
        if (e.buttonID == "CopyCustomer") {
            s.PerformCallback();
        }
        else if (e.buttonID == "CustomerCorrtecDetails") {
            CustomerCorrtecDetails.SetContentUrl("/Admin/KundenOffer/KundenOfferCorrtecDetails?key=" + clickedRowKey);

            if (window.height < 520) {
                CustomerCorrtecDetails.SetHeight(window.height - 50);
            } else {
                CustomerCorrtecDetails.SetHeight(520);
            }

            if (window.width < 650) {
                CustomerCorrtecDetails.SetWidth(window.width - 50);
            } else {
                CustomerCorrtecDetails.SetWidth(650);
            }
            CustomerCorrtecDetails.SetHeaderText("Kundendaten");
            CustomerCorrtecDetails.Show();
        }

    }



    // ]]>
</script>

@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "CustomerCorrtecDetails";
    settings.ScrollBars = ScrollBars.Auto;
    settings.ShowPageScrollbarWhenModal = true;
    settings.Width = 400;
    settings.Height = 510;
    settings.ResizingMode = ResizingMode.Live;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.Modal = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
}).GetHtml()

@Html.Partial("_KundenPageControlCallbacksPartial")
@Html.Partial("_ReadOnlyPopup")
@Html.Partial("_HtmlEditPopup", User.IsInRole(Role.ProjectManager))




