@using System.Web.UI.WebControls
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@model IEnumerable<NeoSysLCS.Repositories.ViewModels.UserViewModel>
@using NeoSysLCS.Resources.Properties

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title - NeoSysLCS</title>
    @Styles.Render("~/Content/css")
    @Scripts.Render("~/bundles/modernizr")
    @Scripts.Render("~/Scripts/helpers/SelectionHelper.js")

    @Html.DevExpress().GetStyleSheets(
        new StyleSheet { ExtensionSuite = ExtensionSuite.NavigationAndLayout },
        new StyleSheet { ExtensionSuite = ExtensionSuite.GridView },
        new StyleSheet { ExtensionSuite = ExtensionSuite.TreeList },
        new StyleSheet { ExtensionSuite = ExtensionSuite.HtmlEditor }
    )

    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")
    <script src="@Url.Content("~/Scripts/jquery.unobtrusive-ajax.min.js")" type="text/javascript"></script>
    <script type="text/javascript">
function onFileUploadComplete(s, e) {
    $.ajax({
        url: '@Url.Action("GetUploadResult", "Kunden")',
        type: 'GET',
        success: function (response) {
            console.log("AJAX Success", response);

            // Clear any previous content
            $('#uploadResult').empty();

            // Check if the response contains an error
            if (response.isError) {
                //$('#uploadResult').text(response.message).css('color', 'red');
                if (Array.isArray(response.message)) {
                    var uploadErrorMessage = '@Resources.UploadErrorExcel'
                    var newDivErr = $('<div style="margin: 2px;margin-top:4px;color:red; font-weight: bold;"></div>').text(uploadErrorMessage);
                    $('#uploadResultErr').append(newDivErr);
                    response.message.forEach(function (msg) {

                        var newDiv = $('<div style="margin: 2px;margin-top:4px;color:red"></div>').text(msg); // Create a new div with the message text
                        $('#uploadResult').append(newDiv); // Append the new div to the container
                    });
                } else {
                    $('#uploadResult').text(response.message).css({
                        'color': 'green',
                        'margin-top': '5px'
                    });
                }
            } else {
                $('#uploadResult').text(response.message).css({
                    'color': 'green',
                    'margin-top': '5px'  
                });

            }
        },
        error: function (xhr, status, error) {
            console.log("AJAX Error", status, error);
            $('#uploadResult').text('Error fetching upload result').css('color', 'red');
        }
    });
}

    </script>

    <!-- The DevExpress ASP.NET MVC Extensions' scripts -->
    @Html.DevExpress().GetScripts(
        new Script { ExtensionSuite = ExtensionSuite.NavigationAndLayout },
        new Script { ExtensionSuite = ExtensionSuite.GridView }
    )

</head>
<body style="background-color: #fff">
    @{ Html.BeginForm(); }

    @Html.DevExpress().UploadControl(
        settings =>
        {
            settings.Name = "uploadControl";
            settings.FileInputCount = 1;
            settings.CallbackRouteValues = new { Controller = "Kunden", Action = "FileUploadErlassImport", standortId = ViewData["standortId"], field = ViewData["field"], languageId = ViewData["languageId"] };
            settings.ClientSideEvents.FileUploadComplete = "onFileUploadComplete";
            settings.ShowProgressPanel = true;
            settings.AutoStartUpload = true;
            settings.Width = Unit.Percentage(100);


        }).GetHtml()

    @{ Html.EndForm(); }

    <div id="uploadResultErr"></div>
    <div id="uploadResult"></div>

    @{
        if (ViewData["url"] != null)
        {

            @Html.DevExpress().HyperLink(hyperlink =>
            {
                hyperlink.Properties.Text = (string)ViewData["url"];
                hyperlink.NavigateUrl = (string)ViewData["url"];
                hyperlink.Properties.Target = "_blank";
            }).GetHtml();

            ViewContext.Writer.Write("&nbsp;&nbsp;");

            @Html.DevExpress().HyperLink(hyperlink =>
            {
                hyperlink.EncodeHtml = false;
                hyperlink.Properties.Text = "<i class=\" fa fa-remove\"></i>";
                hyperlink.NavigateUrl = Url.Action("FileErlassImportDelete", "Kunden", new { standortId = ViewData["standortId"], field = ViewData["field"], languageId = ViewData["languageId"] });

            }).GetHtml();
        }
    }
</body>
</html>