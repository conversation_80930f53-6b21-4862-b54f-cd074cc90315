@using System.Web.UI.WebControls
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@model IEnumerable<NeoSysLCS.Repositories.ViewModels.UserViewModel>
@using NeoSysLCS.Resources.Properties

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title - NeoSysLCS</title>
    @Styles.Render("~/Content/css")
    @Scripts.Render("~/bundles/modernizr")
    @Scripts.Render("~/Scripts/helpers/SelectionHelper.js")

    @Html.DevExpress().GetStyleSheets(
        new StyleSheet { ExtensionSuite = ExtensionSuite.NavigationAndLayout },
        new StyleSheet { ExtensionSuite = ExtensionSuite.GridView },
        new StyleSheet { ExtensionSuite = ExtensionSuite.TreeList },
        new StyleSheet { ExtensionSuite = ExtensionSuite.HtmlEditor }
    )

    @Scripts.Render("~/bundles/jquery")
    @Scripts.Render("~/bundles/bootstrap")
    <script src="@Url.Content("~/Scripts/jquery.unobtrusive-ajax.min.js")" type="text/javascript"></script>


    <!-- The DevExpress ASP.NET MVC Extensions' scripts -->
    @Html.DevExpress().GetScripts(
        new Script { ExtensionSuite = ExtensionSuite.NavigationAndLayout },
        new Script { ExtensionSuite = ExtensionSuite.GridView }
    )

</head>
<body style="background-color: #fff">
    @{ Html.BeginForm(); }

    @Html.DevExpress().UploadControl(
        settings =>
        {
            settings.Name = "uploadControl";
            settings.FileInputCount = 1;
            settings.CallbackRouteValues = new { Controller = "Kunden", Action = "FileUpload", standortId = ViewData["standortId"], field = ViewData["field"], languageId = ViewData["languageId"] };
            settings.ClientSideEvents.FileUploadComplete = "function(s, e){ window.parent.CloseDetailPopup('pcModalMode_StandortBerichtFileUpload'); }";
            settings.ShowProgressPanel = true;
            settings.AutoStartUpload = true;

        }).GetHtml()

    @{ Html.EndForm(); }

    @{
        if (ViewData["url"] != null)
        {

            @Html.DevExpress().HyperLink(hyperlink =>
            {
                hyperlink.Properties.Text = (string)ViewData["url"];
                hyperlink.NavigateUrl = (string)ViewData["url"];
                hyperlink.Properties.Target = "_blank";
            }).GetHtml();

            ViewContext.Writer.Write("&nbsp;&nbsp;");

            @Html.DevExpress().HyperLink(hyperlink =>
            {
                hyperlink.EncodeHtml = false;
                hyperlink.Properties.Text = "<i class=\" fa fa-remove\"></i>";
                hyperlink.NavigateUrl = Url.Action("FileDelete", "Kunden", new { standortId = ViewData["standortId"], field = ViewData["field"], languageId = ViewData["languageId"] });

            }).GetHtml();
        }
    }
</body>
</html>