@using System.Globalization
@using System.Web.UI.WebControls
@using DevExpress.Data.Filtering
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.Site.Areas.Admin.Controllers

@model IQueryable<NeoSysLCS.Repositories.ViewModels.KundeWithSummaryViewModel>

@functions{
    public static string HighlightSearchText(string source, string searchText)
    {
        if (string.IsNullOrWhiteSpace(searchText))
            return source;
        var regex = new System.Text.RegularExpressions.Regex(System.Text.RegularExpressions.Regex.Escape(searchText), System.Text.RegularExpressions.RegexOptions.IgnoreCase);
        if (regex.IsMatch(source))
            return string.Format("<span>{0}</span>", regex.Replace(source, "<span class='dxgvHL'>$0</span>"));
        return source;
    }
}


@{
    if (Model.Any())
    {
        List<RechtsbereichViewModel> rechtsbereicheList = new List<RechtsbereichViewModel>();
        List<SpracheViewModel> sprachenList = new List<SpracheViewModel>();
        NeoSysLCS_Dev context = new NeoSysLCS_Dev();
        var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
        Sprache sprache = context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
        int _currentLang = sprache.SpracheID;
        foreach (var ks in Model.ToList())
        {
            foreach (var rb in ks.Rechtsbereiche)
            {
                rechtsbereicheList.Add(rb);
            }
            foreach (var sp in ks.Sprachen)
            {
                sprachenList.Add(sp);
            }
        }
        var allRechtsbereiche = rechtsbereicheList.Distinct().ToList();
        var allSprachen = sprachenList.Distinct().ToList();

        var grid = Html.DevExpress().GridView(settings =>
        {
            settings.Name = KundenController.AktualisierungGridViewSettings.GetGridViewName();
            GridViewHelper.ApplyDefaultSettings(settings);
            settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

            SessionHelper sessionHelper = new SessionHelper();


            settings.KeyFieldName = KundenController.AktualisierungGridViewSettings.GetGridViewKeyFieldName();

            settings.CallbackRouteValues = new
            {
                Controller = "Kunden",
                Action = "KundenAktualisierungPartialView",
                kundenId = ViewData["KundeID"]
            };
            settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "Kunden", Action = "KundeAktualisierungGridViewBatchEditUpdate" };


            settings.CustomActionRouteValues = new { Controller = "Kunden", Action = "CopyKundeAktualisierungPartialView", key = ViewData["key"] };

            var copybtn = new GridViewCommandColumnCustomButton();
            copybtn.ID = "CopyCustomer"; //todo Sprache
            copybtn.Text = "<img title='Kunde kopieren' src='/Content/images/copy-24.png'>"; //todo Sprache


            var detailsButton = new GridViewCommandColumnCustomButton();
            detailsButton.ID = "CustomerCorrtecDetails"; //todo Sprache
            detailsButton.Text = "<img title='Details' src='/Content/images/folder-open-o_26.png'>"; //todo Sprache

            settings.ClientSideEvents.BeginCallback = "OnBeginCallback";
            settings.ClientSideEvents.CustomButtonClick = "OnCustomButtonClick";

            settings.CommandColumn.CustomButtons.Add(copybtn);
            settings.CommandColumn.CustomButtons.Add(detailsButton);


            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
            settings.CommandColumn.Visible = true;
            settings.Settings.ShowGroupPanel = false;

            settings.SettingsPopup.HeaderFilter.Height = Unit.Pixel(440);
            settings.SettingsPopup.HeaderFilter.Width = Unit.Pixel(300);

            if (User.IsInRole(Role.Admin))
            {
                settings.CommandColumn.ShowDeleteButton = true;
            }

            //Columns

            GridViewHelper.AddNewestFirstSortorderColumn(settings, "KundeID");

            foreach (MVCxGridViewColumn col in KundenController.AktualisierungGridViewSettings.GetDataColumns())
            {
                if (col.FieldName == "Beschreibung")
                {
                    col.ColumnType = MVCxGridViewColumnType.Memo;
                    var memoProp = col.PropertiesEdit as MemoProperties;
                    memoProp.EncodeHtml = false;
                    col.ReadOnly = true;
                    var prop = (col.PropertiesEdit as EditProperties);
                    if (prop != null)
                    {
                        prop.ValidationSettings.Display = Display.Dynamic;
                    }
                    col.SetDataItemTemplateContent(
                        container =>
                        {
                            var beschreibung = (string)DataBinder.Eval(container.DataItem, "Beschreibung");
                            if (string.IsNullOrEmpty(beschreibung))
                            {
                                ViewContext.Writer.Write(Resources.Fehler_Keine_Uebersetzung);
                            }
                            else
                            {
                                ViewContext.Writer.Write(StringHelper.TruncateHtml(beschreibung, 50));
                            }
                        }
                    );
                }

                if (col.FieldName == "BillingInfo")
                {
                    col.SetDataItemTemplateContent(
                        container =>
                        {
                            var billingInfo = (string)DataBinder.Eval(container.DataItem, "BillingInfo");
                            if (!string.IsNullOrEmpty(billingInfo))
                            {
                                ViewContext.Writer.Write(StringHelper.TruncateHtml(billingInfo, 50));
                            }
                        }
                    );
                }

                settings.Columns.Add(col);
            }

            settings.Columns.Add(column =>
            {
                column.FieldName = "Rechtsbereiche";
                column.Caption = Resources.Entitaet_Kunde_RechtsbereicheSumm;
                //define as combobox for filtern over the enum
                column.ColumnType = MVCxGridViewColumnType.ComboBox;

                column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.CheckedList;
                column.Settings.AllowAutoFilter = DefaultBoolean.False;

                column.MinWidth = 140;
                column.ReadOnly = true;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.EditFormSettings.Visible = DefaultBoolean.False;

                column.Settings.ShowFilterRowMenu = DefaultBoolean.False;
                column.Settings.AllowSort = DefaultBoolean.False;
                column.SetDataItemTemplateContent(c =>
                {
                    var test = c.Grid.SearchPanelFilter;
                    var dataItem = DataBinder.Eval(c.DataItem, "Rechtsbereiche");
                    if (dataItem != null)
                    {
                        IEnumerable<RechtsbereichViewModel> rechtsbereiche = dataItem as IEnumerable<RechtsbereichViewModel>;
                        List<string> tbl = new List<string>();

                        foreach (RechtsbereichViewModel t in rechtsbereiche)
                        {
                            tbl.Add(t.Name);
                        }

                        Html.DevExpress().Label(labelSettings =>
                        {
                            labelSettings.Name = "Rechtsbereiche" + c.ClientID;
                            labelSettings.Text = String.Join(", ", tbl.Distinct());
                            labelSettings.EncodeHtml = false;
                            labelSettings.PreRender += (s, e) =>
                            {
                                MVCxLabel lbl = (MVCxLabel)s;
                                lbl.Text = HighlightSearchText(lbl.Text, c.Grid.SearchPanelFilter);
                            };
                        }).Render();
                    }
                });
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Sprachen";
                column.Caption = Resources.Entitaet_Kunde_SprachenSumm;
                //define as combobox for filtern over the enum
                column.ColumnType = MVCxGridViewColumnType.ComboBox;

                column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.CheckedList;
                column.Settings.AllowAutoFilter = DefaultBoolean.False;

                column.MinWidth = 140;
                column.ReadOnly = true;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.EditFormSettings.Visible = DefaultBoolean.False;

                column.Settings.ShowFilterRowMenu = DefaultBoolean.False;
                column.Settings.AllowSort = DefaultBoolean.False;
                column.SetDataItemTemplateContent(c =>
                {
                    var test = c.Grid.SearchPanelFilter;
                    var dataItem = DataBinder.Eval(c.DataItem, "Sprachen");
                    if (dataItem != null)
                    {
                        IEnumerable<SpracheViewModel> sprachen = dataItem as IEnumerable<SpracheViewModel>;
                        List<string> tbl = new List<string>();

                        foreach (SpracheViewModel sp in sprachen)
                        {
                            tbl.Add(sp.Name);
                        }

                        Html.DevExpress().Label(labelSettings =>
                        {
                            labelSettings.Name = "Sprache" + c.ClientID;
                            labelSettings.Text = String.Join(", ", tbl.Distinct());
                            labelSettings.EncodeHtml = false;
                            labelSettings.PreRender += (s, e) =>
                            {
                                MVCxLabel lbl = (MVCxLabel)s;
                                lbl.Text = HighlightSearchText(lbl.Text, c.Grid.SearchPanelFilter);
                            };
                        }).Render();
                    }
                });
            });

            settings.DataBound = (sender, e) =>
            {
                var gr = sender as MVCxGridView;

                var column = gr.Columns["Reccurence"] as MVCxGridViewColumn;
                System.Diagnostics.Debug.Assert(column != null, "column != null");
                column.ColumnType = MVCxGridViewColumnType.SpinEdit;
                (column.PropertiesEdit as SpinEditProperties).NumberType = SpinEditNumberType.Integer;

                var columnAuftragsvolumen = gr.Columns["Auftragsvolumen"] as MVCxGridViewColumn;
                columnAuftragsvolumen.ColumnType = MVCxGridViewColumnType.SpinEdit;
                (columnAuftragsvolumen.PropertiesEdit as SpinEditProperties).NumberType = SpinEditNumberType.Integer;
            };

            settings.HtmlDataCellPrepared += (sender, e) =>
            {
                if (e.DataColumn.FieldName == "Rechtsbereiche")
                {
                    var dataItem = e.GetValue("Rechtsbereiche");
                    if (dataItem != null)
                    {
                        IEnumerable<RechtsbereichViewModel> rechtsbereiche = dataItem as IEnumerable<RechtsbereichViewModel>;
                        List<string> tbl = new List<string>();

                        foreach (RechtsbereichViewModel t in rechtsbereiche)
                        {
                            tbl.Add(t.Name);
                        }
                        e.Cell.Attributes.Add(
                            "onclick",
                            String.Format("openPopupShowDataOnly(this, '{0}', '{1}');", String.Join(", ", tbl), e.DataColumn.Caption)
                            );
                    }
                }
                if (e.DataColumn.FieldName == "Beschreibung" || e.DataColumn.FieldName == "BillingInfo")
                {
                    e.Cell.Attributes.Add(
                    "onclick",
                    "openPopupReadonly(" + settings.Name + ", '" + e.DataColumn.FieldName + "', " + e.VisibleIndex + ", '" + e.DataColumn.Caption + "')");
                }
                if (e.DataColumn.FieldName == "CustomerContractStatus")
                {
                    var dataItem = e.GetValue("CustomerContractStatus");
                    CustomerContractStatus contractStatus = dataItem is CustomerContractStatus ? (CustomerContractStatus) dataItem : CustomerContractStatus.None;
                    switch (contractStatus)
                    {
                        case CustomerContractStatus.Ok:
                            e.Cell.BackColor = System.Drawing.Color.FromArgb(229, 245, 202);
                            break;
                        case CustomerContractStatus.None:
                            e.Cell.BackColor = System.Drawing.Color.FromArgb(246, 251, 138);
                            break;
                        default:
                            e.Cell.BackColor = System.Drawing.Color.FromArgb(225, 140, 140);
                            break;
                    }


                }


            };

            settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
                currentGrid = s;
                if(e.focusedColumn.fieldName == 'Beschreibung' || e.focusedColumn.fieldName == 'BillingInfo'){
                            openPopupOnBatchEditBegin(s, e);
                        }
            }";

            settings.ClientSideEvents.ClipboardCellPasting = @"function(s,e){
                e.cancel = currentGrid != s;
	        }";

            //settings.AfterPerformCallback = (s, e) =>
            //{
            //    MVCxGridView gridView = s as MVCxGridView;
            //    string filterExpression = gridView.FilterExpression;
            //    var test = 2;
            //};

            settings.BeforeHeaderFilterFillItems = (sender, e) =>
            {
                if (e.Column.FieldName == "Rechtsbereiche")
                {
                    foreach (RechtsbereichViewModel rb in allRechtsbereiche)
                    {

                        var testCriteria = new ContainsOperator("Rechtsbereiche",
                            new BinaryOperator(
                                new OperandProperty("Name"), rb.Name,
                                BinaryOperatorType.Equal
                                )
                            );

                        e.AddValue(rb.Name, testCriteria);
                    }
                    e.Handled = true;
                }
                if (e.Column.FieldName == "Sprachen")
                {
                    foreach (SpracheViewModel sp in allSprachen)
                    {

                        var testCriteria = new ContainsOperator("Sprachen",
                            new BinaryOperator(
                                new OperandProperty("Name"), sp.Name,
                                BinaryOperatorType.Equal
                                )
                            );

                        e.AddValue(sp.Name, testCriteria);
                    }
                    e.Handled = true;
                }
            };

            settings.PreRender = (s, e) =>
            {
                MVCxGridView kgrid = (MVCxGridView)s;
                for (var x = 0; x < kgrid.VisibleRowCount; x++)
                {
                    int kundeId = (int)kgrid.GetRowValues(x, "KundeID");
                    if (Request.Params["expanded"] != null)
                    {
                        int expanded = Convert.ToInt32(Request.Params["expanded"]);

                        if (expanded == kundeId)
                        {
                            kgrid.DetailRows.ExpandRow(x);
                        }
                    }
                }
            };

            settings.SettingsDetail.ShowDetailRow = true;
            settings.SetDetailRowTemplateContent(c =>
            {
                Html.DevExpress().PageControl(pageControlSettings =>
                {
                    int kId = DataBinder.Eval(c.DataItem, "KundeID") == null ? Convert.ToInt32(Request.Params["expanded"]) : (int)DataBinder.Eval(c.DataItem, "KundeID");

                    pageControlSettings.Name = "KundePageControl_" + kId;
                    pageControlSettings.Width = Unit.Percentage(100);
                    // TODO �bersetzung

                    pageControlSettings.SaveStateToCookies = true;


                    pageControlSettings.TabPages.Add("Standorte").SetContent(() =>
                    {
                        Html.RenderAction("KundenStandortPartialView", new { kundeId = kId, parentTab = "KundenAktualisierungView" });
                    });
                    // TODO �bersetzung
                    pageControlSettings.TabPages.Add("Kontakte").SetContent(() =>
                    {
                        Html.RenderAction("KundenKontaktePartialView", new { kundeId = kId });
                    });

                    pageControlSettings.TabPages.Add("Menu Standort").SetContent(() =>
                    {
                        Html.RenderAction("StandortMenuPartialView", new { kundeId = kId });
                    });

                    pageControlSettings.TabPages.Add("Standortarchiv").SetContent(() =>
                     {
                        Html.RenderAction("KundenStandortPartialView", new { kundeId = kId, parentTab = "KundenAktualisierungView", archive = true });
                    });

                }).Render();
            });

        });
        if (ViewData["EditError"] != null)
        {
            grid.SetEditErrorText((string)ViewData["EditError"]);
        }

        @grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
        {
            e.QueryableSource = Model;
            e.KeyExpression = KundenController.AktualisierungGridViewSettings.GetGridViewKeyFieldName();
        }).GetHtml()
    }
}




