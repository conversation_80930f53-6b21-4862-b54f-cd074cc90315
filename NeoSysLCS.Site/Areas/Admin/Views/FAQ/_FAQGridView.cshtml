@using System.Web.UI.WebControls
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers
@using System.Globalization
@using Microsoft.Ajax.Utilities
@using NeoSysLCS.DomainModel.Models
@using System.Data.Entity
@using System.Web.UI.WebControls
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Site.Areas.Admin.Controllers
@using NeoSysLCS.Site.Helpers
@model IQueryable<NeoSysLCS.Repositories.ViewModels.FAQViewModel>

@{


    var grid = Html.DevExpress().GridView(settings =>
    {
        settings.Name = "FAQGridView";
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;


        settings.SettingsResizing.ColumnResizeMode = ColumnResizeMode.NextColumn;
        settings.KeyFieldName = "FaqID";

        settings.CallbackRouteValues = new { Controller = "FAQ", Action = "FAQGridView", faqID = ViewData["faqID"] };

        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "FAQ", Action = "FAQGridViewBatchEditUpdate", faqID = ViewData["faqID"] };

        settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
        settings.CommandColumn.Visible = true;
        settings.CommandColumn.ShowNewButtonInHeader = true;
        settings.CommandColumn.ShowDeleteButton = true;
        settings.CommandColumn.ShowEditButton = false;
        settings.Settings.ShowGroupPanel = false;
        settings.SettingsContextMenu.Enabled = false;

        settings.Columns.Add(column =>
        {
            column.FieldName = "Frage";
            //column.Caption = Resources.View_Standort_Name;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Quelle";
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.SetEditItemTemplateContent(c => Html.DevExpress().TextBox(TextBoxSettings =>
            {
                TextBoxSettings.Name = "txt3";

                TextBoxSettings.ReadOnly = true;
                TextBoxSettings.ClientVisible = false;

            }).Render());
            column.SetDataItemTemplateContent(
                container =>
                {
                    var faqId = container.KeyValue;
                    ViewData["faqId"] = faqId;

                    var url = (string)DataBinder.Eval(container.DataItem, "Quelle");
                    if (!string.IsNullOrEmpty(url) && url != "---")
                    {
                        url = !url.StartsWith("http") ? "http://" + url : url;
                        Html.DevExpress().HyperLink(hyperlink =>
                        {
                            hyperlink.EncodeHtml = false;
                            hyperlink.Properties.Text = "<i class=\" fa fa-link\"></i>";
                            hyperlink.NavigateUrl = url;
                            hyperlink.Properties.Target = "_blank";
                        }).Render();

                        ViewContext.Writer.Write("&nbsp;&nbsp;");
                    }

                    if (faqId != null)
                    {
                        // new file
                        Html.DevExpress().HyperLink(hyperlink =>
                        {
                            hyperlink.EncodeHtml = false;
                            hyperlink.Properties.Text = "<i class=\" fa fa-edit\"></i>";
                            hyperlink.Attributes.Add("onClick", "javascript:ShowDetailPopup('" + Url.Action("ShowFaqUploadPopup", "FAQ", new { faqId = faqId, url = url, field = "Quelle" }) + "','pcModalMode_FaqFileUpload');");
                            hyperlink.NavigateUrl = Url.Content("#");

                        }).Render();
                    }
                });
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Status";
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

            var list = from FAQStatus value in Enum.GetValues(typeof(FAQStatus))
                       select new
                       {
                           Id = (int)value,
                           Name = value.GetTranslation()
                       };

            comboBoxProperties.DataSource = list;
            comboBoxProperties.ValueField = "Id";
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueType = typeof(Int32);
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Prio";
            //column.Caption = Resources.View_Standort_Name;
        });

        MVCxGridViewColumn faqKategorieColumn = settings.Columns.Add("FAQKategorien");
        faqKategorieColumn.Caption = "FAQKategorien";

        faqKategorieColumn.SetDataItemTemplateContent(container => Html.DevExpress().HyperLink(hyperlink =>
        {

            var keyValue = container.KeyValue;

            string linktext = "";
            var mylist = (IQueryable<FAQKategorie>)DataBinder.Eval(container.DataItem, "FAQKategorien");
            if (mylist != null)
                mylist.ForEach(u => linktext += u.Name + ", ");


            linktext = (linktext == "" ? "bearbeiten" : linktext.Substring(0, linktext.Length - 2));

            hyperlink.Properties.Text = (keyValue == null ? "" : linktext);
            ViewData["FaqID"] = keyValue;
            hyperlink.Attributes.Add("onClick", "javascript:ShowDetailPopup('" + Url.Action("ShowKategoriePopup", "FAQ", new { faqID = keyValue }) + "','pcModalMode_FAQ');");
            hyperlink.NavigateUrl = Url.Content("#");

        }).Render());

        faqKategorieColumn.EditFormSettings.Visible = DefaultBoolean.False;
        faqKategorieColumn.SetEditItemTemplateContent(c => Html.DevExpress().TextBox(TextBoxSettings =>
        {
            TextBoxSettings.Name = "txt2";

            TextBoxSettings.ReadOnly = true;
            TextBoxSettings.ClientVisible = false;

        }).Render());
        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        //Detail Template
        settings.SettingsDetail.ShowDetailRow = true;
        settings.SetDetailRowTemplateContent(c =>
        {
            Html.RenderAction("FAQUebersetzungenGridView", new { faqID = DataBinder.Eval(c.DataItem, "FaqID") });
        });
    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}

@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "FaqID";
}).GetHtml()

@Html.DevExpress().PopupControl(settings =>
{
    settings.Name = "pcModalMode_FAQ";
    settings.ScrollBars = ScrollBars.Auto;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "Kategorie";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(1000); s.SetHeight(650); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(1000); s.SetHeight(650); }";
    settings.ClientSideEvents.Closing = "function(s, e){ location.reload() }";
}).GetHtml()

@Html.DevExpress().PopupControl(settings =>
{
    settings.Name = "pcModalMode_FaqFileUpload";
    settings.ScrollBars = ScrollBars.None;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "File upload";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(400); s.SetHeight(200); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(400); s.SetHeight(200); }";
    settings.ClientSideEvents.Closing = "function(s, e){ location.reload(); }";
}).GetHtml()