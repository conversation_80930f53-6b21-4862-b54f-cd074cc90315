@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.ViewModels

@section Scripts {
    @Scripts.Render("~/Scripts/jquery.unobtrusive-ajax.min.js")
    @Scripts.Render("~/Scripts/helpers/SelectionHelper.js")
}

@model IQueryable<ErlassViewModel>

@*@functions {
    private int GetErlassfassungId(Forderungsversion forderungsversion)
    {
        int erlassfassungId = Convert.ToInt32(ViewData["ErlassfassungID"]);
        if (erlassfassungId == 0)
        {
            if (forderungsversion == null)
            {
                return -1;
            }
            return forderungsversion.ErlassfassungID;
        }
        return erlassfassungId;
    }
}*@

@{
    IUnitOfWork unitOfWork = new UnitOfWork();
    ViewBag.Title = "Erlassselektierung für Gesetzesänderung";
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}


@*@section breadcrumb{

    <a href="#" onclick="history.go(-1); return false;" title="Zurück"><i class="fa fa-arrow-left fa-lg"></i></a> |
    <span class="breadcrumb-noesys-navigation">
        <a href="/">Home</a>
        <span> > </span>
        @Html.ActionLink("Erlasse", "Index", "Erlasse", new { area = "Admin" }, new { @class = "" })
        <span> > </span>
        @Html.ActionLink("Fassungen von Erlass «" + erlassViewModel.Titel + "» (" + @erlassViewModel.SrNummer + ")", "Index", "Erlassfassungen", new { area = "Admin", id = erlassViewModel.ErlassID }, new { @class = "" })
        <span> > </span>
        @Html.ActionLink("Forderungen von Erlassfassung vom " + @erlassfassungViewModel.Beschluss.ToString("dd.MM.yyyy"), "Index", "Forderungen", new { area = "Admin", id = erlassfassungViewModel.ErlassfassungID }, new { @class = "" })
        <span> > </span>
        <span>Rechtsbereichselektierung für Forderung @forderungsversion.VersionsNummer </span>
    </span>
}*@


<script type="text/javascript">
    //<![CDATA[


    var selectionHelper;

    //grid init function
    function SelectionInit(itemId, grid, selectbox, countSelector, resultSelector) {
        if (selectionHelper == null) {
            selectionHelper = new SelectionHelper(
                '@Url.Action("SaveConsultationErlasse", "ConsultationErlasse", new { Area = "Admin" })',
                "{0}"
            );
        }
        selectionHelper.AddSelection(itemId, grid, selectbox, countSelector, resultSelector);
    }
    //grid selection changed
    function SelectionChanged(itemId, e) {
        selectionHelper.SelectionChanged(itemId, "ErlassID;Titel", e);
    }
    //save button click
    function OnSubmitSelectionClick(itemId) {
        selectionHelper.OnSubmitClick(itemId);
    }
   
    // ]]>
</script>
@Html.Partial("_ConsultationErlassePartial", Model)