@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories;
@using System.Web.UI.WebControls;
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers
@using System.Globalization

@model IQueryable<KundendokumentErlassfassungViewModel>

@{

    var grid = Html.DevExpress().GridView(
        settings =>
        {
            IUnitOfWork unitOfWork = new UnitOfWork();
            int standortId = Convert.ToInt32(ViewData["StandortID"]);
            var isInitialVersion = SessionHelper.GetKundendokumentData((int) ViewData["StandortID"]).VorgaengerKundendokumentID == null;

            settings.Name = "UpdateSelectErlassfassungenGridView_" + (isInitialVersion ? "Initial" : "");
            settings.KeyFieldName = "ErlassfassungID";

            GridViewHelper.ApplyDefaultSettings(settings);

            settings.SettingsBehavior.AutoExpandAllGroups = true;
            settings.CommandColumn.Visible = true;
            settings.CommandColumn.SelectAllCheckboxMode = GridViewSelectAllCheckBoxMode.AllPages;
            settings.CommandColumn.Caption = "Betroffen";
            settings.CommandColumn.ShowSelectCheckbox = true;
            settings.SettingsBehavior.AllowSelectByRowClick = false;
            settings.SettingsPager.Mode = GridViewPagerMode.ShowPager;
            //settings.SettingsPager.Mode = GridViewPagerMode.ShowAllRecords;
            settings.Settings.ShowGroupPanel = true;

            settings.SettingsCookies.Enabled = false;
            settings.SettingsCookies.StoreFiltering = false;
            settings.SettingsCookies.StoreGroupingAndSorting = false;
            settings.SettingsCookies.StorePaging = false;

            settings.SettingsLoadingPanel.Enabled = false;
            settings.SettingsLoadingPanel.Mode = GridViewLoadingPanelMode.Disabled;
            settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;
            // Enable column resizing
            settings.SettingsBehavior.ColumnResizeMode = ColumnResizeMode.NextColumn;

            // Set grid width to 100% to make it responsive
            settings.Width = System.Web.UI.WebControls.Unit.Percentage(100);

            settings.CallbackRouteValues = new
            {
                Controller = "KundendokumentUpdate",
                Action = "_SelectErlassfassungenGridView",
                standortId = Convert.ToInt32(ViewData["StandortID"]),
                selectedForderungenIDs = ViewData["selectedForderungenIDs"],
                selectedPflichtenIDs = ViewData["selectedPflichtenIDs"],
                forderungKundenbezuege = ViewData["forderungKundenbezuege"],
                pflichtKundenbezuege = ViewData["pflichtKundenbezuege"]
            };
            //settings.ClientSideEvents.CallbackError = "OnCallbackError";

            //only possible while grid not grouped/filtered
            //settings.ClientSideEvents.Init = "OnInitGrid";

            settings.CustomJSProperties = (sender, e) =>
            {
                var gridSender = (MVCxGridView) sender;
                e.Properties["cpSelectedKeys"] = gridSender.GetSelectedFieldValues(gridSender.KeyFieldName).Select(k => k.ToString()).ToList();
            };

            settings.ClientSideEvents.SelectionChanged = "OnSelectionChanged";
            settings.ClientSideEvents.BeginCallback = "OnBeginCallback";
            settings.ClientSideEvents.EndCallback = "OnEndCallback";
            settings.ClientSideEvents.Init = "OnSelectionChanged";

            var erlassCol = new MVCxGridViewColumn();
            erlassCol.FieldName = "ErlassID";
            erlassCol.Caption = Resources.Entitaet_Erlass_Singular;
            erlassCol.ColumnType = MVCxGridViewColumnType.ComboBox;

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassID";
                column.Caption = TranslationHelper.GetTranslation(typeof (KundendokumentForderungsversionViewModel), column.FieldName);
                column.Width = System.Web.UI.WebControls.Unit.Percentage(15);
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ColumnType = MVCxGridViewColumnType.ComboBox;
                column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                column.GroupIndex = 0;
                column.SortIndex = 0;
                var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
                comboBoxProperties.DataSource = (Model.Select(x => new { x.ErlassID, x.ErlassTitel }).OrderBy(x => x.ErlassTitel)).Distinct().ToList();
                comboBoxProperties.TextField = "ErlassTitel";
                comboBoxProperties.ValueField = "ErlassID";
                comboBoxProperties.ValueType = typeof(int);
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassfassungID";
                column.Caption = Resources.Select_Erlassfassungen_View_Inkrafttretdatum;
                column.Width = System.Web.UI.WebControls.Unit.Percentage(7);
                column.ColumnType = MVCxGridViewColumnType.ComboBox;
                column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                column.SortDescending();
                // we only want the date to display not the date and time. The combox doesn't provide any options to
                IEnumerable<Erlassfassung>
                    erlassefassungen = unitOfWork.ErlassfassungRepository.Get();
                var datasource = new List<Object>();
                foreach (Erlassfassung erlassfassungCand in erlassefassungen)
                {
                    datasource.Add(new
                    {
                        erlassfassungCand.ErlassfassungID,
                        Inkrafttretung = erlassfassungCand.Inkrafttretung.ToString("d")
                    });
                }

                var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
                if (comboBoxProperties != null)
                {
                    comboBoxProperties.DataSource = datasource;
                    comboBoxProperties.TextField = "Inkrafttretung";
                    comboBoxProperties.ValueField = "ErlassfassungID";
                    comboBoxProperties.ValueType = typeof (int);
                }
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Beschluss";
                column.Width = System.Web.UI.WebControls.Unit.Percentage(7);
                column.Caption = Resources.Select_Erlassfassungen_View_Beschluss;
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "BetroffenKommentar";
                column.Caption = "Betroffen Kommentar";
                column.Width = System.Web.UI.WebControls.Unit.Percentage(15);

                column.ColumnType = MVCxGridViewColumnType.Memo;
                var memoProp = column.PropertiesEdit as MemoProperties;
                memoProp.EncodeHtml = false;
                column.ReadOnly = true;
                var prop = (column.PropertiesEdit as EditProperties);
                if (prop != null)
                {
                    prop.ValidationSettings.Display = Display.Dynamic;
                }
                column.SetDataItemTemplateContent(
                    container =>
                    {
                        var kommentar = (string) DataBinder.Eval(container.DataItem, "BetroffenKommentar");
                        if (!string.IsNullOrEmpty(kommentar))
                        {
                            if ((kommentar.StartsWith("www")) || (kommentar.StartsWith("http")))
                            {
                                string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(kommentar, kommentar, true);
                                ViewContext.Writer.Write(htmlLink);
                            }

                            else
                            {
                                ViewContext.Writer.Write(StringHelper.TruncateHtml(kommentar, 100));
                            }
                        }
                    }
                    );

            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "NichtBetroffenKommentar";
                column.Caption = "Nicht betroffen Kommentar";
                column.Width = System.Web.UI.WebControls.Unit.Percentage(15);

                column.ColumnType = MVCxGridViewColumnType.Memo;
                var memoProp = column.PropertiesEdit as MemoProperties;
                memoProp.EncodeHtml = false;
                column.ReadOnly = true;
                column.SetDataItemTemplateContent(
                    container =>
                    {
                        var kommentar = (string) DataBinder.Eval(container.DataItem, "NichtBetroffenKommentar");
                        if (!string.IsNullOrEmpty(kommentar))
                        {
                            if ((kommentar.StartsWith("www")) || (kommentar.StartsWith("http")))
                            {
                                string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(kommentar, kommentar, true);
                                ViewContext.Writer.Write(htmlLink);
                            }

                            else
                            {
                                ViewContext.Writer.Write(StringHelper.TruncateHtml(kommentar, 100));
                            }
                        }
                    }
                    );

            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "InternerKommentar";
                column.Caption = "Interner Kommentar";
                column.Width = System.Web.UI.WebControls.Unit.Percentage(15);
                column.ColumnType = MVCxGridViewColumnType.Memo;                
            });

            if (!isInitialVersion)
            {
                settings.Columns.Add(column =>
                {
                    column.FieldName = "Status";
                    column.Caption = TranslationHelper.GetTranslation(typeof (KundendokumentForderungsversionViewModel), column.FieldName);

                    //define as combobox for filtern over the enum
                    column.ColumnType = MVCxGridViewColumnType.ComboBox;
                    column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                    var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

                    var test = (from x in Model select x.Status).Distinct().ToList();

                    var list = from KundendokumentItemStatus value in Enum.GetValues(typeof(KundendokumentItemStatus))
                               where test.Contains(value)
                               select new
                               {
                                   Id = (int)value,
                                   Name = value.GetTranslation()
                               };

                    comboBoxProperties.DataSource = list;
                    comboBoxProperties.ValueField = "Id";
                    comboBoxProperties.TextField = "Name";

                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                    column.ReadOnly = true;
                    column.EditFormSettings.Visible = DefaultBoolean.False;
                    column.SetDataItemTemplateContent(container =>
                    {
                        var statusCandidate = DataBinder.Eval(container.DataItem, "Status");
                        if (statusCandidate != null)
                        {
                            var status = (KundendokumentItemStatus) statusCandidate;
                            ViewContext.Writer.Write("<span class=\"label label-" + status + "\">" + status.GetTranslation() + "</span>");
                        }
                    });
                });
            }

            settings.Columns.Add(column =>
            {
                column.FieldName = "Quelle";
                column.SetDataItemTemplateContent(
                    container =>
                    {
                        var url = DataBinder.Eval(container.DataItem, "Quelle");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                        ViewContext.Writer.Write(htmlLink);
                    });
            });

            settings.DataBound += (s, e) =>
            {
                MVCxGridView g = s as MVCxGridView;
                foreach (int id in SessionHelper.GetKundendokumentData(standortId).SelectedErlasssfassungenIDs)
                {
                    g.Selection.SelectRowByKey(id);
                }
            };

            settings.PreRender = (sender, e) =>
            {
                var selectedErlassfassungen = SessionHelper.GetKundendokumentData(standortId).SelectedErlasssfassungenIDs;
                if (selectedErlassfassungen != null && !selectedErlassfassungen.Any())
                // favour user's selection over automatic preselection
                {
                    MVCxGridView gridView = sender as MVCxGridView;
                    if (gridView != null)
                    {
                        foreach (KundendokumentErlassfassungViewModel viewModel in Model)
                        {
                            if (viewModel.Betroffen)
                            {
                                gridView.Selection.SelectRowByKey(viewModel.ErlassfassungID);
                            }
                        }
                    }
                }
            };

            settings.HtmlDataCellPrepared += (sender, e) =>
            {
                if (e.DataColumn.FieldName == "BetroffenKommentar" || e.DataColumn.FieldName == "NichtBetroffenKommentar")
                {
                    e.Cell.Attributes.Add(
                        "onclick",
                        "openPopupReadonly(" + settings.Name + ", '" + e.DataColumn.FieldName + "', " + e.VisibleIndex + ", '" + e.DataColumn.Caption + "')"
                        );
                }
            };

            GridViewHelper.AddNewestFirstSortorderColumn(settings, "ErlassfassungID");
            GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);


        });
}

@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "ErlassfassungID";
}).GetHtml()