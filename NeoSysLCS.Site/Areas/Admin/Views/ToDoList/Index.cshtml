@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Repositories
@using NeoSysLCS.Site.Helpers
@using System.Globalization
@using System.Threading;
@using System;
@using System.Web;
@using System.Web.Mvc;
@using NeoSysLCS.Site.Helpers;

@model IEnumerable<NeoSysLCS.Repositories.ViewModels.ToDoViewModel>

@{
    var sessionhelper = new SessionHelper();
    var unitOfWork = new UnitOfWork();
    var user = sessionhelper.GetCurrentUser();
    var lang = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;

    string cultureName = null;

    // Attempt to read the culture cookie from Request
    HttpCookie cultureCookie = Request.Cookies["_culture"];

    if (Request.Params["lang"] != null)
    {
        cultureName = Request.Params["lang"];

        if (cultureCookie != null)
        {
            cultureCookie.Value = cultureName;
            Response.Cookies.Add(cultureCookie);
        }

    }
    else if (cultureCookie != null)
    {
        cultureName = cultureCookie.Value;
    }
    else
    {
        cultureName = Request.UserLanguages != null && Request.UserLanguages.Length > 0 ? Request.UserLanguages[0] : // obtain it from HTTP header AcceptLanguages
            null;
    }
    // Validate culture name
    cultureName = CultureHelper.GetImplementedCulture(cultureName); // This is safe

    Thread.CurrentThread.CurrentCulture = new System.Globalization.CultureInfo(cultureName);
    Thread.CurrentThread.CurrentUICulture = Thread.CurrentThread.CurrentCulture;

    ViewBag.Title = "Pendenzenliste für PLs";
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}
@Html.Partial("_HtmlEditPopup", User.IsInRole(Role.Admin))
@Html.Partial("_ToDoListGridView", Model)

