@using System.Web.UI.WebControls
@using NeoSysLCS.Site.Areas.Admin.Controllers
@using NeoSysLCS.Site.Helpers


@{
    
    var objgrid = Html.DevExpress().GridView(settings =>
    {

        settings.Name = "ObjDashboardView";
        settings.KeyFieldName = "ObjektID";

        //GridViewHelper.ApplyDefaultSettings(settings);
        settings.Styles.AlternatingRow.Enabled = DefaultBoolean.True;
        
        settings.CallbackRouteValues = new { Controller = "Dashboard", Action = "ObjektGridView", Area = "Admin" };


        settings.CommandColumn.Visible = false;
        settings.CommandColumn.ShowNewButtonInHeader = false;
        settings.CommandColumn.ShowDeleteButton = false;
        settings.CommandColumn.ShowEditButton = false;
        settings.SettingsPager.Visible = false;

        settings.Width = Unit.Percentage(50);

        foreach (MVCxGridViewColumn col in ObjekteController.BaseGridViewSettings.GetDataColumns())
        {
            settings.Columns.Add(col);
        }
        //Columns



        MVCxGridViewColumn gueltigVon = settings.Columns.Add("BearbeitetAm", MVCxGridViewColumnType.DateEdit);
        gueltigVon.Caption = "Bearbeitet";


        settings.Columns.Add("BearbeitetVon");

        MVCxGridViewColumn ErstelltAm = settings.Columns.Add("ErstelltAm", MVCxGridViewColumnType.DateEdit);
        ErstelltAm.Caption = "Erstellt";
        ErstelltAm.SortDescending();


        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

    });

    if (ViewData["EditError"] != null)
    {
        objgrid.SetEditErrorText((string)ViewData["EditError"]);
    }
}
@objgrid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = ObjekteController.BaseGridViewSettings.GetGridViewKeyFieldName();
}).GetHtml()