@using System.Web.UI.WebControls
@using NeoSysLCS.Site.Helpers

@{

    
    var grid = Html.DevExpress().GridView(settings =>
    {

        settings.Name = "HomedView";
        settings.KeyFieldName = "KundeID";

        //GridViewHelper.ApplyDefaultSettings(settings);
        settings.Styles.AlternatingRow.Enabled = DefaultBoolean.True;
        
        settings.CallbackRouteValues = new { Controller = "Dashboard", Action = "KundeGridView", Area = "Admin" };


        settings.CommandColumn.Visible = false;
        settings.CommandColumn.ShowNewButtonInHeader = false;
        settings.CommandColumn.ShowDeleteButton = false;
        settings.CommandColumn.ShowEditButton = false;
        settings.SettingsPager.Visible = false;

        settings.Width = Unit.Percentage(50);

        //Columns
        settings.Columns.Add("Name");

        MVCxGridViewColumn projleiterName = settings.Columns.Add("ProjektleiterName", MVCxGridViewColumnType.TextBox);
        projleiterName.Caption = "Projektleiter";


        MVCxGridViewColumn gueltigVon = settings.Columns.Add("BearbeitetAm", MVCxGridViewColumnType.DateEdit);
        gueltigVon.Caption = "Bearbeitet";

        settings.Columns.Add("BearbeitetVon");

        MVCxGridViewColumn ErstelltAm = settings.Columns.Add("ErstelltAm", MVCxGridViewColumnType.DateEdit);
        ErstelltAm.Caption = "Erstellt";
        ErstelltAm.SortDescending();




        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "KundeID";
}).GetHtml()