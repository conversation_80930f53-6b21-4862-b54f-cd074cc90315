@using System.Web.UI.WebControls
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers
@model NeoSysLCS.Repositories.ViewModels.ForderungsversionViewModel



<div class="form-horizontal">
    <h4>Neue Version</h4>
    <hr />
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    @Html.HiddenFor(model => model.ForderungsversionID)
    @Html.HiddenFor(model => model.ErlassfassungID)
    @Html.HiddenFor(model => model.ArtikelID)
    @Html.HiddenFor(model => model.ForderungID)
    @Html.Hidden("PreviousForderungsversionId", ViewData["ForderungsversionID"])

    <div class="form-group" width="100%">
        @Html.LabelFor(model => model.Beschreibung, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.DevExpress().HtmlEditor(editorSettings =>
            {
                editorSettings.SettingsHtmlEditing.EnterMode = HtmlEditorEnterMode.BR;
                editorSettings.Name = "Beschreibung";
                HtmlEditorHelper.ApplyHtmlEditorSettings(editorSettings);
                editorSettings.Width = Unit.Percentage(100);
            }).Bind(Model, "Beschreibung").GetHtml()
            @Html.ValidationMessageFor(model => model.Beschreibung, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group">
        @Html.Label("Description", htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.DevExpress().HtmlEditor(editorSettings =>
            {
                editorSettings.SettingsHtmlEditing.EnterMode = HtmlEditorEnterMode.BR;
                editorSettings.Name = "BeschreibungFranz";
                HtmlEditorHelper.ApplyHtmlEditorSettings(editorSettings);
                editorSettings.Width = Unit.Percentage(100);
            }).Bind(Model, "BeschreibungFranz").GetHtml()
            @Html.ValidationMessageFor(model => model.BeschreibungFranz, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group">
        @Html.Label("Descrizione", htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.DevExpress().HtmlEditor(editorSettings =>
            {
                editorSettings.SettingsHtmlEditing.EnterMode = HtmlEditorEnterMode.BR;
                editorSettings.Name = "BeschreibungItal";
                HtmlEditorHelper.ApplyHtmlEditorSettings(editorSettings);
                editorSettings.Width = Unit.Percentage(100);
            }).Bind(Model, "BeschreibungItal").GetHtml()
            @Html.ValidationMessageFor(model => model.BeschreibungItal, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group">
        @Html.Label("Description", htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.DevExpress().HtmlEditor(editorSettings =>
            {
                editorSettings.SettingsHtmlEditing.EnterMode = HtmlEditorEnterMode.BR;
                editorSettings.Name = "BeschreibungEn";
                HtmlEditorHelper.ApplyHtmlEditorSettings(editorSettings);
                editorSettings.Width = Unit.Percentage(100);
            }).Bind(Model, "BeschreibungEn").GetHtml()
            @Html.ValidationMessageFor(model => model.BeschreibungEn, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.Inkrafttretung, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.DevExpress().DateEditFor(m => m.Inkrafttretung).GetHtml()

            @Html.ValidationMessageFor(model => model.Inkrafttretung, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.Aufhebung, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.DevExpress().DateEditFor(m => m.Aufhebung).GetHtml()
            @Html.ValidationMessageFor(model => model.Aufhebung, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.Bewilligungspflicht, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            <div class="checkbox">
                @Html.EditorFor(model => model.Bewilligungspflicht)
                @Html.ValidationMessageFor(model => model.Bewilligungspflicht, "", new { @class = "text-danger" })
            </div>
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.Nachweispflicht, htmlAttributes: new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            <div class="checkbox">
                @Html.EditorFor(model => model.Nachweispflicht)
                @Html.ValidationMessageFor(model => model.Nachweispflicht, "", new { @class = "text-danger" })
            </div>
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.InternerKommentar, htmlAttributes: new { @class = "control-label col-md-2" })

        <div class="col-md-10">
            @Html.DevExpress().HtmlEditor(editorSettings =>
            {
                editorSettings.SettingsHtmlEditing.EnterMode = HtmlEditorEnterMode.BR;
                editorSettings.Name = "InternerKommentar";
                HtmlEditorHelper.ApplyHtmlEditorSettings(editorSettings);
                editorSettings.Width = Unit.Percentage(100);
            }).Bind(Model, "InternerKommentar").GetHtml()
            @Html.ValidationMessageFor(model => model.InternerKommentar, "", new { @class = "text-danger" })
        </div>
    </div>

    <div class="form-group">
        <div class="col-md-offset-2 col-md-10">
            @Html.DevExpress().Button(
            settings =>
            {
                settings.Name = "btnUpdate";
                settings.ControlStyle.CssClass = "button";
                settings.Width = Unit.Pixel(180);
                settings.Text = Resources.Allgmein_Einfügen;
                settings.UseSubmitBehavior = true;
            }).GetHtml()
        </div>
    </div>
</div>

