@using System.Data.Entity
@using System.Web.UI.WebControls
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Site.Areas.Admin.Controllers
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers

@model IQueryable<NeoSysLCS.Repositories.ViewModels.ErlassViewModel>

@{


    var grid = Html.DevExpress().GridView(settings =>
    {
        settings.Name = ErlasseController.BaseGridViewSettings.GetGridViewName();
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;


        settings.SettingsResizing.ColumnResizeMode = ColumnResizeMode.NextColumn;
        //settings.Settings.HorizontalScrollBarMode = ScrollBarMode.Auto;

        settings.KeyFieldName = ErlasseController.BaseGridViewSettings.GetGridViewKeyFieldName();

        settings.CallbackRouteValues = new { Controller = "Erlasse", Action = "ErlasseGridView", displayArchived = ViewData["displayArchived"], standortErlasse = ViewData["standortErlasse"] };

        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "Erlasse", Action = "ErlasseGridViewBatchEditUpdate", displayArchived = ViewData["displayArchived"], standortErlasse = ViewData["standortErlasse"] };

        if (User.IsInRole(Role.ProjectManager))
        {
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
            settings.CommandColumn.Visible = true;
            settings.CommandColumn.ShowNewButtonInHeader = true;
            settings.CommandColumn.ShowDeleteButton = true;
            settings.CommandColumn.ShowEditButton = false;
            settings.CommandColumn.Width = Unit.Pixel(40);
        }

        //Columns
        settings.Columns.Add(column =>
        {
            column.Caption = "#";
            column.ReadOnly = true;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.SetDataItemTemplateContent(container =>
            {

                Html.DevExpress().HyperLink(hyperlink =>
                {
                    var keyValue = container.KeyValue;
                    hyperlink.Name = "Artikel" + keyValue;
                    hyperlink.Properties.Text = "Artikel";
                    hyperlink.NavigateUrl = Url.Action("Index", "Artikel", new { id = keyValue });

                }).Render();
                ViewContext.Writer.Write("<br>");
                Html.DevExpress().HyperLink(hyperlink =>
                {
                    var keyValue = container.KeyValue;
                    hyperlink.Name = "Erlassfassungen" + keyValue;
                    hyperlink.Properties.Text = "Erlassfassungen";
                    hyperlink.NavigateUrl = Url.Action("Index", "Erlassfassungen", new { id = keyValue });

                }).Render();
                ViewContext.Writer.Write("<br>");
                Html.DevExpress().HyperLink(hyperlink =>
                {
                    var keyValue = container.KeyValue;
                    hyperlink.Name = "Rechtsbereiche" + keyValue;
                    hyperlink.Properties.Text = Resources.Entitaet_Rechtsbereich_Plural;
                    hyperlink.NavigateUrl = Url.Action("Index", "ErlassRechtsbereiche", new { id = keyValue });

                }).Render();
            });
        });

        foreach (MVCxGridViewColumn col in ErlasseController.BaseGridViewSettings.GetDataColumns(ViewContext, Model, (bool) ViewData["standortErlasse"]))
        {
            settings.Columns.Add(col);
        }

        GridViewHelper.AddNewestFirstSortorderColumn(settings, ErlasseController.BaseGridViewSettings.GetGridViewKeyFieldName());
        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        //Detail Template
        settings.SettingsDetail.ShowDetailRow = true;
        settings.SetDetailRowTemplateContent(c =>
        {
            Html.RenderAction("ErlassUebersetzungenGridView", new { erlassID = DataBinder.Eval(c.DataItem, "ErlassID") });
        });

        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
                currentGrid = s;
	        }";

        settings.ClientSideEvents.ClipboardCellPasting = @"function(s,e){
                e.cancel = currentGrid != s;
	        }";
    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}

@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = ErlasseController.BaseGridViewSettings.GetGridViewKeyFieldName();
}).GetHtml()

