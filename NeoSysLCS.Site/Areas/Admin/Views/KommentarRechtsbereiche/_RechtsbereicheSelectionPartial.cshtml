@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers

@Html.DevExpress().GridView(
    settings =>
    {
        UnitOfWork unitOfWork = new UnitOfWork();

        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        settings.Name = "gvRowSelection";
        settings.KeyFieldName = "RechtsbereichID";
        settings.CallbackRouteValues = new { Controller = "KommentarRechtsbereiche", Action = "RechtsbereicheSelectionPartial", id = ViewData["KommentarID"] };
        settings.ControlStyle.CssClass = "grid";

        settings.SettingsCookies.StoreFiltering = false;
        settings.CommandColumn.Visible = true;
        settings.CommandColumn.ShowSelectCheckbox = true;
        settings.SettingsBehavior.AllowSelectByRowClick = false;
        settings.Settings.ShowGroupPanel = false;

        MVCxGridViewColumn column = settings.Columns.Add("Name");
        column.Caption = "Name";

        //setting preselected
        settings.PreRender = (sender, e) =>
        {
            MVCxGridView gridView = sender as MVCxGridView;
            if (gridView != null)
                foreach (var obj in unitOfWork.RechtsbereichRepository.GetAllRechtsbereichViewModelsByKommentar(Convert.ToInt32(ViewData["KommentarID"])))
                {
                    gridView.Selection.SelectRowByKey(obj.RechtsbereichID);
                }
        };

        settings.ClientSideEvents.Init = @"function(s, e){
            SelectionInit(" +
                                      ViewData["KommentarID"] + @",
                                   s,
                                   SelectedRows" + @",
                                   '#count" + @"',
                                   '#Productresult" + @"');
                                    }";
        settings.ClientSideEvents.SelectionChanged = @"function(s,e){
                SelectionChanged(" + ViewData["KommentarID"] + @", e);
            }";



    }).BindToLINQ(string.Empty, string.Empty, (s, e) =>
    {
        var unitOfWork = new UnitOfWork();
        var query = unitOfWork.RechtsbereichRepository.GetAllRechtsbereichViewModels();
        e.QueryableSource = query;
        e.KeyExpression = "RechtsbereichID";
    }).GetHtml()