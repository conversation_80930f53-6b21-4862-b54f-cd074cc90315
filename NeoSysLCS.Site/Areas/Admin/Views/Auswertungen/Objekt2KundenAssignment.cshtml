@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Areas.Admin.Controllers
@using NeoSysLCS.Site.Utilities.Export
@model IQueryable<NeoSysLCS.Repositories.ViewModels.Objekt2KundenAssignmentViewModel>
@{
    ViewBag.Title = "Verwendete Objekte";
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}

@using (Html.BeginForm("ExportObjekt2KundenAssignmentTo", "Auswertungen", new { area = "Admin" }, FormMethod.Post, new { target = "_blank" }))
{
    <div class="divToolbar">
        <a class="btn btn-default" type="button" onclick="@(AuswertungenController.Objekt2KundenAssignmentGridViewSettings.GetGridViewName()).CollapseAll(); return false;">
            <i class="fa fa-minus-square"></i>&nbsp; @Resources.Allgemein_CollapseAllRows
        </a>
        <a class="btn btn-default" type="button" onclick="@(AuswertungenController.Objekt2KundenAssignmentGridViewSettings.GetGridViewName()).ExpandAll(); return false;">
            <i class="fa fa-plus-square"></i>&nbsp; @Resources.Allgemein_ExpandAllRows
        </a>
        <div class="divButtonsLeft">
            @foreach (KeyValuePair<ExportType, ExportAction> entry in ExportUtilities.GetExportTypes(new List<ExportType>() {ExportType.Pdf, ExportType.Xlsx}))
            {
                <button class="btn btn-default" type="submit" value="@entry.Key" name="exportFormat">@entry.Value.Title</button>
            }
        </div>
    </div>
    @Html.Partial("_Objekt2KundenAssignmentGridView", Model)
}