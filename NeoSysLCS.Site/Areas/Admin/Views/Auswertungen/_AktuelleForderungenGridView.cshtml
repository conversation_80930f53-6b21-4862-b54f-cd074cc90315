@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.Repositories.ViewModels

@model IQueryable<AuswertungForderungenViewModel>

@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        settings.Name = "AktuelleForderungenGridView";
        settings.KeyFieldName = "ForderungsversionID";
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        settings.CallbackRouteValues = new { Controller = "Auswertungen", Action = "_AktuelleForderungenGridView" };

        settings.Columns.Add(column =>
        {
            column.Caption = "";
            column.Name = "Erlassfassung";
            column.ReadOnly = true;
            column.EditFormSettings.Visible = DefaultBoolean.False;

            column.SetDataItemTemplateContent(container =>
            {
                //var erlassFassungId = DataBinder.Eval(container.DataItem, "ErlassfassungID");
                var erlassId = DataBinder.Eval(container.DataItem, "ErlassID");

                Html.DevExpress().HyperLink(hyperlink =>
                {
                    var keyValue = erlassId;
                    //hyperlink.Name = "Neue Version" + keyValue;
                    hyperlink.Properties.Text = "Erlassfassungen";
                    hyperlink.NavigateUrl = Url.Action("Index", "Erlassfassungen", new { id = keyValue });
                }).Render();
                ViewContext.Writer.Write("<br>");

                Html.DevExpress().HyperLink(hyperlink =>
                {
                    var keyValue = erlassId;
                    //hyperlink.Name = "Neue Version" + keyValue;
                    hyperlink.Properties.Text = "Artikel";
                    hyperlink.NavigateUrl = Url.Action("Index", "Artikel", new { id = keyValue });
                }).Render();

            });
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "ErlassID";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ReadOnly = true;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
            comboBoxProperties.DataSource = (Model.Select(x => new { x.ErlassID, x.ErlassTitel }).OrderBy(x => x.ErlassTitel).ToList().Distinct());
            comboBoxProperties.TextField = "ErlassTitel";
            comboBoxProperties.ValueField = "ErlassID";
            comboBoxProperties.ValueType = typeof(int);




        });


        settings.Columns.Add("VersionsNummer");
        settings.Columns.Add("Inkrafttretung");
        settings.Columns.Add("Aufhebung");

        settings.Columns.Add(column =>
        {
            column.FieldName = "Freigabe";
            column.Caption = "Freigabe";
            column.ColumnType = MVCxGridViewColumnType.CheckBox;

        });

        settings.Columns.Add(column =>
        {

            column.FieldName = "QsFreigabe";
            column.Caption = "Freigabe QS";
            column.ColumnType = MVCxGridViewColumnType.CheckBox;

        });



        GridViewHelper.AddNewestFirstSortorderColumn(settings, "ForderungsversionID");
        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "ForderungsversionID";
}).GetHtml()
