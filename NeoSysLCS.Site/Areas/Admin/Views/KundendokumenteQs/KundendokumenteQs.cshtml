@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties

@model  KundendokumentViewModel

@section Scripts {
    @Scripts.Render("~/Scripts/jquery.unobtrusive-ajax.min.js")
}

@{
    UnitOfWork unitOfWork = new UnitOfWork();

    var standort = unitOfWork.StandortRepository.GetStanodrtById((int)ViewData["standortID"]);
    var kunde = unitOfWork.KundeRepository.GetByID(standort.KundeID);
    if (Model != null)
    {
        ViewBag.Title = @Resources.View_Breadcrumb_QsFreigabe.Replace("##PUBLIZIEREN_AM##", (!Model.PublizierenAm.HasValue ? "" : Model.PublizierenAm.Value.ToString("dd.MM.yyyy")));
        ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
    }
}

@section breadcrumb{
    <a href="#" onclick="history.go(-1); return false;" title="Zurück"><i class="fa fa-arrow-left fa-lg"></i></a> |
    <span class="breadcrumb-noesys-navigation">
        <a href="/">@Resources.View_Breadcrumb_Home</a>
        <span> > </span>
        @Html.ActionLink(Resources.View_Breadcrumb_Kundenübersicht, "Index", "Kunden", new { area = "Admin" }, new { @class = "" })
        <span> > </span>
        @Html.ActionLink(Resources.View_Breadcrumb_KundendokumenteVonStandort.Replace("##KUNDE##", kunde.Name).Replace("##STANDORT##", standort.Name),
             "Index",
             "Kundendokumente",
             new { area = "Admin", id = standort.StandortID }, new { @class = "" }
        )
        <span> > </span>
        <span>@ViewBag.Title</span>
    </span>
}


@Html.Partial("_HtmlEditPopup", false)

@Html.Partial("_KundendokumenteQsPageControl", Model)

