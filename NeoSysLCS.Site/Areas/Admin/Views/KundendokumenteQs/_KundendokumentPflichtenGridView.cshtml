@using System.Web.UI.WebControls

@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Site.Helpers

@model IQueryable<KundendokumentPflichtViewModel>

@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        IUnitOfWork unitOfWork = new UnitOfWork();
        var standort = unitOfWork.StandortRepository.GetStanodrtById((int)ViewData["standortID"]);
        var kunde = unitOfWork.KundeRepository.GetByID(standort.KundeID);
        bool showOnlyRemoved = ((bool?)ViewData["showOnlyRemoved"]).HasValue && ((bool?)ViewData["showOnlyRemoved"]).Value;
        settings.Name = "KundendokumentPflichtenGridView_" + ViewData["KundendokumentID"] + showOnlyRemoved;
        settings.KeyFieldName = "KundendokumentPflichtID";
        
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        if (showOnlyRemoved)
        {
            settings.Settings.ShowGroupPanel = false;
        }
        
        settings.SettingsContextMenu.Enabled = false;
        
        settings.CallbackRouteValues = new
        {
            Controller = "KundendokumenteQs",
            Action = "KundendokumentPflichtenGridView",
            standortID = ViewData["StandortID"],
            kundendokumentID = ViewData["KundendokumentID"],
            showOnlyRemoved = ViewData["showOnlyRemoved"]
        };
        if (User.IsInRole(Role.ProjectManager))
        {
            if ((KundendokumentStatus)ViewData["KundendokumentStatus"] < KundendokumentStatus.QsApproved && !showOnlyRemoved)
            {
                //Kundendokument is editable
                settings.SettingsEditing.BatchUpdateRouteValues = new
                {
                    Controller = "KundendokumenteQs",
                    Action = "KundendokumentPflichtenGridViewBatchEditUpdate",
                    standortID = ViewData["StandortID"],
                    kundendokumentID = ViewData["KundendokumentID"]
                };
                settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
                settings.SettingsEditing.BatchEditSettings.EditMode = GridViewBatchEditMode.Row;
                settings.CommandColumn.Visible = true;
                settings.CommandColumn.ShowNewButtonInHeader = false;
                settings.CommandColumn.ShowEditButton = false;
            }
        }

        //Columns
        settings.Columns.Add(column =>
        {
            column.FieldName = "StandortObjektTitel";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
            column.MinWidth = 140;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ReadOnly = true;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.ColumnType = MVCxGridViewColumnType.TextBox;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "ErlassTitel";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
            column.MinWidth = 140;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ReadOnly = true;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.ColumnType = MVCxGridViewColumnType.TextBox;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Beschreibung";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentPflichtViewModel), column.FieldName);
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            
            column.ColumnType = MVCxGridViewColumnType.Memo;
            column.Width = Unit.Percentage(30);
            var memoProp = column.PropertiesEdit as MemoProperties;
            if (memoProp != null)
            {
                memoProp.EncodeHtml = false;
            }
            column.ReadOnly = true;
            column.EditFormSettings.Visible = DefaultBoolean.False;
        });

       settings.Columns.Add(column =>
        {
            column.FieldName = "Status";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentPflichtViewModel), column.FieldName);
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ReadOnly = true;

            //define as combobox for filtern over the enum
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

            var list = from KundendokumentItemStatus value in Enum.GetValues(typeof(KundendokumentItemStatus))
                       select new
                       {
                           Id = (int)value,
                           Name = value.GetTranslation()
                       };

            comboBoxProperties.DataSource = list;
            comboBoxProperties.ValueField = "Id";
            comboBoxProperties.TextField = "Name";
            
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.SetDataItemTemplateContent(container =>
            {
                var statusCandidate = DataBinder.Eval(container.DataItem, "Status");
                if (statusCandidate != null)
                {
                    var status = (KundendokumentItemStatus)statusCandidate;
                    ViewContext.Writer.Write("<span class=\"label label-" + status + "\">" + status.GetTranslation() + "</span>");
                }
            });
        });

        if (!showOnlyRemoved)
        {
            settings.Columns.Add(column =>
            {
                column.FieldName = "QsFreigabe";
                column.Caption = TranslationHelper.GetTranslation(typeof (KundendokumentPflichtViewModel), column.FieldName);
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.CheckBox;
            });
        }
        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);


    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}

@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "KundendokumentPflichtID";
}).GetHtml()
