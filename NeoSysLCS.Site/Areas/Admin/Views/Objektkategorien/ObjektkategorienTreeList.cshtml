@using System.Drawing
@using System.Web.UI.WebControls;
@using NeoSysLCS.Repositories.Helper
@model IEnumerable<NeoSysLCS.Repositories.ViewModels.ObjektkategorieViewModel>
@{
    var treelist = Html.DevExpress().TreeList(settings =>
    {
        settings.Name = "ObjektkategorienTreeList";
        settings.ParentFieldName = "ParentObjektkategorieID";
        settings.KeyFieldName = "ObjektkategorieID";

        settings.CallbackRouteValues = new { Controller = "Objektkategorien", Action = "ObjektkategorienTreeList" };

        settings.Width = Unit.Percentage(100);
        settings.Styles.AlternatingNode.Enabled = DefaultBoolean.True;
        settings.Settings.ShowColumnHeaders = true;
        settings.SettingsBehavior.ExpandCollapseAction = TreeListExpandCollapseAction.NodeDblClick;
        settings.SettingsCookies.StoreExpandedNodes = true;

        if (User.IsInRole(Role.ObjectGuru))
        {
            settings.SettingsEditing.Mode = TreeListEditMode.PopupEditForm;

            settings.SettingsEditing.DeleteNodeRouteValues = new
            {
                Controller = "Objektkategorien",
                Action = "ObjektkategorienTreeListDelete"
            };
            settings.SettingsEditing.NodeDragDropRouteValues = new
            {
                Controller = "Objektkategorien",
                Action = "ObjektkategorienTreeListMove"
            };

            settings.SettingsPopupEditForm.VerticalAlign = PopupVerticalAlign.Middle;
            settings.SettingsPopupEditForm.HorizontalAlign = PopupHorizontalAlign.Center;
            settings.SettingsPopupEditForm.Modal = true;

            settings.SettingsPopupEditForm.Caption = "Objektkategorie";
            settings.SettingsPopupEditForm.AllowResize = true;
            settings.SettingsPopupEditForm.MinHeight = 180;
            settings.SettingsPopupEditForm.MinWidth = 400;
        }

        settings.Columns.Add("Name");

        if (User.IsInRole(Role.ObjectGuru))
        {
            settings.CommandColumn.Visible = true;
            settings.CommandColumn.Width = Unit.Pixel(50);
            settings.CommandColumn.ButtonType = ButtonType.Image;

            settings.CommandColumn.DeleteButton.Visible = true;
            settings.CommandColumn.DeleteButton.Image.Url = "/Content/images/delete_sign-26.png";

            settings.CommandColumn.EditButton.Visible = true;
            settings.CommandColumn.EditButton.Image.Url = "/Content/images/edit-26.png";
        }
        //tooltip
        settings.HtmlDataCellPrepared += (sender, e) =>
        {
            //suppress error if there is no ErstelltVon Field, only on RoleViews view the case
            try
            {
                if (e.CellValue != null)
                    e.Cell.ToolTip = string.Format("{0}: {1}\n{2} {3}\n{4}: {5}\n{6} {7}", "Erstellt am", e.GetValue("ErstelltAm"), "Erstellt von", e.GetValue("ErstelltVon"), "Bearbeitet am", e.GetValue("BearbeitetAm"), "Bearbeitet von", e.GetValue("BearbeitetVon"));

            }
            catch (Exception)
            {

                // throw;
            }
        };

        if (User.IsInRole(Role.ObjectGuru))
        {
            settings.ClientSideEvents.ContextMenu = "OnGridContextMenu";
        }

        settings.PreRender = (sender, e) =>
        {
            var treeListControl = (MVCxTreeList)sender;
            treeListControl.ExpandToLevel(3);

        };

        settings.SetEditFormTemplateContent(c =>
        {
            if (Model.Any())
            {
                Html.RenderAction("ObjektkategorienGridView", new { objektkategorieID = DataBinder.Eval(c.DataItem, "ObjektkategorieID") });
            }
            else
            {
                Html.RenderAction("ObjektkategorienGridView");
            }
        });

        if (ViewData["EditError"] != null)
        {
            settings.Settings.ShowFooter = true;
            settings.Styles.FooterCell.BackColor = Color.FromArgb(243, 214, 214);
            settings.Styles.FooterCell.ForeColor = Color.FromArgb(186, 23, 23);

            settings.SetFooterCellTemplateContent(s =>
            {
                if (s.Column.Index == 0)
                    ViewContext.Writer.WriteLine((string)ViewData["EditError"]);
            });
        }
    });
}

@treelist.Bind(Model).GetHtml()

