@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers

@model  IQueryable<KundendokumentViewModel>


@{
    var unitOfWork = new UnitOfWork();

    var grid = Html.DevExpress().GridView(settings =>
    {
        settings.Name = "KundendokumenteGridView_" + ViewData["StandortID"];
        settings.KeyFieldName = "KundendokumentID";

        GridViewHelper.ApplyDefaultSettings(settings);

        settings.Settings.ShowGroupPanel = false;
        settings.SettingsContextMenu.Enabled = false;
        settings.Settings.ShowFilterRow = false;
        settings.Settings.ShowFooter = false;
        settings.SettingsBehavior.AllowSort = false;

        settings.SettingsPager.Mode = GridViewPagerMode.ShowAllRecords;
        settings.CallbackRouteValues = new { Controller = "KundendokumenteQs", Action = "KundendokumenteGridView", id = ViewData["StandortID"] };

        //Columns
        settings.Columns.Add(column =>
        {
            column.Caption = "#";
            column.SetDataItemTemplateContent(container =>
            {
                var kundendokument = Model.FirstOrDefault(k => k.KundendokumentID == (int)container.KeyValue);
                bool isNewestKundendokument = unitOfWork.KundendokumentRepository.IsNewestKundendokument((int?)ViewData["StandortID"], kundendokument.KundendokumentID);

                if (isNewestKundendokument)
                {
                    var hyperLink = Html.DevExpress().HyperLink(hyperlink =>
                    {
                        var keyValue = container.KeyValue;
                        hyperlink.Name = "QsFreigabe" + keyValue;
                        hyperlink.Properties.Text = Resources.View_KundendokumentQs_QsFreigabe;
                        hyperlink.NavigateUrl = Url.Action("KundendokumenteQs", "KundendokumenteQs", new { id = DataBinder.Eval(container.DataItem, "KundendokumentID") });
                        hyperlink.Properties.ImageUrl = "~/Content/images/check-square-o_26.png";
                        hyperlink.ToolTip = Resources.View_KundendokumentQs_QsFreigabe;
                    });

                    hyperLink.Render();
                }

                if (User.IsInRole(Role.ProjectManager))
                {
                    if (isNewestKundendokument && !kundendokument.HasQsFreigabe)
                    {

                        Html.DevExpress().HyperLink(hl =>
                        {
                            hl.Name = "linkEdit_" + container.KeyValue.ToString();
                            hl.NavigateUrl = Url.Action("KundendokumentEdit", "KundendokumentUpdate", new { kundendokumentId = DataBinder.Eval(container.DataItem, "KundendokumentID") });
                            hl.Properties.Text = "Edit";
                            hl.ToolTip = "Edit";
                            hl.Properties.ImageUrl = "~/Content/images/edit-26.png";
                        }).Render();
                    }

                    if (isNewestKundendokument && !kundendokument.HasFreigabe)
                    {
                        Html.DevExpress().HyperLink(hl =>
                        {
                            hl.Name = "linkDelete" + container.KeyValue.ToString();
                            hl.NavigateUrl = Url.Action("Delete", "Kundendokumente", new { standortId = ViewData["StandortID"], kundendokumentId = DataBinder.Eval(container.DataItem, "KundendokumentID") });
                            hl.Properties.Text = "Delete";
                            hl.ToolTip = "Delete";
                            hl.Properties.ImageUrl = "~/Content/images/delete_sign-26.png";
                            hl.Properties.ClientSideEvents.Click = @"function (s, e){
                                                                   if(!confirm('" + Resources.Kundendokument_Delete_Are_you_Sure + @"')){
                                                                        e.htmlEvent.preventDefault();
                                                                   }
                                                               }";
                        }).Render();

                    }


                    Html.RenderPartial("~/Views/KundendokumentImportExport/_ExportPartial.cshtml", container.KeyValue);

                    if (unitOfWork.KundendokumentRepository.IsCurrentKundendokument((int?)ViewData["StandortID"], (int?)DataBinder.Eval(container.DataItem, "KundendokumentID")))
                    {
                        //import is only possible for the current kundendokument of the kunde
                        Html.RenderPartial("~/Views/KundendokumentImportExport/_ImportPartial.cshtml", container.KeyValue);
                    }

                    Html.RenderPartial("~/Views/KundendokumentImportExport/_QuenticExportPartial.cshtml", container.KeyValue);

                }
            });

        });




        settings.Columns.Add(column =>
        {
            column.FieldName = "ErstelltAm";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentViewModel), column.FieldName);
            column.ReadOnly = true;
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.PropertiesEdit.DisplayFormatString = "dd/MM/yyyy HH:mm:ss";
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.SortDescending();
        });
        settings.Columns.Add(column =>
        {
            column.FieldName = "PublizierenAm";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentViewModel), column.FieldName);
            column.ReadOnly = true;
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.EditFormSettings.Visible = DefaultBoolean.False;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Status";
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentViewModel), column.FieldName);
            column.ReadOnly = true;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.SetDataItemTemplateContent(container =>
            {
                var statusCandidate = DataBinder.Eval(container.DataItem, "Status");
                if (statusCandidate != null)
                {
                    var status = (KundendokumentStatus)statusCandidate;
                    if (status == KundendokumentStatus.Approved)
                    {
                        ViewContext.Writer.Write("<span class=\"label label-success\">" + status.GetTranslation() + "</span>");
                    }
                    else
                    {
                        ViewContext.Writer.Write("<span class=\"label label-info\">" + status.GetTranslation() + "</span>");
                    }

                }
            });
        });
    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }

    @grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
    {
        e.QueryableSource = Model;
        e.KeyExpression = "KundendokumentID";
    }).GetHtml()

}
