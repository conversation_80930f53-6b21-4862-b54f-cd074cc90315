@using NeoSysLCS.DomainModel.Models;
@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers

@Html.DevExpress().GridView(
    settings =>
    {
        IUnitOfWork unitOfWork = new UnitOfWork();

        GridViewHelper.ApplyUebersetzungsSettings(settings);

        settings.Name = "PflichtUebersetzungenGridView_" + ViewData["PflichtID"];
        settings.KeyFieldName = "PflichtUebersetzungID";
        settings.CallbackRouteValues = new { Controller = "Pflichten", Action = "PflichtUebersetzungenGridView", id = ViewData["PflichtID"] };
        if (User.IsInRole(Role.ProjectManager))
        {
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
        }
        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "Pflichten", Action = "PflichtUebersetzungenGridViewBatchEditUpdate", id = ViewData["PflichtID"] };

        settings.CommandColumn.Visible = false;

        if (User.IsInRole(Role.Admin))
        {
            settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
        }

        //Columns
        settings.Columns.Add(column =>
        {
            column.FieldName = "Beschreibung";
            column.Caption = "Beschreibung";
            if (User.IsInRole(Role.ProjectManager))
            {
                
                column.ColumnType = MVCxGridViewColumnType.Memo;

                var memoProp = column.PropertiesEdit as MemoProperties;
                memoProp.Height = 100;
                memoProp.EncodeHtml = false;
            }
            column.SetDataItemTemplateContent(
                container =>
                {
                    var beschreibung = (string)DataBinder.Eval(container.DataItem, "Beschreibung");
                    if (!string.IsNullOrEmpty(beschreibung))
                    {
                        ViewContext.Writer.Write(StringHelper.TruncateHtml(beschreibung, 100));
                    }
                }
            );
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "SpracheID";
            column.Caption = "Sprache";
            column.ReadOnly = true;

            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

            comboBoxProperties.DataSource = unitOfWork.SpracheRepository.Get().ToList();
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueField = "SpracheID";
            comboBoxProperties.ValueType = typeof(int);
        });
        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);
        
        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
		    if(e.focusedColumn.fieldName == 'Beschreibung'){
					    openPopupOnBatchEditBegin(s, e);
				    }
	        }";
        
        settings.HtmlDataCellPrepared += (sender, e) =>
        {
            //register popup event
            if (e.DataColumn.FieldName == "Beschreibung")
            {
                if (!User.IsInRole(Role.ProjectManager))
                {
                    //readonly
                    e.Cell.Attributes.Add(
                       "onclick",
                       "openPopupReadonly(" + settings.Name + ", '" + e.DataColumn.FieldName + "', " + e.VisibleIndex + ", '" + e.DataColumn.Caption + "')"
                       );

                }
                
            }
        };
    }).Bind(Model).GetHtml()