@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.ViewModels

@section Scripts {
    @Scripts.Render("~/Scripts/jquery.unobtrusive-ajax.min.js")
    @Scripts.Render("~/Scripts/helpers/SelectionHelper.js")
}

@model IQueryable<ForderungsversionViewModel>


@{
    ViewBag.Title = "Forderungsversionselektierung für Pflicht " + ViewData["ObligationName"];
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}

<script type="text/javascript">
    //<![CDATA[


    var selectionHelper;

    //grid init function
    function SelectionInit(itemId, grid, selectbox, countSelector, resultSelector) {
        if (selectionHelper == null) {
            selectionHelper = new SelectionHelper(
                '@Url.Action("SaveObligationForderungsversions", "ObligationForderungsversions", new { Area = "Admin", view = ViewData["ObligationView"] })',
                "{0} / {1} / {2}"
            );
        }
        selectionHelper.AddSelection(itemId, grid, selectbox, countSelector, resultSelector);
    }
    //grid selection changed
    function SelectionChanged(itemId, e) {
        selectionHelper.SelectionChanged(itemId, "ForderungsversionID;ErlassTitel;ErlassSrNummer;Inkrafttretung", e);
    }
    //save button click
    function OnSubmitSelectionClick(itemId) {
        selectionHelper.OnSubmitClick(itemId);
    }
   
    // ]]>
</script>
@Html.Partial("_ObligationForderungsversionsPartial", Model)