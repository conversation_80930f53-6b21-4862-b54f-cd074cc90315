@using System.Globalization
@using System.Web.UI.WebControls
@using DevExpress.Data.Filtering.Helpers
@using DevExpress.Web.Mvc.Internal
@using Microsoft.AspNet.Identity
@using Microsoft.AspNet.Identity.EntityFramework
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers

@{
    var grid = Html.DevExpress().GridView(settings =>
    {

        settings.Name = "UserStandortGridView";
        settings.KeyFieldName = "StandortID";

        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        settings.SettingsPager.Mode = GridViewPagerMode.ShowAllRecords;
        settings.SettingsPager.Visible = false;
        settings.Settings.ShowGroupPanel = false;
        settings.SettingsContextMenu.Enabled = false;
        settings.Settings.ShowFilterRow = false;
        settings.Settings.ShowFooter = false;
        settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
        settings.CallbackRouteValues = new { Controller = "UsersAdmin", Action = "UserStandortGridView", kundeId = ViewData["kundeID"], userId = ViewData["userID"] };

        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "UsersAdmin", Action = "UserStandortGridViewBatchEditUpdate", kundeId = ViewData["kundeID"], userId = ViewData["userID"] };


        settings.Columns.Add(column =>        {

            column.FieldName = "Name";
            column.Caption = Resources.View_Standort_Name;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Read";
            column.Caption = "Read";
            column.MinWidth = 100;
            column.ColumnType = MVCxGridViewColumnType.CheckBox;
        });

        settings.Columns.Add(column =>
        {

            column.FieldName = "Write";
            column.Caption = "Write";
            column.MinWidth = 100;
            column.ColumnType = MVCxGridViewColumnType.CheckBox;
        });

        if (User.IsInRole(Role.SuperUser))
        {
            //Fixed NEOS-322 Clear Timer on StartEditing
            settings.ClientSideEvents.BatchEditStartEditing = "OnBatchStartEditing";
            settings.ClientSideEvents.BatchEditEndEditing = "OnBatchEditEndEditing";
        }
        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "StandortID";
}).GetHtml()