@using System.Globalization
@using System.Web.UI.WebControls
@using DevExpress.Data.Filtering.Helpers
@using DevExpress.Web.Mvc.Internal
@using Microsoft.AspNet.Identity
@using Microsoft.AspNet.Identity.EntityFramework
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories;
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers
@using DevExpress.Data
@using DevExpress.Data.Filtering
@using NeoSysLCS.Site.Areas.Admin.Controllers

@model IQueryable<NeoSysLCS.Repositories.ViewModels.UserViewModel>

@{
    bool needCreatCopy = ViewData["key"] != null;
    string userid = ViewData["userid"] != null ? ViewData["userid"].ToString() : null;
}

@functions{
    public static string HighlightSearchText(string source, string searchText)
    {
        if (string.IsNullOrWhiteSpace(searchText))
            return source;
        var regex = new System.Text.RegularExpressions.Regex(System.Text.RegularExpressions.Regex.Escape(searchText), System.Text.RegularExpressions.RegexOptions.IgnoreCase);
        if (regex.IsMatch(source))
            return string.Format("<span>{0}</span>", regex.Replace(source, "<span class='dxgvHL'>$0</span>"));
        return source;
    }
}


@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        UnitOfWork unitOfWork = new UnitOfWork();

        /*Default Settings*/
        settings.Name = "UserAdminView";
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        settings.CallbackRouteValues = new { Controller = "UsersAdmin", Action = "UserAdminViewPartial", displayDeactivated = ViewData["displayDeactivated"] };
        //settings.CallbackRouteValues = new { Controller = "UsersAdmin", Action = "UserAdminViewPartial" };
        //settings.CustomActionRouteValues = new { Controller = "UsersAdmin", Action = "CheckKunde" };

        settings.SettingsEditing.AddNewRowRouteValues = new { Controller = "UsersAdmin", Action = "UserAdminViewPartialAddNew" };
        settings.SettingsEditing.UpdateRowRouteValues = new { Controller = "UsersAdmin", Action = "UserAdminViewPartialUpdate" };
        settings.SettingsEditing.DeleteRowRouteValues = new { Controller = "UsersAdmin", Action = "UserAdminViewPartialDelete" };
        settings.SettingsEditing.Mode = GridViewEditingMode.PopupEditForm;
        settings.ClientSideEvents.EndCallback = "OnEndCallback";

        //settings.ClientSideEvents.Init = "setVisibleRoles";



        settings.SettingsBehavior.ConfirmDelete = true;
        settings.Styles.AlternatingRow.Enabled = DefaultBoolean.True;
        settings.SettingsBehavior.EnableRowHotTrack = true;

        settings.SettingsPopup.EditForm.Modal = true;
        settings.SettingsPopup.EditForm.VerticalAlign = PopupVerticalAlign.WindowCenter;
        settings.SettingsPopup.EditForm.HorizontalAlign = PopupHorizontalAlign.WindowCenter;
        settings.SettingsPopup.EditForm.Height = 500;
        //settings.EditFormLayoutProperties.Styles.LayoutItem.Caption.CssClass = "userCaption";
        settings.SettingsPopup.EditForm.ShowHeader = true;

        settings.BeforeGetCallbackResult = (sender, e) =>
        {
            var gridView = sender as MVCxGridView;

            gridView.SettingsText.PopupEditFormCaption = gridView.IsNewRowEditing ? Resources.Neuen_Benutzer_hinzuf�gen : Resources.Benutzer_bearbeiten;
        };

        settings.CommandColumn.Visible = true;
        settings.CommandColumn.ShowNewButtonInHeader = true;
        if (User.IsInRole(Role.Admin))
        {
            settings.CommandColumn.ShowDeleteButton = true;
        }
        settings.CommandColumn.ShowEditButton = true;

        //todo implement reload after grid save
        //settings.ClientSideEvents.EndCallback = "function(s, e) { s.PerformCallback(); alert('test'); }";

        //pwdreset button
        //settings.CustomActionRouteValues = new { Controller = "UsersAdmin", Action = "UserAdminViewPartial" };
        if (User.IsInRole(Role.Admin) || User.IsInRole(Role.SuperUser))
        {
            var pwdresetbtn = new GridViewCommandColumnCustomButton();
            pwdresetbtn.ID = "PWReset";
            pwdresetbtn.Text = "<img title='Passwort zur�cksetzen' src='/Content/images/key_security-24.png'>";
            settings.ClientSideEvents.BeginCallback = "OnBeginCallback";
            settings.ClientSideEvents.CustomButtonClick = "OnCustomButtonClick";
            settings.CommandColumn.CustomButtons.Add(pwdresetbtn);
        }

        if (User.IsInRole(Role.Admin))
        {
            var deleteCookieButton = new GridViewCommandColumnCustomButton();
            deleteCookieButton.ID = "DeleteCookies";
            deleteCookieButton.Text = "<img title='Cookies l�schen' src='/Content/images/delete_cookies.png'>";
            settings.ClientSideEvents.BeginCallback = "OnBeginCallback";
            settings.ClientSideEvents.CustomButtonClick = "OnCustomButtonClick";
            settings.CommandColumn.CustomButtons.Add(deleteCookieButton);
        }


        settings.KeyFieldName = "Id";

        settings.SettingsPager.Visible = true;
        settings.Settings.ShowGroupPanel = true;
        settings.Settings.ShowFilterRow = true;
        settings.SettingsBehavior.AllowSelectByRowClick = true;
        /*Default Settings*/

        /*Column Definition*/
        settings.Columns.Add(column =>
        {
            column.FieldName = "Vorname";
            column.Caption = TranslationHelper.GetTranslation(typeof(UserViewModel), column.FieldName);
            column.EditFormSettings.VisibleIndex = 1;
            column.EditFormSettings.RowSpan = 1;
            column.EditFormSettings.ColumnSpan = 2;
            var txtBoxProperties = column.PropertiesEdit as TextBoxProperties;
            column.EditFormCaptionStyle.CssClass = "userCaption";
            //txtBoxProperties.Width = 250;
        });
        settings.Columns.Add(column =>
        {
            column.FieldName = "Nachname";
            column.Caption = TranslationHelper.GetTranslation(typeof(UserViewModel), column.FieldName);

            column.EditFormSettings.VisibleIndex = 2;
            column.EditFormSettings.ColumnSpan = 2;
            column.EditFormCaptionStyle.CssClass = "userCaption";
            var txtBoxProperties = column.PropertiesEdit as TextBoxProperties;

            //txtBoxProperties.Width = 250;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Email";
            column.Caption = TranslationHelper.GetTranslation(typeof(UserViewModel), column.FieldName);

            column.ColumnType = MVCxGridViewColumnType.TextBox;

            column.EditFormSettings.ColumnSpan = 2;

            column.EditFormSettings.VisibleIndex = 3;
            column.EditFormCaptionStyle.CssClass = "userCaption";

            var txtBoxProperties = column.PropertiesEdit as TextBoxProperties;

            txtBoxProperties.ValidationSettings.RegularExpression.ValidationExpression = "\\w+([-+.']\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*";
            txtBoxProperties.ValidationSettings.RegularExpression.ErrorText = Resources.SetPasswordViewModel_ValidEmail_ErrorMessage;

            txtBoxProperties.ValidationSettings.ErrorDisplayMode = ErrorDisplayMode.ImageWithTooltip;

        });


        settings.Columns.Add(column =>
        {
            column.FieldName = "ApplicationRoles";
            column.Caption = Resources.Label_Rollen;
            column.ReadOnly = true;
            column.ColumnType = MVCxGridViewColumnType.ComboBox;

            column.Settings.AllowHeaderFilter = DefaultBoolean.True;
            column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.CheckedList;
            column.Settings.AllowAutoFilter = DefaultBoolean.False;
            column.Settings.ShowFilterRowMenu = DefaultBoolean.False;
            column.Settings.AllowSort = DefaultBoolean.False;
            column.EditFormSettings.Visible = DefaultBoolean.False;

            column.SetDataItemTemplateContent(c =>
            {
                var test = c.Grid.SearchPanelFilter;
                var dataItem = DataBinder.Eval(c.DataItem, "ApplicationRoles");
                if (dataItem != null)
                {
                    IQueryable<ApplicationRoleViewModel> applicationRoles = dataItem as IQueryable<ApplicationRoleViewModel>;
                    List<string> tbl = new List<string>();

                    foreach (ApplicationRoleViewModel t in applicationRoles)
                    {
                        tbl.Add(t.Displayname);
                    }

                    Html.DevExpress().Label(labelSettings =>
                    {
                        labelSettings.Name = "ApplicationRoles" + c.ClientID;
                        labelSettings.Text = String.Join(", ", tbl);
                        labelSettings.EncodeHtml = false;
                        labelSettings.PreRender += (s, e) =>
                        {
                            MVCxLabel lbl = (MVCxLabel)s;
                            lbl.Text = HighlightSearchText(lbl.Text, c.Grid.SearchPanelFilter);
                        };
                    }).Render();
                }
            });

        });

        settings.BeforeHeaderFilterFillItems = (sender, e) =>
        {
            UsersAdminController usersAdminController = new UsersAdminController();
            var allApplicationRoles = usersAdminController.GetApplicationRoleComboBox().ToList();

            if (e.Column.FieldName == "ApplicationRoles")
            {
                foreach (ApplicationRoleViewModel ar in allApplicationRoles)
                {

                    var testCriteria = new ContainsOperator("ApplicationRoles",
                        new BinaryOperator(
                            new OperandProperty("DisplayName"), ar.Displayname,
                                BinaryOperatorType.Equal
                            )
                        );

                    e.AddValue(ar.Displayname, testCriteria);
                }
                e.Handled = true;
            }
        };

        settings.Columns.Add(column =>
        {
            ///*Display*/
            column.FieldName = "KundeID";
            column.Caption = TranslationHelper.GetTranslation(typeof(UserViewModel), column.FieldName);

            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.EditFormCaptionStyle.CssClass = "userCaption";

            if (User.IsInRole(Role.Admin))
            {
                column.EditFormSettings.Visible = DefaultBoolean.True;
                column.EditFormSettings.VisibleIndex = 4;
                column.EditFormSettings.ColumnSpan = 2;
            }
            //The Customer Admin should only be able to select his company and Neosys Admin should be able to select all companies
            if (User.IsInRole(Role.Admin))
            {
                column.EditorProperties().ComboBox(c =>
                {
                    c.CallbackRouteValues = new { Controller = "UsersAdmin", Action = "KundeComboBox", kunden = ViewData["Kunden"] };
                    c.TextField = "Name";
                    c.ValueField = "KundeID";
                    c.ValueType = typeof(int);
                    c.CallbackPageSize = 1000;
                    c.BindList(unitOfWork.KundeRepository.GetAllKundenViewModels().Select(x => new { x.KundeID, x.Name }).OrderBy(x => x.Name).ToList());
                    c.ClientSideEvents.Init = "onInitKundenSelection";
                    c.ClientSideEvents.SelectedIndexChanged = "onChangedKundeID";
                });
            }
            else
            {
                var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
                comboBoxProperties.ClientSideEvents.SelectedIndexChanged = "setStandortWrite";
                comboBoxProperties.DataSource = ViewData["Kunden"];
                comboBoxProperties.TextField = "Name";
                comboBoxProperties.ValueField = "KundeID";
                comboBoxProperties.ValueType = typeof(int);
            }
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "SpracheID";
            column.Caption = TranslationHelper.GetTranslation(typeof(UserViewModel), column.FieldName);

            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            column.EditFormSettings.Visible = DefaultBoolean.True;
            column.EditFormCaptionStyle.CssClass = "userCaption";
            column.EditFormSettings.VisibleIndex = 5;
            column.EditFormSettings.ColumnSpan = 2;

            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

            comboBoxProperties.DataSource = unitOfWork.SpracheRepository.Get().ToList();
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueField = "SpracheID";
            comboBoxProperties.ValueType = typeof(int);

            comboBoxProperties.IncrementalFilteringMode = IncrementalFilteringMode.Contains;

            //comboBoxProperties.Width = 250;
            //comboBoxProperties.DropDownWidth = 250;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Password";
            column.Caption = TranslationHelper.GetTranslation(typeof(UserViewModel), column.FieldName);

            column.Visible = false;
            column.EditFormSettings.Visible = DefaultBoolean.True;
            column.EditFormSettings.VisibleIndex = 6;
            column.EditFormSettings.ColumnSpan = 2;

            column.EditFormSettings.Visible = DefaultBoolean.False; // hide for edit form

            column.ColumnType = MVCxGridViewColumnType.TextBox;
            var txtBoxProperties = column.PropertiesEdit as TextBoxProperties;

            txtBoxProperties.Width = 250;
            txtBoxProperties.Password = true;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "ConfirmPassword";
            column.Caption = TranslationHelper.GetTranslation(typeof(UserViewModel), column.FieldName);

            column.Visible = false;
            column.EditFormSettings.Visible = DefaultBoolean.True;
            column.EditFormSettings.VisibleIndex = 7;
            column.EditFormSettings.ColumnSpan = 2;

            column.EditFormSettings.Visible = DefaultBoolean.False; //hide for edit form

            column.ColumnType = MVCxGridViewColumnType.TextBox;
            var txtBoxProperties = column.PropertiesEdit as TextBoxProperties;

            txtBoxProperties.Width = 250;
            txtBoxProperties.Password = true;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "ErstelltVon";
            column.Caption = TranslationHelper.GetTranslation(typeof(UserViewModel), column.FieldName);
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.ColumnType = MVCxGridViewColumnType.TextBox;
            var txtBoxProperties = column.PropertiesEdit as TextBoxProperties;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "BearbeitetVon";
            column.Caption = TranslationHelper.GetTranslation(typeof(UserViewModel), column.FieldName);
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.ColumnType = MVCxGridViewColumnType.TextBox;
            var txtBoxProperties = column.PropertiesEdit as TextBoxProperties;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "LockoutEndDate";
            column.Caption = TranslationHelper.GetTranslation(typeof(UserViewModel), column.FieldName);
            column.Width = 50;
            column.EditFormSettings.VisibleIndex = 10;
            column.ColumnType = MVCxGridViewColumnType.CheckBox;

            //settings.InitNewRow = (s, e) =>
            //{
            //    e.NewValues["LockoutEndDate"] = (short)0;
            //};

        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Hint";
            column.Caption = " ";
            column.Visible = false;
            column.ColumnType = MVCxGridViewColumnType.Memo;
            column.EditFormSettings.Visible = DefaultBoolean.True;
            column.EditFormSettings.ColumnSpan = 2;
            column.EditFormSettings.VisibleIndex = 9;
            column.ReadOnly = true;

            column.PropertiesEdit.Style.CssClass = "hintClass";

            column.PropertiesEdit.Style.Border.BorderStyle = BorderStyle.None;
            column.PropertiesEdit.Style.BackColor = System.Drawing.Color.Transparent;
            column.PropertiesEdit.Style.ForeColor = System.Drawing.Color.Red;
            column.PropertiesEdit.Style.Font.Size = 12;
        });
        settings.Columns.Add(column =>
        {
            column.FieldName = "WriteStandorte";
            column.Caption = NeoSysLCS.Site.Helpers.CultureHelper.GetStandortMenuName(0, 0, false);
            column.Visible = false;
            column.EditFormSettings.Visible = DefaultBoolean.True;
            column.EditFormSettings.VisibleIndex = 8;
            column.EditFormCaptionStyle.CssClass = "userCaption";

            var readonlyRole = unitOfWork.Context.Roles.FirstOrDefault(r => r.Name == "READONLY");

            column.SetEditItemTemplateContent(editTemplate =>
            {

                var userID = (string)DataBinder.Eval(editTemplate.DataItem, "Id");
                var kundeID = DataBinder.Eval(editTemplate.DataItem, "KundeID") is int ? (int)DataBinder.Eval(editTemplate.DataItem, "KundeID") : 0;
                if (User.IsInRole(Role.SuperUser))
                {
                    var sh = new SessionHelper();
                    kundeID = sh.GetCurrentUser().KundeID.Value;
                }
                IEnumerable<StandortViewModel> standorte = new List<StandortViewModel>();
                ViewData["userID"] = userID;
                ViewData["kundeID"] = kundeID;
                //get standorte from kunde for first load, if user not
                if (kundeID != 0)
                    standorte = unitOfWork.StandortRepository.GetAllStandortViewModels(kundeID).ToList();


                if (standorte.Any())
                {
                    ViewData["standorte"] = standorte;
                }

                Html.RenderPartial("_CallbackPanel");



            });

        });


        settings.Columns.Add(chkBoxList =>
        {
            chkBoxList.FieldName = "RolesField";
            chkBoxList.Caption = Resources.Label_Rollen;

            chkBoxList.Visible = false;
            chkBoxList.EditFormSettings.Visible = DefaultBoolean.True;
            chkBoxList.EditFormSettings.VisibleIndex = 8;
            chkBoxList.EditFormCaptionStyle.CssClass = "userRoleCaption";

            chkBoxList.ColumnType = MVCxGridViewColumnType.CheckBox;


            chkBoxList.SetEditItemTemplateContent(editTemplate =>
            {
                var user = editTemplate.DataItem;
                var roles = ((ICollection<ApplicationRole>)ViewData["Roles"]).ToList();

                Html.DevExpress().CheckBoxList(
                     chkSettings =>
                     {

                         chkSettings.Name = "GUIRoles";
                         chkSettings.Properties.ValueField = "Name";
                         chkSettings.Properties.TextField = "DisplayName";
                         chkSettings.Properties.ValidationSettings.RequiredField.IsRequired = true;
                         chkSettings.Properties.ValidationSettings.RequiredField.ErrorText = "Bitte w�hlen Sie mindestens eine Rolle"; //todo translate
                         chkSettings.ShowModelErrors = true;
                         chkSettings.Properties.RepeatColumns = 1;
                         chkSettings.Properties.RepeatDirection = RepeatDirection.Horizontal;
                         if (User.IsInRole(Role.Admin))
                         {
                             chkSettings.Properties.ClientSideEvents.SelectedIndexChanged = "Roles_SelectedIndexChanged";
                             chkSettings.Properties.ClientSideEvents.Init = "preselectCheckbox";
                         }
                         if (User.IsInRole(Role.SuperUser))
                         {
                             chkSettings.Properties.ClientSideEvents.SelectedIndexChanged = "Roles_SelectedIndexChanged_SuperUser";
                             chkSettings.Properties.ClientSideEvents.Init = "preselectCheckbox_SuperUser";

                         }
                         chkSettings.Style.Value = "margin-left:29px;";

                         chkSettings.Width = 250;

                         chkSettings.PreRender = (sender, e) =>
                         {
                             //get roles comma separated values
                             if (ViewData["SelectedRoles"] == null && !editTemplate.Grid.IsNewRowEditing)
                             {
                                 var rolesString = (string)DataBinder.Eval(user, "GUIRoles");

                                 var rolesStringReturn = rolesString == null ? new string[0] : rolesString.Split(',');

                                 ViewData["SelectedRoles"] = rolesStringReturn;
                             }
                             var currentUserRoles = (IEnumerable<string>)ViewData["SelectedRoles"];

                             if (currentUserRoles != null)
                             {
                                 var cbl = (MVCxCheckBoxList)sender;
                                 foreach (ListEditItem item in cbl.Items)
                                 {
                                     item.Selected = currentUserRoles.Contains(item.Value);
                                 }
                             }
                         };
                     }).BindList(roles).GetHtml();
            });
        });

        // grid boolean checkbox if user has newsletter role
        settings.Columns.Add(column =>
        {
            column.FieldName = "NewsletterRole";
            column.Caption = TranslationHelper.GetTranslation(typeof(UserViewModel), column.FieldName);
            column.Width = 50;
            column.ColumnType = MVCxGridViewColumnType.CheckBox;

            column.EditFormSettings.Visible = DefaultBoolean.False;
        });


        // Grid Zeitspanne field
        settings.Columns.Add(column =>
        {
            column.FieldName = "NewsletterPeriodGrid";
            column.Caption = TranslationHelper.GetTranslation(typeof(UserViewModel), column.FieldName);
            column.Visible = true;
            column.Width = 50;

            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.EditFormCaptionStyle.CssClass = "userCaption";

            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
            var list = new[]
            {
                    new {
                        Value = 1,
                        Name = "1 " + Resources.Entitaet_User_NewsletterPeriodMonat
                    },
                    new {
                        Value = 3,
                        Name = "3 "+ Resources.Entitaet_User_NewsletterPeriodMonate
                    },
                    new {
                        Value = 6,
                        Name = "6 "+ Resources.Entitaet_User_NewsletterPeriodMonate
                    },
                                };

            comboBoxProperties.DataSource = list;
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueField = "Value";
            comboBoxProperties.ValueType = typeof(int);

        });

        // edit form Zeitspanne field
        settings.Columns.Add(column =>
        {
            column.FieldName = "NewsletterPeriod";
            column.Caption = TranslationHelper.GetTranslation(typeof(UserViewModel), column.FieldName);
            column.Visible = false;

            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            column.EditFormSettings.Visible = DefaultBoolean.True;

            column.EditFormSettings.VisibleIndex = 7;
            column.EditFormSettings.ColumnSpan = 2;

            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
            var list = new[]
            {
                    new {
                        Value = 1,
                        Name = "1 " + Resources.Entitaet_User_NewsletterPeriodMonat
                    },
                    new {
                        Value = 3,
                        Name = "3 "+ Resources.Entitaet_User_NewsletterPeriodMonate
                    },
                    new {
                        Value = 6,
                        Name = "6 "+ Resources.Entitaet_User_NewsletterPeriodMonate
                    },
                                };

            comboBoxProperties.DataSource = list;
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueField = "Value";
            comboBoxProperties.ValueType = typeof(int);

        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "IsTermsAccepted";
            column.Caption = "Terms";
            column.Width = 50;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.Visible = true;
            column.ColumnType = MVCxGridViewColumnType.CheckBox;
        });

        /*Column Definition*/

        settings.HtmlDataCellPrepared += (sender, e) =>
        {
            var lockoutEndDate = e.GetValue("LockoutEndDate");
            if (!(bool)lockoutEndDate)
            {
                e.Cell.BackColor = System.Drawing.Color.Salmon;
            }
        };

        settings.InitNewRow = (sender, e) =>
        {
            e.NewValues["Hint"] = Resources.View_UserAdmin_Hint;
            e.NewValues["LockoutEndDate"] = true;
        };
    });

}
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "Id";
}).GetHtml()