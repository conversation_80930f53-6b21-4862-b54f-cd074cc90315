@model dynamic

@Html.DevExpress().CallbackPanel(cpsettings =>
{
    cpsettings.Name = "cp";
    cpsettings.CallbackRouteValues = new { Controller = "UsersAdmin", Action = "CallbackPanelPartial" };
    cpsettings.ClientSideEvents.BeginCallback = "function(s, e) {  e.customArgs['Kunde'] = selectedKunde }";

    cpsettings.SetContent(() =>
    {
        Html.RenderAction("UserStandortGridView", "UsersAdmin", new { kundeId = ViewData["kundeID"], userId = ViewData["userID"] });
    });
}).GetHtml()