using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using DevExpress.Mvvm.Native;
using DevExpress.Web;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using DevExpress.Web.Mvc;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Helpers;
using NeoSysLCS.Site.Utilities.Export;
using System;
using System.Dynamic;
using DevExpress.XtraPrinting;
using DevExpress.XtraPrintingLinks;
using System.IO;
using System.Net;
using NeoSysLCS.Site.AzureStorage;
using System.Web.UI;
using System.Web.Http;
using System.Configuration;
using System.Globalization;
using System.Linq.Expressions;
using System.Threading.Tasks;
using System.Web.UI.WebControls;
using DevExpress.Utils;
using Hangfire;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.EntityFramework;
using NeoSysLCS.Site.Cortec.task;
using NeoSysLCS.Site.Cortec.task.kommentar;

namespace NeoSysLCS.Site.Areas.Admin.Controllers
{
    public class KommentarController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;
        NeoSysLCS_Dev context = new NeoSysLCS_Dev();

        public KommentarController()
        {
            _unitOfWork = new UnitOfWork();
        }

        public KommentarController(IUnitOfWork unitOfWorkCandidate)
        {
            _unitOfWork = unitOfWorkCandidate;
        }

        public ActionResult Index(bool displayArchived = false)
        {
            return View("Index");
        }

        public ActionResult KommentareGridView(bool displayArchived = false)
        {
            //return PartialView("_ErlasseGridView");
            var viewModels = _unitOfWork.KommentarRepository.GetAllKommentarViewModels();
            return PartialView("_KommentareGridView", viewModels);
        }

        public ActionResult KommentarPageControlCallbacksPartial()
        {
            return PartialView("_KommentarPageControlCallbacksPartial");
        }

        [ValidateInput(false)]
        public ActionResult KommentareGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<KommentarViewModel, int> updateValues)
        {
            var userId = User.Identity.GetUserId();
            var user = _unitOfWork.UserRepository.GetById(userId);
            foreach (var kommentar in updateValues.Insert)
            {
                GridViewUpdateHelper<Kommentar, KommentarViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(kommentar))
                    {
                        kommentar.ErstelltVonID = userId;
                        var createdKommentar = _unitOfWork.KommentarRepository.Insert(kommentar);
                        _unitOfWork.Save();
                        kommentar.KommentarID = createdKommentar.KommentarID;
                        CreateCortecTask(kommentar, user.CortecId ?? -1);
                    }
                }, kommentar, updateValues);
            }
            foreach (var kommentar in updateValues.Update)
            {
                GridViewUpdateHelper<Kommentar, KommentarViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(kommentar))
                    {

                        var k = _unitOfWork.KommentarRepository.GetByID(kommentar.KommentarID);
                        var statusChanged = k.Status != kommentar.Status;
                        _unitOfWork.KommentarRepository.Update(kommentar);
                        _unitOfWork.Save();
                        kommentar.HauptErlassID = k.HauptErlassID;
                        if (statusChanged)
                        {
                            CreateCortecTask(kommentar, user.CortecId ?? -1);
                        }
                    }
                }, kommentar, updateValues);
            }
            foreach (var kommentarID in updateValues.DeleteKeys)
            {
                GridViewUpdateHelper<Kommentar, KommentarViewModel>.DoDelete(() =>
                {
                    _unitOfWork.KommentarRepository.Delete(kommentarID);
                    _unitOfWork.Save();
                }, _unitOfWork.KommentarRepository, kommentarID, updateValues);

            }

            var viewModels = _unitOfWork.KommentarRepository.GetAllKommentarViewModels();
            return PartialView("_KommentareGridView", viewModels);
        }

        public void CreateCortecTask(KommentarViewModel kommentar, int appUserCortecId)
        {

            // Create an adapter for the Kommentar, making it compatible with the factory
            var adapter = new KommentarAdapter(kommentar, _unitOfWork, appUserCortecId);

            // Use the factory and strategies to create tasks based on the adapted Kommentar
            var cortecTaskFactory = new CortecTaskFactory<KommentarViewModel>(adapter, _unitOfWork);

            cortecTaskFactory.CreateCortecTasks();

            _unitOfWork.KommentarRepository.SetCortecTaskCreatedDate(kommentar.KommentarID);
            _unitOfWork.Save();
        }

        public ActionResult KommentarUebersetzungGridView(int kommentarID)
        {
            //IEnumerable<Sprache> sprachen = _unitOfWork.SpracheRepository.Get();
            //_unitOfWork.KommentarUebersetzungRepository.PopulatedKommentarUebersetzungenByKommentar(kommentarID, sprachen);
            //_unitOfWork.Save();

            var uebersetzungen = _unitOfWork.KommentarRepository.GetKommentarUebersetzungenByKommentar(kommentarID);

            ViewData["KommentarID"] = kommentarID;
            return PartialView("_KommentarUebersetzungenGridView", uebersetzungen);
        }

        [ValidateInput(false)]
        public ActionResult KommentarUebersetzungGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<KommentarViewModel, int> updateValues, int kommentarID)
        {
            foreach (var uebersetzung in updateValues.Update)
            {
                GridViewUpdateHelper<Kommentar, KommentarViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(uebersetzung))
                    {
                        uebersetzung.KommentarID = kommentarID;
                        _unitOfWork.KommentarRepository.UpdateUebersetzungKommentar(uebersetzung, kommentarID);
                        _unitOfWork.Save();
                    }
                }, uebersetzung, updateValues);
            }

            ViewData["KommentarID"] = kommentarID;
            var uebersetzungen = _unitOfWork.KommentarRepository.GetKommentarUebersetzungenByKommentar(kommentarID);


            return PartialView("_KommentarUebersetzungenGridView", uebersetzungen);
        }

        public ActionResult ExportKommentare()
        {
            var kommentare = _unitOfWork.KommentarRepository.GetAllObjectIdsByKommentar().ToList();
            //var kommentarViewModels = _unitOfWork.KommentarRepository.GetAllKommentarViewModels();
            var kunden = _unitOfWork.KundeRepository.GetAllKundenViewModels().ToList();
            var kundenObjectMap = new Dictionary<int, List<int>>();

            foreach (var kunde in kunden)
            {
                var kundendokumentIds = _unitOfWork.KundendokumentRepository.GetAllKundendokumentIdsByKunde(kunde.KundeID);
                List<int> objectIds = _unitOfWork.KundendokumentForderungsversionRepository.GetAllObjectIdsByForderungsversion(kundendokumentIds);
                kundenObjectMap.Add(kunde.KundeID, objectIds);
            };

            List <ExpandoObject> expandos = new List<ExpandoObject>();
            var list = new List<Dictionary<string, object>>();


            for (int i = 0; i < kommentare.Count(); i++)
            {
                dynamic expando = new ExpandoObject();
                var expandoDict = expando as IDictionary<string, object>;

                var kommentar = kommentare[i];

                expandoDict.Add("Kommentar", kommentar.KommentarID);

                //Objekt Id pro kunde
                var objectIds = kommentar.ObjektIds.ToList();//kundenObjectMap.GetValueOrDefault(kunden[i].KundeID);


                foreach (var kunde in kunden)
                {
                    //ObjektId pro Kommentar
                    //List<int> kommentarIds = kommentar.ObjektIds.ToList();

                    List<int> kundeIds = kundenObjectMap.GetValueOrDefault(kunde.KundeID);


                    string id = kunde.Name;
                    kommentar.BetroffenKommentar = WebUtility.HtmlDecode(StringHelper.StripAllTags(kommentar.BetroffenKommentar));
                    kommentar.NichtBetroffenKommentar = WebUtility.HtmlDecode(StringHelper.StripAllTags(kommentar.NichtBetroffenKommentar));
                    if (kundeIds.Any(x => objectIds.Any(y => y == x)))
                    {
                        if (kommentar.LinkBetroffenKommentar != "" && kommentar.LinkBetroffenKommentar != null)
                        {
                            expandoDict.Add(id, kommentar.LinkBetroffenKommentar ?? "");
                        }
                        else {
                            expandoDict.Add(id, kommentar.BetroffenKommentar ?? "");
                        }
                    }
                    else
                    {
                        if (kommentar.LinkNichtBetroffenKommentar != "" && kommentar.LinkNichtBetroffenKommentar != null)
                        {
                            expandoDict.Add(id, kommentar.LinkNichtBetroffenKommentar ?? "");
                        }
                        else
                        {
                            expandoDict.Add(id, kommentar.NichtBetroffenKommentar ?? "");
                        }
                    }
                }
                expandos.Add(expando);
            }

            var exportType = ExportUtilities.GetExportType(Request.Params["exportFormat"]);
            //var exportResult = ExportUtilities.Export(exportType, GetExportGridSettings(exportType, kommentare), expandos, false);

            var ps = new PrintingSystem();
            var kommentarLink = new PrintableComponentLink(ps);
            var kommentarSettings = GetExportGridSettings(exportType, kunden);
            kommentarLink.Component = GridViewExtension.CreatePrintableObject(kommentarSettings, expandos);

            var compositeLink = new CompositeLink(ps);

            compositeLink.Links.AddRange(new[] { kommentarLink});
            compositeLink.CreateDocument();
            compositeLink.CreatePageForEachLink();
            var stream = new MemoryStream();
            compositeLink.PrintingSystem.ExportToXlsx(stream, new XlsxExportOptions()

            {
                ExportMode = XlsxExportMode.SingleFilePageByPage,
            });

            stream.Position = 0;
            var result = new FileStreamResult(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            result.FileDownloadName = "Kommentare_" + DateTime.Today.AddDays(-365).ToShortDateString() + "_bis_" + DateTime.Today.ToShortDateString() + ".xlsx";

            ps.Dispose();

            return result;
        }

        private GridViewSettings GetExportGridSettings(ExportType exportType, List<KundeViewModel> model)
        {
            var settings = new GridViewSettings();
            settings.Name = "ExpandoGrid";
            settings.KeyFieldName = "KundeID";            

            //Add Columns: there need to be the same colums in the view and for the export for sorting
            settings.CommandColumn.Visible = true;
            settings.Columns.Add(c =>
            {
                c.FieldName = "Kommentar";
            });

            foreach (var kunde in model)
            {
                settings.Columns.Add(c =>
                {
                                       
                    c.FieldName = kunde.Name;
                    //c.ColumnType = MVCxGridViewColumnType.HyperLink;
                    //var hyperlink = c.PropertiesEdit as HyperLinkProperties;
                });
            }
            //Exportsettings
            GridViewHelper.ApplyDefaultExportSettings(settings, exportType);


                //settings.SettingsExport.RenderBrick += (sender, e) =>
                //{
                //    var column = e.Column as MVCxGridViewColumn;
                //    column.PropertiesEdit.EncodeHtml = false;
                //};
                //    if (column != null && column.FieldName.StartsWith("Kommentar") && e.RowType == GridViewRowType.Data)
                //    {
                //        if (e.Value != null)
                //        {
                //            var link = e.Value as string;
                //            if (link.StartsWith("www") || link.StartsWith("http") || link.StartsWith("https"))
                //            {
                //                string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(link, link, false);
                //                e.TextValue = htmlLink;
                //            }
                //        }
                //    }


                //};

                return settings;

        }

        public ActionResult ShowUploadPopup(int commentId, string url, string field, int languageId = -1, string popupType = "Kommentar")
        {
            ViewData["commentId"] = commentId;
            ViewData["url"] = url;
            ViewData["field"] = field;
            ViewData["languageId"] = languageId;
            ViewData["popupType"] = popupType;
            return PartialView("KommentarUploadPopupContent");
        }

        public ActionResult FileUpload(int commentId, string field, int languageId)
        {
            UploadedFile[] files = UploadControlExtension.GetUploadedFiles("uploadControl");

            // Your custom logic to process uploaded files.
            if (files != null && files.Length > 0)
            {
                UploadedFile file = files[0];
                
                string filePath = ConfigurationManager.AppSettings["FilePath"];
                string resultFileUrl;
                if (filePath == "Azure")
                {
                    IBlobStorageClientFactory factory = new BlobStorageClientFactory();
                    IBlobStorageClient client = factory.CreateCommentFilesBlobStorageClient(commentId);
                    var uploadResult = client.Upload(file.FileName, file.ContentType, file.FileContent);
                    resultFileUrl = uploadResult.Url;
                }
                else
                {
                    string uploadDirectory = "~/Upload/Kommentar/"; // Directly upload the file to the specific directory
                    resultFileUrl = uploadDirectory + file.FileName;
                    string resultFilePath = Request.MapPath(resultFileUrl);

                    file.SaveAs(resultFilePath);
                    resultFileUrl = ConfigurationManager.AppSettings["FilePath"] + "Kommentar/" + file.FileName;
                }

                _unitOfWork.KommentarRepository.UpdateUploadedFileUrl(commentId, field, resultFileUrl, languageId);
            }




            return null;
        }

        public ActionResult FileDelete(int commentId, string field, int languageId, string popupType = "Kommentar")
        {
            ViewData["commentId"] = commentId;
            ViewData["field"] = field;
            ViewData["languageId"] = languageId;
            ViewData["popupType"] = popupType;

            _unitOfWork.KommentarRepository.UpdateUploadedFileUrl(commentId, field, null, languageId);

            return PartialView("KommentarUploadPopupContent");
        }

        public ActionResult HauptErlassComboBox()
        {
            MVCxColumnComboBoxProperties p = new MVCxColumnComboBoxProperties();
            p.ValueField = "ErlassID";
            p.TextField = "Titel";
            p.Columns.Add("Titel", "Titel");
            p.ValueType = typeof(int);
            p.BindList(_unitOfWork.ErlassRepository.GetErlassComboBox().OrderBy(x => x.Titel));
            return GridViewExtension.GetComboBoxCallbackResult(p);
        }

        /// <summary>
        /// Inner Class for the Gridview Settings (this avoids a separat defninition of the settings in the view and the export).
        /// </summary>
        public static class BaseGridViewSettings
        {
            static NeoSysLCS_Dev context = new NeoSysLCS_Dev();
            private static int _currentLang;

            public static string GetGridViewName()
            {
                return "KommentareGridView";
            }

            public static string GetGridViewKeyFieldName()
            {
                return "KommentarID";

            }

            /// <summary>
            /// Gets the definition of the columns
            /// </summary>
            /// <param name="viewContext">The view context.</param>
            /// <returns></returns>
            public static List<MVCxGridViewColumn> GetDataColumns(ViewContext viewContext, IQueryable<KommentarViewModel> model)
            {
                IUnitOfWork unitOfWork = new UnitOfWork();
                SessionHelper sessionHelper = new SessionHelper();

                var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
                Sprache sprache = context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
                _currentLang = sprache.SpracheID;

                var columns = new MVCxGridViewColumnCollection();
                
                columns.Add(column =>
                {
                    column.FieldName = "ProjektleiterID";
                    column.Caption = "Projektleiter";

                    column.ColumnType = MVCxGridViewColumnType.ComboBox;
                    column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                    var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

                    //get all users with role Projectmanager
                    List<ApplicationUser> users = new List<ApplicationUser>();

                    RoleManager<IdentityRole> roleMngr = sessionHelper.GetRoleManager();
                    ApplicationUserManager usrMngr = sessionHelper.GetUserManager();
                    IdentityRole projektleiterRole = roleMngr.FindByName("PROJECTMANAGER");

                    foreach (var identityUserRole in projektleiterRole.Users)
                    {
                        ApplicationUser usr = usrMngr.FindById(identityUserRole.UserId);
                        if (!usr.LockoutEndDateUtc.HasValue)
                        {
                            users.Add(usr);
                        }

                    }

                    comboBoxProperties.DataSource = users;
                    comboBoxProperties.TextField = "FullName";
                    comboBoxProperties.ValueField = "Id";
                    comboBoxProperties.ValueType = typeof(string);
                });


                columns.Add(column =>
                {
                    column.FieldName = "ProjektleiterQSID";
                    column.Caption = "ProjektleiterQS";

                    column.ColumnType = MVCxGridViewColumnType.ComboBox;
                    column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                    var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

                    //get all users with role Projectmanager
                    List<ApplicationUser> users = new List<ApplicationUser>();

                    RoleManager<IdentityRole> roleMngr = sessionHelper.GetRoleManager();
                    ApplicationUserManager usrMngr = sessionHelper.GetUserManager();
                    IdentityRole projektleiterRole = roleMngr.FindByName("PROJECTMANAGER");

                    foreach (var identityUserRole in projektleiterRole.Users)
                    {
                        ApplicationUser usr = usrMngr.FindById(identityUserRole.UserId);
                        if (!usr.LockoutEndDateUtc.HasValue)
                        {
                            users.Add(usr);
                        }

                    }

                    comboBoxProperties.DataSource = users;
                    comboBoxProperties.TextField = "FullName";
                    comboBoxProperties.ValueField = "Id";
                    comboBoxProperties.ValueType = typeof(string);
                });

                columns.Add(column =>
                {
                    column.FieldName = "Beschluss";
                    column.Caption = "Änderung vom";
                    column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    column.PropertiesEdit.DisplayFormatString = "d";
                });

                columns.Add(column =>
                {
                    column.FieldName = "Inkrafttretung";
                    column.Caption = "Inkrafttretung";
                    column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    column.PropertiesEdit.DisplayFormatString = "d";
                });


                //make a list from the MCxGridViewColumnCollection otherwise settings.Columns.Add(col) won't work --> DevExpress 16.2.15
                var columList = new List<MVCxGridViewColumn>();
                foreach (MVCxGridViewColumn column in columns)
                {
                    columList.Add(column);
                };

                return columList;

            }
        }
    }
}