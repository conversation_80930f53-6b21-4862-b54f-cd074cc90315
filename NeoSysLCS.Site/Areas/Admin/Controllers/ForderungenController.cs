using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Web.Mvc;
using System.Web.SessionState;
using System.Web.UI.WebControls;
using DevExpress.Utils;
using DevExpress.Web;
using DevExpress.Web.Mvc;
using DevExpress.XtraPrinting;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Helpers;
using NeoSysLCS.Site.Utilities.Export;

namespace NeoSysLCS.Site.Areas.Admin.Controllers
{
    [SessionState(SessionStateBehavior.ReadOnly)]
    public class ForderungenController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;

        public ForderungenController()
        {
            _unitOfWork = new UnitOfWork();
        }
        public ForderungenController(IUnitOfWork unitOfWorkCandidate)
        {
            _unitOfWork = unitOfWorkCandidate;
        }


        public ActionResult Index(int id)
        {
            var viewModels = _unitOfWork.ForderungsversionRepository.GetAllForderungViewModelsByErlassfassung(id);

            ViewData["ErlassfassungID"] = id;

            return View("Index", viewModels);
        }

        /// <summary>
        /// Shows the view for creating a new version
        /// </summary>
        /// <param name="id">The id.</param>
        /// <param name="erlassfassungId">The erlassfassung id.</param>
        /// <returns></returns>
        public ActionResult NewVersion(int id, int erlassfassungId)
        {
            var viewModel = _unitOfWork.ForderungsversionRepository.GetForderungViewModelWithAllTranslationsById(id);

            ViewData["ForderungsversionID"] = id;
            ViewData["ErlassfassungID"] = erlassfassungId;

            return View("NewVersion", viewModel);
        }

        /// <summary>
        /// Shows previous version viewmodel
        /// </summary>
        /// <param name="forderungsversionViewModel">The forderungsversionViewModel.</param>
        /// <returns></returns>
        public ActionResult _NewVersionPrevious(ForderungsversionViewModel Model, int erlassfassungId)
        {
            ViewData["ErlassfassungID"] = erlassfassungId;
            return PartialView("_NewVersionPrevious", Model);
        }

        /// <summary>
        /// Shows a new empty version 
        /// </summary>
        /// <param name="forderungsversionId">The forderungsversion id.</param>
        /// <param name="erlassfassungId">The erlassfassung id.</param>
        /// <returns></returns>
        public ActionResult _NewVersionNew(int forderungsversionId, int erlassfassungId)
        {
            // Create new view model based on previous forderungsversion
            var viewModel = _unitOfWork.ForderungsversionRepository.GetForderungViewModelWithAllTranslationsById(forderungsversionId);
            var erlassfassung = _unitOfWork.ErlassfassungRepository.GetByID(erlassfassungId);

            viewModel.ForderungsversionID = 0;
            viewModel.Inkrafttretung = erlassfassung.Inkrafttretung;
            viewModel.Aufhebung = null;
            //viewModel.InternerKommentar = "";
           
            viewModel.ErlassfassungID = erlassfassungId;

            ViewData["ForderungsversionID"] = forderungsversionId;
            ViewData["ErlassfassungID"] = erlassfassungId;

            return PartialView("_NewVersionNew", viewModel);
        }

        /// <summary>
        /// Create the new version 
        /// </summary>
        /// <param name="forderungsversion">The forderungsversion.</param>
        /// <returns></returns>
        [HttpPost]
        //[ValidateAntiForgeryToken]
        public ActionResult CreateNewVersion(ForderungsversionViewModel forderungsversion)
        {
            int previousForderungId = Convert.ToInt32(Request.Params["PreviousForderungsversionId"]);


            //update previous version aufhebungsdatum
            if (Request.Params["PreviousAufhebungDatum"] != "") { 
                ForderungsversionViewModel model = _unitOfWork.ForderungsversionRepository.GetForderungViewModelById(previousForderungId);
                model.Aufhebung = Convert.ToDateTime(Request.Params["PreviousAufhebungDatum"]);
                _unitOfWork.ForderungsversionRepository.Update(model);
            }

            var bewilligungspflicht = Request.Params["Bewilligungspflicht"];
            var nachweispflicht = Request.Params["Nachweispflicht"];
            forderungsversion.Bewilligungspflicht = Boolean.Parse(bewilligungspflicht.Split(',')[1]);
            forderungsversion.Nachweispflicht = Boolean.Parse(nachweispflicht.Split(',')[1]);
            _unitOfWork.ForderungsversionRepository.InsertToExisitingForderung(previousForderungId, forderungsversion);
            _unitOfWork.Save();

            ViewData["ErlassfassungID"] = forderungsversion.ErlassfassungID;

            return RedirectToAction("Index", new { id = forderungsversion.ErlassfassungID });
        }

      
        /// <summary>
        /// Shows all forderungen of the specified erlassfassung
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult ForderungenGridView(int id)
        {
            var viewModels = _unitOfWork.ForderungsversionRepository.GetAllForderungViewModelsByErlassfassung(id);

            ViewData["ErlassfassungID"] = id;

            return PartialView("_ForderungenGridView", viewModels);
        }

        /// <summary>
        /// Update action for the forderungen grid view
        /// </summary>
        /// <param name="updateValues">The update values.</param>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        [HttpPost, ValidateInput(false)]
        public ActionResult ForderungenGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<ForderungsversionViewModel, int> updateValues, int id)
        {
            foreach (var forderungsversion in updateValues.Insert)
            {
                GridViewUpdateHelper<Forderungsversion, ForderungsversionViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(forderungsversion))
                    {
                        forderungsversion.ErlassfassungID = id;
                        forderungsversion.InternerKommentar = StringHelper.StripHtmlTags(forderungsversion.InternerKommentar);
                        forderungsversion.Beschreibung = StringHelper.StripHtmlTags(forderungsversion.Beschreibung);
                        _unitOfWork.ForderungsversionRepository.Insert(forderungsversion);
                        _unitOfWork.Save();
                    }

                }, forderungsversion, updateValues);
            }
            foreach (var forderungsversion in updateValues.Update)
            {
                GridViewUpdateHelper<Forderungsversion, ForderungsversionViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(forderungsversion))
                    {
                        forderungsversion.InternerKommentar = StringHelper.StripHtmlTags(forderungsversion.InternerKommentar);
                        forderungsversion.Beschreibung = StringHelper.StripHtmlTags(forderungsversion.Beschreibung);
                        _unitOfWork.ForderungsversionRepository.Update(forderungsversion);
                        _unitOfWork.Save();
                    }

                }, forderungsversion, updateValues);
            }
            foreach (var forderungsverionId in updateValues.DeleteKeys)
            {
                GridViewUpdateHelper<Forderungsversion, ForderungsversionViewModel>.DoDelete(() =>
                {
                    _unitOfWork.ForderungsversionRepository.Delete(forderungsverionId);
                    _unitOfWork.Save();
                }, _unitOfWork.ForderungsversionRepository, forderungsverionId, updateValues);
            }

            ViewData["ErlassfassungID"] = id;
            var viewModels = _unitOfWork.ForderungsversionRepository.GetAllForderungViewModelsByErlassfassung(id);
            return PartialView("_ForderungenGridView", viewModels);
        }

        /// <summary>
        /// Shows all uebersetzungen of the specified forderung
        /// </summary>
        /// <param name="erlassfassungId">The erlassfassung id.</param>
        /// <param name="forderungsversionId">The forderungsversion id.</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult ForderungsversionUebersetzungenGridView(int erlassfassungId, int forderungsversionId)
        {
            var uebersetzungen = _unitOfWork.ForderungsversionRepository.GetForderungsversionUebersetzungenByForderungsversion(forderungsversionId);

            ViewData["ErlassfassungID"] = erlassfassungId;
            ViewData["ForderungsversionID"] = forderungsversionId;

            return PartialView("_ForderungUebersetzungenGridView", uebersetzungen);
        }

        /// <summary>
        /// Update action for the uebersetzungen grid view
        /// </summary>
        /// <param name="updateValues">The update values.</param>
        /// <param name="erlassfassungId">The erlassfassung identifier.</param>
        /// <param name="forderungsversionId">The forderungsversion identifier.</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult ForderungsversionUebersetzungenGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<ForderungsversionViewModel, int> updateValues, int erlassfassungId, int forderungsversionId)
        {
            //if (updateValues.Update.Any())
            //{
            //    foreach (ForderungsversionViewModel uebersetzung in updateValues.Update)
            //    {
            //        int forderungsversionsId = Convert.ToInt32(uebersetzung.ForderungsversionUebersetzungID.Split('_')[1]);
            //        ForderungsversionViewModel viewModel = _unitOfWork.ForderungsversionRepository.GetForderungViewModelById(forderungsversionsId);
            //        uebersetzung.ArtikelID = viewModel.ArtikelID;
            //    }
            //}
            foreach (var uebersetzung in updateValues.Update)
            {
                GridViewUpdateHelper<Forderungsversion, ForderungsversionViewModel>.DoUpdateInsert(() =>
                {
                    //if (updateValues.IsValid(uebersetzung))
                    //{
                        uebersetzung.ForderungsversionID = forderungsversionId;
                        //decode html before saving it into the database
                        uebersetzung.Beschreibung = WebUtility.HtmlDecode(uebersetzung.Beschreibung);
                        _unitOfWork.ForderungsversionRepository.UpdateForderungsversionUebersetzung(uebersetzung);
                        _unitOfWork.Save();
                    //}
                }, uebersetzung, updateValues);
            }

            ViewData["ErlassfassungID"] = erlassfassungId;
            ViewData["ForderungsversionID"] = forderungsversionId;

            var uebersetzungen = _unitOfWork.ForderungsversionRepository.GetForderungsversionUebersetzungenByForderungsversion(forderungsversionId);

            return PartialView("_ForderungUebersetzungenGridView", uebersetzungen);
        }

        /// <summary>
        /// Action for exporting the forderungen of the specified erlassfassung
        /// </summary>
        /// <param name="erlassfassungID">The erlassfassung id.</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult ExportTo(int erlassfassungID)
        {
            var viewModels = _unitOfWork.ForderungsversionRepository.GetAllForderungViewModelsByErlassfassung(erlassfassungID).ToList();

            //Fixed NEOS-297 Html Tags werden beim Export nicht entfernt
            foreach (var viewModel in viewModels)
            {
                viewModel.Beschreibung = WebUtility.HtmlDecode(StringHelper.StripAllTags(viewModel.Beschreibung));
                viewModel.InternerKommentar = WebUtility.HtmlDecode(StringHelper.StripAllTags(viewModel.InternerKommentar));
            }
            var exportType = ExportUtilities.GetExportType(Request.Params["exportFormat"]);
            var exportResult = ExportUtilities.Export(exportType, GetExportGridSettings(exportType, erlassfassungID), viewModels, false);

            if (exportResult != null)
            {
                return exportResult;
            }

            return RedirectToAction("Index", new { id = erlassfassungID });
        }


        /// <summary>
        /// Gets the export grid settings.
        /// </summary>
        /// <param name="exportType">Type of the export.</param>
        /// <param name="erlassfassungID">The erlassfassung identifier.</param>
        /// <returns></returns>
        private GridViewSettings GetExportGridSettings(ExportType exportType, int erlassfassungID)
        {
            var settings = new GridViewSettings();
            settings.Name = BaseGridViewSettings.GetGridViewName();
            settings.KeyFieldName = BaseGridViewSettings.GetGridViewKeyFieldName();

            foreach (MVCxGridViewColumn col in BaseGridViewSettings.GetDataColumns(erlassfassungID, false))
            {
                settings.Columns.Add(col);

            }

            //Exportsettings
            GridViewHelper.ApplyDefaultExportSettings(settings, exportType);

            settings.SettingsExport.MaxColumnWidth = 210;
            settings.SettingsExport.Landscape = true;
            if (exportType == ExportType.Pdf)
            {

                settings.SettingsExport.PageHeader.Left = "Forderungen";
                settings.SettingsExport.PageHeader.Center = Url.Action("Index", "Forderungen", new { area = "Admin" }, Request.Url.Scheme);
            }

            settings.SettingsExport.BeforeExport += (sender, args) =>
            {
                var grid = sender as MVCxGridView;
                if (grid != null)
                {
                    grid.Columns["VersionsNummer"].Visible = false;
                    grid.Columns["Assignements"].Visible = false;
                    grid.Columns["VersionPlural"].Visible = false;
                }
                
            };

            return settings;

        }

        /// <summary>
        /// Inner Class for the Gridview Settings (this avoids a separat defninition of the settings in the view and the export).
        /// </summary>
        public static class BaseGridViewSettings
        {
            public static string GetGridViewName()
            {
                return "ForderungGridView";
            }

            public static string GetGridViewKeyFieldName()
            {
                return "ForderungsversionID";

            }

            /// <summary>
            /// Get the defintion of columns
            /// </summary>
            /// <param name="erlassfassungID">The erlassfassung id.</param>
            /// <param name="editable">if set to <c>true</c> [editable].</param>
            /// <returns></returns>
            public static List<MVCxGridViewColumn> GetDataColumns(int erlassfassungID, bool editable)
            {
                IUnitOfWork unitOfWork = new UnitOfWork();
                Erlassfassung erlassfassung = unitOfWork.ErlassfassungRepository.GetByID(erlassfassungID);

                var columns = new MVCxGridViewColumnCollection();
                columns.Add(column =>
                {
                    column.FieldName = "VersionsNummer";
                    column.Name = "VersionsNummer";
                    column.Caption = Resources.Properties.Resources.Allgemein_Version_Singular;
                    column.ReadOnly = true;
                    column.EditFormSettings.Visible = DefaultBoolean.False;
                });

                if (editable)
                {
                    columns.Add(column =>
                    {
                        column.Caption = "";
                        column.Name = "NewVersion";
                        column.ReadOnly = true;
                        column.EditFormSettings.Visible = DefaultBoolean.False;
                    });
                }
                

                columns.Add(column =>
                {
                    column.Caption = Resources.Properties.Resources.Allgemein_Zuordnungen;
                    column.Name = "Assignements";
                    column.ReadOnly = true;
                    column.EditFormSettings.Visible = DefaultBoolean.False;
                });

                columns.Add(column =>
                {
                    column.FieldName = "ErlassfassungInkrafttretung";
                    column.Name = "ErlassfassungInkrafttretung";
                    column.Caption = Resources.Properties.Resources.View_Forderungen_Definition;
                    column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    column.EditFormSettings.Visible = DefaultBoolean.False;
                    column.PropertiesEdit.DisplayFormatString = "d";
                    column.ReadOnly = true;
                });

                columns.Add(column =>
                {
                    column.Caption = Resources.Properties.Resources.Allgemein_Version_Plural;
                    column.Name = "VersionPlural";
                    column.EditFormSettings.Visible = DefaultBoolean.False;
                    column.ReadOnly = true;
                });

                columns.Add(column =>
                {
                    column.FieldName = "ArtikelID";
                    column.Name = "ArtikelID";
                    column.Caption = Resources.Properties.Resources.Entitaet_Artikel_Singular;
                    column.ColumnType = MVCxGridViewColumnType.ComboBox;
                    column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                    IEnumerable<Artikel> artikel = unitOfWork.ArtikelRepository.GetByErlass(erlassfassung.ErlassID).ToList();

                    var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
                    if (comboBoxProperties != null)
                    {
                        comboBoxProperties.DataSource = artikel;
                        comboBoxProperties.TextField = "Nummer";
                        comboBoxProperties.ValueField = "ArtikelID";
                        comboBoxProperties.ValueType = typeof(int);
                    }
                });

                columns.Add(column =>
                {
                    column.FieldName = "Beschreibung";
                    column.Name = "Beschreibung";
                    column.Caption = Resources.Properties.Resources.Allgemein_Beschreibung;
                    column.Width = Unit.Percentage(50);
                    column.ExportWidth = 400;
                    
                    column.ColumnType = MVCxGridViewColumnType.Memo;
                    var memoProp = column.PropertiesEdit as MemoProperties;
                    if (memoProp != null)
                    {
                        memoProp.EncodeHtml = false;
                    }

                });

               

                var inkrafttretugnsColumn = columns.Add("Inkrafttretung", MVCxGridViewColumnType.DateEdit);
                inkrafttretugnsColumn.PropertiesEdit.DisplayFormatString = "d";

                var  aufhebungColumn = columns.Add("Aufhebung", MVCxGridViewColumnType.DateEdit);
                aufhebungColumn.PropertiesEdit.DisplayFormatString = "d";

                columns.Add(column =>
                {
                    column.Caption = Resources.Properties.Resources.Entitaet_Forderungsversion_Bewilligungspflicht_Kurz;
                    column.Width = 100;
                    column.ExportWidth = 100;
                    column.FieldName = "Bewilligungspflicht";
                    column.ColumnType = MVCxGridViewColumnType.CheckBox;

                    var prop = (column.PropertiesEdit as CheckBoxProperties);
                    if (prop != null)
                    {
                        prop.AllowGrayed = true;
                        prop.AllowGrayedByClick = false;
                        prop.ValidationSettings.Display = Display.Dynamic;
                        prop.ValidationSettings.RequiredField.IsRequired = false;
                    }
                });

                columns.Add(column =>
                {
                    column.Caption = Resources.Properties.Resources.Entitaet_Forderungsversion_Nachweispflicht_Kurz;
                    column.Width = 100;
                    column.ExportWidth = 100;
                    column.FieldName = "Nachweispflicht";
                    column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    var prop = (column.PropertiesEdit as CheckBoxProperties);
                    if (prop != null)
                    {
                        prop.AllowGrayed = true;
                        prop.AllowGrayedByClick = false;
                        prop.ValidationSettings.Display = Display.Dynamic;
                        prop.ValidationSettings.RequiredField.IsRequired = false;
                    }
                });

                columns.Add(column =>
                {
                    column.FieldName = "InternerKommentar";
                    column.Name = "InternerKommentar";
                    column.Caption = Resources.Properties.Resources.Allgemein_InternerKommentar;
                    column.Width = Unit.Percentage(25);
                    column.ExportWidth = 200;
                    
                    column.ColumnType = MVCxGridViewColumnType.Memo;
                    var memoProp = column.PropertiesEdit as MemoProperties;
                    if (memoProp != null)
                    {
                        memoProp.EncodeHtml = false;
                    }
                });

                columns.Add(column =>
                {
                    column.FieldName = "Freigabe";
                    column.Caption = "Freigabe";
                    column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    var prop = (column.PropertiesEdit as EditProperties);
                    if (prop != null)
                    {
                        prop.ValidationSettings.Display = Display.Dynamic;
                    }
                });

                columns.Add(column =>
                {
                    column.FieldName = "QsFreigabe";
                    column.Caption = "Freigabe QS";
                    column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    var prop = (column.PropertiesEdit as EditProperties);
                    if (prop != null)
                    {
                        prop.ValidationSettings.Display = Display.Dynamic;
                    }
                });

                //make a list from the MCxGridViewColumnCollection otherwise settings.Columns.Add(col) won't work --> DevExpress 16.2.15
                var columList = new List<MVCxGridViewColumn>();
                foreach (MVCxGridViewColumn column in columns)
                {
                    columList.Add(column);
                };

                return columList;
            }
        }

       
    }
}
