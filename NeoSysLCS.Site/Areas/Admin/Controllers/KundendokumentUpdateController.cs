using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using System.Web.Script.Serialization;
using log4net;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Helpers;
using NeoSysLCS.Site.Utilities;


namespace NeoSysLCS.Site.Areas.Admin.Controllers
{

    public class KundendokumentUpdateController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;

        #region Constructors

        public KundendokumentUpdateController()
        {
            _unitOfWork = new UnitOfWork();
        }

        public KundendokumentUpdateController(IUnitOfWork unitOfWorkCandidate)
        {
            _unitOfWork = unitOfWorkCandidate;
        }

        #endregion

        #region Actions

        /// <summary>
        /// Edits an existing Kundendokument
        /// </summary>
        /// <param name="kundendokumentId"></param>
        /// <returns></returns>
        public ActionResult KundendokumentEdit(int kundendokumentId)
        {
            var kundendokument = _unitOfWork.KundendokumentRepository.GetByID(kundendokumentId, "KundendokumentForderungen,KundendokumentPflichten,KundendokumentErlassfassungen,");

            var forderungenIDs = new HashSet<string>();
            var forderungKundenbezugsItems = new List<KundendokumentData.KundenbezugItem>();
            foreach (var kundendokumentForderungsversion in kundendokument.KundendokumentForderungen)
            {
                if (kundendokumentForderungsversion.Relevant)
                {
                    forderungenIDs.Add(
                        ForderungsversionRepository.GenerateStandortObjektForderungsversionID(kundendokumentForderungsversion.StandortObjektID,
                            kundendokumentForderungsversion.ForderungsversionID));
                    forderungKundenbezugsItems.Add(new KundendokumentData.KundenbezugItem()
                    {
                        Key = kundendokumentForderungsversion.StandortObjektID + "_" + kundendokumentForderungsversion.ForderungsversionID,
                        Value = kundendokumentForderungsversion.Kundenbezug
                    });
                }
            }

            var pflichtenIDs = new HashSet<int>();
            var pflichtKundenbezugsItems = new List<KundendokumentData.KundenbezugItem>();
            foreach (var kundendokumentPflicht in kundendokument.KundendokumentPflichten)
            {
                if (kundendokumentPflicht.Relevant)
                {
                    pflichtenIDs.Add(kundendokumentPflicht.PflichtID);
                    pflichtKundenbezugsItems.Add(new KundendokumentData.KundenbezugItem()
                    {
                        Key = kundendokumentPflicht.StandortObjektID + "_" + kundendokumentPflicht.PflichtID,
                        Value = kundendokumentPflicht.Kundenbezug
                    });
                }
            }

            var erlassfassungenIDs = new HashSet<int>();
            foreach (var kundendokumentErlassfassungen in kundendokument.KundendokumentErlassfassungen)
            {
                if (kundendokumentErlassfassungen.Betroffen)
                {
                    erlassfassungenIDs.Add(kundendokumentErlassfassungen.ErlassfassungID);
                }
            }

            SessionHelper.SetKundendokumentData(new KundendokumentData()
            {
                StandortID = kundendokument.StandortID,
                KundendokumentID = kundendokumentId,
                SelectedErlasssfassungenIDs = erlassfassungenIDs,
                SelectedForderungenIDs = forderungenIDs,
                SelectedPflichtenIDs = pflichtenIDs,
                ForderungKundenbezuege = forderungKundenbezugsItems,
                PflichtKundenbezuege = pflichtKundenbezugsItems,
                VorgaengerKundendokumentID = kundendokument.VorgaengerKundendokumentID
            });

            return RedirectToAction("SelectForderungen", new { standortId = kundendokument.StandortID, kundendokumentId = kundendokument.KundendokumentID });
        }

        /// <summary>
        /// Creates an new Kundendokument update for an existing Kundendokument
        /// </summary>
        /// <param name="standortId">Current Standort</param>
        /// <returns></returns>
        public ActionResult KundendokumentCreateUpdate(int standortId)
        {
            var kundendokument = _unitOfWork.KundendokumentRepository.GetCurrentKundendokumentViewModel(standortId);
            SessionHelper.SetKundendokumentData(new KundendokumentData()
            {
                StandortID = kundendokument.StandortID,
                VorgaengerKundendokumentID = kundendokument.KundendokumentID
            });

            return RedirectToAction("SelectForderungen", new { standortId });
        }

        /// <summary>
        /// Creates a initial Version of the kundendokument
        /// </summary>
        /// <param name="standortId"></param>
        /// <returns></returns>
        public ActionResult KundendokumentCreateInitialVersion(int standortId)
        {
            SessionHelper.SetKundendokumentData(new KundendokumentData()
            {
                StandortID = standortId,
            });

            return RedirectToAction("SelectForderungen", new { standortId });
        }

        #region Forderungen

        /// <summary>
        /// Step 1: Select the relevant Forderungen
        /// </summary>
        /// <param name="standortId">Current Standort</param>
        /// <param name="forderungKundenbezuege">Kundenbezuege from Step 2</param>
        /// <returns></returns>
        public ActionResult SelectForderungen(int standortId, string forderungKundenbezuege = null)
        {
            try
            {
                ViewData["StandortID"] = standortId;
                var kundendokumentdata = SessionHelper.GetKundendokumentData(standortId);
                if (forderungKundenbezuege != null)
                {
                    //comming back from StandortobjektForderung: Save Kundenbezuege
                    kundendokumentdata.ForderungKundenbezuege = new JavaScriptSerializer().Deserialize<IList<KundendokumentData.KundenbezugItem>>(forderungKundenbezuege) ??
                                                                new List<KundendokumentData.KundenbezugItem>();
                }
                return View("SelectForderungen", GetForderungenToSelectViewModels(kundendokumentdata));
            }
            catch(Exception e)
            {
                LogManager.GetLogger(typeof(KundendokumentUpdateController)).Error(e.Message, e);
                return RedirectToAction("Index", "Kundendokumente", new { id = standortId });
            }
        }

        /// <summary>
        /// Callback form Button, Save Kundendokumentdata
        /// </summary>
        /// <param name="standortId">Current Standort</param>
        /// <param name="selectedForderungenIDs">selected Forderungen</param>
        /// <returns></returns>
        public void CallbackPanelPartialForderungen(int standortId, string selectedForderungenIDs = null)
        {
            try
            {
                ViewData["StandortID"] = standortId;
                var kundendokumentdata = SessionHelper.GetKundendokumentData(standortId);

                if (selectedForderungenIDs != null)
                {
                    //callback form grid: save selection
                    kundendokumentdata.SelectedForderungenIDs = CsvHelper.ExtractCombinedIdsFromCsvString(selectedForderungenIDs);
                }

            }
            catch (Exception e)
            {
                LogManager.GetLogger(typeof(KundendokumentUpdateController)).Error(e.Message, e);
            }
        }

        /// <summary>
        /// Callback form Button, Save Kundendokumentdata
        /// </summary>
        /// <param name="standortId">Current Standort</param>
        /// <param name="selectedErlasssfassungenIDs">selected erlassfassungen</param>
        /// <returns></returns>
        public void CallbackPanelPartialErlassfassungen(int standortId, string selectedErlasssfassungenIDs = null)
        {
            try
            {
                ViewData["StandortID"] = standortId;
                var kundendokumentdata = SessionHelper.GetKundendokumentData(standortId);

                if (selectedErlasssfassungenIDs != null)
                {
                    //callback form grid: save selection
                    kundendokumentdata.SelectedErlasssfassungenIDs =
                        CsvHelper.ExtractIdsFromCsvString(selectedErlasssfassungenIDs);
                }
            }
            catch (Exception e)
            {
                LogManager.GetLogger(typeof(KundendokumentUpdateController)).Error(e.Message, e);
            }
        }

        /// <summary>
        /// Callback form gridview
        /// </summary>
        /// <param name="standortId">Current Standort</param>
        /// <param name="selectedForderungenIDs">selected Forderungen</param>
        /// <returns></returns>
        public ActionResult _SelectForderungenGridView(int standortId, string selectedForderungenIDs = null)
        {
            try
            {
                ViewData["StandortID"] = standortId;
                var kundendokumentdata = SessionHelper.GetKundendokumentData(standortId);
                if (selectedForderungenIDs != null)
                {
                    //callback form grid: save selection
                    kundendokumentdata.SelectedForderungenIDs = CsvHelper.ExtractCombinedIdsFromCsvString(selectedForderungenIDs);
                }
            
                return PartialView("_SelectForderungenGridView", GetForderungenToSelectViewModels(kundendokumentdata));
            }
            catch (Exception e)
            {
                LogManager.GetLogger(typeof(KundendokumentUpdateController)).Error(e.Message, e);
                return RedirectToAction("Index", "Kundendokumente", new { id = standortId });
            }
        }

        /// <summary>
        /// Step 2: Overview over all Forderungen per StandortObjekt, administration of Kundenbezuege
        /// </summary>
        /// <param name="standortId">Current Standort</param>
        /// <param name="selectedForderungenIDs">Selected Forderungen form Step 1</param>
        /// <param name="selectedPflichtenIDs">Selected Forderungen form Step 3</param>
        /// <returns></returns>
        public ActionResult StandortobjektForderungen(int standortId, string selectedForderungenIDs = null, string selectedPflichtenIDs = null)
        {
            try
            {
                ViewData["StandortID"] = standortId;
                var kundendokumentdata = SessionHelper.GetKundendokumentData(standortId);
                ViewData["forderungKundenbezuege"] = new JavaScriptSerializer().Serialize(kundendokumentdata.ForderungKundenbezuege);

                if (selectedForderungenIDs != null)
                {
                    //comming from SelectForderungen: Save selection
                    kundendokumentdata.SelectedForderungenIDs = CsvHelper.ExtractCombinedIdsFromCsvString(selectedForderungenIDs);
                }
                else if (selectedPflichtenIDs != null)
                {
                    //comming back from PflichtSelection: Save selection
                    kundendokumentdata.SelectedPflichtenIDs = CsvHelper.ExtractIdsFromCsvString(selectedPflichtenIDs);
                }

                return View("StandortobjektForderungen", GetSelectedStandortForderungViewModels(kundendokumentdata));
            }
            catch (Exception e)
            {
                LogManager.GetLogger(typeof(KundendokumentUpdateController)).Error(e.Message, e);
                return RedirectToAction("Index", "Kundendokumente", new { id = standortId });
            }

            
        }

        /// <summary>
        /// Callback form gridview
        /// </summary>
        /// <param name="standortId">Current Standort</param>
        /// <param name="forderungKundenbezuege">Kundenbezuege</param>
        /// <returns></returns>
        public ActionResult _StandortobjektForderungenGridView(int standortId, string forderungKundenbezuege = null)
        {
            try
            {
                ViewData["StandortID"] = standortId;
                var kundendokumentdata = SessionHelper.GetKundendokumentData(standortId);

                if (forderungKundenbezuege != null)
                {
                    //callback form grid: save kundenbezuege
                    kundendokumentdata.ForderungKundenbezuege = new JavaScriptSerializer().Deserialize<IList<KundendokumentData.KundenbezugItem>>(forderungKundenbezuege) ??
                                                                new List<KundendokumentData.KundenbezugItem>();
                }

                return PartialView("_StandortobjektForderungenGridView", GetSelectedStandortForderungViewModels(kundendokumentdata));
            }
            catch (Exception e)
            {
                LogManager.GetLogger(typeof(KundendokumentUpdateController)).Error(e.Message, e);
                return RedirectToAction("Index", "Kundendokumente", new { id = standortId});
            }
        }

        #endregion

        #region Pflichten
        //Die Pflichten werden im Moment nich genutzt --> Schritt wird übersprungen //Patrick Schmed, 26.06.2017

        ///// <summary>
        ///// Step 3: Select the relevant Pflichten
        ///// </summary>
        ///// <param name="standortId">Current Standort</param>
        ///// <param name="forderungKundenbezuege"></param>
        ///// <param name="pflichtKundenbezuege"></param>
        ///// <returns></returns>
        //public ActionResult SelectPflichten(int standortId, string forderungKundenbezuege = null, string pflichtKundenbezuege = null)
        //{
        //    ViewData["StandortID"] = standortId;
        //    var kundendokumentdata = SessionHelper.GetKundendokumentData(standortId);
        //    if (kundendokumentdata == null)
        //    {
        //        return RedirectToAction("Index", "Kundendokumente", new { id = standortId });
        //    }

        //    if (forderungKundenbezuege != null)
        //    {
        //        //comming from StandortobjektForderung: save Kundenbezuege
        //        kundendokumentdata.ForderungKundenbezuege = new JavaScriptSerializer().Deserialize<IList<KundendokumentData.KundenbezugItem>>(forderungKundenbezuege) ??
        //                                                    new List<KundendokumentData.KundenbezugItem>();
        //    }
        //    else if (pflichtKundenbezuege != null)
        //    {
        //        //comming back from StandortobjektPflichten: save Kundenbezuege
        //        kundendokumentdata.PflichtKundenbezuege = new JavaScriptSerializer().Deserialize<IList<KundendokumentData.KundenbezugItem>>(pflichtKundenbezuege) ??
        //                                                    new List<KundendokumentData.KundenbezugItem>();
        //    }

        //    return View("SelectPflichten", GetPflichtenToSelectViewModels(kundendokumentdata));
        //}

        ///// <summary>
        ///// Callback form gridview
        ///// </summary>
        ///// <param name="standortId">Current Standort</param>
        ///// <param name="selectedPflichtenIDs">Selected Pflichten</param>
        ///// <returns></returns>
        //public ActionResult _SelectPflichtenGridView(int standortId, string selectedPflichtenIDs = null)
        //{
        //    ViewData["StandortID"] = standortId;
        //    var kundendokumentdata = SessionHelper.GetKundendokumentData(standortId);
        //    if (kundendokumentdata == null)
        //    {
        //        return RedirectToAction("Index", "Kundendokumente", new { id = standortId });
        //    }


        //    if (selectedPflichtenIDs != null)
        //    {
        //        kundendokumentdata.SelectedPflichtenIDs = CsvHelper.ExtractIdsFromCsvString(selectedPflichtenIDs);
        //    }
        //    return PartialView("_SelectPflichtenGridView", GetPflichtenToSelectViewModels(kundendokumentdata));
        //}

        ///// <summary>
        ///// Step 4: Overview over all Pflichten per StandortObjekt, administration of Kundenbezuege
        ///// </summary>
        ///// <param name="standortId">Current Standort</param>
        ///// <param name="selectedPflichtenIDs">Selected Pflichten form Step 3</param>
        ///// <param name="selectedErlasssfassungenIDs">Selected Erlassfassungen form Step 4</param>
        ///// <returns></returns>
        //public ActionResult StandortobjektPflichten(int standortId, string selectedPflichtenIDs = null, string selectedErlasssfassungenIDs = null)
        //{
        //    ViewData["StandortID"] = standortId;
        //    var kundendokumentdata = SessionHelper.GetKundendokumentData(standortId);
        //    if (kundendokumentdata == null)
        //    {
        //        return RedirectToAction("Index", "Kundendokumente", new { id = standortId });
        //    }

        //    ViewData["pflichtKundenbezuege"] =
        //      new JavaScriptSerializer().Serialize(kundendokumentdata.PflichtKundenbezuege);

        //    if (selectedPflichtenIDs != null)
        //    {
        //        //comming form PflichtSelection: Save selection
        //        kundendokumentdata.SelectedPflichtenIDs = CsvHelper.ExtractIdsFromCsvString(selectedPflichtenIDs);
        //    }
        //    else if (selectedErlasssfassungenIDs != null)
        //    {
        //        //comming back from ErlassSelection: save selection
        //        kundendokumentdata.SelectedErlasssfassungenIDs = CsvHelper.ExtractIdsFromCsvString(selectedErlasssfassungenIDs);
        //    }

        //    return View("StandortobjektPflichten", GetSelectedStandortPflichtViewModels(kundendokumentdata));
        //}

        ///// <summary>
        ///// Callback form gridview
        ///// </summary>
        ///// <param name="standortId">Curren Standort</param>
        ///// <param name="pflichtKundenbezuege">Kundenbezuege</param>
        ///// <returns></returns>
        //public ActionResult _StandortobjektPflichtenGridView(int standortId, string pflichtKundenbezuege = null)
        //{
        //    ViewData["StandortID"] = standortId;
        //    var kundendokumentdata = SessionHelper.GetKundendokumentData(standortId);
        //    if (kundendokumentdata == null)
        //    {
        //        return RedirectToAction("Index", "Kundendokumente", new { id = standortId });
        //    }

        //    if (pflichtKundenbezuege != null)
        //    {
        //        //callback form grid: save selection
        //        kundendokumentdata.PflichtKundenbezuege = new JavaScriptSerializer().Deserialize<IList<KundendokumentData.KundenbezugItem>>(pflichtKundenbezuege) ??
        //                                                    new List<KundendokumentData.KundenbezugItem>();
        //    }

        //    return PartialView("_StandortobjektPflichtenGridView", GetSelectedStandortPflichtViewModels(kundendokumentdata));
        //}

        #endregion

        #region Erlassfassungen

        /// <summary>
        /// Step 5: Select the Erlassfassungen
        /// </summary>
        /// <param name="standortId">Current Standort</param>
        /// <param name="pflichtKundenbezuege">Kundenbezuege from Step 4</param>
        /// <returns></returns>
        public ActionResult SelectErlassfassungen(int standortId, string pflichtKundenbezuege = null)
        {
            try
            {
                ViewData["StandortID"] = standortId;
                var kundendokumentdata = SessionHelper.GetKundendokumentData(standortId);

            //Die Pflichten werden im Moment nich genutzt-- > Schritt wird übersprungen //Patrick Schmed, 26.06.2017
            //if (pflichtKundenbezuege != null)
            //{
            //    //comming from StandortobjektForderung: save Kundenbezuege
            //    kundendokumentdata.PflichtKundenbezuege = new JavaScriptSerializer().Deserialize<IList<KundendokumentData.KundenbezugItem>>(pflichtKundenbezuege) ??
            //                                                new List<KundendokumentData.KundenbezugItem>();
            //}

                return View("SelectErlassfassungen", GetSelectedErlassfassungViewModels(kundendokumentdata));
            }
            catch (Exception e)
            {
                LogManager.GetLogger(typeof(KundendokumentUpdateController)).Error(e.Message, e);
                return RedirectToAction("Index", "Kundendokumente", new { id = standortId});
            }
        }

        /// <summary>
        /// Callback form gridview
        /// </summary>
        /// <param name="standortId">CurrentStandort</param>
        /// <param name="selectedErlasssfassungenIDs">Selected Erlassfassungen</param>
        /// <returns></returns>
        public ActionResult _SelectErlassfassungenGridView(int standortId, string selectedErlasssfassungenIDs = null)
        {
            try
            {
                ViewData["StandortID"] = standortId;
                var kundendokumentdata = SessionHelper.GetKundendokumentData(standortId);

                if (selectedErlasssfassungenIDs != null)
                {
                    //callback form grid: save selection
                    kundendokumentdata.SelectedErlasssfassungenIDs = CsvHelper.ExtractIdsFromCsvString(selectedErlasssfassungenIDs);
                }

                return PartialView("_SelectErlassfassungenGridView", GetSelectedErlassfassungViewModels(kundendokumentdata));
            }
            catch (Exception e)
            {
                LogManager.GetLogger(typeof(KundendokumentUpdateController)).Error(e.Message, e);
                return RedirectToAction("Index", "Kundendokumente", new { id = standortId });
            }
        }

        #endregion

        #region Kundendokument

        /// <summary>
        /// Step 6: Create new Kundendokument
        /// </summary>
        /// <param name="standortId"></param>
        /// <param name="selectedErlasssfassungenIDs"></param>
        /// <returns></returns>
        public ActionResult Create(int standortId, string selectedErlasssfassungenIDs = null)
        {
            var kundendokumentdata = SessionHelper.GetKundendokumentData(standortId);
            if (kundendokumentdata == null)
            {
                return RedirectToAction("Index", "Kundendokumente", new { id = standortId });
            }

            if (selectedErlasssfassungenIDs != null)
            {
                //comming back from ErlassSelection: save selection
                kundendokumentdata.SelectedErlasssfassungenIDs = CsvHelper.ExtractIdsFromCsvString(selectedErlasssfassungenIDs);
            }

            var kundendokument = new Kundendokument
            {
                StandortID = standortId,
                Status = KundendokumentStatus.InQs,
                VorgaengerKundendokumentID = kundendokumentdata.VorgaengerKundendokumentID
            };

            GenerateAndAttachChildElementsToKundendokument(kundendokument, kundendokumentdata);

            if (kundendokumentdata.VorgaengerKundendokumentID.HasValue)
            {
                SetStateOfKundendokumentForderungen(kundendokumentdata.VorgaengerKundendokumentID.Value, kundendokument);
                //Die Pflichten werden im Moment nich genutzt-- > Schritt wird übersprungen //Patrick Schmed, 26.06.2017
                //SetStateOfKundendokumentPflichten(kundendokumentdata.VorgaengerKundendokumentID.Value, kundendokument);
                SetStateOfKundendokumentErlassfassungen(kundendokumentdata.VorgaengerKundendokumentID.Value, kundendokument);
                SetStateOfKundendokumentChecklist(kundendokumentdata.VorgaengerKundendokumentID.Value, kundendokument);
                AddRemovedForderungsversionen(kundendokumentdata.VorgaengerKundendokumentID.Value, kundendokument);
                SetSpaltenLabels(kundendokumentdata.VorgaengerKundendokumentID.Value, kundendokument);


                // NEOS-491: Add indivuduelle forderungen to new kundendokument
                List<IndividuelleForderungViewModel> individuelleForderungenViewModels = _unitOfWork.IndividuelleForderungRepository.GetAllIndividuelleForderungenViewModels(kundendokumentdata.VorgaengerKundendokumentID.Value, 0, 0).ToList();

                foreach (IndividuelleForderungViewModel individuelleForderungViewModel in individuelleForderungenViewModels)
                {
                    IndividuelleForderungViewModel newIndividuelleForderungViewModel = new IndividuelleForderungViewModel();
                    newIndividuelleForderungViewModel.GueltigBis = individuelleForderungViewModel.GueltigBis;
                    newIndividuelleForderungViewModel.GueltigVon = individuelleForderungViewModel.GueltigVon;
                    newIndividuelleForderungViewModel.Beschreibung = individuelleForderungViewModel.Beschreibung;
                    newIndividuelleForderungViewModel.Kommentar = individuelleForderungViewModel.Kommentar;
                    newIndividuelleForderungViewModel.Kundenbezug = individuelleForderungViewModel.Kundenbezug;
                    newIndividuelleForderungViewModel.StandortID = individuelleForderungViewModel.StandortID;
                    newIndividuelleForderungViewModel.StandortObjekt = individuelleForderungViewModel.StandortObjekt;
                    newIndividuelleForderungViewModel.Erfuellung = individuelleForderungViewModel.Erfuellung;
                    newIndividuelleForderungViewModel.Erfuellungszeitpunkt = individuelleForderungViewModel.Erfuellungszeitpunkt;
                    newIndividuelleForderungViewModel.ErfuelltDurch = individuelleForderungViewModel.ErfuelltDurch;
                    newIndividuelleForderungViewModel.Ablageort = individuelleForderungViewModel.Ablageort;
                    newIndividuelleForderungViewModel.Verantwortlich = individuelleForderungViewModel.Verantwortlich;

                    _unitOfWork.IndividuelleForderungRepository.Insert(newIndividuelleForderungViewModel, kundendokument.KundendokumentID);


                }

            }

            try
            {
                _unitOfWork.KundendokumentRepository.Insert(kundendokument);
                _unitOfWork.Save();
            }
            catch (Exception e)
            {
                ExceptionUtility.LogException(e, "KundendokumentUpdate");
                return RedirectToAction("Index", "Kundendokumente", new { area = "Admin", id = standortId, createKundendokumentError = e.Message });
            }

            return RedirectToAction("Index", "Kundendokumente", new { area = "Admin", id = standortId });
        }

        /// <summary>
        /// Sets the existing spaltenlabels
        /// </summary>
        /// <param name="KundendokumentID"></param>
        /// <param name="kundendokument"></param>
        /// <returns></returns>

        private void SetSpaltenLabels(int KundendokumentID, Kundendokument kundendokument)
        {
            var columns = _unitOfWork.KundendokumentRepository.GetByID(KundendokumentID, "KundendokumentSpaltenlabel").KundendokumentSpaltenlabel;

            if (columns != null)
            {
                kundendokument.KundendokumentSpaltenlabel = new KundendokumentSpaltenlabel()
                {
                    LabelSpalte1 = columns.LabelSpalte1,
                    LabelSpalte2 = columns.LabelSpalte2,
                    LabelSpalte3 = columns.LabelSpalte3,
                    LabelSpalte4 = columns.LabelSpalte4,
                    LabelSpalte5 = columns.LabelSpalte5,
                    LabelSpalte6 = columns.LabelSpalte6,
                    LabelSpalte7 = columns.LabelSpalte7,
                    LabelSpalte8 = columns.LabelSpalte8,
                    LabelSpalte9 = columns.LabelSpalte9,
                    LabelSpalte10 = columns.LabelSpalte10
                };
            }

        }

        /// <summary>
        /// Step 6: Update existing Kundendokument
        /// </summary>
        /// <param name="standortId"></param>
        /// <param name="selectedErlasssfassungenIDs"></param>
        /// <returns></returns>
        public ActionResult Update(int standortId, string selectedErlasssfassungenIDs = null)
        {
            var kundendokumentdata = SessionHelper.GetKundendokumentData(standortId);
            if (kundendokumentdata == null)
            {
                return RedirectToAction("Index", "Kundendokumente", new { id = standortId });
            }

            if (selectedErlasssfassungenIDs != null)
            {
                //comming back from ErlassSelection: save selection
                kundendokumentdata.SelectedErlasssfassungenIDs = CsvHelper.ExtractIdsFromCsvString(selectedErlasssfassungenIDs);
            }

            if (kundendokumentdata.KundendokumentID.HasValue)
            {
                var kundendokument = _unitOfWork.KundendokumentRepository.GetByID(kundendokumentdata.KundendokumentID.Value, "KundendokumentForderungen,KundendokumentPflichten,KundendokumentErlassfassungen,KundendokumentChecklists");

                KundendokumentManager.DeleteChildElementsOfKundendokument(_unitOfWork, kundendokument);

                GenerateAndAttachChildElementsToKundendokument(kundendokument, kundendokumentdata);

                if (kundendokumentdata.VorgaengerKundendokumentID.HasValue)
                {
                    SetStateOfKundendokumentForderungen(kundendokumentdata.VorgaengerKundendokumentID.Value, kundendokument);
                    //Die Pflichten werden im Moment nich genutzt-- > Schritt wird übersprungen //Patrick Schmed, 26.06.2017
                    //SetStateOfKundendokumentPflichten(kundendokumentdata.VorgaengerKundendokumentID.Value, kundendokument);
                    SetStateOfKundendokumentErlassfassungen(kundendokumentdata.VorgaengerKundendokumentID.Value, kundendokument);
                    SetStateOfKundendokumentChecklist(kundendokumentdata.VorgaengerKundendokumentID.Value, kundendokument);
                    AddRemovedForderungsversionen(kundendokumentdata.VorgaengerKundendokumentID.Value, kundendokument);
                }

                _unitOfWork.KundendokumentRepository.Update(kundendokument);
                _unitOfWork.Save();
            }

            return RedirectToAction("Index", "Kundendokumente", new { area = "Admin", id = standortId });
        }

        #endregion

        #endregion

        #region Helpers

        #region Forderungen Helpers

        private IQueryable<KundendokumentForderungsversionViewModel> GetForderungenToSelectViewModels(KundendokumentData data)
        {
            if (data.VorgaengerKundendokumentID.HasValue)
            {
                var list = _unitOfWork.ForderungsversionRepository.GetAllFeasibleForderungViewModelsForStandort(data.StandortID, data.KundendokumentID).ToList();
                //set state of forderungen compared to the previous version of the Kundendokument
                SetStateOfForderungsversionenViewModel(
                    _unitOfWork.KundendokumentForderungsversionRepository.GetKundendokumentForderungsversionenByKundendokument(data.VorgaengerKundendokumentID),
                    list
                );
                return list.AsQueryable();
            }


            var viewModels =
                _unitOfWork.ForderungsversionRepository.GetAllFeasibleForderungViewModelsForStandort(data.StandortID,
                    data.KundendokumentID).ToList();

            return viewModels.AsQueryable();
        }

        private IQueryable<KundendokumentForderungsversionViewModel> GetSelectedStandortForderungViewModels(KundendokumentData data)
        {

            List<KundendokumentForderungsversionViewModel> viewModels = _unitOfWork.ForderungsversionRepository.
                GetSelectedStandortObjektForderungsversionViewModelsByStandort(data.StandortID, data.SelectedForderungenIDs, data.KundendokumentID).ToList();            
            SetViewModelKundenbezuege(data.ForderungKundenbezuege, viewModels);

            return viewModels.AsQueryable();
        }

        /// <summary>
        /// Compares the Forderungsversionen of the current and previous Kundendokument and sets the Status accordingly:
        /// - New: Neither Forderung nor Forderungsversion were in the previous Kundendokument
        /// - New version: The Forderungsversion was not part of the previous Kundendokument but the Forderung was
        /// - Unchanged: The Forderungsversion was in the previous Kundendokument AND relevant
        /// - To Examine: The Forderungsversion was in the previous Kundendokument but NOT marked as relevant
        /// </summary>
        /// <param name="forderungsversionenOfPreviousKundendokument"></param>
        /// <param name="viewModels"></param>
        internal void SetStateOfForderungsversionenViewModel(IEnumerable<KundendokumentForderungsversion> forderungsversionenOfPreviousKundendokument,
            IEnumerable<KundendokumentForderungsversionViewModel> viewModels)
        {
            //todo optimise query here, tolist?
            //generates a lookup-hashset out of the ids of forderungsversion per standortobjekt of the previous kundendokument 
            var previousForderungsversionenIdIndex =
                new HashSet<string>(
                    forderungsversionenOfPreviousKundendokument.Select(
                        f => ForderungsversionRepository.GenerateStandortObjektForderungsversionID(f.StandortObjektID,f.ForderungsversionID)));
            //generates a lookup-hashset out of the ids of the foderungen per standortobjekt used of the previous kundendokument 
            var previousForderungIdIndex =
                new HashSet<string>(forderungsversionenOfPreviousKundendokument.Select(
                    f => ForderungsversionRepository.GenerateStandortObjektForderungsversionID(f.StandortObjektID, f.Forderungsversion.ForderungID)));

            //generates a lookuptable containing the forderungsversionen per forderung, in order to lookup other versions of the current forderung
            IDictionary<int, List<KundendokumentForderungsversionViewModel>> forderung2ForderungsversionenMap =
                CreateForderung2ForderungsversionenLookup(viewModels);

            // set the state of the kundendokumentforderungsversionen
            foreach (var viewModel in viewModels)
            {
               
                //check if the forderungsversion has already been in the previous kundendokument
                if (previousForderungsversionenIdIndex.Contains(
                    ForderungsversionRepository.GenerateStandortObjektForderungsversionID(viewModel.StandortObjektID, viewModel.ForderungsversionID))
                ){
                    
                    //check  if there is any relevant kundendokument forderungsversion in the previous kundendokument
                    viewModel.Relevant =
                        forderungsversionenOfPreviousKundendokument.Any(
                            e => e.ForderungsversionID == viewModel.ForderungsversionID && e.StandortObjektID == viewModel.StandortObjektID && e.Relevant);

                    if (IsForderungsversionReplacedWithANewVersion(viewModel, forderung2ForderungsversionenMap, forderungsversionenOfPreviousKundendokument))
                    {
                        //forderungsversion has been replaced by an other version
                        viewModel.Status = KundendokumentItemStatus.OldVersion;
                    }
                    else if (viewModel.Relevant)
                    {
                        //the forderungsversion was relevant in the last kundendokument -> it is unchanged
                        viewModel.Status = KundendokumentItemStatus.Unchanged;
                    }
                    else
                    {
                        //the forderungsversion was not relevent in the last kundendokument -> it needs to be examined again.
                        viewModel.Status = KundendokumentItemStatus.ToExamine;

                    }
                }
                else if (previousForderungIdIndex.Contains(
                    ForderungsversionRepository.GenerateStandortObjektForderungsversionID(viewModel.StandortObjektID, viewModel.ForderungID))
                ){
                    //check  if there is any relevant kundendokument forderungsversion in the previous kundendokument
                    viewModel.Relevant =
                        forderungsversionenOfPreviousKundendokument.Any(
                            e => e.Forderungsversion.ForderungID == viewModel.ForderungID && e.StandortObjektID == viewModel.StandortObjektID && e.Relevant);

                    //there was an older version of the current forderung in the previous kundendokument -> this is new version
                    viewModel.Status = KundendokumentItemStatus.NewVersion;
                }
                else
                {
                    //No version of the forderung has been in the kundendokument
                    viewModel.Status = KundendokumentItemStatus.New;
                }
            }
        }

        /// <summary>
        /// Returns a dictionary that maps kundendokumentforderungsversionen to the forderungsId
        /// </summary>
        /// <param name="kundendokumentforderungsversionen">The forderungsversionen of previous kundendokument.</param>
        /// <returns></returns>
        private IDictionary<int, List<KundendokumentForderungsversionViewModel>> CreateForderung2ForderungsversionenLookup(
            IEnumerable<KundendokumentForderungsversionViewModel> kundendokumentforderungsversionen
        ){
            var map = new Dictionary<int, List<KundendokumentForderungsversionViewModel>>();
            foreach (var kundendokumentForderungsversion in kundendokumentforderungsversionen)
            {
                var forderungsId = kundendokumentForderungsversion.ForderungID;
                if (map.ContainsKey(forderungsId))
                {
                    map[forderungsId].Add(kundendokumentForderungsversion);
                }
                else
                {
                    var list = new List<KundendokumentForderungsversionViewModel>();
                    list.Add(kundendokumentForderungsversion);
                    map.Add(forderungsId, list);
                }
            }

            return map;
        }

        /// <summary>
        /// Sets the view model kundenbezuege.
        /// </summary>
        /// <param name="kundenbezuege">The kundenbezuege.</param>
        /// <param name="viewModels">The view models.</param>
        private void SetViewModelKundenbezuege(IEnumerable<KundendokumentData.KundenbezugItem> kundenbezuege,
            IEnumerable<KundendokumentForderungsversionViewModel> viewModels)
        {

            foreach (var item in kundenbezuege)
            {
                var vm = viewModels.FirstOrDefault(e => e.ID == item.Key);
                if (vm != null)
                {
                    vm.Kundenbezug = item.Value;
                }
            }
        }

        /// <summary>
        /// Adds the removed forderungsversionen that are not proposed anymore and marks them as removed
        /// </summary>
        /// <param name="previousKundendokumentId">The previous kundendokument identifier.</param>
        /// <param name="kundendokument">The kundendokument.</param>
        private void AddRemovedForderungsversionen(int previousKundendokumentId, Kundendokument kundendokument)
        {
            //previous forderungsversionen (relevant and not relevant)
            var forderungsversionenOfPreviousKundendokument =
                 _unitOfWork.KundendokumentForderungsversionRepository.GetKundendokumentForderungsversionenByKundendokument(
                     previousKundendokumentId);

            //current forderugnsversionen (relevant and not relevant)
            ISet<int> currentForderungsversionIdIndex = new HashSet<int>(kundendokument.KundendokumentForderungen.ToList()
                .Select(f => f.ForderungsversionID));

            //load ids of all forderungen to matched forderungsversionen
            var currentForderungIdIndex = _unitOfWork.KundendokumentForderungsversionRepository.GetIDs(currentForderungsversionIdIndex);
            //var currentForderungIdIndex = new HashSet<int>(currentKundendokumentForderungsversionViewModels.Select(f => f.ForderungID));

            foreach (var previousKundendokumentForderung in forderungsversionenOfPreviousKundendokument)
            {
                // if forderungsversion was part of old kundendokument and no new version (same forderungId) has been added. 
                //The forderung has been removed ( e.g. standortobjekt deleted-> the forderungen for this object were not proposed to selected anymore ) 
                if (!currentForderungsversionIdIndex.Contains(previousKundendokumentForderung.ForderungsversionID) && 
                    !currentForderungIdIndex.Contains(previousKundendokumentForderung.Forderungsversion.ForderungID) && previousKundendokumentForderung.Relevant
                ){
                    var kundendokumentForderungsversion = new KundendokumentForderungsversion();
                    kundendokumentForderungsversion.ForderungsversionID = previousKundendokumentForderung.ForderungsversionID;
                    kundendokumentForderungsversion.StandortObjektID = previousKundendokumentForderung.StandortObjektID;
                    kundendokumentForderungsversion.Status = KundendokumentItemStatus.Removed;
                    kundendokumentForderungsversion.Relevant = false;
                    kundendokument.KundendokumentForderungen.Add(kundendokumentForderungsversion);
                }
            }
        }

        /// <summary>
        /// Sets the state of kundendokument forderungen.
        /// </summary>
        /// <param name="previousKundendokumentId">The previous kundendokument identifier.</param>
        /// <param name="kundendokument">The kundendokument.</param>
        private void SetStateOfKundendokumentForderungen(int previousKundendokumentId, Kundendokument kundendokument)
        {
            //get all forderungsversionen of the previous kundendokument (relevant and not relevant)
            var forderungsversionenOfPreviousKundendokument =
                 _unitOfWork.KundendokumentForderungsversionRepository.GetKundendokumentForderungsversionenByKundendokument(
                     previousKundendokumentId);

            //generates a lookup-hashset out of the ids of forderungsversion per standortobjekt of the previous kundendokument 
            var previousForderungsversionenIdIndex = new HashSet<string>();
            //generates a lookup-hashset out of the ids of the foderungen per standortobjekt used of the previous kundendokument 
            var previousForderungIdIndex = new HashSet<string>();



            foreach (var version in forderungsversionenOfPreviousKundendokument)
            {
                previousForderungsversionenIdIndex.Add(ForderungsversionRepository.GenerateStandortObjektForderungsversionID(version.StandortObjektID, version.ForderungsversionID));
                previousForderungIdIndex.Add(ForderungsversionRepository.GenerateStandortObjektForderungsversionID(version.StandortObjektID, version.Forderungsversion.ForderungID));
            }

            foreach (var kundendokumentForderungsversion in kundendokument.KundendokumentForderungen)
            {
                var previousForderungsversion = forderungsversionenOfPreviousKundendokument.FirstOrDefault(
                            e => e.ForderungsversionID == kundendokumentForderungsversion.ForderungsversionID &&
                                 e.StandortObjektID == kundendokumentForderungsversion.StandortObjektID
                            );

                //check if the forderungsversion already exists in the previous kundendokument
                if (previousForderungsversionenIdIndex.Contains( 
                    ForderungsversionRepository.GenerateStandortObjektForderungsversionID(kundendokumentForderungsversion.StandortObjektID,
                    kundendokumentForderungsversion.ForderungsversionID)))
                {
                    
                    if (kundendokumentForderungsversion.Relevant == previousForderungsversion.Relevant)
                    {
                        //the forderungsversion has the same relevant value as in the kundendokument before
                        kundendokumentForderungsversion.Status = KundendokumentItemStatus.Existing;
                        
                        //if state is unchanged we keep erfuellung of old kundendokument, and the other data is keept anyway
                        if (kundendokumentForderungsversion.Relevant)
                        {
                            kundendokumentForderungsversion.Erfuellung = previousForderungsversion.Erfuellung;
                        }
                       
                       
                    }
                    else if (previousForderungsversion.Relevant && !kundendokumentForderungsversion.Relevant)
                    {
                        //the forderungsversion was relevant in the previous kundendokument and now it is not relevant anymore --> 
                        //it was removed or replaced

                        //check if there is a other version relevant
                        if (IsThereAnotherRelevantForderungsversion(kundendokumentForderungsversion, kundendokument.KundendokumentForderungen))
                        {
                            //there is a newer version of the current forderung selected -> it was replaced
                            kundendokumentForderungsversion.Status = KundendokumentItemStatus.Replaced;
                        }
                        else
                        {
                            //there is no other version selected -> it was removed
                            kundendokumentForderungsversion.Status = KundendokumentItemStatus.Removed;
                        }
                    }
                    else if (!previousForderungsversion.Relevant && kundendokumentForderungsversion.Relevant)
                    {
                        if (IsThereAnotherRelevantForderungsversion(previousForderungsversion, forderungsversionenOfPreviousKundendokument))
                        {
                            //the forderungsversion was not relevant in the previous kundendokument and now it is relevant  --> it is new
                            kundendokumentForderungsversion.Status = KundendokumentItemStatus.NewVersion;
                        }
                        else
                        {
                            //the forderungsversion was not relevant in the previous kundendokument and now it is relevant  --> it is new
                            kundendokumentForderungsversion.Status = KundendokumentItemStatus.New;
                        }

                       
                    }
                    

                }
                else
                {
                    //the forderungsversion was not in the previous kundendokuemnt ->lets check if it is a new version in the current kundendokuemtn
                    Forderungsversion forderungsversion = _unitOfWork.ForderungsversionRepository.GetByID(kundendokumentForderungsversion.ForderungsversionID);
                    
                    if (previousForderungIdIndex.Contains(ForderungsversionRepository.GenerateStandortObjektForderungsversionID(
                        kundendokumentForderungsversion.StandortObjektID, forderungsversion.ForderungID)))
                    {
                        kundendokumentForderungsversion.Status = KundendokumentItemStatus.NewVersion;
                    }
                    else
                    {
                        //the forderung was not in the kundendokument at all
                        kundendokumentForderungsversion.Status = KundendokumentItemStatus.New;
                    }
                }
                
                //set the previous inserted data from customer, except erfuellung
                if (previousForderungsversion == null)
                {
                    //get data from the latestforderungsversion, insert data on new version
                    var latestForderungsversion = forderungsversionenOfPreviousKundendokument.FirstOrDefault(
                        e =>
                            e.Forderungsversion.NachfolgeversionID ==
                            kundendokumentForderungsversion.ForderungsversionID &&
                            e.StandortObjektID == kundendokumentForderungsversion.StandortObjektID
                        );

                    if (latestForderungsversion != null)
                    {
                        kundendokumentForderungsversion.BearbeitetAm = latestForderungsversion.BearbeitetAm;
                        kundendokumentForderungsversion.BearbeitetVonID = latestForderungsversion.BearbeitetVonID;
                        kundendokumentForderungsversion.NaechstePruefungAm = latestForderungsversion.NaechstePruefungAm;
                        kundendokumentForderungsversion.LetztePruefungAm = latestForderungsversion.LetztePruefungAm;
                        kundendokumentForderungsversion.Pruefmethode = latestForderungsversion.Pruefmethode;
                        kundendokumentForderungsversion.Ablageort = latestForderungsversion.Ablageort;
                        kundendokumentForderungsversion.Verantwortlich = latestForderungsversion.Verantwortlich;
                        kundendokumentForderungsversion.Kommentar = latestForderungsversion.Kommentar;
                        kundendokumentForderungsversion.Spalte1 = latestForderungsversion.Spalte1;
                        kundendokumentForderungsversion.Spalte2 = latestForderungsversion.Spalte2;
                        kundendokumentForderungsversion.Spalte3 = latestForderungsversion.Spalte3;
                        kundendokumentForderungsversion.Spalte4 = latestForderungsversion.Spalte4;
                        kundendokumentForderungsversion.Spalte5 = latestForderungsversion.Spalte5;
                        kundendokumentForderungsversion.Spalte6 = latestForderungsversion.Spalte6;
                        kundendokumentForderungsversion.Spalte7 = latestForderungsversion.Spalte7;
                        kundendokumentForderungsversion.Spalte8 = latestForderungsversion.Spalte8;
                        kundendokumentForderungsversion.Spalte9 = latestForderungsversion.Spalte9;
                        kundendokumentForderungsversion.Spalte10 = latestForderungsversion.Spalte10;
                        //Fixed NEOS-303 Shortcuts not copied to new kundendokument
                        kundendokumentForderungsversion.Shortcut = latestForderungsversion.Shortcut;
                    }
                }
                else
                {
                    //insert data on all other cases
                    kundendokumentForderungsversion.BearbeitetAm = previousForderungsversion.BearbeitetAm;
                    kundendokumentForderungsversion.BearbeitetVonID = previousForderungsversion.BearbeitetVonID;
                    kundendokumentForderungsversion.NaechstePruefungAm = previousForderungsversion.NaechstePruefungAm;
                    kundendokumentForderungsversion.LetztePruefungAm = previousForderungsversion.LetztePruefungAm;
                    kundendokumentForderungsversion.Pruefmethode = previousForderungsversion.Pruefmethode;
                    kundendokumentForderungsversion.Ablageort = previousForderungsversion.Ablageort;
                    kundendokumentForderungsversion.Verantwortlich = previousForderungsversion.Verantwortlich;
                    kundendokumentForderungsversion.Kommentar = previousForderungsversion.Kommentar;
                    kundendokumentForderungsversion.Spalte1 = previousForderungsversion.Spalte1;
                    kundendokumentForderungsversion.Spalte2 = previousForderungsversion.Spalte2;
                    kundendokumentForderungsversion.Spalte3 = previousForderungsversion.Spalte3;
                    kundendokumentForderungsversion.Spalte4 = previousForderungsversion.Spalte4;
                    kundendokumentForderungsversion.Spalte5 = previousForderungsversion.Spalte5;
                    kundendokumentForderungsversion.Spalte6 = previousForderungsversion.Spalte6;
                    kundendokumentForderungsversion.Spalte7 = previousForderungsversion.Spalte7;
                    kundendokumentForderungsversion.Spalte8 = previousForderungsversion.Spalte8;
                    kundendokumentForderungsversion.Spalte9 = previousForderungsversion.Spalte9;
                    kundendokumentForderungsversion.Spalte10 = previousForderungsversion.Spalte10;
                    //Fixed NEOS-303 Shortcuts not copied to new kundendokument
                    kundendokumentForderungsversion.Shortcut = previousForderungsversion.Shortcut;
                }
            }

            var listNewForderungen = kundendokument.KundendokumentForderungen.Where(f => f.Status == KundendokumentItemStatus.NewVersion 
                        && !forderungsversionenOfPreviousKundendokument.Any(e => e.ForderungsversionID == f.ForderungsversionID && e.StandortObjektID == f.StandortObjektID)
                        && !forderungsversionenOfPreviousKundendokument.Any(e => e.Forderungsversion.NachfolgeversionID == f.ForderungsversionID && e.StandortObjektID == f.StandortObjektID)            
            ).ToList();

            foreach (var kundendokumentForderungsversion in listNewForderungen)
            {
                var forderungsId = _unitOfWork.ForderungsversionRepository.GetByID(kundendokumentForderungsversion.ForderungsversionID).ForderungID;

                var latestForderungsversion = forderungsversionenOfPreviousKundendokument.OrderByDescending(f => f.ErstelltAm).FirstOrDefault(
                    e =>
                        e.Forderungsversion.ForderungID == forderungsId &&
                        e.StandortObjektID == kundendokumentForderungsversion.StandortObjektID
                    );

                if (latestForderungsversion != null)
                {
                    kundendokumentForderungsversion.BearbeitetAm = latestForderungsversion.BearbeitetAm;
                    kundendokumentForderungsversion.BearbeitetVonID = latestForderungsversion.BearbeitetVonID;
                    kundendokumentForderungsversion.NaechstePruefungAm = latestForderungsversion.NaechstePruefungAm;
                    kundendokumentForderungsversion.LetztePruefungAm = latestForderungsversion.LetztePruefungAm;
                    kundendokumentForderungsversion.Pruefmethode = latestForderungsversion.Pruefmethode;
                    kundendokumentForderungsversion.Ablageort = latestForderungsversion.Ablageort;
                    kundendokumentForderungsversion.Verantwortlich = latestForderungsversion.Verantwortlich;
                    kundendokumentForderungsversion.Kommentar = latestForderungsversion.Kommentar;
                    kundendokumentForderungsversion.Spalte1 = latestForderungsversion.Spalte1;
                    kundendokumentForderungsversion.Spalte2 = latestForderungsversion.Spalte2;
                    kundendokumentForderungsversion.Spalte3 = latestForderungsversion.Spalte3;
                    kundendokumentForderungsversion.Spalte4 = latestForderungsversion.Spalte4;
                    kundendokumentForderungsversion.Spalte5 = latestForderungsversion.Spalte5;
                    kundendokumentForderungsversion.Spalte6 = latestForderungsversion.Spalte6;
                    kundendokumentForderungsversion.Spalte7 = latestForderungsversion.Spalte7;
                    kundendokumentForderungsversion.Spalte8 = latestForderungsversion.Spalte8;
                    kundendokumentForderungsversion.Spalte9 = latestForderungsversion.Spalte9;
                    kundendokumentForderungsversion.Spalte10 = latestForderungsversion.Spalte10;
                    //Fixed NEOS-303 Shortcuts not copied to new kundendokument
                    kundendokumentForderungsversion.Shortcut = latestForderungsversion.Shortcut;
                }
            }
        }

        /// <summary>
        /// Checks if there are other version of the overgiven forderungsversion relevant for the overgiven standortobjekt
        /// </summary>
        /// <param name="forderungsversionID">The forderungsversion identifier.</param>
        /// <param name="standortobjektID">The standortobjekt identifier.</param>
        /// <param name="kundendokumentForderungsversionen">The kundendokument forderungsversionen.</param>
        /// <returns></returns>
        private bool IsThereAnotherRelevantForderungsversion(KundendokumentForderungsversion kundendokumentForderungsversion, IEnumerable<KundendokumentForderungsversion> kundendokumentForderungsversionen)
        {
            //check if there is a other version relevant

            var otherVersions = _unitOfWork.ForderungsversionRepository.GetForderungsverionsSiblingIds(kundendokumentForderungsversion.ForderungsversionID);

            if (otherVersions.Any())
            {

                return kundendokumentForderungsversionen.Any(
                    e => e.StandortObjektID == kundendokumentForderungsversion.StandortObjektID && otherVersions.Contains(e.ForderungsversionID) && e.Relevant);
            }

            return false;
        }

        private bool IsForderungsversionReplacedWithANewVersion(KundendokumentForderungsversionViewModel viewModel, 
            IDictionary<int, List<KundendokumentForderungsversionViewModel>>  foderungsversionenLookupMap, IEnumerable<KundendokumentForderungsversion> forderungsversionenOfPreviousKundendokument)
        {
            //gets all siblings of the current forderungsversion
            var relatedForderungsversionen = foderungsversionenLookupMap[viewModel.ForderungID];

            //go through the previous kundendokumenForderungsversion of the current forderungsversionviewmodel 
            //in order to find out if there is one item replaced 
            foreach (var relatedForderungsversion in relatedForderungsversionen)
            {
                //check inkrafttretungsdate of related version and if the related forderungsversion is new and was not in 
                //the previous kundendokument
                if (relatedForderungsversion.Inkrafttretung > viewModel.Inkrafttretung && 
                    !forderungsversionenOfPreviousKundendokument.Any(
                        e => e.ForderungsversionID == relatedForderungsversion.ForderungsversionID &&
                        e.StandortObjektID == relatedForderungsversion.StandortObjektID)
                    )
                {
                    return true;
                }

            }
            return false;
        }
        #endregion

        #region Pflichten Helpers
        //Die Pflichten werden im Moment nich genutzt-- > Schritt wird übersprungen //Patrick Schmed, 26.06.2017
        //private IQueryable<KundendokumentStandortobjektPflichtViewModel> GetSelectedStandortPflichtViewModels(KundendokumentData data)
        //{
        //    List<KundendokumentStandortobjektPflichtViewModel> viewModels = _unitOfWork.PflichtRepository.
        //        GetSelectedStandortObjektPflichtenViewModelsByStandort(data.StandortID, data.SelectedPflichtenIDs, data.KundendokumentID).ToList();

        //    SetViewModelKundenbezuege(data.PflichtKundenbezuege, viewModels);

        //    return viewModels.AsQueryable();
        //}

        //private IQueryable<KundendokumentPflichtViewModel> GetPflichtenToSelectViewModels(KundendokumentData data)
        //{
        //    if (data.VorgaengerKundendokumentID.HasValue)
        //    {
        //        var list = new List<KundendokumentPflichtViewModel>();
        //        list = _unitOfWork.PflichtRepository.GetAllFeasiblePflichtenViewModelsByStandort(data.StandortID).ToList();

        //        SetPflichtenViewModelState(_unitOfWork.KundendokumentPflichtRepository.GetKundendokumentPflichtenByKundendokument(
        //                data.VorgaengerKundendokumentID), list);

        //        return list.AsQueryable();
        //    }
        //    return _unitOfWork.PflichtRepository.GetAllFeasiblePflichtenViewModelsByStandort(data.StandortID);
        //}

        //private void SetViewModelKundenbezuege(IEnumerable<KundendokumentData.KundenbezugItem> kundenbezuege,
        //    IEnumerable<KundendokumentStandortobjektPflichtViewModel> viewModels)
        //{
        //    foreach (var item in kundenbezuege)
        //    {
        //        var vm = viewModels.FirstOrDefault(e => e.ID == item.Key);
        //        if (vm != null)
        //        {
        //            vm.Kundenbezug = item.Value;
        //        }
        //    }

        //}

        //private void SetPflichtenViewModelState(List<KundendokumentPflicht> pflichtenOfPreviousKundendokument,
        //    List<KundendokumentPflichtViewModel> viewModels)
        //{
        //    var previousPflichtIdIndex =
        //        new HashSet<int>(pflichtenOfPreviousKundendokument.Select(f => f.PflichtID));

        //    // set the state of the kundendokumentpflichten
        //    foreach (var viewModel in viewModels)
        //    {
        //        if (previousPflichtIdIndex.Contains(viewModel.PflichtID) )
        //        {
        //            if (viewModel.Relevant)
        //            {
        //                viewModel.Status = KundendokumentItemStatus.Unchanged;
        //            }
        //            else
        //            {
        //                viewModel.Status = KundendokumentItemStatus.ToExamine;
        //            }
        //        }

        //        else
        //        {
        //            viewModel.Status = KundendokumentItemStatus.New;
        //        }
        //    }
        //}

        //private void SetStateOfKundendokumentPflichten(int previousKundendokumentId, Kundendokument kundendokument)
        //{
        //    var pflichtenOfPreviousKundendokument =
        //         _unitOfWork.KundendokumentPflichtRepository.GetKundendokumentPflichtenByKundendokument(
        //             previousKundendokumentId);
        //    var previousPflichtenIdIndex =
        //         new HashSet<int>(pflichtenOfPreviousKundendokument.Select(f => f.PflichtID));

        //    foreach (var kundendokumentpflicht in kundendokument.KundendokumentPflichten)
        //    {
        //        if (previousPflichtenIdIndex.Contains(kundendokumentpflicht.PflichtID))
        //        {
        //            kundendokumentpflicht.Status = KundendokumentItemStatus.Unchanged;

        //            var previouspflicht = pflichtenOfPreviousKundendokument.FirstOrDefault(
        //                   e => e.PflichtID == kundendokumentpflicht.PflichtID &&
        //                   e.StandortObjektID == kundendokumentpflicht.StandortObjektID);
        //            //if state is unchanged we keep data of old kundendokument
        //            if (previouspflicht != null)
        //            {
        //                kundendokumentpflicht.ErfuelltDurch = previouspflicht.ErfuelltDurch;
        //                kundendokumentpflicht.Erfuellung = previouspflicht.Erfuellung;
        //                kundendokumentpflicht.Erfuellungszeitpunkt = previouspflicht.Erfuellungszeitpunkt;
        //                kundendokumentpflicht.Ablageort = previouspflicht.Ablageort;
        //                kundendokumentpflicht.Verantwortlich = previouspflicht.Verantwortlich;
        //            }
        //        }
        //        else
        //        {
        //            kundendokumentpflicht.Status = KundendokumentItemStatus.New;
        //        }
        //    }
        //}


        #endregion

        #region Erlassfassung Helpers

        private IQueryable<KundendokumentErlassfassungViewModel> GetSelectedErlassfassungViewModels(KundendokumentData data)
        {
            //get ids of the erlassfassungen which contains the selcted forderungen and pflichten
            var erlassfassungIds = ErlassfassungHelper.ExtractErlassfassungIds(_unitOfWork, data.StandortID, data.SelectedForderungenIDs, data.SelectedPflichtenIDs);

            var erlassfassungViewModels = _unitOfWork.KundendokumentErlassfassungRepository.
                GetAllKundendokumentErlassfassungViewModelByErlassfassungIds(erlassfassungIds).ToList();

            if (data.VorgaengerKundendokumentID.HasValue)
            {
                SetErlassfassungenViewModelState(_unitOfWork.KundendokumentErlassfassungRepository.GetKundendokumentErlassfassungenByKundendokument(
                        data.VorgaengerKundendokumentID), erlassfassungViewModels);
            }
           
            return erlassfassungViewModels.AsQueryable();
        }

        private void SetErlassfassungenViewModelState(IEnumerable<KundendokumentErlassfassung> erlassfassungenOfPreviousKundendokument,
            List<KundendokumentErlassfassungViewModel> viewModels)
        {
            var previousErlassIdIndex =
                new HashSet<int>(erlassfassungenOfPreviousKundendokument.Select(f => f.Erlassfassung.ErlassID));
            // set the state of the kundendokumenterlassfassungen
            foreach (KundendokumentErlassfassungViewModel viewModel in viewModels)
            {
                var previousKundendokumentErlassfassung =
                    erlassfassungenOfPreviousKundendokument.FirstOrDefault(e => e.ErlassfassungID == viewModel.ErlassfassungID);
                if (previousKundendokumentErlassfassung != null)
                {
                    viewModel.Status = KundendokumentItemStatus.Existing;
                    viewModel.Betroffen = previousKundendokumentErlassfassung.Betroffen;
                }
                else if (previousErlassIdIndex.Contains(viewModel.ErlassID))
                {
                    viewModel.Status = KundendokumentItemStatus.NewVersion;
                }
                else
                {
                    viewModel.Status = KundendokumentItemStatus.New;
                }
            }
        }

        private void SetStateOfKundendokumentErlassfassungen(int previousKundendokumentId, Kundendokument kundendokument)
        {
            var erlassfassungenOfPreviousKundendokument =
                 _unitOfWork.KundendokumentErlassfassungRepository.GetKundendokumentErlassfassungenByKundendokument(
                     previousKundendokumentId);
            var previousErlassfassungIdIndex =
                 new HashSet<int>(erlassfassungenOfPreviousKundendokument.Select(f => f.ErlassfassungID));
            var previousErlassIdIndex =
                new HashSet<int>(erlassfassungenOfPreviousKundendokument.Select(f => f.Erlassfassung.ErlassID));

            foreach (var kundendokumentErlassfassung in kundendokument.KundendokumentErlassfassungen)
            {
                if (previousErlassfassungIdIndex.Contains(kundendokumentErlassfassung.ErlassfassungID))
                {
                    kundendokumentErlassfassung.Status = KundendokumentItemStatus.Existing;
                }
                else if (previousErlassIdIndex.Contains(kundendokumentErlassfassung.Erlassfassung.ErlassID))
                {
                    kundendokumentErlassfassung.Status = KundendokumentItemStatus.NewVersion;
                }
                else
                {
                    kundendokumentErlassfassung.Status = KundendokumentItemStatus.New;
                }
            }
        }

        private void SetStateOfKundendokumentChecklist(int previousKundendokumentId, Kundendokument kundendokument)
        {
            var checklistsOfPreviousKundendokument =
                _unitOfWork.kundendokumentChecklistRepository.GetKundendokumentChecklistsByKundendokument(
                    previousKundendokumentId);

            foreach (var kundendokumentErlassfassung in kundendokument.KundendokumentErlassfassungen)
            {
                if (kundendokumentErlassfassung.Status == KundendokumentItemStatus.Existing)
                {
                    // find previous checklist for erlassfassung
                    var previousChecklist = checklistsOfPreviousKundendokument.FirstOrDefault(c =>
                        c.Checklist.ErlassfassungID == kundendokumentErlassfassung.ErlassfassungID);
                    // if previous checklist exists copy
                    if (previousChecklist != null)
                    {
                        var currentChecklist =
                            kundendokument.KundendokumentChecklists.FirstOrDefault(c =>
                                c.ChecklistID == previousChecklist.ChecklistID);
                        if (currentChecklist != null)
                        {
                            currentChecklist.Status = KundendokumentItemStatus.Existing;
                            currentChecklist.BearbeitetAm = previousChecklist.BearbeitetAm;
                            currentChecklist.BearbeitetVonID = previousChecklist.BearbeitetVonID;
                            currentChecklist.Translation = previousChecklist.Translation;

                            foreach (var previousQuestion in previousChecklist.Questions)
                            {
                                var currentQuestion = currentChecklist.Questions.FirstOrDefault(q =>
                                    q.ChecklistQuestionID == previousQuestion.ChecklistQuestionID);
                                if (currentQuestion != null)
                                {
                                    currentQuestion.Status = KundendokumentItemStatus.Existing;
                                    currentQuestion.BearbeitetAm = previousQuestion.BearbeitetAm;
                                    currentQuestion.BearbeitetVonID = previousQuestion.BearbeitetVonID;
                                    currentQuestion.Translation = previousQuestion.Translation;
                                    currentQuestion.SharedImage = previousQuestion.SharedImage;
                                    currentQuestion.Answer = previousQuestion.Answer;
                                }
                            }

                        }
                    }

                }
            }
        }

        #endregion

        private void GenerateAndAttachChildElementsToKundendokument(Kundendokument kundendokument, KundendokumentData data)
        {
            IQueryable<KundendokumentForderungsversionViewModel> feasibleForderungsversionen =
                _unitOfWork.ForderungsversionRepository.GetAllPublishedFeasibleForderungViewModelsForStandort(data.StandortID);

            var kundendokumentForderungen = new List<KundendokumentForderungsversion>();

            foreach (KundendokumentForderungsversionViewModel viewModel in feasibleForderungsversionen)
            {
                    var kundendokumentForderung = new KundendokumentForderungsversion();
                    kundendokumentForderung.ForderungsversionID = viewModel.ForderungsversionID;
                    kundendokumentForderung.StandortObjektID = viewModel.StandortObjektID;
                    kundendokumentForderung.Status = KundendokumentItemStatus.New;

                    if (data.ForderungKundenbezuege != null)
                    {
                        var kundenbezugItem = data.ForderungKundenbezuege.FirstOrDefault(e => e.Key == viewModel.ID);
                        if (kundenbezugItem != null)
                        {
                            kundendokumentForderung.Kundenbezug = kundenbezugItem.Value;
                        }
                    }

                    if (data.SelectedForderungenIDs.Contains(viewModel.ID))
                    {
                        //erlassfassungIds.Add(viewModel.ErlassfassungID);
                        kundendokumentForderung.Relevant = true;
                    }
                    else
                    {
                        kundendokumentForderung.Relevant = false;
                    }

                    kundendokumentForderungen.Add(kundendokumentForderung);
            }

            var erlassfassungIds = ErlassfassungHelper.ExtractErlassfassungIds(_unitOfWork, data.StandortID, data.SelectedForderungenIDs, data.SelectedPflichtenIDs);
            var resolvedErlassfassungen = _unitOfWork.ErlassfassungRepository.GetByIDs(erlassfassungIds.ToArray());
            var kundendokumentErlassfassungen = new List<KundendokumentErlassfassung>();
            foreach (var erlassfassung in resolvedErlassfassungen)
            {
                var kundendokumentErlassfassung = new KundendokumentErlassfassung();
                kundendokumentErlassfassung.ErlassfassungID = erlassfassung.ErlassfassungID;
                kundendokumentErlassfassung.Betroffen = data.SelectedErlasssfassungenIDs.Contains(erlassfassung.ErlassfassungID);
                kundendokumentErlassfassung.Status = KundendokumentItemStatus.New;
                kundendokumentErlassfassung.Erlassfassung = erlassfassung;

                kundendokumentErlassfassungen.Add(kundendokumentErlassfassung);
            }


            // create kundendokumentcheklist based on elassfassungIds
            var checklists = _unitOfWork.ChecklistRepository.GetAllChecklistViewModels();
            var kundendokumentChecklists = new List<KundendokumentChecklist>();
            foreach (var erlassfassungId in erlassfassungIds)
            {
                var checklist = checklists.FirstOrDefault(c => c.ErlassfassungID == erlassfassungId);
                if (checklist == null)
                {
                    continue;
                }

                var kundendokumentChecklist = new KundendokumentChecklist();
                kundendokumentChecklist.ChecklistID = checklist.ChecklistID;
                kundendokumentChecklist.Translation = checklist.Translation;
                kundendokumentChecklist.Status = KundendokumentItemStatus.New;
                kundendokumentChecklist.ErlassfassungID = checklist.ErlassfassungID;

                foreach (var question in checklist.Questions)
                {
                    var kundendokumentChecklistQuestion = new KundendokumentChecklistQuestion();
                    kundendokumentChecklistQuestion.ChecklistQuestionID = question.ChecklistQuestionID;
                    kundendokumentChecklistQuestion.Translation = question.Translation;
                    kundendokumentChecklistQuestion.Status = KundendokumentItemStatus.New;
                    if (question.ChecklistType == "Question")
                    {
                        kundendokumentChecklistQuestion.Answer = KundendokumentQuestionAnswer.NotEdited;
                    }
                    kundendokumentChecklistQuestion.SharedImage = question.SharedImage;
                    kundendokumentChecklistQuestion.ChecklistType = question.ChecklistType;
                    kundendokumentChecklistQuestion.ChecklistHeaderID = question.ChecklistHeaderID;
                    kundendokumentChecklistQuestion.Numeration = question.Numeration;
                    kundendokumentChecklist.Questions.Add(kundendokumentChecklistQuestion);
                }

                kundendokumentChecklists.Add(kundendokumentChecklist);
            }


            kundendokument.KundendokumentForderungen = kundendokumentForderungen;
            //kundendokument.KundendokumentPflichten = kundendokumentPflichten;
            kundendokument.KundendokumentErlassfassungen = kundendokumentErlassfassungen;
            kundendokument.KundendokumentChecklists = kundendokumentChecklists;
        }

        #endregion

    }


}
