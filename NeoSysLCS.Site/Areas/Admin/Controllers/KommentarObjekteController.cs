using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web.Mvc;
using System.Web.SessionState;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Site.Controllers;

namespace NeoSysLCS.Site.Areas.Admin.Controllers
{
    [SessionState(SessionStateBehavior.ReadOnly)]
    public class KommentarObjekteController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;

        public KommentarObjekteController()
        {
            _unitOfWork = new UnitOfWork();
        }

        public KommentarObjekteController(IUnitOfWork unitOfWorkCandidate)
        {
            _unitOfWork = unitOfWorkCandidate;
        }

        public ActionResult Index(int id)
        {
            ViewData["KommentarID"] = id;
            return View("Index", _unitOfWork.ObjektRepository.GetAllObjektViewModelsByKommentar(id));
        }

        /// <summary>
        /// Shows the listbox with the selected values
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        public ActionResult KommentarObjektePartial(int id)
        {
            ViewData["KommentarID"] = id;
            return PartialView("_KommentarObjektePartial");
        }

        /// <summary>
        /// Shows the gridview with the objekte that can be selected
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        public ActionResult ObjekteSelectionPartial(int id)
        {
            string selectedIDs = Request.Params["selectedIDsHF"];
            ViewData["_selectedIDs"] = selectedIDs;
            ViewData["KommentarID"] = id;
            return PartialView("_ObjekteSelectionPartial");
        }

        public ActionResult SaveKommentarObjekte()
        {
            //Get all selected keys from e.customArgs on GridView callback
            string selectedIDs = Request.Params["selectedIDsHF"];
            int kommentarID = Convert.ToInt32(Request.Params["id"]);
            ViewData["_selectedIDs"] = selectedIDs;
            ViewData["KommentarID"] = kommentarID;

            _unitOfWork.KommentarRepository.SaveKommentarObjekte(selectedIDs, kommentarID);
            _unitOfWork.Save();

            return Json(new { Url = Url.Action("Index", "Kommentar") });
        }
    }
}
