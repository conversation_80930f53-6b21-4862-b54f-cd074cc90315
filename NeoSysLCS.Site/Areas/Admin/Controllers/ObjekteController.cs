using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web.Mvc;
using System.Web.SessionState;
using System.Web.UI.WebControls;
using DevExpress.Web;
using DevExpress.XtraGrid;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using DevExpress.Web.Mvc;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Helpers;
using NeoSysLCS.Site.Utilities.Export;
using DevExpress.Utils;

namespace NeoSysLCS.Site.Areas.Admin.Controllers
{
    [SessionState(SessionStateBehavior.ReadOnly)]
    public class ObjekteController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;
        
        public ObjekteController()
        {
            _unitOfWork = new UnitOfWork();
        }

        public ObjekteController(IUnitOfWork unitOfWorkCandidate)
        {
            _unitOfWork = unitOfWorkCandidate;
        }

        public ActionResult Index()
        {
            var objekte = _unitOfWork.ObjektRepository.GetAllObjektViewModels();
            return View("Index", objekte);
        }

        /// <summary>
        /// Shows the grid with all objekte
        /// </summary>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult ObjekteGridView()
        {
            var objekte = _unitOfWork.ObjektRepository.GetAllObjektViewModels();
            return PartialView("ObjekteGridView", objekte);
        }

        /// <summary>
        /// Saves the changes of objekte
        /// </summary>
        /// <param name="updateValues">The update values.</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult ObjekteGridViewBatchEditUpdate(
            MVCxGridViewBatchUpdateValues<ObjektViewModel, int> updateValues)
        {

            foreach (var objekt in updateValues.Insert)
            {
                GridViewUpdateHelper<Objekt, ObjektViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(objekt))
                    {
                        _unitOfWork.ObjektRepository.Insert(objekt);
                        _unitOfWork.Save();
                    }
                }, objekt, updateValues);


            }
            foreach (var objekt in updateValues.Update)
            {
                GridViewUpdateHelper<Objekt, ObjektViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(objekt))
                    {
                        string userID = System.Web.HttpContext.Current.User.Identity.GetUserId();
                        _unitOfWork.ObjektRepository.Update(objekt, userID);
                        _unitOfWork.Save();
                    }
                }, objekt, updateValues);
            }
            foreach (var objektID in updateValues.DeleteKeys)
            {
                GridViewUpdateHelper<Objekt, ObjektViewModel>.DoDelete(() =>
                {
                    _unitOfWork.ObjektRepository.Delete(objektID);
                    _unitOfWork.Save();
                }, _unitOfWork.ObjektRepository, objektID, updateValues);

            }

            _unitOfWork.Save();

            var objekte = _unitOfWork.ObjektRepository.GetAllObjektViewModels();
            return PartialView("ObjekteGridView", objekte);
        }

        /// <summary>
        /// Shows the uebersetzungen of the specified objekt
        /// </summary>
        /// <param name="objektID">The objekt identifier.</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult ObjektUebersetzungenGridView(int objektID)
        {
            ViewData["ObjektID"] = objektID;

            //IEnumerable<Sprache> sprachen = _unitOfWork.SpracheRepository.Get();
            //_unitOfWork.ObjektUebersetzungRepository.PopulatedObjektUebersetzungenByObjekt(objektID, sprachen);
            //_unitOfWork.Save();

            var objektUebersetzungen = _unitOfWork.ObjektRepository.GetObjektUebersetzungenByObjekt(objektID);

            return PartialView("ObjektUebersetzungenGridView", objektUebersetzungen);
        }

        /// <summary>
        /// Saves the uebersetzungen
        /// </summary>
        /// <param name="updateValues">The update values.</param>
        /// <param name="objektID">The objekt identifier.</param>
        /// <returns></returns>
        public ActionResult ObjektUebersetzungenGridViewBatchEditingUpdate(
            MVCxGridViewBatchUpdateValues<ObjektViewModel, int> updateValues, int objektID)
        {
            foreach (var uebersetzung in updateValues.Update)
            {
                GridViewUpdateHelper<Objekt, ObjektViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(uebersetzung))
                    {
                        uebersetzung.ObjektID = objektID;
                        _unitOfWork.ObjektRepository.UpdateObjektUebersetzung(uebersetzung);
                        _unitOfWork.Save();
                    }
                }, uebersetzung, updateValues);
            }

            _unitOfWork.Save();

            ViewData["ObjektID"] = objektID;
            var objektUebersetzungen = _unitOfWork.ObjektRepository.GetObjektUebersetzungenByObjekt(objektID);

            return PartialView("ObjektUebersetzungenGridView", objektUebersetzungen);
        }

        /// <summary>
        /// Exports the objekte 
        /// </summary>
        /// <returns></returns>
        public ActionResult ExportTo()
        {
            var viewModels = _unitOfWork.ObjektRepository.GetAllObjektViewModels().ToList();
            var exportType = ExportUtilities.GetExportType(Request.Params["exportFormat"]);
            var exportResult = ExportUtilities.Export(exportType, GetExportGridSettings(exportType), viewModels,
                false);

            if (exportResult != null)
            {
                return exportResult;
            }

            return RedirectToAction("Index");
        }

        /// <summary>
        /// Gets the export settings of the grid
        /// </summary>
        /// <param name="exportType">Type of the export.</param>
        /// <returns></returns>
        private GridViewSettings GetExportGridSettings(ExportType exportType)
        {
            var settings = new GridViewSettings();
            settings.Name = BaseGridViewSettings.GetGridViewName();
            settings.KeyFieldName = BaseGridViewSettings.GetGridViewKeyFieldName();

            //columns: there need to be the same colums in the view and for the export for sorting
            settings.CommandColumn.Visible = true;
            foreach (MVCxGridViewColumn col in BaseGridViewSettings.GetDataColumns())
            {
                settings.Columns.Add(col);
            }

            //Exportsettings
            GridViewHelper.ApplyDefaultExportSettings(settings, exportType);

            if (exportType == ExportType.Pdf)
            {
                settings.SettingsExport.PageHeader.Left = "Objekte";
                settings.SettingsExport.PageHeader.Center = Url.Action("Index", "Objekte", new { area = "Admin" }, Request.Url.Scheme);
            }
            return settings;

        }

        public ActionResult ObjektKatComboBox()
        {
            MVCxColumnComboBoxProperties p = new MVCxColumnComboBoxProperties();
            p.ValueField = "ObjektKategorieID";
            p.ValueType = typeof(int);
            p.Columns.Add("Name", Resources.Properties.Resources.Entitaet_Objektkategorie_Name);
            p.Columns.Add("ParentObjektkategorieName", Resources.Properties.Resources.Entitaet_Objektkategorie_Parent);
            p.BindList(_unitOfWork.ObjektkategorieRepository.GetAllObjektkategorieViewModels().ToList());
            return GridViewExtension.GetComboBoxCallbackResult(p);
        }

        /// <summary>
        ///  Inner Class for the Gridview Settings (this avoids a separat defninition of the settings in the view and the export).
        /// </summary>
        public static class BaseGridViewSettings
        {
            static NeoSysLCS_Dev context = new NeoSysLCS_Dev();
            private static int _currentLang;

            public static string GetGridViewName()
            {
                return "ObjekteGridView";
            }

            public static string GetGridViewKeyFieldName()
            {
                return "ObjektID";

            }

            /// <summary>
            /// Gets the column definition
            /// </summary>
            /// <returns></returns>
            public static List<MVCxGridViewColumn> GetDataColumns()
            {
                IUnitOfWork unitOfWork = new UnitOfWork();
                var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
                Sprache sprache = context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
                _currentLang = sprache.SpracheID;

                var columns = new MVCxGridViewColumnCollection();

                columns.Add(column =>
                {
                    column.FieldName = "ObjektID";
                    column.Caption = "Id";
                    column.ReadOnly = true;
                    column.EditFormSettings.Visible = DefaultBoolean.False;
                    column.SortDescending();
                });
                columns.Add(column =>
                {
                    column.FieldName = "Name";
                    column.Caption = Resources.Properties.Resources.Colunm_Name;
                });
                columns.Add(column =>
                {
                    column.FieldName = "Beschreibung";
                    column.Caption = TranslationHelper.GetTranslation(typeof(ObjektViewModel), column.FieldName);
                });

                columns.Add(column =>
                {
                    column.FieldName = "ObjektkategorieID";
                    column.Caption = TranslationHelper.GetTranslation(typeof(ObjektViewModel), column.FieldName);
                    column.Settings.SortMode = ColumnSortMode.DisplayText;

                    column.ColumnType = MVCxGridViewColumnType.ComboBox;
                    column.Settings.FilterMode = DevExpress.Web.ColumnFilterMode.DisplayText;
                    column.EditorProperties().ComboBox(c =>
                    {
                        c.CallbackRouteValues = new {Controller = "Objekte", Action = "ObjektKatComboBox"};
                        c.ValueField = "ObjektkategorieID";
                        c.ValueType = typeof(int);
                        c.TextFormatString = "{0}";
                        c.CallbackPageSize = 1000;
                        c.Columns.Add("Name", Resources.Properties.Resources.Entitaet_Objektkategorie_Name);
                        c.Columns.Add("ParentObjektkategorieName", Resources.Properties.Resources.Entitaet_Objektkategorie_Parent);
                        c.BindList(unitOfWork.ObjektkategorieRepository.GetAllObjektkategorieViewModels().ToList());
                    });


                    
                });

                columns.Add(column =>
                {
                    column.FieldName = "Freigabe";
                    column.Caption = TranslationHelper.GetTranslation(typeof(ObjektViewModel), column.FieldName);
                    column.Width = Unit.Pixel(75);
                    column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    var prop = (column.PropertiesEdit as EditProperties);
                    if (prop != null)
                    {
                        prop.ValidationSettings.Display = Display.Dynamic;
                    }
                });

                columns.Add(column =>
                {
                    column.FieldName = "QsFreigabe";
                    column.Caption = TranslationHelper.GetTranslation(typeof(ObjektViewModel), column.FieldName);
                    column.Width = Unit.Pixel(75);
                    column.ColumnType = MVCxGridViewColumnType.CheckBox;
                    var prop = (column.PropertiesEdit as EditProperties);
                    if (prop != null)
                    {
                        prop.ValidationSettings.Display = Display.Dynamic;
                    }

                });

                //make a list from the MCxGridViewColumnCollection otherwise settings.Columns.Add(col) won't work --> DevExpress 16.2.15
                var columList = new List<MVCxGridViewColumn>();
                foreach (MVCxGridViewColumn column in columns)
                {
                    columList.Add(column);
                };

                return columList;
            }

        }
    }

}
