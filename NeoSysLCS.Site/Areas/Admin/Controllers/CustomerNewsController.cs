using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using DevExpress.Mvvm.Native;
using DevExpress.Web;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using DevExpress.Web.Mvc;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Helpers;
using NeoSysLCS.Site.Utilities.Export;
using System;
using System.Dynamic;
using DevExpress.XtraPrinting;
using DevExpress.XtraPrintingLinks;
using System.IO;
using System.Net;
using NeoSysLCS.Site.AzureStorage;
using System.Configuration;
using System.Globalization;
using DevExpress.Utils;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.EntityFramework;

namespace NeoSysLCS.Site.Areas.Admin.Controllers
{
    public class CustomerNewsController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;

        public CustomerNewsController()
        {
            _unitOfWork = new UnitOfWork();
        }

        public CustomerNewsController(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public ActionResult Index()
        {
            var viewModels = _unitOfWork.CustomerNewsRepository.GetAllCustomerNewsViewModels();
            return View("Index", viewModels);
        }

        public ActionResult CustomerNewsGridView()
        {
            var viewModels = _unitOfWork.CustomerNewsRepository.GetAllCustomerNewsViewModels();
            return PartialView("_CustomerNewsGridView", viewModels);
        }

        [ValidateInput(false)]
        public ActionResult CustomerNewsGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<CustomerNewsViewModel, int> updateValues)
        {
            foreach (var news in updateValues.Insert)
            {
                GridViewUpdateHelper<CustomerNews, CustomerNewsViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(news))
                    {
                        _unitOfWork.CustomerNewsRepository.Insert(news);
                        _unitOfWork.Save();
                    }
                }, news, updateValues);
            }
            foreach (var news in updateValues.Update)
            {
                GridViewUpdateHelper<CustomerNews, CustomerNewsViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(news))
                    {
                        _unitOfWork.CustomerNewsRepository.Update(news);
                        _unitOfWork.Save();
                    }
                }, news, updateValues);
            }
            foreach (var customerNewsId in updateValues.DeleteKeys)
            {
                GridViewUpdateHelper<CustomerNews, CustomerNewsViewModel>.DoDelete(() =>
                {
                    _unitOfWork.CustomerNewsRepository.Delete(customerNewsId);
                    _unitOfWork.Save();
                }, _unitOfWork.CustomerNewsRepository, customerNewsId, updateValues);

            }

            var viewModels = _unitOfWork.CustomerNewsRepository.GetAllCustomerNewsViewModels();
            return PartialView("_CustomerNewsGridView", viewModels);
        }

        public ActionResult CustomerNewsTranslationsGridView(int customerNewsId)
        {
            var translations = _unitOfWork.CustomerNewsRepository.GetCustomerNewsTranslationsByNewsId(customerNewsId);

            ViewData["CustomerNewsID"] = customerNewsId;
            return PartialView("_CustomerNewsTranslationsGridView", translations);
        }

        [ValidateInput(false)]
        public ActionResult CustomerNewsTranslationsGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<CustomerNewsViewModel, int> updateValues, int customerNewsId)
        {
            foreach (var translation in updateValues.Update)
            {
                GridViewUpdateHelper<CustomerNews, CustomerNewsViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(translation))
                    {
                        translation.CustomerNewsID= customerNewsId;
                        _unitOfWork.CustomerNewsRepository.UpdateCustomerNewsTranslation(translation, customerNewsId);
                        _unitOfWork.Save();
                    }
                }, translation, updateValues);
            }

            ViewData["CustomerNewsID"] = customerNewsId;
            var translations = _unitOfWork.CustomerNewsRepository.GetCustomerNewsTranslationsByNewsId(customerNewsId);


            return PartialView("_CustomerNewsTranslationsGridView", translations);
        }

        public ActionResult ShowUploadPopup(int customerNewsId, string url, string field, int languageId = -1)
        {
            ViewData["CustomerNewsID"] = customerNewsId;
            ViewData["url"] = url;
            ViewData["field"] = field;
            ViewData["languageId"] = languageId;
            return PartialView("CustomerUploadPopupContent");
        }

        public ActionResult FileUpload(int customerNewsId, string field, int languageId)
        {
            UploadedFile[] files = UploadControlExtension.GetUploadedFiles("uploadControl");

            // Your custom logic to process uploaded files.
            if (files != null && files.Length > 0)
            {
                UploadedFile file = files[0];
                
                string filePath = ConfigurationManager.AppSettings["FilePath"];
                string resultFileUrl;
                if (filePath == "Azure")
                {
                    IBlobStorageClientFactory factory = new BlobStorageClientFactory();
                    IBlobStorageClient client = factory.CreateCustomerNewsFilesBlobStorageClient(customerNewsId);
                    var uploadResult = client.Upload(file.FileName, file.ContentType, file.FileContent);
                    resultFileUrl = uploadResult.Url;
                }
                else
                {
                    string uploadDirectory = "~/Upload/CustomerNews/"; // Directly upload the file to the specific directory
                    resultFileUrl = uploadDirectory + file.FileName;
                    string resultFilePath = Request.MapPath(resultFileUrl);

                    file.SaveAs(resultFilePath);
                    resultFileUrl = ConfigurationManager.AppSettings["FilePath"] + "CustomerNews/" + file.FileName;
                }

                _unitOfWork.CustomerNewsRepository.UpdateUploadedFileUrl(customerNewsId, field, resultFileUrl, languageId);
            }




            return null;
        }

        public ActionResult FileDelete(int customerNewsId, string field, int languageId)
        {
            ViewData["CustomerNewsID"] = customerNewsId;
            ViewData["field"] = field;
            ViewData["languageId"] = languageId;

            _unitOfWork.CustomerNewsRepository.UpdateUploadedFileUrl(customerNewsId, field, null, languageId);

            return PartialView("CustomerUploadPopupContent");
        }

        /// <summary>
        /// Inner Class for the Gridview Settings (this avoids a separat defninition of the settings in the view and the export).
        /// </summary>
        public static class BaseGridViewSettings
        {
            static NeoSysLCS_Dev context = new NeoSysLCS_Dev();
            private static int _currentLang;

            public static string GetGridViewName()
            {
                return "CustomerNewsGridView";
            }

            public static string GetGridViewKeyFieldName()
            {
                return "CustomerNewsID";

            }

            /// <summary>
            /// Gets the definition of the columns
            /// </summary>
            /// <param name="viewContext">The view context.</param>
            /// <returns></returns>
            public static List<MVCxGridViewColumn> GetDataColumns(ViewContext viewContext, IQueryable<CustomerNewsViewModel> model)
            {
                IUnitOfWork unitOfWork = new UnitOfWork();
                SessionHelper sessionHelper = new SessionHelper();

                var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
                Sprache sprache = context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
                _currentLang = sprache.SpracheID;

                var columns = new MVCxGridViewColumnCollection();

                columns.Add(column =>
                {
                    column.FieldName = "CustomerNewsID";
                    column.Caption = "NewsID";
                    column.EditFormSettings.Visible = DefaultBoolean.False;
                });

                columns.Add(column =>
                {
                    column.FieldName = "FromDate";
                    column.Caption = "Datum Von";
                    column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    column.PropertiesEdit.DisplayFormatString = "d";
                });

                columns.Add(column =>
                {
                    column.FieldName = "ToDate";
                    column.Caption = "Datum Bis";
                    column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    column.PropertiesEdit.DisplayFormatString = "d";
                });


                //make a list from the MCxGridViewColumnCollection otherwise settings.Columns.Add(col) won't work --> DevExpress 16.2.15
                var columList = new List<MVCxGridViewColumn>();
                foreach (MVCxGridViewColumn column in columns)
                {
                    columList.Add(column);
                };

                return columList;

            }
        }
    }
}