using System;
using System.Collections.Generic;
using System.Web.Mvc;
using System.Web.SessionState;
using DevExpress.Web.Mvc;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Helpers;

namespace NeoSysLCS.Site.Areas.Admin.Controllers
{
    [SessionState(SessionStateBehavior.ReadOnly)]
    public class RechtsbereicheController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;

        public RechtsbereicheController()
        {
            _unitOfWork = new UnitOfWork();
        }

        public RechtsbereicheController(IUnitOfWork unitOfWorkCandidate)
        {
            _unitOfWork = unitOfWorkCandidate;
        }


        public ActionResult Index()
        {
            var viewModels = _unitOfWork.RechtsbereichRepository.GetAllRechtsbereichViewModels();
            return View( "Index", viewModels);
        }


        /// <summary>
        /// Shows all rechtsbereiche
        /// </summary>
        /// <returns></returns>
        public ActionResult RechtsbereicheGridView()
        {
            var viewModels = _unitOfWork.RechtsbereichRepository.GetAllRechtsbereichViewModels();

            return PartialView("RechtsbereicheGridView", viewModels);
        }

        /// <summary>
        /// Saves the changes of rechtsbereiche
        /// </summary>
        /// <param name="updateValues">The update values.</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult RechtsbereicheGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<RechtsbereichViewModel, int> updateValues)
        {

            foreach (var rechtsbereich in updateValues.Insert)
            {

                GridViewUpdateHelper<Rechtsbereich, RechtsbereichViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(rechtsbereich))
                    {
                        _unitOfWork.RechtsbereichRepository.Insert(rechtsbereich);
                        _unitOfWork.Save();
                    }
                }, rechtsbereich, updateValues);


            }
            foreach (var rechtsbereich in updateValues.Update)
            {

                GridViewUpdateHelper<Rechtsbereich, RechtsbereichViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(rechtsbereich))
                    {
                        _unitOfWork.RechtsbereichRepository.Update(rechtsbereich);
                        _unitOfWork.Save();
                    }
                }, rechtsbereich, updateValues);

            }
            foreach (var rechtsbereichID in updateValues.DeleteKeys)
            {
                GridViewUpdateHelper<Rechtsbereich, RechtsbereichViewModel>.DoDelete(() =>
                {
                    _unitOfWork.RechtsbereichRepository.Delete(rechtsbereichID);
                    _unitOfWork.Save();
                }, _unitOfWork.RechtsbereichRepository, rechtsbereichID, updateValues);
            }

            var viewModels = _unitOfWork.RechtsbereichRepository.GetAllRechtsbereichViewModels();
            return PartialView("RechtsbereicheGridView", viewModels);
        }


        /// <summary>
        /// Shows the uebersetzungen of the specified rechtsbereich
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult RechtsbereichUebersetzungenGridView(int id)
        {
            //IEnumerable<Sprache> sprachen = _unitOfWork.SpracheRepository.Get();
            //_unitOfWork.RechtsbereichUebersetzungRepository.PopulatedRechtsbereichUebersetzungenByRechtsbereich(id, sprachen);
            //_unitOfWork.Save();

            var uebersetzungen = _unitOfWork.RechtsbereichRepository.GetRechtsbereichUebersetzungenByRechtsbereich(id);

            ViewData["RechtsbereichID"] = id;
            return PartialView("RechtsbereichUebersetzungenGridView", uebersetzungen);
        }

        /// <summary>
        /// Saves the changes of the uebersetzunen
        /// </summary>
        /// <param name="updateValues">The update values.</param>
        /// <param name="rechtsbereichID">The rechtsbereich identifier.</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult RechtsbereichUebersetzungenGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<RechtsbereichViewModel, int> updateValues, int rechtsbereichID)
        {
            foreach (var uebersetzung in updateValues.Update)
            {

                GridViewUpdateHelper<Rechtsbereich, RechtsbereichViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(uebersetzung))
                    {
                        uebersetzung.RechtsbereichID = rechtsbereichID;
                        uebersetzung.BearbeitetAm = DateTime.Now;
                        uebersetzung.BearbeitetVonID = System.Web.HttpContext.Current.User.Identity.GetUserId();
                        _unitOfWork.RechtsbereichRepository.UpdateRechtsbereichUebersetzung(uebersetzung);
                        _unitOfWork.Save();
                    }
                }, uebersetzung, updateValues);

            }

            ViewData["RechtsbereichID"] = rechtsbereichID;
            var uebersetzungen = _unitOfWork.RechtsbereichRepository.GetRechtsbereichUebersetzungenByRechtsbereich(rechtsbereichID);

            return PartialView("RechtsbereichUebersetzungenGridView", uebersetzungen);
        }

    }


}
