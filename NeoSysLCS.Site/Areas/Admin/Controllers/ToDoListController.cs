using System.Collections.Generic;
using System.Web.Mvc;
using System.Web.SessionState;
using DevExpress.Web.Mvc;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Site.Helpers;

namespace NeoSysLCS.Site.Areas.Admin.Controllers
{
    [SessionState(SessionStateBehavior.ReadOnly)]
    public class ToDoListController : Controller
    {
        private readonly IUnitOfWork _unitOfWork;

        public ToDoListController()
        {
            _unitOfWork = new UnitOfWork();
        }

        public ToDoListController(IUnitOfWork unitOfWorkCandidate)
        {
            _unitOfWork = unitOfWorkCandidate;
        }

        public ActionResult Index()
        {
            var userID = User.Identity.GetUserId();
            var todos = _unitOfWork.TodoRepository.GetToDoViewModelsForUser(userID);
            return View("Index", todos);
        }
        
        /// <summary>
        /// Shows the to-do's gridview
        /// </summary>
        /// <returns></returns>
        public ActionResult ToDoListGridView()
        {
            var userID = User.Identity.GetUserId();
            var todos = _unitOfWork.TodoRepository.GetToDoViewModelsForUser(userID);

            return PartialView("_ToDoListGridView", todos);
        }
        
    }
}