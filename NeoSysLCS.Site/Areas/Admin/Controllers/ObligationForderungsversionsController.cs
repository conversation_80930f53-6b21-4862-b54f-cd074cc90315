using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web.Mvc;
using System.Web.SessionState;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Site.Controllers;

namespace NeoSysLCS.Site.Areas.Admin.Controllers
{
    [SessionState(SessionStateBehavior.ReadOnly)]
    public class ObligationForderungsversionsController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;

        public ObligationForderungsversionsController()
        {
            _unitOfWork = new UnitOfWork();
        }

        public ObligationForderungsversionsController(IUnitOfWork unitOfWorkCandidate)
        {
            _unitOfWork = unitOfWorkCandidate;
        }

        public ActionResult Index(int obligationId, string view)
        {
            var obligation = _unitOfWork.ObligationRepository.GetByID(obligationId);
            ViewData["ObligationID"] = obligationId;
            ViewData["ObjektID"] = obligation.ObjektID;
            ViewData["ObligationName"] = obligation.Name;
            ViewData["ObligationView"] = view;
            return View("Index", _unitOfWork.ForderungsversionRepository.GetByObject(obligation.ObjektID));
        }

        /// <summary>
        /// Shows the listbox with the selected values
        /// </summary>
        /// <param name="obligationId">The identifier.</param>
        /// <returns></returns>
        public ActionResult ObligationForderungsversionsPartial(int obligationId, string view)
        {
            var obligation = _unitOfWork.ObligationRepository.GetByID(obligationId);
            ViewData["ObligationID"] = obligationId;
            ViewData["ObjektID"] = obligation.ObjektID;
            ViewData["ObligationName"] = obligation.Name;
            ViewData["ObligationView"] = view;
            return PartialView("_ObligationForderungsversionsPartial");
        }

        /// <summary>
        /// Shows the gridview with the Forderungsversions that can be selected
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        public ActionResult ForderungsversionsSelectionPartial(int obligationId, string view)
        {
            string selectedIDs = Request.Params["selectedIDsHF"];
            var obligation = _unitOfWork.ObligationRepository.GetByID(obligationId);
            ViewData["_selectedIDs"] = selectedIDs;
            ViewData["ObligationID"] = obligationId;
            ViewData["ObjektID"] = obligation.ObjektID;
            ViewData["ObligationName"] = obligation.Name;
            ViewData["ObligationView"] = view;
            return PartialView("_ForderungsversionsSelectionPartial");
        }

        public ActionResult SaveObligationForderungsversions(string view)
        {
            //Get all selected keys from e.customArgs on GridView callback
            string selectedIDs = Request.Params["selectedIDsHF"];
            int obligationId = Convert.ToInt32(Request.Params["id"]);
            ViewData["_selectedIDs"] = selectedIDs;
            ViewData["ObligationID"] = obligationId;

            _unitOfWork.ObligationRepository.SaveObligationForderungsversions(selectedIDs, obligationId);
            _unitOfWork.Save();


            var obligation = _unitOfWork.ObligationRepository.GetByID(obligationId);
            ViewData["ObjektID"] = obligation.ObjektID;
            ViewData["ObligationName"] = obligation.Name;

            if (view == "Obligation")
            {
                return Json(new { Url = Url.Action("Index", "Obligation") });
            }
            else
            {
                return Json(new { Url = Url.Action("Index", "ObjektObligation", new { objektID = obligation.ObjektID }) });
            }
        }
    }
}
