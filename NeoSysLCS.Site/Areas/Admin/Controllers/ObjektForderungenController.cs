using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web.Mvc;
using System.Web.SessionState;
using DevExpress.Web.Mvc;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Site.Helpers;
using NeoSysLCS.Site.Utilities.Export;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Repositories.ViewModels;
using DevExpress.Web;
using System.Web.UI.WebControls;
using System.Xml.Linq;
using NeoSysLCS.Repositories.Helper;
using DevExpress.Utils;
using DevExpress.Data;
using DevExpress.XtraGrid;
using System.Web.UI;
using System.Net;

namespace NeoSysLCS.Site.Areas.Admin.Controllers
{
    [SessionState(SessionStateBehavior.ReadOnly)]
    public class ObjektForderungenController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;
        private static NeoSysLCS_Dev context = new NeoSysLCS_Dev();
        private static int _currentLang;

        public ObjektForderungenController()
        {
            _unitOfWork = new UnitOfWork();
        }

        public ObjektForderungenController(IUnitOfWork unitOfWorkCandidate)
        {
            _unitOfWork = unitOfWorkCandidate;
        }

        /// <summary>
        /// Shows the listbox with the selected values
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        public ActionResult ObjektForderungenPartial(int id)
        {
            ViewData["ObjektID"] = id;
            
            return PartialView("_ObjektForderungenPartial", _unitOfWork.ForderungsversionRepository.GetByObject(id));
        }

        /// <summary>
        /// Shows the gridview with the forderungen that can be selected
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        public ActionResult ForderungSelectionPartial(int id)
        {
            string selectedIDs = Request.Params["selectedIDsHF"];
            ViewData["_selectedIDs"] = selectedIDs;
            ViewData["ObjektID"] = id;
            return PartialView("_ForderungSelectionPartial");
        }


        /// <summary>
        /// Saves the selected forderungen in the objekt.
        /// </summary>
        /// <returns></returns>
        public ActionResult SaveForderungen()
        {
            //Get all selected keys from e.customArgs on GridView callback
            string selectedIDs = Request.Params["selectedIDsHF"];
            string objektID = Request.Params["id"];
            ViewData["_selectedIDs"] = selectedIDs;
            ViewData["ObjektID"] = objektID;

            _unitOfWork.ObjektRepository.SaveForderungen(selectedIDs, objektID);
            _unitOfWork.Save();


            return Content(Resources.Properties.Resources.Notification_Zuordnungen_Gespeichert);
        }

        public ActionResult ExportTo(int objektId)
        {
            var viewModels = _unitOfWork.ForderungsversionRepository.GetAllForderungsversionViewModels(objektId).Where(x => x.isSelected).ToList();

            foreach (var viewModel in viewModels)
            {
                viewModel.Beschreibung = WebUtility.HtmlDecode(StringHelper.StripAllTags(viewModel.Beschreibung));
                viewModel.InternerKommentar = WebUtility.HtmlDecode(StringHelper.StripAllTags(viewModel.InternerKommentar));
            }

            var exportType = ExportType.Xlsx;
            var exportResult = ExportUtilities.Export(exportType, GetExportGridSettings(exportType, objektId), viewModels, false);

            if (exportResult != null)
            {
                return exportResult;
            }

            string selectedIDs = Request.Params["selectedIDsHF"];
            ViewData["_selectedIDs"] = selectedIDs;
            ViewData["ObjektID"] = objektId;
            return PartialView("_ForderungSelectionPartial");
        }

        private GridViewSettings GetExportGridSettings(ExportType exportType, int objektId)
        {
            var settings = new GridViewSettings();
            settings.Name = "gvRowForderungenSelection_" + objektId;
            settings.KeyFieldName = "ForderungsversionID";

            //columns: there need to be the same colums in the view and for the export for sorting
            settings.CommandColumn.Visible = true;
            foreach (MVCxGridViewColumn col in GetDataColumns(objektId))
            {
                settings.Columns.Add(col);
            }

            //Exportsettings
            GridViewHelper.ApplyDefaultExportSettings(settings, exportType);

            return settings;
        }

        public static List<MVCxGridViewColumn> GetDataColumns(int objektId)
        {
            IUnitOfWork unitOfWork = new UnitOfWork();
            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = context.Sprachen.FirstOrDefault(s => s.Lokalisierung == language);
            _currentLang = sprache.SpracheID;

            var columns = new MVCxGridViewColumnCollection();

            columns.Add(column =>
            {
                column.Caption = "Erlass";
                column.FieldName = "ErlassID";
                column.ColumnType = MVCxGridViewColumnType.ComboBox;
                column.Settings.FilterMode = DevExpress.Web.ColumnFilterMode.DisplayText;
                column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.CheckedList;
                List<ErlassViewModel> viewModels = (from e in context.Erlasse
                                                    select new ErlassViewModel()
                                                    {
                                                        ErlassID = e.ErlassID,
                                                        Uebersetzung = e.Uebersetzung
                                                    }).ToList();

                foreach (ErlassViewModel viewModel in viewModels)
                {
                    XDocument uebersetzung = XDocument.Parse(viewModel.Uebersetzung);
                    viewModel.Titel = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Titel", _currentLang);
                }

                var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
                comboBoxProperties.DataSource = viewModels.ToList();
                comboBoxProperties.TextField = "Titel";
                comboBoxProperties.ValueField = "ErlassID";
                comboBoxProperties.ValueType = typeof(int);
            });
            columns.Add(column =>
            {
                column.Width = Unit.Pixel(50);
                column.Caption = "SR";
                column.FieldName = "ErlassSrNummer";
            });
            columns.Add(column =>
            {
                column.Width = Unit.Pixel(50);
                column.Caption = "Version";
                column.FieldName = "VersionsNummer";
            });

            columns.Add(column =>
            {
                column.Caption = "Forderung Beschreibung";
                column.FieldName = "Beschreibung";
                column.ColumnType = MVCxGridViewColumnType.Memo;
                var memoProp = column.PropertiesEdit as MemoProperties;
                if (memoProp != null)
                {
                    memoProp.EncodeHtml = false;
                }
            });

            columns.Add(column =>
            {
                column.FieldName = "ArtikelNummer";
                column.Caption = Resources.Properties.Resources.Entitaet_Artikel_Singular;
                column.ColumnType = MVCxGridViewColumnType.ComboBox;
                column.Settings.FilterMode = DevExpress.Web.ColumnFilterMode.DisplayText;
                //IEnumerable<Artikel> artikel = unitOfWork.ArtikelRepository.GetByErlass(erlassfassung.ErlassID).ToList();

                //var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
                //if (comboBoxProperties != null)
                //{
                //    comboBoxProperties.DataSource = artikel;
                //    comboBoxProperties.TextField = "Nummer";
                //    comboBoxProperties.ValueField = "ArtikelID";
                //    comboBoxProperties.ValueType = typeof(int);
                //}
            });

            columns.Add(column =>
            {
                column.FieldName = "InternerKommentar";
                column.Name = "InternerKommentar";
                column.Caption = Resources.Properties.Resources.Allgemein_InternerKommentar;
                column.Width = Unit.Percentage(25);
                column.ExportWidth = 200;

                column.ColumnType = MVCxGridViewColumnType.Memo;
                var memoProp = column.PropertiesEdit as MemoProperties;
                if (memoProp != null)
                {
                    memoProp.EncodeHtml = false;
                }
            });

            var columList = new List<MVCxGridViewColumn>();
            foreach (MVCxGridViewColumn column in columns)
            {
                columList.Add(column);
            };

            return columList;
        }

    }
}
