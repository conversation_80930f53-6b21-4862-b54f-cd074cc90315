@using System.Web.UI.WebControls
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers
@model IEnumerable<NeoSysLCS.Repositories.ViewModels.FAQViewModel>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title - NeoSysLCS</title>
    @Styles.Render("~/Content/css")
    @*@Scripts.Render("~/bundles/modernizr")
    @Scripts.Render("~/Scripts/helpers/SelectionHelper.js")*@
</head>
<body style="background-color: #fff;">

    @{
        var sessionhelper = new SessionHelper();
        var unitOfWork = new UnitOfWork();
        //var faq = unitOfWork.FAQRepository.GetAllFAQViewModels();
        foreach (var entry in Model)
        {
            //ViewContext.Writer.Write("<br>");
            var url = entry.Quelle;
            string frage = "";

            if (entry.Status == FAQStatus.New)
            {
                frage = Resources.Enum_FAQStatus_New + ": " + entry.Frage;
            }
            else if (entry.Status == FAQStatus.Edited)
            {
                frage = Resources.Enum_FAQStatus_Edited + ": " + entry.Frage;
            }
            else {
                frage = entry.Frage;
            }
            string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, frage);
            ViewContext.Writer.Write("<li style='padding-left:1.28571429em; text-indent:-1.28571429em; list-style-position:inside;'>");
            ViewContext.Writer.Write(StringHelper.TruncateHtml(htmlLink, 500));
            ViewContext.Writer.Write("</li>");

            ViewContext.Writer.Write("</br>");
        }
    }

</body>
</html>