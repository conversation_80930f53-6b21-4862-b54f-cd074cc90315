@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Areas.Admin.Controllers
@using NeoSysLCS.Site.Utilities.Export
@model IQueryable<NeoSysLCS.Repositories.ViewModels.StandortBerichtViewModel>
@{
    ViewBag.Title = Resources.View_Bericht_Name;
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}


@Html.Partial("_StandorteMitBerichtGridView", Model)
