@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Areas.Admin.Controllers
@using NeoSysLCS.Site.Utilities.Export
@model IQueryable<KundendokumentViewModel>
@{
    ViewBag.Title = Resources.View_All_Documents_Title;
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}


@Html.Partial("_AllDocumentsGridView", Model)

<script type="text/javascript">
    function ShowDetailPopup(url, win) {
        modal = eval(win);

        if (window.height < 600) {
            modal.SetHeight(window.height - 50);
        } else {
            modal.SetHeight(600);
        }

        if (window.width < 800) {
            modal.SetWidth(window.width - 50);
        } else {
            modal.SetWidth(800);
        }

        modal.SetContentUrl(url);
        modal.Show();
    }
</script>


@Html.DevExpress().PopupControl(settings =>
     {

         settings.Name = "pcModalMode_FAQ_Downloadbereich";
         settings.ShowPageScrollbarWhenModal = true;
         settings.ResizingMode = ResizingMode.Live;
         settings.ShowRefreshButton = false;
         settings.AutoUpdatePosition = true;
         settings.AllowDragging = true;
         settings.CloseAction = CloseAction.CloseButton;
         settings.PopupAnimationType = AnimationType.None;
         settings.HeaderText = "FAQ";
         settings.Modal = true;
         settings.AllowResize = true;
         settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
         settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
         settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(400); }";
         settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(400); }";
         }).GetHtml()