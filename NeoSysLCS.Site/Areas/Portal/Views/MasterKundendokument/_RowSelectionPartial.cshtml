@using System.Web.UI.WebControls;
@using DevExpress.XtraGrid
@using NeoSysLCS.Repositories

@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Areas.Admin.Controllers
@using NeoSysLCS.Site.Helpers

@model IQueryable<NeoSysLCS.Repositories.ViewModels.NewStandortObjektViewModel>

@{
   List<int> kundendokumentIDs = (List<int>)ViewData["KundendokumentIDs"];
   string commaSeparatedKundendokumentIDs = string.Join(",", kundendokumentIDs);
}


@Html.DevExpress().TreeList(
    settings =>
    {
        settings.Name = "NewStandortObjektGrid";
        settings.CallbackRouteValues = new { Controller = "MasterKundendokument", Action = "NewStandortObjekteGridView", kundendokumentIDs = commaSeparatedKundendokumentIDs, spracheID = (int)ViewData["SpracheID"] };
        settings.Width = Unit.Percentage(100);

        settings.AutoGenerateColumns = false;
        settings.KeyFieldName = "ObjektkategorieID";
        settings.ParentFieldName = "ParentObjektkategorieID";
        //settings.RootValue = 0;


        settings.Columns.Add(column =>
        {
            column.FieldName = "Name";
            column.Caption = Resources.Colunm_Name;
        });
        settings.Columns.Add(column =>
        {
            column.FieldName = "Beschreibung";
            column.Caption = TranslationHelper.GetTranslation(typeof(ObjektViewModel), column.FieldName);
            column.Visible = false;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "ObjektID";
            column.Visible = false;
        });

        settings.SettingsBehavior.AutoExpandAllNodes = false;
        //settings.SettingsBehavior.ExpandCollapseAction = TreeListExpandCollapseAction.NodeDblClick;

        settings.SettingsCookies.Enabled = false;
        settings.SettingsCookies.StoreExpandedNodes = false;
        settings.SettingsCookies.StoreSelection = false;

        settings.SettingsSelection.Enabled = true;
        settings.SettingsSelection.Recursive = true;
        settings.SettingsSelection.AllowSelectAll = true;

        settings.ClientSideEvents.SelectionChanged = "function(s, e) { s.PerformCallback(); }";


    }
).Bind(Model).GetHtml()


