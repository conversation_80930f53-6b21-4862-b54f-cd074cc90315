@using System.Web.UI.WebControls
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Repositories


@model IQueryable<StandortViewModel>


@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        IUnitOfWork unitOfWork = new UnitOfWork();

        settings.Name = "StandortAdministration";
        settings.KeyFieldName = "StandortID";

        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        settings.SettingsContextMenu.Enabled = false;
        settings.Settings.ShowGroupPanel = false;
        settings.Settings.ShowFilterRow = false;
        settings.CallbackRouteValues = new { Controller = "Auswertung", Action = "StandortAdministration", id = ViewData["StandortID"] };

        settings.Columns.Add(column =>
        {
            column.FieldName = "Name";
            column.Caption = Resources.View_Standort_Name;
            column.SetDataItemTemplateContent(container => Html.DevExpress().HyperLink(hyperlink =>
            {
                var keyValue = container.KeyValue;
                hyperlink.Name = "Standort_" + keyValue;
                hyperlink.Properties.Text = Resources.Menu_Auswertung + " " + Model.Where(y => y.StandortID == (int)keyValue).Select(x => x.Name).FirstOrDefault();
                hyperlink.NavigateUrl = Url.Action("StandortIndex", "Auswertung", new { id = DataBinder.Eval(container.DataItem, "StandortID") });
            }).Render());
        });

        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);
    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "StandortID";
}).GetHtml()