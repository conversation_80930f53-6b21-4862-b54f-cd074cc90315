@using System.Web.UI.WebControls
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Utilities.Export
@using NeoSysLCS.Site.Areas.Portal.Controllers
@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties

@Html.DevExpress().PageControl(pageControlSettings =>
{
    UnitOfWork unitOfWork = new UnitOfWork();
    pageControlSettings.Name = "KundendokumentPageControl";
    pageControlSettings.Width = Unit.Percentage(100);
    pageControlSettings.SaveStateToCookies = true;
    pageControlSettings.CallbackRouteValues = new { Controller = "Kundendokument", Action = "KundendokumentPageControlCallbacksPartial", standortID = ViewData["StandortID"], kundendokumentID = ViewData["KundendokumentID"], spracheID = ViewData["SpracheID"] };
    pageControlSettings.ClientSideEvents.ActiveTabChanging = "OnActiveTabChanging";
    //@(kundendok)
    pageControlSettings.TabPages.Add(Resources.View_Kundendokument_TabForderungen).SetContent(() =>
    {
        ViewData["Ansicht"] = "Forderungen";
        ViewContext.Writer.Write("<div style='display:inline-block'>");
        ViewContext.Writer.Write("<h2 style='display:inline-block'>" + Resources.View_Kundendokument_TabForderungen + "</h2>");
        ViewContext.Writer.Write("<button type='button' class='btn btn-default' style='border: none; background-color: transparent; outline: none; padding-left: 5px; padding-right: 0px; padding-bottom:20px; padding-top: 5px' onclick=" + "javascript:ShowDetailPopup('/Portal/FAQPortal/ShowFAQPopup?view=Forderungen','pcModalMode_FAQ_" + ViewData["Ansicht"] + "');>" + "<img src='/Content/images/Question_Mark.png' style='width: 20px; height: 20px' /></button>");
        ViewContext.Writer.Write("</div>");
        ViewContext.Writer.Write("<div>");

        ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='PortalKundendokumentForderungenGridView.CollapseAll();return false;' >" +
                                 "<i class='fa fa-minus-square'></i>&nbsp;" +
                                    Resources.Allgemein_CollapseAllRows +
                                 "</a>&nbsp;");
        ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='PortalKundendokumentForderungenGridView.ExpandAll();return false;' >" +
                               "<i class='fa fa-plus-square'></i>&nbsp;" +
                                  Resources.Allgemein_ExpandAllRows +
                               "</a>&nbsp;");
        ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='PortalKundendokumentForderungenGridView.ShowCustomizationDialog();return false;' >" +
                       "<i class='fa fas fa-cog'></i>&nbsp;" +
                          Resources.View_KundendokumentForderungen_Dialog +
                       "</a>&nbsp;");
        ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='javascript:ShowNewPopup(this)'; >" +
               "<i class='fa fas fa-filter'></i>&nbsp;" +
                  Resources.Filter_Standortobjekt +
               "</a>&nbsp;");

        ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='javascript:ShowNewPopupExportForderungen(this)'; >" +
              "<i class='fa fas fa-filter'></i>&nbsp;" +
                 Resources.XLSX_Export_Format +
              "</a>&nbsp;");

        // using (Html.BeginForm("ForderungenExportDataAware", "Kundendokument", new { kundendokumentId = ViewData["KundendokumentID"] }, FormMethod.Post, new { target = "_blank", id = "ErlasseExportWrapperForm", style = "display:inline-block" }))
        // {
        // ViewContext.Writer.Write("<button class='btn btn-default' type='submit' value='Xlsx' name='exportFormat'>" + "<i class='fa fa-download'></i>&nbsp;" + Resources.XLSX_Export_Format + "</button>");
        ViewContext.Writer.Write("</div>");

        KundendokumentController kundendokumentController = new KundendokumentController();
        var forderungenViewModels = kundendokumentController.InitializeKundendokumentForderungenGridView(Convert.ToInt32(ViewData["StandortID"]), Convert.ToInt32(ViewData["KundendokumentID"]), false, Convert.ToInt32(ViewData["SpracheID"]));

        Html.RenderPartial("KundendokumentForderungenGridView", forderungenViewModels);
        // }
    });
    pageControlSettings.TabPages.Add(Resources.View_Kundendokument_TabErlassfassungen).SetContent(() =>
    {
        ViewData["Ansicht"] = "Gesetzesliste";
        ViewContext.Writer.Write("<div style='display:inline-block'>");
        ViewContext.Writer.Write("<h2 style='display:inline-block'>" + Resources.View_Kundendokument_TabErlassfassungen + "</h2>");
        ViewContext.Writer.Write("<button type='button' class='btn btn-default' style='border: none; background-color: transparent; outline: none; padding-left: 5px; padding-right: 0px; padding-bottom:20px; padding-top: 5px' onclick=" + "javascript:ShowDetailPopup('/Portal/FAQPortal/ShowFAQPopup?view=Gesetzesliste','pcModalMode_FAQ_" + ViewData["Ansicht"] + "');>" + "<img src='/Content/images/Question_Mark.png' style='width: 20px; height: 20px' /></button>");
        ViewContext.Writer.Write("</div>");

        ViewContext.Writer.Write("<div>");
        ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='ErlassfassungenGridView.CollapseAll();return false;' >" +
                                 "<i class='fa fa-minus-square'></i>&nbsp;" +
                                    Resources.Allgemein_CollapseAllRows +
                                 "</a>&nbsp;");
        ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='ErlassfassungenGridView.ExpandAll();return false;' >" +
                               "<i class='fa fa-plus-square'></i>&nbsp;" +
                                  Resources.Allgemein_ExpandAllRows +
                               "</a>&nbsp;");
        ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='ErlassfassungenGridView.ShowCustomizationDialog();return false;' >" +
                      "<i class='fa fas fa-cog'></i>&nbsp;" +
                         Resources.View_KundendokumentForderungen_Dialog +
                      "</a>&nbsp;");
        ViewContext.Writer.Write("<a class='btn btn-default' type='button' onclick='javascript:ShowNewPopupExportErlassfassung(this)'; >" +
              "<i class='fa fas fa-filter'></i>&nbsp;" +
                 Resources.XLSX_Export_Format +
              "</a>&nbsp;");
        var erlassfassungViewModels = unitOfWork.KundendokumentErlassfassungRepository.GetKundendokumentErlassfassungViewModelsByKundendokument(Convert.ToInt32(ViewData["KundendokumentID"]));

        //Html.RenderPartial("KundendokumentErlassfassungenGridView", erlassfassungViewModels);
        //ViewContext.Writer.Write("</div>");


        //using (Html.BeginForm("ErlassfassungExportDataAware", "Kundendokument", new { kundendokumentId = ViewData["KundendokumentID"] }, FormMethod.Post, new { target = "_blank", id = "ErlasseExportWrapperForm", style = "display:inline-block" }))
        //{
        //    ViewContext.Writer.Write("<button class='btn btn-default' type='submit' value='Xlsx' name='exportFormat'>" + "<i class='fa fa-download'></i>&nbsp;" + Resources.XLSX_Export_Format + "</button>");
        //    ViewContext.Writer.Write("</div>");


        //}
        ViewContext.Writer.Write("</div>");

        Html.RenderAction(
             "KundendokumentErlassfassungenGridView",
             "Kundendokument",
             new { standortID = ViewData["StandortID"], kundendokumentID = ViewData["KundendokumentID"], spracheID = ViewData["SpracheID"] });
    });
    pageControlSettings.TabPages.Add(Resources.View_KundendokumentQS_RemovedItems).SetContent(() =>
    {
        ViewData["Ansicht"] = "EntForderungen";
        ViewContext.Writer.Write("<div style='display:inline-block'>");
        ViewContext.Writer.Write("<h3 style='display:inline-block'>" + Resources.View_KundendokumentQs_RemovedForderungen + "</h3>");
        ViewContext.Writer.Write("<button type='button' class='btn btn-default' style='border: none; background-color: transparent; outline: none; padding-left: 5px; padding-right: 0px; padding-bottom:20px; padding-top: 5px' onclick=" + "javascript:ShowDetailPopup('/Portal/FAQPortal/ShowFAQPopup?view=EntfernteForderungen','pcModalMode_FAQ_" + ViewData["Ansicht"] + "');>" + "<img src='/Content/images/Question_Mark.png' style='width: 20px; height: 20px' /></button>");
        ViewContext.Writer.Write("</div>");

        Html.RenderAction(
           "KundendokumentForderungenGridView",
           "Kundendokument",
           new { standortID = ViewData["StandortID"], kundendokumentID = ViewData["KundendokumentID"], showOnlyRemoved = true, spracheID = ViewData["SpracheID"] });
    });

    pageControlSettings.TabPages.Add(Resources.View_Kundendokument_TabIndividuelleForderungen).SetContent(() =>
    {
        ViewData["Ansicht"] = "IndForderungen";
        ViewContext.Writer.Write("<div style='display:inline-block'>");
        ViewContext.Writer.Write("<h3 style='display:inline-block'>" + Resources.Entitaet_IndividuelleForderung_Plural + "</h3>");
        ViewContext.Writer.Write("<button type='button' class='btn btn-default' style='border: none; background-color: transparent; outline: none; padding-left: 5px; padding-right: 0px; padding-bottom:20px; padding-top: 5px' onclick=" + "javascript:ShowDetailPopup('/Portal/FAQPortal/ShowFAQPopup?view=IndividuelleForderungen','pcModalMode_FAQ_" + ViewData["Ansicht"] + "');>" + "<img src='/Content/images/Question_Mark.png' style='width: 20px; height: 20px' /></button>");
        ViewContext.Writer.Write("</div>");
        ViewContext.Writer.Write("<p>" + Resources.View_Kundendokument_IndividuelleForderungen_Explanation + "</p>");
        Html.RenderAction("IndividuelleForderungenGridView", "IndividuelleForderungen", new { id = ViewData["StandortID"], kundendokumentId = Convert.ToInt32(ViewData["KundendokumentID"]), read = false });
    });

    pageControlSettings.TabPages.Add(Resources.View_Kundendokument_Tab_SUVA).SetContent(() =>
    {
        ViewData["Ansicht"] = "Checkliste";
        ViewContext.Writer.Write("<div style='display:inline-block'>");
        ViewContext.Writer.Write("<h2 style='display:inline-block'>" + Resources.View_Kundendokument_Tab_SUVA + "</h2>");
        ViewContext.Writer.Write("<button type='button' class='btn btn-default' style='border: none; background-color: transparent; outline: none; padding-left: 5px; padding-right: 0px; padding-bottom:20px; padding-top: 5px' onclick=" + "javascript:ShowDetailPopup('/Portal/FAQPortal/ShowFAQPopup?view=Forderungen','pcModalMode_FAQ_" + ViewData["Ansicht"] + "');>" + "<img src='/Content/images/Question_Mark.png' style='width: 20px; height: 20px' /></button>");
        ViewContext.Writer.Write("</div>");
        ViewContext.Writer.Write("<div>");

        Html.RenderAction(
            "KundendokumentChecklisteGridView",
            "Kundendokument",
            new { standortID = ViewData["StandortID"], kundendokumentID = ViewData["KundendokumentID"], spracheID = ViewData["SpracheID"] });
    });

}).GetHtml()

<script type="text/javascript">
    //<![CDATA[

    function ShowDetailPopup(url, win) {
        modal = eval(win);

        if (window.height < 600) {
            modal.SetHeight(window.height - 50);
        } else {
            modal.SetHeight(600);
        }

        if (window.width < 800) {
            modal.SetWidth(window.width - 50);
        } else {
            modal.SetWidth(800);
        }

        modal.SetContentUrl(url);
        modal.Show();
    }
    // ]]>
</script>

@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "pcModalMode_FAQ_" + ViewData["Ansicht"];
    //settings.ScrollBars = ScrollBars.Auto;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "FAQ";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(400); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(400); }";
}).GetHtml()