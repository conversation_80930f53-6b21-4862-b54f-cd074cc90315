@using System.Drawing
@using System.Web.UI.WebControls
@using DevExpress.Data
@using DevExpress.Data.Filtering
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.Site.Utilities.Export
@using MenuItemStyle = DevExpress.Web.MenuItemStyle

@model IQueryable<KundendokumentForderungsversionViewModel>

@functions{
    public static string HighlightSearchText(string source, string searchText)
    {
        if (string.IsNullOrWhiteSpace(searchText))
            return source;
        var regex = new System.Text.RegularExpressions.Regex(System.Text.RegularExpressions.Regex.Escape(searchText), System.Text.RegularExpressions.RegexOptions.IgnoreCase);
        if (regex.IsMatch(source))
            return string.Format("<span>{0}</span>", regex.Replace(source, "<span class='dxgvHL'>$0</span>"));
        return source;
    }
}

@*@using (Html.BeginForm("ExportTo", "Kundendokument", new { area = "Portal", kundendokumentId = ViewData["KundendokumentID"] }, FormMethod.Post, new { target = "_blank", id = "ErlasseExportWrapperForm" }))
    {
        <div class="divToolbar">
            <div class="divButtonsLeft">
                @foreach (KeyValuePair<ExportType, ExportAction> entry in ExportUtilities.GetExportTypes(new List<ExportType>() { ExportType.Xlsx }))
                {
                    <button class="btn btn-default" type="submit" value="@entry.Key" name="exportFormat">@entry.Value.Title</button>
                }
            </div>
        </div>
    }*@

@{

    var grid = Html.DevExpress().GridView(settings =>
    {
        IUnitOfWork unitOfWork = new UnitOfWork();
        NeoSysLCS_Dev context = new NeoSysLCS_Dev();
        var sessionHelper = new SessionHelper();
        var kundeID = sessionHelper.GetCurrentUser().KundeID;
        var user = unitOfWork.UserRepository.GetUserViewModelById(sessionHelper.GetCurrentUser().Id);
        var kundendokumentID = ViewData["KundendokumentID"];
        bool showOnlyRemoved = ((bool?)ViewData["showOnlyRemoved"]).HasValue && ((bool?)ViewData["showOnlyRemoved"]).Value;
        ViewBag.EnableCheckedListMode = true;
        settings.SettingsPopup.CustomizationWindow.Width = 300;
        settings.SettingsCustomizationDialog.Enabled = true;
        Session["Model"] = Model;
        //settings.Styles.CustomizationDialogColumnChooserPage.CssClass = "customChooserClass";
        //settings.Styles.CustomizationDialogColumnItem.CssClass = "customItemClass";
        settings.Styles.CustomizationDialog.CssClass = "addScroll";
        if (!showOnlyRemoved)
        {
            settings.Name = "PortalKundendokumentForderungenGridView";
            settings.ClientSideEvents.Init = "AdjustSize";
            settings.ClientSideEvents.ColumnResized = "SetHeaderColumnSize";
            settings.ClientSideEvents.EndCallback = "AdjustSize";
        }
        else
        {
            settings.Name = "PortalKundendokumentRemovedForderungenGridView";
        }
        settings.SettingsExport.ExcelExportMode = DevExpress.Export.ExportType.DataAware;
        settings.SettingsExport.EnableClientSideExportAPI = true;

        settings.KeyFieldName = "KundendokumentForderungsversionID";


        var toolbar = new MVCxGridViewToolbar();
        toolbar.Enabled = true;
        toolbar.Visible = true;
        toolbar.Position = GridToolbarPosition.Top;

        var toolbarItemUpdate = new MVCxGridViewToolbarItem();
        toolbarItemUpdate.Command = GridViewToolbarCommand.Update;
        toolbarItemUpdate.DisplayMode = GridToolbarItemDisplayMode.Image;
        toolbarItemUpdate.Image.Url = "/Content/images/save-26.png";
        toolbarItemUpdate.ToolTip = "�nderungen speichern";
        toolbarItemUpdate.ItemStyle.CssClass = "toolbar-button";



        toolbar.Items.Add(toolbarItemUpdate);

        var toolbarItemCancel = new MVCxGridViewToolbarItem();
        toolbarItemCancel.Command = GridViewToolbarCommand.Cancel;
        toolbarItemCancel.DisplayMode = GridToolbarItemDisplayMode.Image;
        toolbarItemCancel.Image.Url = "/Content/images/eraser-24.png";
        toolbarItemCancel.ToolTip = "�nderungen abbrechen";
        toolbarItemCancel.ItemStyle.CssClass = "toolbar-button";

        toolbar.Items.Add(toolbarItemCancel);

        settings.Toolbars.Add(toolbar);


        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsCookies.Enabled = false;

        settings.SettingsContextMenu.Enabled = true;
        settings.SettingsContextMenu.EnableRowMenu = DefaultBoolean.False;

        settings.Styles.Cell.Wrap = DefaultBoolean.True;
        settings.SettingsBehavior.AllowEllipsisInText = true;
        settings.SettingsResizing.ColumnResizeMode = ColumnResizeMode.Control;
        settings.SettingsCookies.StoreColumnsWidth = true;
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;
        settings.Styles.GroupPanel.CssClass = "noWrapClass";
        settings.ClientSideEvents.BatchEditEndEditing = "OnBatchEditEndEditing";
        settings.ClientSideEvents.BatchEditStartEditing = "OnBatchStartEditing";
        if (showOnlyRemoved)
        {
            settings.Settings.ShowGroupPanel = false;
        }


        settings.CallbackRouteValues = new
        {
            Controller = "Kundendokument",
            Action = "KundendokumentForderungenGridView",
            standortID = ViewData["StandortID"],
            kundendokumentID = ViewData["KundendokumentID"],
            showOnlyRemoved = ViewData["showOnlyRemoved"],
            spracheID = ViewData["SpracheID"],
        };


        /*
        settings.Toolbars.Add(t =>
        {
            t.EnableAdaptivity = true;
            t.Items.Add(GridViewToolbarCommand.ExportToXlsx);
        });
    */

        if (unitOfWork.KundendokumentRepository.IsCurrentKundendokument((int?)ViewData["StandortID"], (int?)ViewData["KundendokumentID"]) && !showOnlyRemoved &&
                (ViewData["roleId"].ToString() == "9536dddb-bfaa-47ee-9fba-c0077e38c9d3" || User.IsInRole(Role.Admin) || User.IsInRole(Role.SuperUser) || User.IsInRole(Role.Accountable)))
        {
            GridViewHelper.SetEditableSettingsForKundendokumentforderungsversion(settings, (int)ViewData["StandortID"], (int)ViewData["KundendokumentID"], (int)ViewData["SpracheID"]);
        }
        else if (unitOfWork.KundendokumentRepository.IsCurrentKundendokument((int?)ViewData["StandortID"], (int?)ViewData["KundendokumentID"]) && !showOnlyRemoved &&
                    (User.IsInRole(Role.ReadOnly) && user.WriteStandorte.Count(s => s.StandortID == (int)ViewData["StandortID"]) == 1) || User.IsInRole(Role.EndUser))
        {
            GridViewHelper.SetEditableSettingsForKundendokumentforderungsversion(settings, (int)ViewData["StandortID"], (int)ViewData["KundendokumentID"], (int)ViewData["SpracheID"]);
        }

        //Fixed NEOS-327 Set Visible to true otherwise the cookies are not applied correct if rights change from read to write
        settings.CommandColumn.Visible = true;
        settings.CommandColumn.MinWidth = 85;
        settings.CommandColumn.AllowDragDrop = DefaultBoolean.False;

        settings.CustomUnboundColumnData = (s, e) =>
        {
            if (e.Column.FieldName == "RechtsbereicheUnbound")
            {
                var dataItem = e.GetListSourceFieldValue("Rechtsbereiche");
                IQueryable<RechtsbereichViewModel> rechtsbereiche = dataItem as IQueryable<RechtsbereichViewModel>;
                List<string> tbl = new List<string>();

                foreach (RechtsbereichViewModel t in rechtsbereiche)
                {
                    tbl.Add(t.Name);
                }
                e.Value = String.Join(", ", tbl);
            }
        };

        settings.Columns.Add(column =>
        {
            column.FieldName = "RechtsbereicheUnbound";
            column.MinWidth = 140;
            column.ReadOnly = true;
            column.SetHeaderCaptionTemplateContent("<span class=\"glyphicon glyphicon-lock\" style=\"margin-right: 5px\"></span>" + TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), "Rechtsbereiche"));
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), "Rechtsbereiche");
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.EditFormSettings.Visible = DefaultBoolean.False;

            column.Settings.ShowFilterRowMenu = DefaultBoolean.False;
            column.UnboundType = UnboundColumnType.String;
            column.Settings.AllowSort = DefaultBoolean.False;
            column.SetDataItemTemplateContent(c =>
            {
                var test = c.Grid.SearchPanelFilter;
                var dataItem = DataBinder.Eval(c.DataItem, "Rechtsbereiche");
                if (dataItem != null)
                {
                    IQueryable<RechtsbereichViewModel> rechtsbereiche = dataItem as IQueryable<RechtsbereichViewModel>;
                    List<string> tbl = new List<string>();

                    foreach (RechtsbereichViewModel t in rechtsbereiche)
                    {
                        tbl.Add(t.Name);
                    }

                    Html.DevExpress().Label(labelSettings =>
                    {
                        labelSettings.Name = "RechtsbereicheUnbound" + c.ClientID;
                        labelSettings.Text = String.Join(", ", tbl);
                        labelSettings.EncodeHtml = false;
                        labelSettings.PreRender += (s, e) =>
                        {
                            MVCxLabel lbl = (MVCxLabel)s;
                            lbl.Text = HighlightSearchText(lbl.Text, c.Grid.SearchPanelFilter);
                        };
                    }).Render();
                }
            });
        });


        settings.AutoFilterCellEditorCreate = (s, e) =>
        {
            if (e.Column.FieldName == "RechtsbereicheUnbound")
            {
                ComboBoxProperties combo = new ComboBoxProperties();
                e.EditorProperties = combo;
            }
        };

        settings.AutoFilterCellEditorInitialize = (s, e) =>
        {
            var gv = s as MVCxGridView;
            var expr = gv.FilterExpression;
            if (e.Column.FieldName == "RechtsbereicheUnbound")
            {
                MVCxComboBox cb = ((MVCxComboBox)e.Editor);
                cb.DataSource = unitOfWork.RechtsbereichRepository.GetRechtsbereichComboBox((int)ViewData["StandortID"], (int)ViewData["KundendokumentID"], (int)ViewData["SpracheID"]).Select(i => i.Name);
                cb.DataBindItems();
                if (Session["RechtsbereicheFilter"] != null && expr.Contains("Rechtsbereiche"))
                {
                    e.Editor.Value = Session["RechtsbereicheFilter"];
                }
            }
            if (e.Column.FieldName == "Bewilligungspflicht")
            {
                var combo = e.Editor as MVCxComboBox;
                if (combo != null)
                {
                    combo.Items.FirstOrDefault(i => (bool)i.Value == true).Text = Resources.Dropdown_Bewilligung;
                    combo.Items.FirstOrDefault(i => (bool)i.Value == false).Text = Resources.Dropdown_KeineBewilligung;
                }
            }
            if (e.Column.FieldName == "Nachweispflicht")
            {
                var combo = e.Editor as MVCxComboBox;
                if (combo != null)
                {
                    combo.Items.FirstOrDefault(i => (bool)i.Value == true).Text = Resources.Dropdown_Nachweis;
                    combo.Items.FirstOrDefault(i => (bool)i.Value == false).Text = Resources.Dropown_KeinNachweis;
                }
            }

        };

        settings.ProcessColumnAutoFilter = (sender, e) =>
        {
            var gv = sender as MVCxGridView;
            if (e.Column.FieldName == "RechtsbereicheUnbound")
            {
                string value = e.Value;
                if (gv.FilterExpression != "")
                {
                    e.Criteria = CriteriaOperator.Parse(gv.FilterExpression + "OR Rechtsbereiche[contains(Name, ?)]", value);
                    gv.FilterExpression = null;
                    Session["RechtsbereicheFilter"] = "";
                }
                else
                {
                    e.Criteria = DevExpress.Data.Filtering.CriteriaOperator.Parse("Rechtsbereiche[contains(Name, ?)]", value);
                    Session["RechtsbereicheFilter"] = e.Value;
                }

            }
        };

        settings.Columns.Add(column =>
        {
            column.FieldName = "StandortObjektTitel";
            column.MinWidth = 200;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ReadOnly = true;
            column.SetHeaderCaptionTemplateContent("<span class=\"glyphicon glyphicon-lock\" style=\"margin-right: 5px\"></span>" + TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName));
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.GroupIndex = 0;

        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "ErlassSrNummer";
            column.Caption = Resources.Erlassnummer;
            column.MinWidth = 125;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ReadOnly = true;
            column.SetHeaderCaptionTemplateContent("<span class=\"glyphicon glyphicon-lock\" style=\"margin-right: 5px\"></span>" + Resources.Erlassnummer);
            column.EditFormSettings.Visible = DefaultBoolean.False;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "ErlassID";
            column.MinWidth = 200;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ReadOnly = true;
            column.SetHeaderCaptionTemplateContent("<span class=\"glyphicon glyphicon-lock\" style=\"margin-right: 5px\"></span>" + TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName));
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
            comboBoxProperties.DataSource = (Model.Select(x => new { x.ErlassID, x.ErlassTitel }).OrderBy(x => x.ErlassTitel)).Distinct().ToList();
            comboBoxProperties.TextField = "ErlassTitel";
            comboBoxProperties.ValueField = "ErlassID";
            comboBoxProperties.ValueType = typeof(int);
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "ArtikelNummer";
            column.SetHeaderCaptionTemplateContent("<span class=\"glyphicon glyphicon-lock\" style=\"margin-right: 5px\"></span>" + Resources.Entitaet_Artikel_Singular);
            column.Caption = Resources.Entitaet_Artikel_Singular;
            column.MinWidth = 110;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.CellStyle.HorizontalAlign = HorizontalAlign.Center;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            column.SetDataItemTemplateContent(
                container =>
                {

                    var url = DataBinder.Eval(container.DataItem, "ArtikelQuelle");
                    var nr = DataBinder.Eval(container.DataItem, "ArtikelNummer");
                    string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, nr, false);
                    ViewContext.Writer.Write(htmlLink);
                });
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Beschreibung";
            column.HeaderStyle.Wrap = DefaultBoolean.True;

            column.ColumnType = MVCxGridViewColumnType.Memo;
            column.MinWidth = 350;
            var memoProp = column.PropertiesEdit as MemoProperties;
            if (memoProp != null)
            {
                memoProp.EncodeHtml = false;
            }
            column.ReadOnly = true;
            column.SetHeaderCaptionTemplateContent("<span class=\"glyphicon glyphicon-lock\" style=\"margin-right: 5px\"></span>" + TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName));
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
            column.EditFormSettings.Visible = DefaultBoolean.False;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Bewilligungspflicht";
            column.MinWidth = 115;
            column.ColumnType = MVCxGridViewColumnType.CheckBox;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ReadOnly = true;
            column.SetHeaderCaptionTemplateContent("<span class=\"glyphicon glyphicon-lock\" style=\"margin-right: 5px\"></span>" + TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName));
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
            column.EditFormSettings.Visible = DefaultBoolean.False;
            //column.HeaderStyle.Font.Size = 8;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Nachweispflicht";
            column.MinWidth = 115;
            column.ColumnType = MVCxGridViewColumnType.CheckBox;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ReadOnly = true;
            column.SetHeaderCaptionTemplateContent("<span class=\"glyphicon glyphicon-lock\" style=\"margin-right: 5px\"></span>" + TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName));
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
            column.EditFormSettings.Visible = DefaultBoolean.False;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Status";

            //define as combobox for filtern over the enum
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

            var test = (from x in Model select x.Status).Distinct().ToList();

            var list = from KundendokumentItemStatus value in Enum.GetValues(typeof(KundendokumentItemStatus))
                       where test.Contains(value)
                       select new
                       {
                           Id = (int)value,
                           Name = value.GetTranslation()
                       };

            comboBoxProperties.DataSource = list;
            comboBoxProperties.ValueField = "Id";
            comboBoxProperties.TextField = "Name";


            column.MinWidth = 125;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ReadOnly = true;
            column.SetHeaderCaptionTemplateContent("<span class=\"glyphicon glyphicon-lock\" style=\"margin-right: 5px\"></span>" + TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName));
            column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.SetDataItemTemplateContent(container =>
            {
                var statusCandidate = DataBinder.Eval(container.DataItem, "Status");
                if (statusCandidate != null)
                {
                    var status = (KundendokumentItemStatus)statusCandidate;
                    ViewContext.Writer.Write("<span class=\"label label-" + status + "\">" + status.GetTranslation() + "</span>");
                }
            });
        });

        if (!showOnlyRemoved)
        {

            settings.Columns.Add(column =>
            {
                column.FieldName = "Erfuellung";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 150;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.ComboBox;
                column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

                var list = from KundendokumentErfuellung value in Enum.GetValues(typeof(KundendokumentErfuellung))
                           where (int)value != 4
                           select new
                           {
                               Id = (int)value,
                               Name = value.GetTranslation()
                           };

                comboBoxProperties.DataSource = list;
                comboBoxProperties.ValueField = "Id";
                comboBoxProperties.TextField = "Name";
                comboBoxProperties.ValueType = typeof(Int32);
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Massnahme";
                column.Caption = Resources.Entitaet_Massnahme_Singular;
                column.MinWidth = 225;
                column.ReadOnly = true;
                column.Settings.AllowAutoFilter = DefaultBoolean.False;
                column.Settings.ShowFilterRowMenu = DefaultBoolean.False;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.SetDataItemTemplateContent(container =>
                {
                    var standortId = ViewData["StandortID"];
                    var erlassName = DataBinder.Eval(container.DataItem, "ErlassTitel");
                    var erlassNr = DataBinder.Eval(container.DataItem, "ErlassSrNummer");
                    var KundendokumentForderungsversionID = DataBinder.Eval(container.DataItem, "KundendokumentForderungsversionID");
                    var ArtikelID = DataBinder.Eval(container.DataItem, "ArtikelID");
                    var ArtikelNummer = DataBinder.Eval(container.DataItem, "ArtikelNummer");

                    Html.DevExpress().Button(btn =>
                    {
                        btn.Text = Resources.Allgemein_Button_Massnahme;
                        btn.Name = "Massnahme_" + container.KeyValue;
                        btn.Attributes.Add("onClick", "javascript:ShowDetailPopup('" + Url.Action("ShowMassnahmePopupForForderungen", "kundendokument", new { erlassName = erlassName, standortID = standortId, erlassNr = erlassNr, originId = KundendokumentForderungsversionID, artikelNummer = ArtikelNummer, artikelId = ArtikelID }) + "','pcModalMode_Massnahme_Erfassen_Forderungen');");
                    }).Render();

                    var MassnahmeNewCount = DataBinder.Eval(container.DataItem, "MassnahmeNewCount");
                    var MassnahmeInProgressCount = DataBinder.Eval(container.DataItem, "MassnahmeInProgressCount");
                    var MassnahmeFinishedCount = DataBinder.Eval(container.DataItem, "MassnahmeFinishedCount");

                    ViewContext.Writer.Write("<br></br>");
                    ViewContext.Writer.Write("<div style='display:inline-block'>" + Resources.Enum_MassnahmeStatus_New + " (" + MassnahmeNewCount + ")" + "</div>");
                    ViewContext.Writer.Write("<br>");
                    ViewContext.Writer.Write("<div style='display:inline-block'>" + Resources.Enum_MassnahmeStatus_InProgress + " (" + MassnahmeInProgressCount + ")" + "</div>");
                    ViewContext.Writer.Write("<br>");
                    ViewContext.Writer.Write("<div style='display:inline-block'>" + Resources.Enum_MassnahmeStatus_Finished + " (" + MassnahmeFinishedCount + ")" + "</div>");
                });
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "LetzterPruefZeitpunkt";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 150;
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "NaechstePruefungAm";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 180;
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Pruefmethode";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 140;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.SetDataItemTemplateContent(
                    container =>
                    {

                        var url = DataBinder.Eval(container.DataItem, "Pruefmethode");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                        ViewContext.Writer.Write(htmlLink);
                    });
                var prop = column.PropertiesEdit as MemoProperties;
                prop.Rows = 10;
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "ShortcutID";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), "Verantwortlich");
                column.MinWidth = 140;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.ComboBox;
                column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
                comboBoxProperties.DataSource = unitOfWork.ShortcutRepository.GetShortcutCombobox((int)ViewData["StandortID"]);
                comboBoxProperties.ValueField = "ShortcutID";
                comboBoxProperties.TextField = "Name";
                comboBoxProperties.ValueType = typeof(Int32);
                if (User.IsInRole(Role.Accountable))
                {
                    column.EditFormSettings.Visible = DefaultBoolean.False;
                }

            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Ablageort";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 150;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.SetDataItemTemplateContent(
                    container =>
                    {

                        var url = DataBinder.Eval(container.DataItem, "Ablageort");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, false);
                        ViewContext.Writer.Write(htmlLink);
                    });
                var prop = column.PropertiesEdit as MemoProperties;
                prop.Rows = 10;
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Kommentar";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 110;
                column.HeaderStyle.Wrap = DefaultBoolean.True;

                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                var prop = column.PropertiesEdit as MemoProperties;
                prop.Rows = 10;
            });

            //Additinal Columns
            var labels = unitOfWork.KundendokumentRepository.GetByID((int)ViewData["KundendokumentID"], "KundendokumentSpaltenlabel").KundendokumentSpaltenlabel;
            settings.Columns.Add(column =>
            {
                column.FieldName = "Spalte1";
                column.MinWidth = 135;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.Caption = (labels == null || labels.LabelSpalte1 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte1;
                column.SetDataItemTemplateContent(
                    container =>
                    {

                        var url = DataBinder.Eval(container.DataItem, "Spalte1");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                        ViewContext.Writer.Write(htmlLink);
                    });
                var prop = column.PropertiesEdit as MemoProperties;
                prop.Rows = 10;
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Spalte2";
                column.MinWidth = 135;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.Caption = (labels == null || labels.LabelSpalte2 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte2;
                column.SetDataItemTemplateContent(
                    container =>
                    {

                        var url = DataBinder.Eval(container.DataItem, "Spalte2");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, true);
                        ViewContext.Writer.Write(htmlLink);
                    });
                var prop = column.PropertiesEdit as MemoProperties;
                prop.Rows = 10;
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Spalte3";
                column.MinWidth = 135;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.Caption = (labels == null || labels.LabelSpalte3 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte3;
                column.SetDataItemTemplateContent(
                    container =>
                    {

                        var url = DataBinder.Eval(container.DataItem, "Spalte3");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                        ViewContext.Writer.Write(htmlLink);
                    });
                var prop = column.PropertiesEdit as MemoProperties;
                prop.Rows = 10;
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Spalte4";
                column.MinWidth = 135;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.Caption = (labels == null || labels.LabelSpalte4 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte4;
                column.SetDataItemTemplateContent(
                    container =>
                    {

                        var url = DataBinder.Eval(container.DataItem, "Spalte4");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, true);
                        ViewContext.Writer.Write(htmlLink);
                    });
                var prop = column.PropertiesEdit as MemoProperties;
                prop.Rows = 10;

            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Spalte5";
                column.MinWidth = 135;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.Caption = (labels == null || labels.LabelSpalte5 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte5;
                column.SetDataItemTemplateContent(
                    container =>
                    {

                        var url = DataBinder.Eval(container.DataItem, "Spalte5");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                        ViewContext.Writer.Write(htmlLink);
                    });
                var prop = column.PropertiesEdit as MemoProperties;
                prop.Rows = 10;
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Spalte6";
                column.MinWidth = 135;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.Caption = (labels == null || labels.LabelSpalte6 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte6;
                column.SetDataItemTemplateContent(
                    container =>
                    {

                        var url = DataBinder.Eval(container.DataItem, "Spalte6");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, true);
                        ViewContext.Writer.Write(htmlLink);
                    });
                var prop = column.PropertiesEdit as MemoProperties;
                prop.Rows = 10;
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Spalte7";
                column.MinWidth = 135;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.Caption = (labels == null || labels.LabelSpalte7 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte7;
                column.SetDataItemTemplateContent(
                    container =>
                    {
                        var url = DataBinder.Eval(container.DataItem, "Spalte7");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                        ViewContext.Writer.Write(htmlLink);
                    });
                var prop = column.PropertiesEdit as MemoProperties;
                prop.Rows = 10;
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Spalte8";
                column.MinWidth = 135;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.Caption = (labels == null || labels.LabelSpalte8 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte8;
                column.SetDataItemTemplateContent(
                    container =>
                    {
                        var url = DataBinder.Eval(container.DataItem, "Spalte8");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                        ViewContext.Writer.Write(htmlLink);
                    });
                var prop = column.PropertiesEdit as MemoProperties;
                prop.Rows = 10;
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Spalte9";
                column.MinWidth = 135;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.Caption = (labels == null || labels.LabelSpalte9 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte9;
                column.SetDataItemTemplateContent(
                    container =>
                    {
                        var url = DataBinder.Eval(container.DataItem, "Spalte9");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                        ViewContext.Writer.Write(htmlLink);
                    });
                var prop = column.PropertiesEdit as MemoProperties;
                prop.Rows = 10;
            });
            settings.Columns.Add(column =>
            {
                column.FieldName = "Spalte10";
                column.MinWidth = 135;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                column.Caption = (labels == null || labels.LabelSpalte10 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte10;
                column.SetDataItemTemplateContent(
                    container =>
                    {
                        var url = DataBinder.Eval(container.DataItem, "Spalte10");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                        ViewContext.Writer.Write(htmlLink);
                    });
                var prop = column.PropertiesEdit as MemoProperties;
                prop.Rows = 10;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassfassungInkraftretung";
                column.MinWidth = 160;
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.SetHeaderCaptionTemplateContent("<span class=\"glyphicon glyphicon-lock\" style=\"margin-right: 5px\"></span>" + TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), "Inkrafttretung"));
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), "Inkrafttretung");
                column.EditFormSettings.Visible = DefaultBoolean.False;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassfassungBearbeitetAm";
                column.MinWidth = 160;
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.SetHeaderCaptionTemplateContent("<span class=\"glyphicon glyphicon-lock\" style=\"margin-right: 5px\"></span>" + TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), "Beschluss"));
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), "Beschluss");
                column.EditFormSettings.Visible = DefaultBoolean.False;
            });
        }

        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        settings.HtmlDataCellPrepared += (sender, e) =>
        {
            var erfuellung = "";
            if (e.VisibleIndex >= 0)
            {
                erfuellung = e.GetValue("Erfuellung").ToString();
            }
            var bewilligungspflichtig = Convert.ToBoolean(e.GetValue("Bewilligungspflicht"));
            var nachweispflichtig = Convert.ToBoolean(e.GetValue("Nachweispflicht"));
            if (bewilligungspflichtig)
            {
                e.Cell.BackColor = System.Drawing.Color.Lavender;
            }
            if (nachweispflichtig)
            {
                e.Cell.BackColor = System.Drawing.Color.BlanchedAlmond;
            }
            if (bewilligungspflichtig && nachweispflichtig)
            {
                e.Cell.BackColor = System.Drawing.Color.PowderBlue;
            }
            if (erfuellung == "Yes")
            {
                e.Cell.BackColor = System.Drawing.ColorTranslator.FromHtml("#E2EFDA");
            }
        };
        settings.ClientLayout = (sender, e) =>
        {
            MVCxGridView gridView = sender as MVCxGridView;
            var cookieID = gridView.SettingsCookies.CookiesID + "_" + kundendokumentID;
            if (e.LayoutMode == ClientLayoutMode.Loading)
            {
                var layout = CookieHelper.GetCookie(cookieID);
                if (layout != "")
                {
                    e.LayoutData = layout;
                }
            }
            else if (e.LayoutMode == ClientLayoutMode.Saving)
            {
                CookieHelper.SetCookie(cookieID, e.LayoutData);
            }
        };

        settings.SettingsPopup.HeaderFilter.Height = Unit.Pixel(440);
        settings.SettingsPopup.HeaderFilter.Width = Unit.Pixel(300);
        foreach (GridViewDataColumn column in settings.Columns)
        {
            if (column.FieldName == "LetzterPruefZeitpunkt" || column.FieldName == "NaechstePruefungAm" || column.FieldName == "ErlassfassungInkraftretung" || column.FieldName == "ErlassfassungBearbeitetAm")
            {
                column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.DateRangePicker;
            }
            else if (column.FieldName == "StandortObjektTitel" || column.FieldName == "Pruefmethode" || column.FieldName == "ShortcutID" || column.FieldName == "Ablageort" || column.FieldName == "ErlassID"
                            || column.FieldName == "ErlassSrNummer" || column.FieldName == "Erfuellung" || column.FieldName == "Status" || column.FieldName == "Massnahme")
            {
                column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.CheckedList;
            }
            else
            {
                column.Settings.AllowHeaderFilter = DefaultBoolean.False;
            }

            string cookieId = "PortalKundendokumentForderungenGridView_" + sessionHelper.GetCurrentUserID() + "_" + kundendokumentID;
            var cookie = CookieHelper.GetCookie(cookieId);
            if (cookie == "")
            {
                var columns = unitOfWork.StandortRepository.GetByID((int)ViewData["StandortID"], "KundendokumentSpaltenauswahl").KundendokumentSpaltenauswahl;

                if (columns != null)
                {
                    // NEOS-528 Check if no column is selected which will result in no columns show
                    if (!columns.RechtsbereicheUnbound && !columns.StandortObjektTitel && !columns.ErlassID && !columns.ArtikelNummer && !columns.Beschreibung && !columns.Bewilligungspflicht && !columns.Nachweispflicht
                                && !columns.Status && !columns.Erfuellung && !columns.LetzterPruefZeitpunkt && !columns.NaechstePruefungAm && !columns.Pruefmethode && !columns.ShortcutID && !columns.Ablageort
                                && !columns.Spalte1 && !columns.Spalte2 && !columns.Spalte3 && !columns.Spalte4 && !columns.Spalte5 && !columns.Spalte6 && !columns.Spalte7 && !columns.Spalte8 && !columns.Spalte9 && !columns.Spalte10
                                && !columns.ErlassSrNummer && !columns.ErlassfassungInkraftretung && !columns.ErlassfassungBearbeitetAm && !columns.Kommentar)
                    {
                        // do nothing if no column is selected to show the standard view when no cookies set and no selection is made
                    }
                    else
                    {
                        if (column.FieldName == "RechtsbereicheUnbound" && !columns.RechtsbereicheUnbound) { column.Visible = false; }
                        else if (column.FieldName == "StandortObjektTitel" && !columns.StandortObjektTitel) { column.Visible = false; }
                        else if (column.FieldName == "ErlassSrNummer" && !columns.ErlassSrNummer) { column.Visible = false; }
                        else if (column.FieldName == "ErlassID" && !columns.ErlassID) { column.Visible = false; }
                        else if (column.FieldName == "ArtikelNummer" && !columns.ArtikelNummer) { column.Visible = false; }
                        else if (column.FieldName == "Beschreibung" && !columns.Beschreibung) { column.Visible = false; }
                        else if (column.FieldName == "Bewilligungspflicht" && !columns.Bewilligungspflicht) { column.Visible = false; }
                        else if (column.FieldName == "Nachweispflicht" && !columns.Nachweispflicht) { column.Visible = false; }
                        else if (column.FieldName == "Status" && !columns.Status) { column.Visible = false; }
                        else if (column.FieldName == "Erfuellung" && !columns.Erfuellung) { column.Visible = false; }
                        else if (column.FieldName == "Massnahme" && !columns.Massnahme) { column.Visible = false; }
                        else if (column.FieldName == "LetzterPruefZeitpunkt" && !columns.LetzterPruefZeitpunkt) { column.Visible = false; }
                        else if (column.FieldName == "NaechstePruefungAm" && !columns.NaechstePruefungAm) { column.Visible = false; }
                        else if (column.FieldName == "Pruefmethode" && !columns.Pruefmethode) { column.Visible = false; }
                        else if (column.FieldName == "ShortcutID" && !columns.ShortcutID) { column.Visible = false; }
                        else if (column.FieldName == "Ablageort" && !columns.Ablageort) { column.Visible = false; }
                        else if (column.FieldName == "Kommentar" && !columns.Kommentar) { column.Visible = false; }
                        else if (column.FieldName == "Spalte1" && !columns.Spalte1) { column.Visible = false; }
                        else if (column.FieldName == "Spalte2" && !columns.Spalte2) { column.Visible = false; }
                        else if (column.FieldName == "Spalte3" && !columns.Spalte3) { column.Visible = false; }
                        else if (column.FieldName == "Spalte4" && !columns.Spalte4) { column.Visible = false; }
                        else if (column.FieldName == "Spalte5" && !columns.Spalte5) { column.Visible = false; }
                        else if (column.FieldName == "Spalte6" && !columns.Spalte6) { column.Visible = false; }
                        else if (column.FieldName == "Spalte7" && !columns.Spalte7) { column.Visible = false; }
                        else if (column.FieldName == "Spalte8" && !columns.Spalte8) { column.Visible = false; }
                        else if (column.FieldName == "Spalte9" && !columns.Spalte9) { column.Visible = false; }
                        else if (column.FieldName == "Spalte10" && !columns.Spalte10) { column.Visible = false; }
                        else if (column.FieldName == "ErlassfassungInkraftretung" && !columns.ErlassfassungInkraftretung) { column.Visible = false; }
                        else if (column.FieldName == "ErlassfassungBearbeitetAm" && !columns.ErlassfassungBearbeitetAm) { column.Visible = false; }
                    }
                }
            }
        }

        settings.BeforeHeaderFilterFillItems = (sender, e) =>
        {
            if (e.Column.FieldName == "Massnahme")
            {
                e.AddValue("Neu", new BinaryOperator("MassnahmeNewCount", 1, BinaryOperatorType.GreaterOrEqual));
                e.AddValue("In Arbeit", new BinaryOperator("MassnahmeInProgressCount", 1, BinaryOperatorType.GreaterOrEqual));
                e.AddValue("Erledigt", new BinaryOperator("MassnahmeFinishedCount", 1, BinaryOperatorType.GreaterOrEqual));
                e.Handled = true;
            }
        };

        settings.SettingsDetail.ShowDetailRow = true;
        //settings.HeaderFilterFillItems = (sender, e) =>
        //{
        //    ASPxGridView gridV = (ASPxGridView)sender;
        //    if(e.Column.FieldName == "Bewilligungspflicht")
        //    {
        //        e.Values.Clear();
        //        if (e.Column.SettingsHeaderFilter.Mode == GridHeaderFilterMode.List)
        //            e.AddShowAll();
        //        e.AddValue("Bewilligt", "true");
        //        e.AddValue("Nicht bewilligt", "false");
        //    }
        //};
        settings.SetDetailRowTemplateContent(c =>
        {
            @Html.DevExpress().PageControl(pageControlSettings =>
            {

                pageControlSettings.Name = "ForderungenPageControl_" + DataBinder.Eval(c.DataItem, "ForderungsversionID") + "_" + DataBinder.Eval(c.DataItem, "StandortObjektID");
                pageControlSettings.Width = Unit.Percentage(80);
                pageControlSettings.SaveStateToCookies = true;
                pageControlSettings.TabPages.Add(Resources.View_Tab_ErlassDetail).SetContent(() =>
                {
                    Html.RenderAction(
                        "ErlassDetailGridView",
                        "Kundendokument",
                        new
                        {
                            erlassID = DataBinder.Eval(c.DataItem, "ErlassID"),
                            standortID = ViewData["StandortID"],
                            kundendokumentID = ViewData["KundendokumentID"]
                        });
                });
                pageControlSettings.TabPages.Add(Resources.View_Tab_ErlassfassungDetail).SetContent(() =>
                {
                    Html.RenderAction(
                       "ErlassfassungDetailGridView",
                       "Kundendokument",
                       new
                       {
                           erlassfassungID = DataBinder.Eval(c.DataItem, "ErlassfassungID"),
                           standortID = ViewData["StandortID"],
                           kundendokumentID = ViewData["KundendokumentID"]
                       });
                });
                pageControlSettings.TabPages.Add(Resources.View_Tab_ForderungDetail).SetContent(() =>
                {

                    Html.RenderAction(
                       "ForderungsversionDetailGridView",
                       "Kundendokument",
                       new
                       {
                           forderungsversionID = DataBinder.Eval(c.DataItem, "ForderungsversionID"),
                           standortID = ViewData["StandortID"],
                           kundendokumentID = ViewData["KundendokumentID"]
                       });

                });

                var erlassID = DataBinder.Eval(c.DataItem, "ErlassID");
                var erlassIDInt = erlassID != null ? (int)erlassID : 0;
                var kundendokumentChecklistId = unitOfWork.ChecklistRepository.GetKundendokumentChecklistIdByErlassId(erlassIDInt, Convert.ToInt32(ViewData["KundendokumentID"]));

                if (kundendokumentChecklistId != 0)
                {
                    pageControlSettings.TabPages.Add(Resources.View_Tab_ChecklistDetail).SetContent(() =>
                    {
                        Html.RenderAction(
                            "ChecklistDetailGridView",
                            "Kundendokument",
                            new
                            {
                                kundendokumentChecklistID = kundendokumentChecklistId,
                                kundendokumentForderungsversionsID = DataBinder.Eval(c.DataItem, "KundendokumentForderungsversionID"),
                                standortID = ViewData["StandortID"],
                                kundendokumentID = ViewData["KundendokumentID"],
                                spracheID = ViewData["SpracheID"]
                            });
                    });
                }
                if (DataBinder.Eval(c.DataItem, "Status") != null && (KundendokumentItemStatus)DataBinder.Eval(c.DataItem, "Status") == KundendokumentItemStatus.NewVersion)
                {
                    pageControlSettings.TabPages.Add(Resources.View_Tab_CompareForderung).SetContent(() =>
                    {

                        Html.RenderAction(
                           "ForderungsversionCompareDetailGridView",
                           "Kundendokument",
                           new
                           {
                               forderungsversionID = DataBinder.Eval(c.DataItem, "ForderungsversionID"),
                               standortID = ViewData["StandortID"],
                               kundendokumentID = ViewData["KundendokumentID"]
                           });
                    });
                }
            }).Render();
        });

    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}
@*grid.Bind(Model).GetHtml();*@

@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "KundendokumentForderungsversionID";
}).GetHtml()