@using System.Globalization
@using System.Linq
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.Resources.Properties
@model IQueryable<NeoSysLCS.Repositories.ViewModels.StandortObjektViewModel>

@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        IUnitOfWork unitOfWork = new UnitOfWork();
        settings.Name = "KundendokumentStandortObjektGridView";
        GridViewHelper.ApplyDefaultSettings(settings);
        settings.SettingsCookies.Enabled = false;
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        var kundendokumentID = ViewData["KundendokumentID"];

        settings.KeyFieldName = "StandortObjektID";
        var standortID = ViewData["StandortID"];
        var spracheID = ViewData["SpracheID"];

        settings.CallbackRouteValues = new { Controller = "KundendokumentStandortObjekt", Action = "KundendokumentStandortObjektGridView", kundendokumentID, standortID, spracheID };

        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "KundendokumentStandortObjekt", Action = "StandortObjektGridViewBatchEditUpdate", standortID, kundendokumentID, spracheID };

        settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
        settings.CommandColumn.Visible = true;

        settings.CommandColumn.ShowNewButtonInHeader = false;
        settings.CommandColumn.ShowDeleteButton = false;
        settings.CommandColumn.ShowEditButton = false;
        settings.Settings.ShowGroupPanel = false;

        settings.Columns.Add("Name", Resources.Entitaet_StandortObjekt_Singular);

        settings.Columns.Add(column =>
        {
            column.FieldName = "ShortcutID";
            column.Caption = Resources.Entitaet_KundendokumentForderungsversion_Verantwortlich;
            column.MinWidth = 140;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
            comboBoxProperties.DataSource = unitOfWork.ShortcutRepository.GetShortcutCombobox((int)ViewData["StandortID"]);
            comboBoxProperties.ValueField = "ShortcutID";
            comboBoxProperties.TextField = "Name";
            comboBoxProperties.ValueType = typeof(Int32);
            if (User.IsInRole(Role.Accountable))
            {
                column.EditFormSettings.Visible = DefaultBoolean.False;
            }

        });

        settings.SettingsDetail.ShowDetailRow = true;
        settings.SetDetailRowTemplateContent(c =>
        {
            Html.RenderAction("StandortObjektDetailGridView", new { id = DataBinder.Eval(c.DataItem, "StandortObjektID"), standortID });
        });

        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        var errorList = ViewData["ErrorList"];

        if (errorList != null)
        {
            settings.Settings.ShowFooter = true;
            settings.SetFooterRowTemplateContent(c => Html.ViewContext.Writer.Write("StandortObjekt ist noch in Verwendung und kann nicht gelöscht werden"));
        }

        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        settings.ClientLayout = (sender, e) =>
        {
            MVCxGridView gridView = sender as MVCxGridView;
            var cookieID = gridView.SettingsCookies.CookiesID + "_" + kundendokumentID;
            if (e.LayoutMode == ClientLayoutMode.Loading)
            {
                var layout = CookieHelper.GetCookie(cookieID);
                if (layout != "")
                {
                    e.LayoutData = layout;
                }
            }
            else if (e.LayoutMode == ClientLayoutMode.Saving)
            {
                CookieHelper.SetCookie(cookieID, e.LayoutData);
            }
        };

        settings.ClientSideEvents.BatchEditStartEditing = @"function(s,e){
                currentGrid = s;
	        }";

        settings.ClientSideEvents.ClipboardCellPasting = @"function(s,e){
                e.cancel = currentGrid != s;
	        }";
    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "StandortObjektID";
}).GetHtml()

