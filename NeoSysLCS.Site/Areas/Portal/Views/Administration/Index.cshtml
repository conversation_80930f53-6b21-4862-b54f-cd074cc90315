@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties


@section Scripts {
    @Scripts.Render("~/Scripts/helpers/BatchEditDeleteHelper.js")
}

@{

    ViewBag.Title = "Administration";
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}

<script type="text/javascript">
    function OnActiveTabChanging(s, e) {
        if (e.tab.name == "Benutzerverwaltung") {
            e.cancel = true;
            window.location.replace("/Admin/UsersAdmin/Index");
        }
    }

</script>

@Html.Partial("_HtmlEditPopup", false)
@Html.Partial("AdministrationPageControllCallbacksPartial")
