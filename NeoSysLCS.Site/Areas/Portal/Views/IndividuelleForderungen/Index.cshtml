@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.ViewModels

@model IQueryable<IndividuelleForderungViewModel>

@{
    ViewBag.Title = "Individuelle Forderungen";
    if (ViewData["standortID"] != null)
    {
        IUnitOfWork unitOfWork = new UnitOfWork();
        Standort standortViewModel = unitOfWork.StandortRepository.GetStanodrtById((int)ViewData["standortID"]);
        ViewBag.Title += " von Standort " + standortViewModel.Name;
    }
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");

}


@Html.Partial("IndividuelleForderungenGridView", Model)
