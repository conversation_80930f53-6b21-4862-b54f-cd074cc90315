@using System.Web.UI.WebControls
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Repositories
@using System.Globalization
@using System.Web.UI.WebControls
@using Microsoft.Ajax.Utilities
@using NeoSysLCS.DomainModel.Models
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.Helper
@using NeoSysLCS.Site.Helpers
@using DevExpress.Data
@using DevExpress.Data.Filtering

@model IQueryable<ShortcutViewModel>

@functions{
    public static string HighlightSearchText(string source, string searchText)
    {
        if (string.IsNullOrWhiteSpace(searchText))
            return source;
        var regex = new System.Text.RegularExpressions.Regex(System.Text.RegularExpressions.Regex.Escape(searchText), System.Text.RegularExpressions.RegexOptions.IgnoreCase);
        if (regex.IsMatch(source))
            return string.Format("<span>{0}</span>", regex.Replace(source, "<span class='dxgvHL'>$0</span>"));
        return source;
    }
}

@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        IUnitOfWork unitOfWork = new UnitOfWork();

        settings.Name = "ShortcutView";
        settings.KeyFieldName = "ShortcutID";

        GridViewHelper.ApplyDefaultSettings(settings);

        settings.SettingsLoadingPanel.Mode = GridViewLoadingPanelMode.ShowOnStatusBar;
        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;

        //settings.ClientSideEvents.BeginCallback = "OnBeginCallback";
        //settings.ClientSideEvents.CustomButtonClick = "OnCustomButtonClick";

        settings.SettingsContextMenu.Enabled = false;
        settings.Settings.ShowGroupPanel = false;
        settings.Settings.ShowFilterRow = false;
        settings.CallbackRouteValues = new { Controller = "StandortAdministration", Action = "ShortcutAdministration" };
        settings.SettingsEditing.BatchUpdateRouteValues = new { Controller = "StandortAdministration", Action = "_ShortcutsUpdate", kundeID = Convert.ToInt32(ViewData["KundeID"]) };

        settings.SettingsEditing.Mode = GridViewEditingMode.Batch;
        settings.SettingsEditing.BatchEditSettings.EditMode = GridViewBatchEditMode.Row;
        settings.SettingsBehavior.ConfirmDelete = true;

        settings.CommandColumn.Visible = true;

        settings.CommandColumn.ShowNewButtonInHeader = true;
        settings.CommandColumn.ShowDeleteButton = true;

        settings.SettingsPager.Visible = true;
        settings.Settings.ShowGroupPanel = false;
        settings.Settings.ShowFilterRow = true;
        settings.SettingsBehavior.AllowSelectByRowClick = true;

        settings.Columns.Add(column =>
        {
            column.FieldName = "StandortID";
            column.Caption = NeoSysLCS.Site.Helpers.CultureHelper.GetStandortMenuName();
            column.ColumnType = MVCxGridViewColumnType.ComboBox;
            column.Settings.FilterMode = ColumnFilterMode.DisplayText;
            var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
            var list = unitOfWork.StandortRepository.GetAllStandortViewModels((int)ViewData["KundeID"]).ToList();
            comboBoxProperties.DataSource = list;
            comboBoxProperties.ValueField = "StandortID";
            comboBoxProperties.TextField = "Name";
            column.Settings.AllowHeaderFilter = DefaultBoolean.True;
            column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.CheckedList;
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Name";
            column.Caption = Resources.View_Shortcut_Name;
            //column.Caption = Resources.View_Shortcut_Role;
            //column.SetDataItemTemplateContent(container => Html.DevExpress().HyperLink(hyperlink =>
            //{
            //    var keyValue = container.KeyValue;
            //    hyperlink.Name = "Standort_" + keyValue;
            //    hyperlink.Properties.Text = Resources.Verwaltung + " " + Model.Where(y => y.StandortID == (int)keyValue).Select(x => x.Name).FirstOrDefault();
            //    hyperlink.NavigateUrl = Url.Action("KundendokumentIndex", "StandortAdministration", new { id = DataBinder.Eval(container.DataItem, "StandortID") });
            //}).Render());
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "UserUnbound";
            column.Caption = Resources.View_Shortcut_User;
            column.HeaderStyle.Wrap = DefaultBoolean.True;
            column.EditFormSettings.Visible = DefaultBoolean.False;
            column.UnboundType = UnboundColumnType.String;
            column.Settings.AllowSort = DefaultBoolean.False;

            column.SetDataItemTemplateContent(container => Html.DevExpress().HyperLink(hyperlink =>
            {
                var keyValue = container.KeyValue;

                string linktext = "";
                var dataItem = DataBinder.Eval(container.DataItem, "User");

                if (dataItem != null)
                {
                    IQueryable<ApplicationUser> applicationUsers = dataItem as IQueryable<ApplicationUser>;
                    List<string> tbl = new List<string>();

                    foreach (ApplicationUser appUser in applicationUsers)
                    {
                        linktext += appUser.FullName + ", ";
                    }

                    linktext = (linktext == "" ? Resources.Link_bearbeiten : linktext.Substring(0, linktext.Length - 2));

                    hyperlink.Properties.Text = (keyValue == null ? "" : linktext);
                    ViewData["ShortcutID"] = keyValue;
                    ViewData["StandortID"] = DataBinder.Eval(container.DataItem, "StandortID");
                    hyperlink.Attributes.Add("onClick", "javascript:ShowDetailPopup('" + Url.Action("ShowUserPopup", "StandortAdministration", new { shortcutID = keyValue, standortID = DataBinder.Eval(container.DataItem, "StandortID") }) + "','pcModalMode_User');");
                    hyperlink.NavigateUrl = Url.Content("#");

                    //hyperlink.PreRender += (s, e) =>
                    //{
                    //    MVCxLabel lbl = (MVCxLabel)s;
                    //    lbl.Text = HighlightSearchText(lbl.Text, container.Grid.SearchPanelFilter);
                    //};
                }

            }).Render());

            column.SetEditItemTemplateContent(c => Html.DevExpress().TextBox(TextBoxSettings =>
            {
                TextBoxSettings.Name = "txt2";
                TextBoxSettings.ReadOnly = true;
                TextBoxSettings.ClientVisible = false;
            }).Render());
        });

        GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

        settings.ClientSideEvents.BatchEditStartEditing = "BatchEditStarting";

        settings.AutoFilterCellEditorCreate = (s, e) =>
        {
            if (e.Column.FieldName == "UserUnbound")
            {
                ComboBoxProperties combo = new ComboBoxProperties();
                e.EditorProperties = combo;
            }
        };

        settings.AutoFilterCellEditorInitialize = (s, e) =>
        {
            var gv = s as MVCxGridView;
            var expr = gv.FilterExpression;
            if (e.Column.FieldName == "UserUnbound")
            {
                MVCxComboBox cb = ((MVCxComboBox)e.Editor);
                cb.DataSource = unitOfWork.UserRepository.GetApplicationUsersByKundeId((int)ViewData["KundeID"]).ToList().Select(x => x.FullName);
                cb.DataBindItems();
                if (Session["UserFilter"] != null && expr.Contains("User"))
                {
                    e.Editor.Value = Session["UserFilter"];
                }
            }
        };

        settings.ProcessColumnAutoFilter = (sender, e) =>
        {
            var gv = sender as MVCxGridView;
            if (e.Column.FieldName == "UserUnbound")
            {
                string value = e.Value;
                if (gv.FilterExpression != "")
                {
                    e.Criteria = CriteriaOperator.Parse(gv.FilterExpression + "OR User[contains(FullName, ?)]", value);
                    gv.FilterExpression = null;
                    Session["UserFilter"] = "";
                }
                else
                {
                    e.Criteria = DevExpress.Data.Filtering.CriteriaOperator.Parse("User[contains(FullName, ?)]", value);
                    Session["UserFilter"] = e.Value;
                }

            }
        };

        settings.SetFooterRowTemplateContent(c =>
        {
            if (ViewData["DeleteWarning"] != null)
            {
                Html.ViewContext.Writer.Write("<span class=\"text-danger\">" + (string)ViewData["DeleteWarning"] + "</span>");
            }
        });
    });

    if (ViewData["EditError"] != null)
    {
        grid.SetEditErrorText((string)ViewData["EditError"]);
    }
}
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "ShortcutID";
}).GetHtml()