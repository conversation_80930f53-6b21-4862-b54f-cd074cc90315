@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories
@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties

@{
    ViewBag.Title = @Resources.Titel_Kundendokument_Singular;
    ViewContext.Writer.Write("<h1 class='page-header'>" + ViewBag.Title + "</h1>");
}

@section breadcrumb{
    <a href="#" onclick="history.go(-1); return false;" title="@Resources.Navigation_Zurueck"><i class="fa fa-arrow-left fa-lg"></i></a> |
    <span class="breadcrumb-noesys-navigation">
        <a href="/">@Resources.View_Breadcrumb_Home</a>
        <span> > </span>
        <span>@ViewBag.Title</span>
    </span>

}

@*Fixed NEOS-298 Speicherbutton nicht sichtbar bei Masteransicht*@
<script type="text/javascript">
    function OnActiveTabChanging(s, e) {
        e.reloadContentOnCallback = true;
    }

    function BatchEditStarting(s, e) {
        var currentGrid = s;
        if (e.visibleIndex < 0)
            currentGrid.GetEditor("StandortID").SetEnabled(true);
        else
            currentGrid.GetEditor("StandortID").SetEnabled(false);
    }

</script>

@Html.DevExpress().PopupControl(settings =>
{
    settings.Name = "pcModalMode_User";
    settings.ScrollBars = ScrollBars.Auto;
    settings.ShowPageScrollbarWhenModal = true;
    settings.Width = 800;
    settings.Height = 600;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = true;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "User";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(1000); s.SetHeight(650); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(1000); s.SetHeight(650); }";
    settings.ClientSideEvents.Closing = "function(s, e){ location.reload() }";
}).GetHtml()

@Html.Partial("KundendokumentPageControlCallbacksPartial")

