@using System.Globalization
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers
@{
    
    var standortMenuName = NeoSysLCS.Site.Helpers.CultureHelper.GetStandortMenuName(0,0,false);
    if (CultureInfo.CurrentUICulture.TwoLetterISOLanguageName != "de")
    {
        standortMenuName = standortMenuName.ToLower();
    }
}

<div class="container-fluid">

    <div class="row">
        <div class=" pull-left">
            <h3>@Resources.View_PortalDashboard_Erfüllungsgrad @Resources.Alle_Standorte.ToLower() @standortMenuName</h3>
            <br />
            @Html.Partial("_PieChartForderungenFullfilment")
            @if (false.Equals(ViewData["HasKundendokument"]))
            {
                <h5><span class="label label-warning">Warnung</span> Es sind keine Kundendokumente/Forderungen verfügbar</h5>
            }
        </div>
    </div>
</div>