@using NeoSysLCS.Repositories
@using NeoSysLCS.Site.Helpers
@using NeoSysLCS.Resources.Properties
@model IQueryable<NeoSysLCS.Repositories.ViewModels.CustomerNewsViewModel>

@{
    var grid = Html.DevExpress().GridView(settings =>
    {
        IUnitOfWork unitOfWork = new UnitOfWork();

        settings.Name = "CustomerNewsGridView";
        //GridViewHelper.ApplyDefaultSettings(settings);
        GridViewHelper.ApplyUebersetzungsSettings(settings);
        settings.CommandColumn.Visible = false;

        settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;
        settings.Settings.ShowGroupPanel = false;
        settings.Settings.ShowGroupedColumns = false;
        settings.Settings.ShowFilterRow = true;
        settings.SettingsBehavior.AllowSort = true;

        SessionHelper sessionHelper = new SessionHelper();


        settings.KeyFieldName = "CustomerNewsID";
        settings.CallbackRouteValues = new { Controller = "Dashboard", Action = "NewsPartialView", customerNewsId = ViewData["CustomerNewsID"] };

        //GridViewHelper.AddNewestFirstSortorderColumn(settings, "CustomerNewsID");

        settings.Columns.Add(column =>
        {
            column.FieldName = "FromDate";
            column.Caption = Resources.View_News_FromDate;
            column.ColumnType = MVCxGridViewColumnType.DateEdit;
            column.Settings.AllowHeaderFilter = DefaultBoolean.True;
            column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.DateRangePicker;
            column.SortDescending();
            column.SortIndex = 0;
            //column.PropertiesEdit.DisplayFormatString = "d";
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Text";
            column.Caption = Resources.View_News_News;
            column.Settings.AllowHeaderFilter = DefaultBoolean.True;
            column.SetDataItemTemplateContent(
                container =>
                {
                    var text = (string)DataBinder.Eval(container.DataItem, "Text");
                    if (!string.IsNullOrEmpty(text))
                    {
                        ViewContext.Writer.Write(text);
                    }
                }
                );
        });

        settings.Columns.Add(column =>
        {
            column.FieldName = "Link";
            column.Caption = Resources.View_News_Link;
            column.Settings.AllowHeaderFilter = DefaultBoolean.True;
            column.SetDataItemTemplateContent(
                container =>
                {

                    var url = DataBinder.Eval(container.DataItem, "Link");
                    string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                    ViewContext.Writer.Write(htmlLink);
                });

        });
    });


}
@grid.BindToLINQ(string.Empty, string.Empty, (s, e) =>
{
    e.QueryableSource = Model;
    e.KeyExpression = "CustomerNewsID";
}).GetHtml()