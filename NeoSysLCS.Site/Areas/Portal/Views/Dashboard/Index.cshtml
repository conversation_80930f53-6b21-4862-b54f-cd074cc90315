@using NeoSysLCS.Repositories
@using NeoSysLCS.Resources.Properties
@using NeoSysLCS.Site.Helpers
@using System.Globalization
@using System.Web.UI.WebControls

@{
    var sessionhelper = new SessionHelper();
    var unitOfWork = new UnitOfWork();
    var user = sessionhelper.GetCurrentUser();
    var kunde = unitOfWork.KundeRepository.GetByID(user.KundeID);
    ViewBag.Title = Resources.View_Title_Kunden_Portal_Dashboard.Replace("##KUNDE##", kunde != null ? kunde.Name : "");

    <div class="page-header" style="display:inline-block">
        <h1 style="display:inline-block">@Resources.View_Title_Kunden_Portal_Dashboard.Replace("##KUNDE##", kunde != null ? kunde.Name : "")</h1>
        <button type="button" class="btn btn-default" style="border:none; background-color: transparent; outline:none; padding-left: 5px; padding-right: 0px; padding-bottom:20px; padding-top: 5px"
                onclick="javascript: ShowDetailPopup('/Portal/FAQPortal/ShowFAQPopup?view=Dashboard', 'pcModalMode_FAQ_Dashboard');">
            <img src="~/Content/images/Question_Mark.png" style="width:20px;height:20px" />
        </button>
    </div>
}

@Html.Partial("_DashboardPageControlCallbacksPartial")

<script type="text/javascript">
    //<![CDATA[

    function ShowDetailPopup(url, win) {
        modal = eval(win);

        if (window.height < 600) {
            modal.SetHeight(window.height - 50);
        } else {
            modal.SetHeight(600);
        }

        if (window.width < 800) {
            modal.SetWidth(window.width - 50);
        } else {
            modal.SetWidth(800);
        }

        modal.SetContentUrl(url);
        modal.Show();
    }
    // ]]>
</script>

@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "pcModalMode_FAQ_Dashboard";
    //settings.ScrollBars = ScrollBars.Auto;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "FAQ";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(400) }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(400) }";
    //settings.ClientSideEvents.Closing = "function(s, e){ KundenView.Refresh(); }";

}).GetHtml()

@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "pcModalMode_Massnahme_Erfassen_Gesetzaenderungen";
    settings.ScrollBars = ScrollBars.Auto;
    settings.MaxHeight = 550;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = Resources.Allgemein_Button_Massnahme;
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(800) }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(800) }";

}).GetHtml()

@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "pcModalMode_Massnahme_Erfassen_Kommentare";
    settings.ScrollBars = ScrollBars.Auto;
    settings.MaxHeight = 550;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = Resources.Allgemein_Button_Massnahme;
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(800) }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(800) }";

}).GetHtml()

@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "pcModalMode_Massnahme_Erfassen_Vernehmlassung";
    settings.ScrollBars = ScrollBars.Auto;
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.MaxHeight = 550;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = Resources.Allgemein_Button_Massnahme;
    settings.Modal = true;
    settings.AllowResize = false;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(800); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(800); }";

}).GetHtml()