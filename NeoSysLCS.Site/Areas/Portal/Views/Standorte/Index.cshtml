@using NeoSysLCS.Repositories.ViewModels
@using NeoSysLCS.Resources.Properties

@model IQueryable<StandortViewModel>

@section Scripts {
    @Scripts.Render("~/Scripts/helpers/BatchEditDeleteHelper.js")
}

@{
    ViewBag.Title = NeoSysLCS.Site.Helpers.CultureHelper.GetStandortMenuName(0, 0, false);
    <div class="page-header" style="display:inline-block">
        <h1 style="display:inline-block">@NeoSysLCS.Site.Helpers.CultureHelper.GetStandortMenuName(0, 0, false)</h1>
        <button type="button" class="btn btn-default" style="border:none; background-color: transparent; outline:none; padding-left: 5px; padding-right: 0px; padding-bottom:20px; padding-top: 5px"
                onclick="javascript: ShowDetailPopup('/Portal/FAQPortal/ShowFAQPopup?view=Standorte', 'pcModalMode_FAQ_Standort');">
            <img src="~/Content/images/Question_Mark.png" style="width:20px;height:20px" />
        </button>
    </div>

    
    <div style="display:block; margin-bottom: 20px;">
        @Html.DevExpress().Button(settings =>
        {
            settings.Name = "masterdocument_btn";
            settings.Text = Resources.View_Masterdokument_Title;
            settings.RouteValues = new { Controller = "MasterKundendokument", Action = "Index" };
        }).GetHtml()
    </div>
    
}

@Html.Partial("_HtmlEditPopup", false)
@Html.Partial("StandorteGridView", Model)

<script type="text/javascript">
    //<![CDATA[

    function ShowDetailPopup(url, win) {
        modal = eval(win);

        if (window.height < 600) {
            modal.SetHeight(window.height - 50);
        } else {
            modal.SetHeight(600);
        }

        if (window.width < 800) {
            modal.SetWidth(window.width - 50);
        } else {
            modal.SetWidth(800);
        }

        modal.SetContentUrl(url);
        modal.Show();
    }
    // ]]>
</script>

@Html.DevExpress().PopupControl(settings =>
{

    settings.Name = "pcModalMode_FAQ_Standort";
    settings.ShowPageScrollbarWhenModal = true;
    settings.ResizingMode = ResizingMode.Live;
    settings.ShowRefreshButton = false;
    settings.AutoUpdatePosition = true;
    settings.AllowDragging = true;
    settings.CloseAction = CloseAction.CloseButton;
    settings.PopupAnimationType = AnimationType.None;
    settings.HeaderText = "FAQ";
    settings.Modal = true;
    settings.AllowResize = true;
    settings.PopupHorizontalAlign = PopupHorizontalAlign.WindowCenter;
    settings.PopupVerticalAlign = PopupVerticalAlign.WindowCenter;
    settings.ClientSideEvents.Init = "function(s, e) { s.SetWidth(400); }";
    settings.ClientSideEvents.Shown = "function(s, e) { s.SetWidth(400); }";
}).GetHtml()