@using System.Web.UI.WebControls
@using NeoSysLCS.Repositories
@using NeoSysLCS.Site.Areas.Portal.Controllers
@using NeoSysLCS.Site.Utilities.Export
@using NeoSysLCS.Resources.Properties

@Html.DevExpress().PageControl(pageControlSettings =>
{
    pageControlSettings.Name = "MassnahmePageControl";
    pageControlSettings.Width = Unit.Percentage(100);
    pageControlSettings.Height = Unit.Percentage(100);
    pageControlSettings.SaveStateToCookies = true;
    pageControlSettings.CallbackRouteValues = new
    {
        Controller = "Massnahme",
        Action = "MassnahmePageControlCallbacksPartial",
        KundeID = ViewData["KundeID"]
    };
    pageControlSettings.ClientSideEvents.ActiveTabChanging = "OnActiveTabChanging";

    UnitOfWork _unitOfWork = new UnitOfWork();
    MassnahmeController massnahmeController = new MassnahmeController();

    pageControlSettings.TabPages.Add(Resources.Enum_MassnahmeStatus_InProgress).SetContent(() =>
    {
        Html.BeginForm("ExportMassnahmen", "Massnahme", new { activeOnly = true }, FormMethod.Post, new { target = "_blank", id = "NeuMassnahmenExportWrapperForm" });
        ViewContext.Writer.Write("<div class=\"divToolbar\"><div class=\"divButtonsLeft\"><button class=\"btn btn-default pull-right\" type=\"submit\" value=\"" + ExportType.Xlsx + "\" name=\"exportFormat\">" + Resources.XLSX_Export_Format + "</button></div></div>");
        Html.EndForm();
        Html.RenderPartial(MassnahmeController.NeuMassnahmenView, massnahmeController.GetMassnahmeViewModelsByStandort(MassnahmeRepository.ActiveStatusArray));
    });
    pageControlSettings.TabPages.Add(Resources.Enum_MassnahmeStatus_Finished).SetContent(() =>
    {
        Html.BeginForm("ExportMassnahmen", "Massnahme", new { activeOnly = false }, FormMethod.Post, new { target = "_blank", id = "ErledigtMassnahmenExportWrapperForm" });
        ViewContext.Writer.Write("<div class=\"divToolbar\"><div class=\"divButtonsLeft\"><button class=\"btn btn-default pull-right\" type=\"submit\" value=\"" + ExportType.Xlsx + "\" name=\"exportFormat\">" + Resources.XLSX_Export_Format + "</button></div></div>");
        Html.EndForm();
        Html.RenderPartial(MassnahmeController.ErledigtMassnahmenView, massnahmeController.GetMassnahmeViewModelsByStandort(MassnahmeRepository.InactiveStatusArray));
    });

}).GetHtml()