using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Web.Mvc;
using System.Web;
using DevExpress.Web.Mvc;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Helpers;
using System.Globalization;
using Microsoft.AspNet.Identity;

namespace NeoSysLCS.Site.Areas.Portal.Controllers
{
    public class AuswertungController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;

        public AuswertungController()
        {
            _unitOfWork = new UnitOfWork();
        }

        public ActionResult Index(int? id)
        {
            ViewData["StandortID"] = id;
            var sessionHelper = new SessionHelper();
            var kundeID = sessionHelper.GetCurrentUser().KundeID;
            var viewModels = _unitOfWork.KundeRepository.GetStandorteByKundeViewModel(kundeID.HasValue ? kundeID.Value : 0);
            return View("Index", viewModels);
        }

        public ActionResult StandortAdministration(int? id)
        {
            ViewData["StandortID"] = id;
            var sessionHelper = new SessionHelper();
            var kundeID = sessionHelper.GetCurrentUser().KundeID;
            var viewModels = _unitOfWork.KundeRepository.GetStandorteByKundeViewModel(kundeID.HasValue ? kundeID.Value : 0);

            return PartialView("StandortAuswertung", viewModels);
        }

        public ActionResult StandortIndex(int id)
        {
            ViewData["StandortID"] = id;

            StandortViewModel viewModel;

            viewModel = _unitOfWork.StandortRepository.GetStandortViewModelById(id);

            var selectedMonths = CheckBoxListExtension.GetSelectedValues<string>("Months");
            string[] selectedMonthIds = selectedMonths;

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _unitOfWork.SpracheRepository.Get(s => s.Lokalisierung == language).FirstOrDefault();
            var standortSprachen = _unitOfWork.StandortRepository.GetByID(id).Sprachen.OrderBy(s => s.SpracheID);

            if (standortSprachen.Contains(sprache))
            {
                ViewData["SpracheID"] = sprache.SpracheID;
            }
            else
            {
                ViewData["SpracheID"] = standortSprachen.FirstOrDefault().SpracheID;
            }

            ViewData["StandortObjektID"] = 0;
            ViewData["RechtsbereichID"] = 0;
            ViewData["ShortcutID"] = 0;
            ViewData["selectedMonths"] = selectedMonthIds;

            return View("StandortIndex", viewModel);
        }

        public ActionResult StandortIndexObj(int standortObjektId)
        {
            ViewData["StandortObjektID"] = standortObjektId;
            ViewData["RechtsbereichID"] = 0;
            ViewData["ShortcutID"] = 0;
            ViewData["StandortID"] = _unitOfWork.StandortObjektRepository.GetByID(standortObjektId).StandortID;
            ViewData["selectedMonths"] = new string[] { };

            StandortViewModel viewModel;

            viewModel = _unitOfWork.StandortRepository.GetStandortViewModelById(_unitOfWork.StandortObjektRepository.GetByID(standortObjektId).StandortID.Value);


            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _unitOfWork.SpracheRepository.Get(s => s.Lokalisierung == language).FirstOrDefault();
            var standortSprachen = _unitOfWork.StandortRepository.GetByID(_unitOfWork.StandortObjektRepository.GetByID(standortObjektId).StandortID).Sprachen.OrderBy(s => s.SpracheID);

            if (standortSprachen.Contains(sprache))
            {
                ViewData["SpracheID"] = sprache.SpracheID;
            }
            else
            {
                ViewData["SpracheID"] = standortSprachen.FirstOrDefault().SpracheID;
            }
            return View("StandortIndex", viewModel);
        }

        public ActionResult ShortcutIndex(int shortcutID)
        {
            ViewData["ShortcutID"] = shortcutID;
            ViewData["RechtsbereichID"] = 0;
            ViewData["StandortObjektID"] = 0;
            ViewData["StandortID"] = _unitOfWork.ShortcutRepository.GetByID(shortcutID).StandortID;
            ViewData["selectedMonths"] = new string[] { };
            StandortViewModel viewModel;

            viewModel = _unitOfWork.StandortRepository.GetStandortViewModelById(_unitOfWork.ShortcutRepository.GetByID(shortcutID).StandortID);


            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _unitOfWork.SpracheRepository.Get(s => s.Lokalisierung == language).FirstOrDefault();
            var standortSprachen = _unitOfWork.StandortRepository.GetByID(_unitOfWork.ShortcutRepository.GetByID(shortcutID).StandortID).Sprachen.OrderBy(s => s.SpracheID);

            if (standortSprachen.Contains(sprache))
            {
                ViewData["SpracheID"] = sprache.SpracheID;
            }
            else
            {
                ViewData["SpracheID"] = standortSprachen.FirstOrDefault().SpracheID;
            }
            return View("StandortIndex", viewModel);
        }

        public ActionResult RechtsbereichIndex(int rechtsbereichId)
        {
            int standortId = (int) Session["StandortID"];
            ViewData["RechtsbereichID"] = rechtsbereichId;
            ViewData["ShortcutID"] = 0;
            ViewData["StandortObjektID"] = 0;
            ViewData["StandortID"] = standortId;
            ViewData["selectedMonths"] = new string[] { };
            StandortViewModel viewModel;
            viewModel = _unitOfWork.StandortRepository.GetStandortViewModelById(standortId);


            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _unitOfWork.SpracheRepository.Get(s => s.Lokalisierung == language).FirstOrDefault();
            var standortSprachen = _unitOfWork.StandortRepository.GetByID(standortId).Sprachen.OrderBy(s => s.SpracheID);

            if (standortSprachen.Contains(sprache))
            {
                ViewData["SpracheID"] = sprache.SpracheID;
            }
            else
            {
                ViewData["SpracheID"] = standortSprachen.FirstOrDefault().SpracheID;
            }
            return View("StandortIndex", viewModel);
        }

        public ActionResult StandortPageControlCallbacksPartial(int standortID, int spracheID, int standortObjektID = 0, int rechtsbereichID = 0, int shortcutID = 0, params string[] selectedMonthIds)
        {
            ViewData["StandortID"] = standortID;
            ViewData["SpracheID"] = spracheID;
            ViewData["StandortObjektID"] = standortObjektID;
            ViewData["RechtsbereichID"] = rechtsbereichID;
            ViewData["ShortcutID"] = shortcutID;
            if (selectedMonthIds == null)
            {
                selectedMonthIds = new string[] { };
            }
            ViewData["selectedMonths"] = selectedMonthIds;
            return PartialView("StandortPageControlCallbacksPartial");
        }




    }


}