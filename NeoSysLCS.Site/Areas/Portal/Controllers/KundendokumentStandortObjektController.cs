using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web.Mvc;
using System.Web.SessionState;
using DevExpress.Data.ODataLinq.Helpers;
using DevExpress.Web;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Controllers;
using DevExpress.Web.Mvc;
using NeoSysLCS.Site.Helpers;

namespace NeoSysLCS.Site.Areas.Portal.Controllers
{
    [SessionState(SessionStateBehavior.ReadOnly)]
    public class KundendokumentStandortObjektController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly NeoSysLCS_Dev context;

        public KundendokumentStandortObjektController()
        {
            _unitOfWork = new UnitOfWork();
            context = new NeoSysLCS_Dev();
        }

        public KundendokumentStandortObjektController(IUnitOfWork unitOfWorkCandidate)
        {
            _unitOfWork = unitOfWorkCandidate;
        }

        /// <summary>
        /// Shows the standortobjekte
        /// </summary>
        /// <param name="standortID">The standort identifier.</param>
        /// <returns></returns>
        public ActionResult KundendokumentStandortObjektGridView(int kundendokumentID, int standortID, int spracheID)
        {
            ViewData["KundendokumentID"] = kundendokumentID;
            ViewData["StandortID"] = standortID;
            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _unitOfWork.SpracheRepository.Get(s => s.Lokalisierung == language).FirstOrDefault();
            var standortSprachen = _unitOfWork.StandortRepository.GetByID(standortID).Sprachen.OrderBy(s => s.SpracheID);

            if (standortSprachen.Contains(sprache))
            {
                ViewData["SpracheID"] = sprache.SpracheID;
            }
            else
            {
                ViewData["SpracheID"] = standortSprachen.FirstOrDefault().SpracheID;
            }

            var standortObjekte = _unitOfWork.StandortObjektRepository.GetAllStandortObjektViewModelsByKundendokument(kundendokumentID, spracheID);
            return PartialView("KundendokumentStandortObjektGridView", standortObjekte);
        }

        /// <summary>
        /// Shows all standortobjekte
        /// </summary>
        /// <param name="standortID">The standort identifier.</param>
        /// <returns></returns>
        public ActionResult AllStandortObjekteGridView()
        {
            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _unitOfWork.SpracheRepository.Get(s => s.Lokalisierung == language).FirstOrDefault();
            ViewData["SpracheID"] = sprache.SpracheID;
            var standorteVm = usersStandorte();
            var standortIds = standorteVm.Select(s => s.StandortID);
            ViewData["AllStandorteIds"] = String.Join(", ", standortIds);
            var sh = new SessionHelper();
            ViewData["KundeID"] = sh.GetCurrentUser().KundeID;

            var standortObjekte = new List<StandortObjektViewModel>() { };
            foreach (var standortVm in standorteVm)
            {
                var kundendokumentVm = _unitOfWork.KundendokumentRepository.GetCurrentKundendokumentViewModel(standortVm.StandortID);
                
                if (kundendokumentVm != null)
                {
                    standortObjekte.AddRange(
                    _unitOfWork.StandortObjektRepository.GetAllStandortObjektViewModelsByKundendokumentFilteredByKunde(kundendokumentVm.KundendokumentID, sprache.SpracheID, standortVm.KundeID)
                     );
                }
            }

            return PartialView("AllStandortObjekteGridView", standortObjekte.AsQueryable());
        }

        private List<StandortViewModel> usersStandorte()
        {
            var sh = new SessionHelper();
            var kundeID = sh.GetCurrentUser().KundeID;
            return _unitOfWork.StandortRepository.GetAllStandortViewModels(kundeID.HasValue ? kundeID.Value : 0).AsEnumerable().ToList();
        }

        /// <summary>
        /// Shows all uebersetzungen of the specified StandortObjekt
        /// </summary>
        /// <param name="id">The id.</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult StandortObjektDetailGridView(int id, int standortID)
        {
            var uebersetzungen = _unitOfWork.StandortObjektRepository.GetStandortObjektUebersetzungenByStandort(id, standortID);

            ViewData["StandortObjektID"] = id;
            ViewData["StandortIID"] = standortID;
            var sh = new SessionHelper();
            ViewData["KundeID"] = sh.GetCurrentUser().KundeID;
            return PartialView("StandortObjektDetailGridView", uebersetzungen);
        }

        /// <summary>
        /// Saves all changes of standortobjekte
        /// </summary>
        /// <param name="updateValues">The update values.</param>
        /// <param name="standortID">The standort identifier.</param>
        /// <returns></returns>

        [ValidateInput(false)]
        public ActionResult AllStandortObjekteGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<StandortObjektViewModel, int> updateValues, int spracheID)
        {
            if (ModelState.IsValid)
            {
                foreach (var standortObjekt in updateValues.Update)
                {
                    //Get ObjektID und Beschreibung because the columns are not visible and the grid doesn't get the data from non-visible columns
                    var so = _unitOfWork.StandortObjektRepository.GetByID(standortObjekt.StandortObjektID);
                    standortObjekt.StandortID = so.StandortID;
                    standortObjekt.ObjektID = so.ObjektID;
                    standortObjekt.SpracheID = spracheID;
                    //standortObjekt.Beschreibung = _unitOfWork.StandortObjektRepository.Get(u => u.SpracheID == spracheID && u.StandortObjektID == standortObjekt.StandortObjektID).FirstOrDefault().Beschreibung;
                    _unitOfWork.StandortObjektRepository.Update(standortObjekt, "Portal");
                    _unitOfWork.Save();
                }
            }
            
            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _unitOfWork.SpracheRepository.Get(s => s.Lokalisierung == language).FirstOrDefault();
            ViewData["SpracheID"] = sprache.SpracheID;
            var standorteVm = usersStandorte();
            var standortIds = standorteVm.Select(s => s.StandortID);
            ViewData["AllStandorteIds"] = String.Join(", ", standortIds);
            var sh = new SessionHelper();
            ViewData["KundeID"] = sh.GetCurrentUser().KundeID;

            var standortObjekte = new List<StandortObjektViewModel>() { };
            foreach (var standortVm in standorteVm)
            {
                var kundendokumentVm = _unitOfWork.KundendokumentRepository.GetCurrentKundendokumentViewModel(standortVm.StandortID);
                if (kundendokumentVm != null) {
                    standortObjekte.AddRange(_unitOfWork.StandortObjektRepository.GetAllStandortObjektViewModelsByKundendokumentFilteredByKunde(kundendokumentVm.KundendokumentID, sprache.SpracheID, standortVm.KundeID));
                }
            }

            return PartialView("AllStandortObjekteGridView", standortObjekte.AsQueryable());
        }

        /// <summary>
        /// Saves all changes of standortobjekte
        /// </summary>
        /// <param name="updateValues">The update values.</param>
        /// <param name="standortID">The standort identifier.</param>
        /// <returns></returns>

        [ValidateInput(false)]
        public ActionResult StandortObjektGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<StandortObjektViewModel, int> updateValues, int standortID, int kundendokumentID, int spracheID)
        {
            if(ModelState.IsValid)
            {
                foreach (var standortObjekt in updateValues.Update)
                {
                    //Get ObjektID und Beschreibung because the columns are not visible and the grid doesn't get the data from non-visible columns
                    standortObjekt.StandortID = standortID;
                    standortObjekt.ObjektID = _unitOfWork.StandortObjektRepository.GetByID(standortObjekt.StandortObjektID).ObjektID;
                    standortObjekt.SpracheID = spracheID;
                    //standortObjekt.Beschreibung = _unitOfWork.StandortObjektRepository.Get(u => u.SpracheID == spracheID && u.StandortObjektID == standortObjekt.StandortObjektID).FirstOrDefault().Beschreibung;
                    _unitOfWork.StandortObjektRepository.Update(standortObjekt, "Portal");
                    _unitOfWork.Save();
                }
            }

            ViewData["StandortID"] = standortID;
            ViewData["KundendokumentID"] = kundendokumentID;
            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _unitOfWork.SpracheRepository.Get(s => s.Lokalisierung == language).FirstOrDefault();
            var standortSprachen = _unitOfWork.StandortRepository.GetByID(standortID).Sprachen.OrderBy(s => s.SpracheID);
            if (standortSprachen.Contains(sprache))
            {
                ViewData["SpracheID"] = sprache.SpracheID;
            }
            else
            {
                ViewData["SpracheID"] = standortSprachen.FirstOrDefault().SpracheID;
            }
            var viewModels = _unitOfWork.StandortObjektRepository.GetAllStandortObjektViewModelsByKundendokument(kundendokumentID, spracheID);
            return PartialView("KundendokumentStandortObjektGridView", viewModels);
        }

        /// <summary>
        /// Update Action for the uebersetzungen gridview
        /// </summary>
        /// <param name="updateValues">The update values.</param>
        /// <param name="StandortObjektId">The StandortObjekt id.</param>
        /// <returns></returns>

        [ValidateInput(false)]
        public ActionResult StandortObjektDetailGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<StandortObjektViewModel, int> updateValues, int StandortObjektId)
        {
            foreach (var uebersetzung in updateValues.Insert)
            {
                GridViewUpdateHelper<StandortObjekt, StandortObjektViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(uebersetzung))
                    {
                        _unitOfWork.StandortObjektRepository.Insert(uebersetzung);
                        _unitOfWork.Save();
                    }
                }, uebersetzung, updateValues);

            }
            foreach (var uebersetzung in updateValues.Update)
            {
                GridViewUpdateHelper<StandortObjekt, StandortObjektViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(uebersetzung))
                    {
                        uebersetzung.StandortObjektID = StandortObjektId;
                        _unitOfWork.StandortObjektRepository.UpdateStandortObjektUebersetzung(uebersetzung);
                        _unitOfWork.Save();
                    }
                }, uebersetzung, updateValues);
            }
            foreach (var uebersetzungId in updateValues.DeleteKeys)
            {
                GridViewUpdateHelper<StandortObjekt, StandortObjektViewModel>.DoDelete(() =>
                {
                    _unitOfWork.StandortObjektRepository.Delete(uebersetzungId);
                    _unitOfWork.Save();

                }, _unitOfWork.StandortObjektRepository, uebersetzungId, updateValues);
            }

            ViewData["StandortObjektID"] = StandortObjektId;
            var standortId = (from x in context.StandortObjekte
                                where x.StandortObjektID == StandortObjektId
                                select x.StandortID).FirstOrDefault();
            //ViewData["StandortID"] = standortId;
            var uebersetzungen = _unitOfWork.StandortObjektRepository.GetStandortObjektUebersetzungenByStandortObjekt(StandortObjektId);

            return PartialView("StandortObjektDetailGridView", uebersetzungen);
        }
    }
}