using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using System.Web.UI;
using DevExpress.Utils;
using DevExpress.Web;
using DevExpress.Web.Mvc;
using DevExpress.XtraPrinting.Native;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Helpers;
using NeoSysLCS.Site.Utilities.Export;


namespace NeoSysLCS.Site.Areas.Portal.Controllers
{
    [Authorize]
    public class MassnahmeController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;
        public const string NeuMassnahmenView = "~/Areas/Portal/Views/Massnahme/_NeuMassnahmenPartialView.cshtml";
        public const string ErledigtMassnahmenView = "~/Areas/Portal/Views/Massnahme/_ErledigtMassnahmenPartialView.cshtml";

        public MassnahmeController()
        {
            _unitOfWork = new UnitOfWork();
        }


        public ActionResult Index(int standortId = 0)
        {
            var sh = new SessionHelper();
            var kundenId = sh.GetKundeIdOfCurrentUser();

            ViewData["KundeID"] = kundenId;

            var standorteIDs = usersStandorteIDs(_unitOfWork);
            ViewData["UsersStandorteIDs"] = string.Join(",", standorteIDs.ToArray());
            ViewData["HideDeleteButton"] = hideDeleteButtonForUser(sh);
            return View("Index");
        }

        public ActionResult MassnahmePageControlCallbacksPartial(int KundeID)
        {
            ViewData["KundeID"] = KundeID;
            var standorteIDs = usersStandorteIDs(_unitOfWork);
            ViewData["UsersStandorteIDs"] = string.Join(",", standorteIDs.ToArray());
            ViewData["HideDeleteButton"] = hideDeleteButtonForUser(null);
            return PartialView("_MassnahmePageControlCallbacksPartial");
        }

        public ActionResult NeuMassnahmePartialView()
        {
            var standorteIDs = usersStandorteIDs(_unitOfWork);
            ViewData["UsersStandorteIDs"] = string.Join(",", standorteIDs.ToArray());
            ViewData["HideDeleteButton"] = hideDeleteButtonForUser(null);
            var massnahmen = GetMassnahmeViewModelsByStandort(MassnahmeRepository.ActiveStatusArray);
            return PartialView(NeuMassnahmenView, massnahmen);
        }

        public ActionResult ErledigtMassnahmePartialView()
        {
            var standorteIDs = usersStandorteIDs(_unitOfWork);
            ViewData["UsersStandorteIDs"] = string.Join(",", standorteIDs.ToArray());
            ViewData["HideDeleteButton"] = hideDeleteButtonForUser(null);
            var massnahmen = GetMassnahmeViewModelsByStandort(MassnahmeRepository.InactiveStatusArray);
            return PartialView(ErledigtMassnahmenView, massnahmen);
        }
        public IQueryable<MassnahmeViewModel> GetMassnahmeViewModelsByStandort(MassnahmeStatus[] statuses = null)
        {
            var sessionHelper = new SessionHelper();
            var kundeID = sessionHelper.GetCurrentUser().KundeID;
            var roles = sessionHelper.GetCurrentUserRoles();
            var userId = sessionHelper.GetCurrentUser().Id;
            // reverting changes prior to commit: 17810741022d9b8fe34c7efb20d6ca4ee5ff3095
            List<int> shortcutsIds = new List<int>();
            if (HasEditorAndOrNewsletterRole(roles))
            {
                shortcutsIds = _unitOfWork.ShortcutRepository.GetAllShortcutIdsByUserId(userId);
                // add default shortcut if the user is not assigned to any
                if (!shortcutsIds.Any())
                {
                    shortcutsIds.Add(1);
                }
            }

            return _unitOfWork.MassnahmeRepository.GetMassnahmeViewModels(shortcutsIds, statuses);
        }

        private bool HasEditorAndOrNewsletterRole(IList<string> roles)
        {
            if (roles.Count == 1 && (roles.Contains("ACCOUNTABLE") || roles.Contains("NEWSLETTER")))
            {
                return true;
            }

            if (roles.Count == 2 && roles.Contains("ACCOUNTABLE") && roles.Contains("NEWSLETTER"))
            {
                return true;
            }

            return false;
        }


        /// <summary>
        /// Shows all uebersetzungen of the specified Massnahme
        /// </summary>
        /// <param name="massnahmeID">The massnahme id.</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult MassnahmeUebersetzungenGridView(int massnahmeID)
        {
            var uebersetzungen = _unitOfWork.MassnahmeRepository.GetMassnamhmeUebersetzungenByMassnahmeId(massnahmeID);

            ViewData["MassnahmeID"] = massnahmeID;
            ViewData["HideDeleteButton"] = hideDeleteButtonForUser(null);
            return PartialView("_MassnahmeUebersetzungenGridView", uebersetzungen);
        }


        /// <summary>
        /// Update action for the Massnahme uebersetzungen grid view.
        /// </summary>
        /// <param name="updateValues">The update values.</param>
        /// <param name="erlassId">The massnahme id.</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult MassnahmeUebersetzungenGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<MassnahmeViewModel, int> updateValues, int massnahmeID)
        {
            foreach (var uebersetzung in updateValues.Update)
            {
                GridViewUpdateHelper<Massnahme, MassnahmeViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(uebersetzung))
                    {
                        _unitOfWork.MassnahmeRepository.InsertUebersetzungMassnahme(uebersetzung, massnahmeID);
                        _unitOfWork.Save();
                    }
                }, uebersetzung, updateValues);
            }

            ViewData["MassnahmeID"] = massnahmeID;
            var uebersetzungen = _unitOfWork.MassnahmeRepository.GetMassnamhmeUebersetzungenByMassnahmeId(massnahmeID);

            ViewData["HideDeleteButton"] = hideDeleteButtonForUser(null);
            return PartialView("_MassnahmeUebersetzungenGridView", uebersetzungen);
        }


        [ValidateInput(false)]
        public ActionResult NeuMassnahmeGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<MassnahmeViewModel, int> updateValues)
        {
            updateMassnahme(updateValues);
            var standorteIDs = usersStandorteIDs(_unitOfWork);
            ViewData["UsersStandorteIDs"] = string.Join(",", standorteIDs.ToArray());

            var massnahmen = GetMassnahmeViewModelsByStandort(MassnahmeRepository.ActiveStatusArray);
            return PartialView(NeuMassnahmenView, massnahmen);

        }

        [ValidateInput(false)]
        public ActionResult ErledigtMassnahmeGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<MassnahmeViewModel, int> updateValues)
        {
            updateMassnahme(updateValues);
            var standorteIDs = usersStandorteIDs(_unitOfWork);
            ViewData["UsersStandorteIDs"] = string.Join(",", standorteIDs.ToArray());

            var massnahmen = GetMassnahmeViewModelsByStandort(MassnahmeRepository.InactiveStatusArray);
            return PartialView(ErledigtMassnahmenView, massnahmen);

        }

        private void updateMassnahme(MVCxGridViewBatchUpdateValues<MassnahmeViewModel, int> updateValues)
        {
            foreach (var massnahme in updateValues.Update)
            {
                GridViewUpdateHelper<Massnahme, MassnahmeViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(massnahme))
                    {
                        var k = _unitOfWork.MassnahmeRepository.GetByID(massnahme.MassnahmeID);
                        massnahme.BearbeitetAm = DateTime.Now;
                        massnahme.BearbeitetVonID = User.Identity.GetUserId();
                        if (k.ErstelltAm != null) massnahme.ErstelltAm = k.ErstelltAm.Value;
                        if (k.ErstelltVonID != null) massnahme.ErstelltVonID = k.ErstelltVonID;
                        _unitOfWork.MassnahmeRepository.Update(massnahme);
                        _unitOfWork.Save();
                    }
                }, massnahme, updateValues);
            }
            foreach (var massnahmeID in updateValues.DeleteKeys)
            {
                GridViewUpdateHelper<Offer, MassnahmeViewModel>.DoDelete(() =>
                {
                    Massnahme massnahmeToDelete = _unitOfWork.MassnahmeRepository.GetByID(massnahmeID);
                    _unitOfWork.MassnahmeRepository.Delete(massnahmeToDelete);
                    _unitOfWork.Save();
                }, _unitOfWork.MassnahmeRepository, massnahmeID, updateValues);

            }
        }

        public ActionResult ExportMassnahmen(bool activeOnly = true)
        {
            var viewModels = activeOnly
                ? GetMassnahmeViewModelsByStandort(MassnahmeRepository.ActiveStatusArray).ToList()
                : GetMassnahmeViewModelsByStandort(MassnahmeRepository.InactiveStatusArray).ToList();

            var exportType = ExportUtilities.GetExportType(Request.Params["exportFormat"]);
            var exportResult = ExportUtilities.Export(exportType, GetExportGridSettings(exportType, activeOnly), viewModels,
                false);

            if (exportResult != null)
            {
                return exportResult;
            }

            return RedirectToAction("NeuMassnahmePartialView");
        }


        /// <summary>
        /// Gets the export settings of the grid
        /// </summary>
        /// <param name="exportType">Type of the export.</param>
        /// <returns></returns>
        private GridViewSettings GetExportGridSettings(ExportType exportType, bool activeOnly)
        {
            var settings = new GridViewSettings();
            if (activeOnly)
            {
                settings.Name = MassnahmeController.BaseGridViewSettings.GetInArbeitGridViewName();
            }
            else
            {
                settings.Name = MassnahmeController.BaseGridViewSettings.GetErledigtGridViewName();
            }
            settings.KeyFieldName = MassnahmeController.BaseGridViewSettings.GetGridViewKeyFieldName();

            //columns: there need to be the same colums in the view and for the export for sorting
            settings.CommandColumn.Visible = true;
            foreach (MVCxGridViewColumn col in MassnahmeController.BaseGridViewSettings.GetDataColumns())
            {
                settings.Columns.Add(col);
            }

            GridViewHelper.ApplyDefaultExportSettings(settings, exportType);
            return settings;
        }

        /// <summary>
        ///  Inner Class for the Gridview Settings (this avoids a separat defninition of the settings in the view and the export).
        /// </summary>
        public static class BaseGridViewSettings
        {
            public static string GetInArbeitGridViewName()
            {
                return "MassnahmeInProgressGridView";
            }

            public static string GetErledigtGridViewName()
            {
                return "MassnahmeErledigtGridView";
            }

            public static string GetGridViewKeyFieldName()
            {
                return "MassnahmeID";

            }

            public static List<ShortcutViewModel> GetShortcutsList(object standortId)
            {

                IUnitOfWork unitOfWork = new UnitOfWork();
                var sIDs = new List<int>();
                if (standortId != null)
                {
                    sIDs.Add((int)standortId);
                }
                else
                {
                    sIDs.AddRange(usersStandorteIDs(unitOfWork).ToList());
                }

                var sc = unitOfWork.ShortcutRepository.GetAllShortcutViewModelsByStandortIds(sIDs).OrderBy(x => x.Name).ToList();
                return sc;
            }

            /// <summary>
            /// Gets the column definition
            /// </summary>
            /// <returns></returns>
            public static List<MVCxGridViewColumn> GetDataColumns()
            {
                IUnitOfWork unitOfWork = new UnitOfWork();
                SessionHelper sessionHelper = new SessionHelper();
                var columns = new MVCxGridViewColumnCollection();

                int kundeId = sessionHelper.GetKundeIdOfCurrentUser().Value;

                columns.Add(column =>
                {
                    column.FieldName = "KundeMassnahmeID";
                    column.Caption = "ID";
                    column.ReadOnly = true;
                    column.EditFormSettings.Visible = DefaultBoolean.False;
                    column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                });

                columns.Add(column =>
                {
                    column.FieldName = "ErstelltAm";
                    column.Caption = Resources.Properties.Resources.Entitaet_Massnahme_Created;
                    column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    column.ReadOnly = true;
                    column.EditFormSettings.Visible = DefaultBoolean.False;
                    column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                    column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.DateRangePicker;
                });

                columns.Add(column =>
                {
                    column.FieldName = "Betreff";
                    column.Caption = Resources.Properties.Resources.Entitaet_Massnahme_Betreff;
                    column.ReadOnly = true;
                    column.EditFormSettings.Visible = DefaultBoolean.False;
                    column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                });


                columns.Add(column =>
                {
                    column.FieldName = "MassnahmeText";
                    column.Caption = Resources.Properties.Resources.Entitaet_Massnahme_Singular;
                    column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                });

                columns.Add(column =>
                {
                    column.FieldName = "StandortID";
                    column.MinWidth = 105;
                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                    column.Caption = CultureHelper.GetStandortMenuName();
                    column.ReadOnly = true;
                    column.EditFormSettings.Visible = DefaultBoolean.False;
                    column.ColumnType = MVCxGridViewColumnType.ComboBox;
                    var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
                    comboBoxProperties.DataSource = unitOfWork.StandortRepository.GetAllStandortViewModels(kundeId).ToList();
                    comboBoxProperties.ValueField = "StandortID";
                    comboBoxProperties.TextField = "Name";
                });

                columns.Add(column =>
                {
                    column.FieldName = "Bemerkung";
                    column.Caption = Resources.Properties.Resources.Entitaet_Massnahme_Bemerkung;
                    column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                });

                columns.Add(column =>
                {
                    column.FieldName = "ErstelltVon";
                    column.Caption = Resources.Properties.Resources.Entitaet_Base_ErstelltVon;
                    column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                    column.ReadOnly = true;
                    column.EditFormSettings.Visible = DefaultBoolean.False;
                });

                columns.Add(column =>
                {
                    column.FieldName = "Status";
                    column.Caption = Resources.Properties.Resources.Entitaet_Massnahme_Status;
                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                    column.ColumnType = MVCxGridViewColumnType.ComboBox;
                    column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                    var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

                    var list = from MassnahmeStatus value in Enum.GetValues(typeof(MassnahmeStatus))
                               select new
                               {
                                   Id = (int)value,
                                   Name = value.GetTranslation()
                               };

                    comboBoxProperties.DataSource = list;
                    comboBoxProperties.ValueField = "Id";
                    comboBoxProperties.TextField = "Name";
                    comboBoxProperties.ValueType = typeof(Int32);
                    comboBoxProperties.ValidationSettings.RequiredField.IsRequired = true;
                    comboBoxProperties.ValidationSettings.RequiredField.ErrorText = Resources.Properties.Resources.Fehler_FehlendeWerte;
                });

                columns.Add(column =>
                {
                    column.FieldName = "Termin";
                    column.Caption = Resources.Properties.Resources.Entitaet_Massnahme_Termin;
                    column.ColumnType = MVCxGridViewColumnType.DateEdit;

                    column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                    column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.DateRangePicker;
                });

                columns.Add(column =>
                {
                    column.FieldName = "Link";
                    column.ReadOnly = true;
                    column.EditFormSettings.Visible = DefaultBoolean.False;
                    column.Caption = Resources.Properties.Resources.View_News_Link;
                });

                columns.Add(column =>
                {
                    column.FieldName = "ShortcutID";
                    column.ReadOnly = true;
                    column.Caption = Resources.Properties.Resources.Entitaet_KundendokumentForderungsversion_Verantwortlich;
                    column.ColumnType = MVCxGridViewColumnType.ComboBox;
                    column.Settings.FilterMode = ColumnFilterMode.DisplayText;

                    var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
                    comboBoxProperties.TextField = "Name";
                    comboBoxProperties.ValueField = "ShortcutID";
                    comboBoxProperties.ValueType = typeof(int);
                    var standorteIDs = usersStandorteIDs(unitOfWork);
                    var usersStandOrteIDs = string.Join(",", standorteIDs.ToArray());
                    var standortIDList = usersStandOrteIDs.Split(',').Select(standortId => Int32.Parse(standortId));
                    comboBoxProperties.DataSource = unitOfWork.ShortcutRepository.GetAllShortcutViewModelsByStandortIds(standortIDList).Select(x => new { x.ShortcutID, x.Name }).OrderBy(x => x.Name);
                });

                //make a list from the MCxGridViewColumnCollection otherwise settings.Columns.Add(col) won't work --> DevExpress 16.2.15
                var columList = new List<MVCxGridViewColumn>();
                foreach (MVCxGridViewColumn column in columns)
                {
                    columList.Add(column);
                };

                return columList;
            }

        }

        private static List<int> usersStandorteIDs(IUnitOfWork _unitOfWork)
        {
            var sh = new SessionHelper();
            var kundeID = sh.GetCurrentUser().KundeID;
            return _unitOfWork.StandortRepository.GetAllStandortViewModels(kundeID.HasValue ? kundeID.Value : 0).Select(s => s.StandortID).AsEnumerable().ToList();
        }
        private static bool hideDeleteButtonForUser(SessionHelper sessionHelper)
        {
            var sh = sessionHelper ?? new SessionHelper();
            var roles = sh.GetCurrentUserRoles();
            return roles.Contains("ACCOUNTABLE") || roles.Contains("READONLY");
        }

    }
}