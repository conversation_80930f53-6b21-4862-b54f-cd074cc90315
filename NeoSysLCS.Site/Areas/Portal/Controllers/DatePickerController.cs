using DevExpress.Web.Mvc;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace NeoSysLCS.Site.Areas.Portal.Controllers
{
    public class DatePickerController : Controller
    {
        private static string[] standortIdsTest;

        public ActionResult Index()
        {
            var sh = new SessionHelper();
            var currentUser = sh.GetCurrentUser();
            int kundeId = (int)currentUser.KundeID;
            ViewData["KundeId"] = kundeId;

            var selectedStandorte = CheckBoxListExtension.GetSelectedValues<string>("Standort");
            ViewData["selectedStandorte"] = selectedStandorte;
            standortIdsTest = selectedStandorte;

            //var viewModels = GetAllViewModels();
            return View("DatePicker");
        }
    }
}