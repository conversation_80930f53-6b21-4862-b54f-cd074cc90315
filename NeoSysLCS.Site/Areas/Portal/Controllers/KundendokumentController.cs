using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Mvc;
using DevExpress.Web.Mvc;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Helpers;
using System.Globalization;
using Microsoft.AspNet.Identity;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Site.Utilities.Export;
using DevExpress.Web;
using System.Web.UI.WebControls;
using DevExpress.Utils;
using DevExpress.Data;
using System.Web.UI;
using DevExpress.XtraPrinting;
using System.IO;
using DevExpress.XtraPrintingLinks;
using System.Net;
using System.Drawing;
using DevExpress.Export;
using System.Diagnostics;
using System.Web.WebPages;

namespace NeoSysLCS.Site.Areas.Portal.Controllers
{
    public class KundendokumentController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;
        public KundendokumentController()
        {
            _unitOfWork = new UnitOfWork();
        }

        public KundendokumentController(IUnitOfWork unitOfWorkCandidate)
        {
            _unitOfWork = unitOfWorkCandidate;
        }

        public ActionResult Index(int id, int? kundendokumentId)
        {
            var sh = new SessionHelper();
            var roleId = _unitOfWork.UserStandortRoleRepository.GetRoleIdById(sh.GetCurrentUserID() + "_" + id);
            if (roleId != null)
            {
                ViewData["roleId"] = roleId;
            } else
            {
                ViewData["roleId"] = "NoRole";
            }

            ViewData["StandortID"] = id;


            KundendokumentViewModel viewModel;

            if (kundendokumentId.HasValue)
            {
                viewModel = _unitOfWork.KundendokumentRepository.GetFreigegenesKundendokumentViewModelByID(kundendokumentId.Value);
            }
            else
            {
                viewModel = _unitOfWork.KundendokumentRepository.GetCurrentKundendokumentViewModel(id);
            }
            if (viewModel != null)
            {
                ViewData["KundendokumentID"] = viewModel.KundendokumentID;
            }


            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _unitOfWork.SpracheRepository.Get(s => s.Lokalisierung == language).FirstOrDefault();
            var standortSprachen = _unitOfWork.StandortRepository.GetByID(id).Sprachen.OrderBy(s => s.SpracheID);

            if (standortSprachen.Contains(sprache))
            {
                ViewData["SpracheID"] = sprache.SpracheID;
            }
            else
            {
                ViewData["SpracheID"] = standortSprachen.FirstOrDefault().SpracheID;
            }

            return View("Index", viewModel);
        }

        /// <summary>
        /// Shows all kundendokumente of the specified standort
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        public ActionResult KundendokumentArchivGridView(int id)
        {
            ViewData["StandortID"] = id;
            var viewModels = _unitOfWork.KundendokumentRepository.GetAllFreigegebeneKundendokumentViewModel(id);
            return PartialView("KundendokumentArchivGridView", viewModels);
        }

        private List<int> usersStandorteIds(SessionHelper sh, int? standortId)
        {
            if (standortId != null)
            {
                return new List<int>() {(int) standortId};
            }

            var kundeID = sh.GetCurrentUser().KundeID;
            return _unitOfWork.StandortRepository.GetAllStandortViewModels(kundeID.HasValue ? kundeID.Value : 0).Select(s => s.StandortID).AsEnumerable().ToList();
        }

        public ActionResult KundendokumentSpaltenlabelsGridView(int? standortId)
        {
            var sh = new SessionHelper();
            var cols = InitializeKundendokumentSpaltenlabelsGridViewData(sh, standortId);
            ViewData["KundeID"] = sh.GetCurrentUser().KundeID;

            return PartialView("KundendokumentSpaltenlabelsGridView", cols);
        }

        private List<KundendokumentSpaltenlabelViewModel> InitializeKundendokumentSpaltenlabelsGridViewData(SessionHelper sh, int? standortId)
        {

            var standorteIds = usersStandorteIds(sh, standortId);
            var cols = new List<KundendokumentSpaltenlabelViewModel>();
            foreach (var standorteId in standorteIds)
            {
                var roleId = _unitOfWork.UserStandortRoleRepository.GetRoleIdById(sh.GetCurrentUserID() + "_" + standorteId);
                if (roleId != null)
                {
                    ViewData["roleId"] = roleId;
                }
                else
                {
                    ViewData["roleId"] = "NoRole";
                }

                var kd = _unitOfWork.KundendokumentRepository.GetCurrentKundendokumentViewModel(standorteId);
                var kdId = kd?.KundendokumentID ?? 0;

                var columns = _unitOfWork.KundendokumentRepository.GetByID(kdId, "KundendokumentSpaltenlabel")?.KundendokumentSpaltenlabel ?? null;
                var col = new KundendokumentSpaltenlabelViewModel()
                {

                    StandortId = standorteId,
                    KundendokumentId = kdId,
                    StandortName = kd?.StandortName,
                };

                if (columns != null)
                    col = new KundendokumentSpaltenlabelViewModel()
                    {
                        KundendokumentSpaltenlabelID = columns.KundendokumentSpaltenlabelID,
                        StandortId = standorteId,
                        KundendokumentId = kdId,
                        StandortName = kd?.StandortName,
                        LabelSpalte1 = columns.LabelSpalte1,
                        LabelSpalte2 = columns.LabelSpalte2,
                        LabelSpalte3 = columns.LabelSpalte3,
                        LabelSpalte4 = columns.LabelSpalte4,
                        LabelSpalte5 = columns.LabelSpalte5,
                        LabelSpalte6 = columns.LabelSpalte6,
                        LabelSpalte7 = columns.LabelSpalte7,
                        LabelSpalte8 = columns.LabelSpalte8,
                        LabelSpalte9 = columns.LabelSpalte9,
                        LabelSpalte10 = columns.LabelSpalte10
                    };

                if (kdId != 0)
                {
                    cols.Add(col);
                }
            }
            ViewData["KundeID"] = sh.GetCurrentUser().KundeID;
            return cols;
        }

        public ActionResult KundendokumentPageControlCallbacksPartial(int standortID, int kundendokumentID, int spracheID)
        {
            var sh = new SessionHelper();
            var roleId = _unitOfWork.UserStandortRoleRepository.GetRoleIdById(sh.GetCurrentUserID() + "_" + standortID);
            if (roleId != null)
            {
                ViewData["roleId"] = roleId;
            }
            else
            {
                ViewData["roleId"] = "NoRole";
            }
            ViewData["StandortID"] = standortID;
            ViewData["KundendokumentID"] = kundendokumentID;

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _unitOfWork.SpracheRepository.Get(s => s.Lokalisierung == language).FirstOrDefault();
            var standortSprachen = _unitOfWork.StandortRepository.GetByID(standortID).Sprachen.OrderBy(s => s.SpracheID);

            if (standortSprachen.Contains(sprache))
            {
                ViewData["SpracheID"] = sprache.SpracheID;
            }
            else
            {
                ViewData["SpracheID"] = standortSprachen.FirstOrDefault().SpracheID;
            }
            return PartialView("KundendokumentPageControlCallbacksPartial");
        }


        [ValidateInput(false)]
        public ActionResult KundendokumentSpaltenlabelsGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<KundendokumentSpaltenlabelViewModel, int> updateValues)
        {
            var sh = new SessionHelper();
 
            NeoSysLCS_Dev _context = new NeoSysLCS_Dev();


            foreach (var row in updateValues.Update)
            {
                GridViewUpdateHelper<KundendokumentSpaltenlabel, KundendokumentSpaltenlabelViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(row))
                    {
                        var standortId = row.StandortId;
                        var kundendokumentID = row.KundendokumentId;
                        var roleId = _unitOfWork.UserStandortRoleRepository.GetRoleIdById(sh.GetCurrentUserID() + "_" + standortId);
                        if (roleId != null)
                        {
                            ViewData["roleId"] = roleId;
                        }
                        else
                        {
                            ViewData["roleId"] = "NoRole";
                        }
                        ViewData["KundendokumentID"] = kundendokumentID;
                        ViewData["StandortID"] = standortId;
                        var kundendokument = _unitOfWork.KundendokumentRepository.GetByID(kundendokumentID, "KundendokumentSpaltenLabel");

                        if (kundendokument.KundendokumentSpaltenlabel == null)
                            kundendokument.KundendokumentSpaltenlabel = new KundendokumentSpaltenlabel();

                        kundendokument.KundendokumentSpaltenlabel.LabelSpalte1 = row.LabelSpalte1;
                        kundendokument.KundendokumentSpaltenlabel.LabelSpalte2 = row.LabelSpalte2;
                        kundendokument.KundendokumentSpaltenlabel.LabelSpalte3 = row.LabelSpalte3;
                        kundendokument.KundendokumentSpaltenlabel.LabelSpalte4 = row.LabelSpalte4;
                        kundendokument.KundendokumentSpaltenlabel.LabelSpalte5 = row.LabelSpalte5;
                        kundendokument.KundendokumentSpaltenlabel.LabelSpalte6 = row.LabelSpalte6;
                        kundendokument.KundendokumentSpaltenlabel.LabelSpalte7 = row.LabelSpalte7;
                        kundendokument.KundendokumentSpaltenlabel.LabelSpalte8 = row.LabelSpalte8;
                        kundendokument.KundendokumentSpaltenlabel.LabelSpalte9 = row.LabelSpalte9;
                        kundendokument.KundendokumentSpaltenlabel.LabelSpalte10 = row.LabelSpalte10;
                        _unitOfWork.KundendokumentRepository.Update(kundendokument);
                        _unitOfWork.Save();
                    }
                }, row, updateValues);
            }

            foreach (var row in updateValues.Insert)
            {
                GridViewUpdateHelper<KundendokumentSpaltenlabel, KundendokumentSpaltenlabelViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(row))
                    {
                        var kundendokumentID = row.KundendokumentId;
                        var kundendokument = _unitOfWork.KundendokumentRepository.GetByID(kundendokumentID, "KundendokumentSpaltenLabel");
                        kundendokument.KundendokumentSpaltenlabel = row.GetModel();
                        _unitOfWork.KundendokumentRepository.Update(kundendokument);
                        _unitOfWork.Save();
                    }
                }, row, updateValues);
            }

            foreach (var rowId in updateValues.DeleteKeys)
            {
                GridViewUpdateHelper<KundendokumentSpaltenlabel, KundendokumentSpaltenlabelViewModel>.DoDelete(() =>
                {
                    var kundendokumentID = rowId;
                    var kundendokument = _unitOfWork.KundendokumentRepository.GetByID(kundendokumentID, "KundendokumentSpaltenLabel");
                    kundendokument.KundendokumentSpaltenlabel = null;
                    _unitOfWork.KundendokumentRepository.Update(kundendokument);
                    _unitOfWork.Save();

                    var klabel = _context.KundendokumentSpaltenlabel.FirstOrDefault(k => k.KundendokumentSpaltenlabelID == rowId);
                    _context.KundendokumentSpaltenlabel.Remove(klabel);
                    _context.SaveChanges();

                }, _unitOfWork, rowId, updateValues);
            }

            var cols = InitializeKundendokumentSpaltenlabelsGridViewData(sh, null);
            ViewData["KundeID"] = sh.GetCurrentUser().KundeID;
            return PartialView("KundendokumentSpaltenlabelsGridView", cols);
        }


        /// <summary>
        /// Shows all kundendokument erlassfassungen of the specified standort
        /// </summary>
        /// <param name="standortID">The standort identifier.</param>
        /// <param name="kundendokumentID">The kundendokument identifier.</param>
        /// <returns></returns>
        public ActionResult KundendokumentErlassfassungenGridView(int standortID, int kundendokumentID, int spracheID)
        {
            var sh = new SessionHelper();
            var roleId = _unitOfWork.UserStandortRoleRepository.GetRoleIdById(sh.GetCurrentUserID() + "_" + standortID);
            if (roleId != null)
            {
                ViewData["roleId"] = roleId;
            }
            else
            {
                ViewData["roleId"] = "NoRole";
            }
            ViewData["StandortID"] = standortID;
            ViewData["KundendokumentID"] = kundendokumentID;

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _unitOfWork.SpracheRepository.Get(s => s.Lokalisierung == language).FirstOrDefault();
            var standortSprachen = _unitOfWork.StandortRepository.GetByID(standortID).Sprachen.OrderBy(s => s.SpracheID);

            if (standortSprachen.Contains(sprache))
            {
                ViewData["SpracheID"] = sprache.SpracheID;
            }
            else
            {
                ViewData["SpracheID"] = standortSprachen.FirstOrDefault().SpracheID;
            }

            var viewModels = _unitOfWork.KundendokumentErlassfassungRepository.GetKundendokumentErlassfassungViewModels(kundendokumentID, spracheID);

            return PartialView("KundendokumentErlassfassungenGridView", viewModels.AsQueryable());
        }

        public ActionResult KundendokumentChecklisteGridView(int standortID, int kundendokumentID, int spracheID)
        {
            var sh = new SessionHelper();
            var roleId = _unitOfWork.UserStandortRoleRepository.GetRoleIdById(sh.GetCurrentUserID() + "_" + standortID);
            if (roleId != null)
            {
                ViewData["roleId"] = roleId;
            }
            else
            {
                ViewData["roleId"] = "NoRole";
            }
            ViewData["StandortID"] = standortID;
            ViewData["KundendokumentID"] = kundendokumentID;

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _unitOfWork.SpracheRepository.Get(s => s.Lokalisierung == language).FirstOrDefault();
            var standortSprachen = _unitOfWork.StandortRepository.GetByID(standortID).Sprachen.OrderBy(s => s.SpracheID);

            if (standortSprachen.Contains(sprache))
            {
                ViewData["SpracheID"] = sprache.SpracheID;
            }
            else
            {
                ViewData["SpracheID"] = standortSprachen.FirstOrDefault().SpracheID;
            }

            var viewModels =
                _unitOfWork.kundendokumentChecklistRepository.GetKundendokumentChecklistsViewModels(kundendokumentID,
                    spracheID);

            return PartialView("KundendokumentChecklisteGridView", viewModels.AsQueryable());
        }

        public ActionResult KundendokumentChecklistQuestionsGridView(int? kundendokumentChecklistID, int spracheID)
        {
            if (kundendokumentChecklistID == null)
            {
                return PartialView("KundendokumentChecklistQuestionsGridView", new List<KundendokumentChecklistQuestionViewModel>().AsQueryable());
            }

            var questions =
                _unitOfWork.kundendokumentChecklistRepository.GetKundendokumentChecklistQuestionsViewModels(
                    kundendokumentChecklistID.Value, spracheID);

            ViewData["KundendokumentChecklistID"] = kundendokumentChecklistID;
            ViewData["SpracheID"] = spracheID;
            return PartialView("KundendokumentChecklistQuestionsGridView", questions);
        }

        public ActionResult ForderungsversionChecklistGridView(int? kundendokumentChecklistID, int kundendokumentForderungsversionsID, int? standortID, int? kundendokumentID, int spracheID)
        {
            ViewData["KundendokumentID"] = kundendokumentID;
            ViewData["StandortID"] = standortID;
            ViewData["SpracheID"] = spracheID;
            ViewData["KundendokumentChecklistID"] = kundendokumentChecklistID;
            ViewData["KundendokumentForderungsversionID"] = kundendokumentForderungsversionsID;

            if (kundendokumentChecklistID == null)
            {
                return PartialView("ForderungsversionChecklistGridView", new List<KundendokumentChecklistQuestionViewModel>().AsQueryable());
            }

            var questions =
                _unitOfWork.kundendokumentChecklistRepository.GetKundendokumentChecklistQuestionsViewModels(
                    kundendokumentChecklistID.Value, spracheID);

            foreach (var question in questions)
            {
                question.KundendokumentForderungsversionID = kundendokumentForderungsversionsID;
            }

           
            return PartialView("ForderungsversionChecklistGridView", questions.AsQueryable());
        }

        public ActionResult KundendokumentChecklistQuestionsBatchEditUpdate(MVCxGridViewBatchUpdateValues<KundendokumentChecklistQuestionViewModel, int> updateValues, int kundendokumentChecklistID, int spracheID)
        {
            var checklist = _unitOfWork.kundendokumentChecklistRepository.GetByID(kundendokumentChecklistID);
            foreach (var question in updateValues.Update)
            {
                GridViewUpdateHelper<KundendokumentChecklistQuestion, KundendokumentChecklistQuestionViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(question))
                    {
                        var dbQuestion = checklist.Questions.FirstOrDefault(q =>
                            q.KundenDokumentChecklistQuestionID == question.KundendokumentChecklistQuestionID);
                        if (dbQuestion != null)
                        {
                            dbQuestion.Answer = question.Answer;
                            dbQuestion.BearbeitetAm = DateTime.Now;
                            dbQuestion.BearbeitetVonID = System.Web.HttpContext.Current.User.Identity.GetUserId();
                        }
                    }
                }, question, updateValues);

            }

            _unitOfWork.kundendokumentChecklistRepository.Update(checklist);
            _unitOfWork.Save();

            _unitOfWork.kundendokumentChecklistRepository.CheckIfAllChecklistAreFullfiled(kundendokumentChecklistID);
            _unitOfWork.Save();

            var questions =
                _unitOfWork.kundendokumentChecklistRepository.GetKundendokumentChecklistQuestionsViewModels(
                    kundendokumentChecklistID, spracheID);

            ViewData["KundendokumentChecklistID"] = kundendokumentChecklistID;
            ViewData["SpracheID"] = spracheID;
            return PartialView("KundendokumentChecklistQuestionsGridView", questions);
        }

        /// <summary>
        /// Shows all kundendokument forderungen of the specified kundendokument
        /// </summary>
        /// <param name="standortID">The standort identifier.</param>
        /// <param name="kundendokumentID">The kundendokument identifier.</param>
        /// <param name="showOnlyRemoved">The show only removed.</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult KundendokumentForderungenGridView(int? standortID, int kundendokumentID, bool? showOnlyRemoved, int? spracheID)
        {
            var sh = new SessionHelper();
            var roleId = _unitOfWork.UserStandortRoleRepository.GetRoleIdById(sh.GetCurrentUserID() + "_" + standortID);
            if (roleId != null)
            {
                ViewData["roleId"] = roleId;
            }
            else
            {
                ViewData["roleId"] = "NoRole";
            }
            ViewData["showOnlyRemoved"] = showOnlyRemoved;
            ViewData["StandortID"] = standortID;
            ViewData["KundendokumentID"] = kundendokumentID;
            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _unitOfWork.SpracheRepository.Get(s => s.Lokalisierung == language).FirstOrDefault();
            var standortSprachen = _unitOfWork.StandortRepository.GetByID(standortID).Sprachen.OrderBy(s => s.SpracheID);

            if (standortSprachen.Contains(sprache))
            {
                ViewData["SpracheID"] = sprache.SpracheID;
            }
            else
            {
                ViewData["SpracheID"] = standortSprachen.FirstOrDefault().SpracheID;
            }

            showOnlyRemoved = ((bool?)ViewData["showOnlyRemoved"]).HasValue && ((bool?)ViewData["showOnlyRemoved"]).Value;
            var viewModels = new List<KundendokumentForderungsversionViewModel>();
            if (User.IsInRole(Role.Accountable))
            {
               viewModels = _unitOfWork.KundendokumentForderungsversionRepository.
                                    GetAllFreigegebeneKundendokumentForderungsversionViewModelsByShortcut(Int32.Parse(ViewData["KundendokumentID"].ToString()), showOnlyRemoved.Value, Int32.Parse(ViewData["StandortID"].ToString()), sh.GetCurrentUserID(), spracheID).ToList();
            }
            else
            {
                viewModels = _unitOfWork.KundendokumentForderungsversionRepository.
                     GetAllFreigegebeneKundendokumentForderungsversionViewModels(Int32.Parse(ViewData["KundendokumentID"].ToString()), showOnlyRemoved.Value, Int32.Parse(ViewData["StandortID"].ToString()), spracheID).ToList();
            }

            var test = viewModels.Where(x => x.ErlassID == 370 || (x.ErlassID == 1373 && x.ErlassfassungID == 1262));


            return PartialView("KundendokumentForderungenGridView", viewModels.AsQueryable());


        }

        public IQueryable<KundendokumentForderungsversionViewModel> InitializeKundendokumentForderungenGridView(int? standortID, int kundendokumentID, bool? showOnlyRemoved, int? spracheID)
        {
            var sh = new SessionHelper();
            var roleId = _unitOfWork.UserStandortRoleRepository.GetRoleIdById(sh.GetCurrentUserID() + "_" + standortID);
            if (roleId != null)
            {
                ViewData["roleId"] = roleId;
            }
            else
            {
                ViewData["roleId"] = "NoRole";
            }
            ViewData["showOnlyRemoved"] = showOnlyRemoved;
            ViewData["StandortID"] = standortID;
            ViewData["KundendokumentID"] = kundendokumentID;
            ViewData["SpracheID"] = spracheID;

            showOnlyRemoved = ((bool?)ViewData["showOnlyRemoved"]).HasValue && ((bool?)ViewData["showOnlyRemoved"]).Value;
            var userId = sh.GetCurrentUserID();
            //sh.GetCurrentUser();
            var roles = sh.GetCurrentUserRoles();

            if (roles.Contains("ACCOUNTABLE"))
            {
                return _unitOfWork.KundendokumentForderungsversionRepository.
                                     GetAllFreigegebeneKundendokumentForderungsversionViewModelsByShortcut(Int32.Parse(ViewData["KundendokumentID"].ToString()), showOnlyRemoved.Value, Int32.Parse(ViewData["StandortID"].ToString()), sh.GetCurrentUserID(), spracheID);
            }

            return _unitOfWork.KundendokumentForderungsversionRepository.
                     GetAllFreigegebeneKundendokumentForderungsversionViewModels(Int32.Parse(ViewData["KundendokumentID"].ToString()), showOnlyRemoved.Value, Int32.Parse(ViewData["StandortID"].ToString()), spracheID);




        }

        /// <summary>
        /// Saves the changes of kundendokument forderungen done by the enduser
        /// </summary>
        /// <param name="updateValues">The update values.</param>
        /// <param name="visibleColumsn">The field names of all visible columns</param>
        /// <param name="standortID">The standort identifier.</param>
        /// <param name="kundendokumentID">The kundendokument identifier.</param>
        /// <returns></returns>

        [ValidateInput(false)]
        public ActionResult KundendokumentForderungenGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<KundendokumentForderungsversionViewModel, int> updateValues, string[] visibleColumns, int? standortID, int? kundendokumentID, int? spracheID)
        {
            if (ModelState.IsValid)
            {
                foreach (var viewModel in updateValues.Update)
                {
                    var kfv = _unitOfWork.KundendokumentForderungsversionRepository.GetByID(viewModel.KundendokumentForderungsversionID);

                    //check if column is visible in grid otherwise it will always get a null entry foreach invisible column
                    for (var i = 0; i < visibleColumns.Count(); i++)
                    {
                        if (visibleColumns[i] == "Erfuellung")
                        {
                            kfv.Erfuellung = viewModel.Erfuellung;
                        }
                        if (visibleColumns[i] == "LetzterPruefZeitpunkt")
                        {
                            kfv.LetztePruefungAm = viewModel.LetzterPruefZeitpunkt;
                        }
                        if (visibleColumns[i] == "NaechstePruefungAm")
                        {
                            kfv.NaechstePruefungAm = viewModel.NaechstePruefungAm;
                        }
                        if (visibleColumns[i] == "Pruefmethode")
                        {
                            kfv.Pruefmethode = viewModel.Pruefmethode;
                        }
                        if (visibleColumns[i] == "Verantwortlich")
                        {
                            kfv.Verantwortlich = viewModel.Verantwortlich;
                        }
                        if (visibleColumns[i] == "Kommentar")
                        {
                            kfv.Kommentar = viewModel.Kommentar;
                        }
                        if (visibleColumns[i] == "Ablageort")
                        {
                            kfv.Ablageort = viewModel.Ablageort;
                        }
                        if (visibleColumns[i] == "Spalte1")
                        {
                            kfv.Spalte1 = viewModel.Spalte1;
                        }
                        if (visibleColumns[i] == "Spalte2")
                        {
                            kfv.Spalte2 = viewModel.Spalte2;
                        }
                        if (visibleColumns[i] == "Spalte3")
                        {
                            kfv.Spalte3 = viewModel.Spalte3;
                        }
                        if (visibleColumns[i] == "Spalte4")
                        {
                            kfv.Spalte4 = viewModel.Spalte4;
                        }
                        if (visibleColumns[i] == "Spalte5")
                        {
                            kfv.Spalte5 = viewModel.Spalte5;
                        }
                        if (visibleColumns[i] == "Spalte6")
                        {
                            kfv.Spalte6 = viewModel.Spalte6;
                        }
                        if (visibleColumns[i] == "Spalte7")
                        {
                            kfv.Spalte7 = viewModel.Spalte7;
                        }
                        if (visibleColumns[i] == "Spalte8")
                        {
                            kfv.Spalte8 = viewModel.Spalte8;
                        }
                        if (visibleColumns[i] == "Spalte9")
                        {
                            kfv.Spalte9 = viewModel.Spalte9;
                        }
                        if (visibleColumns[i] == "Spalte10")
                        {
                            kfv.Spalte10 = viewModel.Spalte10;
                        }
                        if (visibleColumns[i] == "ShortcutID")
                        {
                            if (viewModel.ShortcutID == 1)
                            {
                                kfv.ShortcutID = null;
                                kfv.Shortcut = null;
                            } else
                            {
                                kfv.Shortcut = _unitOfWork.ShortcutRepository.GetByID(viewModel.ShortcutID);
                            } 
                        }
                    }



                    kfv.BearbeitetAm = DateTime.Now;
                    kfv.BearbeitetVonID = System.Web.HttpContext.Current.User.Identity.GetUserId();
                    _unitOfWork.KundendokumentForderungsversionRepository.Update(kfv);
                }
            }

            var sh = new SessionHelper();
            var roleId = _unitOfWork.UserStandortRoleRepository.GetRoleIdById(sh.GetCurrentUserID() + "_" + standortID);
            if (roleId != null)
            {
                ViewData["roleId"] = roleId;
            }
            else
            {
                ViewData["roleId"] = "NoRole";
            }
            ViewData["StandortID"] = standortID;
            ViewData["KundendokumentID"] = kundendokumentID;
            ViewData["SpracheID"] = spracheID;
            var viewModels = new List<KundendokumentForderungsversionViewModel>();
            if (User.IsInRole(Role.Accountable))
            {
                viewModels = _unitOfWork.KundendokumentForderungsversionRepository.
                                     GetAllFreigegebeneKundendokumentForderungsversionViewModelsByShortcut(Int32.Parse(ViewData["KundendokumentID"].ToString()), false, Int32.Parse(ViewData["StandortID"].ToString()), sh.GetCurrentUserID(), spracheID).ToList();
            }
            else
            {
                viewModels = _unitOfWork.KundendokumentForderungsversionRepository.
                     GetAllFreigegebeneKundendokumentForderungsversionViewModels(Int32.Parse(ViewData["KundendokumentID"].ToString()), false, Int32.Parse(ViewData["StandortID"].ToString()), spracheID).ToList();
            }
            return PartialView("KundendokumentForderungenGridView", viewModels.AsQueryable());
        }

        [ValidateInput(false)]
        public ActionResult KundendokumentChecklistGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<KundendokumentChecklistViewModel, int> updateValues, int? standortID, int? kundendokumentID, int? spracheID)
        {
            if (ModelState.IsValid)
            {
                foreach (var viewModel in updateValues.Update)
                {

                    var checklistErlassfassungID = _unitOfWork.kundendokumentChecklistRepository.GetByID(viewModel.KundendokumentChecklistID).ErlassfassungID;
                    var kfv = _unitOfWork.KundendokumentForderungsversionRepository.GetKfvByErlassfassungID(checklistErlassfassungID, kundendokumentID.Value);
                    kfv.LetztePruefungAm = viewModel.LetzterPruefZeitpunkt;
                    kfv.NaechstePruefungAm = viewModel.NaechstePruefungAm;
                    kfv.BearbeitetAm = DateTime.Now;
                    kfv.BearbeitetVonID = System.Web.HttpContext.Current.User.Identity.GetUserId();
                    _unitOfWork.KundendokumentForderungsversionRepository.Update(kfv);
                }
            }

            var sh = new SessionHelper();
            var roleId = _unitOfWork.UserStandortRoleRepository.GetRoleIdById(sh.GetCurrentUserID() + "_" + standortID);
            if (roleId != null)
            {
                ViewData["roleId"] = roleId;
            }
            else
            {
                ViewData["roleId"] = "NoRole";
            }
            ViewData["StandortID"] = standortID;
            ViewData["KundendokumentID"] = kundendokumentID;

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _unitOfWork.SpracheRepository.Get(s => s.Lokalisierung == language).FirstOrDefault();
            var standortSprachen = _unitOfWork.StandortRepository.GetByID(standortID).Sprachen.OrderBy(s => s.SpracheID);
            if (standortSprachen.Contains(sprache))
            {
                ViewData["SpracheID"] = sprache.SpracheID;
            }
            else
            {
                ViewData["SpracheID"] = standortSprachen.FirstOrDefault().SpracheID;
            }

            var viewModels = _unitOfWork.kundendokumentChecklistRepository.GetKundendokumentChecklistsViewModels(kundendokumentID, spracheID.Value);

            return PartialView("KundendokumentChecklisteGridView", viewModels.AsQueryable());
        }


        [ValidateInput(false)]
        public ActionResult KundendokumentErlassfassungGridViewBatchEditUpdate(MVCxGridViewBatchUpdateValues<KundendokumentErlassfassungViewModel, int> updateValues, string[] visibleColumns, int? standortID, int? kundendokumentID, int? spracheID)
        {
            if (ModelState.IsValid)
            {
                foreach (var viewModel in updateValues.Update)
                {
                    var kfv = _unitOfWork.KundendokumentErlassfassungRepository.GetByID(viewModel.KundendokumentErlassfassungID);

                    //check if column is visible in grid otherwise it will always get a null entry foreach invisible column
                    for (var i = 0; i < visibleColumns.Count(); i++)
                    {
                        if (visibleColumns[i] == "Kommentar")
                        {
                            kfv.Kommentar = viewModel.Kommentar;
                        }                       
                    }

                    kfv.BearbeitetAm = DateTime.Now;
                    kfv.BearbeitetVonID = System.Web.HttpContext.Current.User.Identity.GetUserId();
                    _unitOfWork.KundendokumentErlassfassungRepository.Update(kfv);
                }
            }

            var sh = new SessionHelper();
            var roleId = _unitOfWork.UserStandortRoleRepository.GetRoleIdById(sh.GetCurrentUserID() + "_" + standortID);
            if (roleId != null)
            {
                ViewData["roleId"] = roleId;
            }
            else
            {
                ViewData["roleId"] = "NoRole";
            }
            ViewData["StandortID"] = standortID;
            ViewData["KundendokumentID"] = kundendokumentID;
            ViewData["SpracheID"] = spracheID;
            var viewModels = _unitOfWork.KundendokumentErlassfassungRepository.GetKundendokumentErlassfassungViewModels(kundendokumentID, (int) spracheID);
            return PartialView("KundendokumentErlassfassungenGridView", viewModels.AsQueryable());
        }

        /// <summary>
        /// Shows all kundendokument pflichten of the specified kundendokument
        /// </summary>
        /// <param name="standortID">The standort identifier.</param>
        /// <param name="kundendokumentID">The kundendokument identifier.</param>
        /// <param name="showOnlyRemoved">The show only removed.</param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult KundendokumentPflichtenGridView(int? standortID, int? kundendokumentID, bool? showOnlyRemoved)
        {
            ViewData["showOnlyRemoved"] = showOnlyRemoved;
            ViewData["KundendokumentID"] = kundendokumentID;
            ViewData["StandortID"] = standortID;

            var query = _unitOfWork.KundendokumentPflichtRepository.GetAllFreigegebeneKundendokumentPflichtViewModels(kundendokumentID, (bool) showOnlyRemoved);

            return PartialView("KundendokumentPflichtenGridView", query);
        }

        /// <summary>
        /// Kundendokuments the pflichten grid view batch edit update.
        /// </summary>
        /// <param name="updateValues">The update values.</param>
        /// <param name="standortID">The standort identifier.</param>
        /// <param name="kundendokumentID">The kundendokument identifier.</param>
        /// <returns></returns>

        [ValidateInput(false)]
        public ActionResult KundendokumentPflichtenGridViewBatchEditUpdate(
            MVCxGridViewBatchUpdateValues<KundendokumentPflichtViewModel, int> updateValues, int? standortID, int? kundendokumentID)
        {
            ViewData["KundendokumentID"] = kundendokumentID;
            ViewData["StandortID"] = standortID;
            foreach (var pflicht in updateValues.Update)
            {
                GridViewUpdateHelper<KundendokumentPflicht, KundendokumentPflichtViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(pflicht))
                    {
                        _unitOfWork.KundendokumentPflichtRepository.UpdateEndUser(pflicht);
                        _unitOfWork.Save();
                    }
                }, pflicht, updateValues);

            }

            var viewModels = _unitOfWork.KundendokumentPflichtRepository.GetAllFreigegebeneKundendokumentPflichtViewModels(kundendokumentID);
            return PartialView("KundendokumentPflichtenGridView", viewModels);
        }

        /// <summary>
        /// Shows the details of the specified erlass
        /// </summary>
        /// <param name="erlassID">The erlass identifier.</param>
        /// <param name="standortID">The standort identifier.</param>
        /// <param name="kundendokumentID">The kundendokument identifier.</param>
        /// <returns></returns>
        public ActionResult ErlassDetailGridView(int erlassID, int? standortID, int? kundendokumentID, int spracheID)
        {
            ViewData["KundendokumentID"] = kundendokumentID;
            ViewData["StandortID"] = standortID;
            ViewData["SpracheID"] = spracheID;

            var viewModel = _unitOfWork.ErlassRepository.GetErlassDetailView(erlassID, spracheID);
            return PartialView("ErlassDetailGridView", viewModel);

        }

        /// <summary>
        /// Shows the details of the specified erlassfassung
        /// </summary>
        /// <param name="erlassfassungID">The erlassfassung identifier.</param>
        /// <param name="standortID">The standort identifier.</param>
        /// <param name="kundendokumentID">The kundendokument identifier.</param>
        /// <returns></returns>
        public ActionResult ErlassfassungDetailGridView(int erlassfassungID, int? standortID, int? kundendokumentID, int spracheID)
        {
            ViewData["KundendokumentID"] = kundendokumentID;
            ViewData["StandortID"] = standortID;
            ViewData["SpracheID"] = spracheID;
            var viewModel = _unitOfWork.KundendokumentErlassfassungRepository.GetKundendokumentErlassfassungViewModelsByErlassfassung(
                kundendokumentID, erlassfassungID, spracheID);
            return PartialView("ErlassfassungDetailGridView", viewModel);

        }

        /// <summary>
        /// Shows the details of the specified forderung
        /// </summary>
        /// <param name="forderungsversionID">The forderungsversion identifier.</param>
        /// <param name="standortID">The standort identifier.</param>
        /// <param name="kundendokumentID">The kundendokument identifier.</param>
        /// <returns></returns>
        public ActionResult ForderungsversionDetailGridView(int forderungsversionID, int? standortID, int? kundendokumentID, int spracheID)
        {
            ViewData["KundendokumentID"] = kundendokumentID;
            ViewData["StandortID"] = standortID;
            ViewData["SpracheID"] = spracheID;

            var viewModel = _unitOfWork.ForderungsversionRepository.GetForderungDetailGridView(forderungsversionID, spracheID);

            return PartialView("ForderungsversionDetailGridView", viewModel);
        }

        public ActionResult ChecklistDetailGridView(int kundendokumentChecklistID, int kundendokumentForderungsversionsID, int? standortID, int? kundendokumentID, int spracheID)
        {
            ViewData["KundendokumentID"] = kundendokumentID;
            ViewData["StandortID"] = standortID;
            ViewData["SpracheID"] = spracheID;
            ViewData["KundendokumentChecklistID"] = kundendokumentChecklistID;
            ViewData["KundendokumentForderungsversionID"] = kundendokumentForderungsversionsID;
            var questions = _unitOfWork.kundendokumentChecklistRepository.GetKundendokumentChecklistQuestionsViewModels(kundendokumentChecklistID, spracheID).ToList();

            foreach (var question in questions)
            {
                question.KundendokumentForderungsversionID = kundendokumentForderungsversionsID;
            }

            return PartialView("ForderungsversionChecklistGridView", questions.AsQueryable());
        }

        public ActionResult ChecklistDetailBatchEditUpdate(MVCxGridViewBatchUpdateValues<KundendokumentChecklistQuestionViewModel, int> updateValues, int kundendokumentChecklistID, int kundendokumentForderungsversionsID, int? standortID, int? kundendokumentID, int spracheID)
        {
            var checklist = _unitOfWork.kundendokumentChecklistRepository.GetByID(kundendokumentChecklistID);
            foreach (var question in updateValues.Update)
            {
                GridViewUpdateHelper<KundendokumentChecklistQuestion, KundendokumentChecklistQuestionViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(question))
                    {
                        var dbQuestion = checklist.Questions.FirstOrDefault(q =>
                            q.KundenDokumentChecklistQuestionID == question.KundendokumentChecklistQuestionID);
                        if (dbQuestion != null)
                        {
                            dbQuestion.Answer = question.Answer;
                            dbQuestion.BearbeitetAm = DateTime.Now;
                            dbQuestion.BearbeitetVonID = System.Web.HttpContext.Current.User.Identity.GetUserId();
                        }
                    }
                }, question, updateValues);

            }

            _unitOfWork.kundendokumentChecklistRepository.Update(checklist);
            _unitOfWork.Save();

            _unitOfWork.kundendokumentChecklistRepository.CheckIfAllChecklistAreFullfiled(kundendokumentChecklistID);
            _unitOfWork.Save();

            var questions =
                _unitOfWork.kundendokumentChecklistRepository.GetKundendokumentChecklistQuestionsViewModels(
                    kundendokumentChecklistID, spracheID).ToList();

            foreach (var question in questions)
            {
                question.KundendokumentForderungsversionID = kundendokumentForderungsversionsID;
            }

            ViewData["KundendokumentID"] = kundendokumentID;
            ViewData["StandortID"] = standortID;
            ViewData["SpracheID"] = spracheID;
            ViewData["KundendokumentChecklistID"] = kundendokumentChecklistID;
            ViewData["KundendokumentForderungsversionID"] = kundendokumentForderungsversionsID;
            return PartialView("ForderungsversionChecklistGridView", questions.AsQueryable());
        }

        /// <summary>
        /// Shows the spalte 1-10 of the specified kundendokument forderung
        /// </summary>
        /// <param name="kundendokumentForderungsversionsID">The kundendokument forderungsversions identifier.</param>
        /// <param name="standortID">The standort identifier.</param>
        /// <param name="kundendokumentID">The kundendokument identifier.</param>
        /// <returns></returns>
        public ActionResult ForderungsversionAdditionalDataGridView(int kundendokumentForderungsversionsID, int? standortID, int? kundendokumentID)
        {
            ViewData["KundendokumentID"] = kundendokumentID;
            ViewData["StandortID"] = standortID;
            ViewData["KundendokumentForderungsversionID"] = kundendokumentForderungsversionsID;
            var viewModel = _unitOfWork.KundendokumentForderungsversionRepository.GetViewModelByID(kundendokumentForderungsversionsID);

            return PartialView("ForderungsversionAdditionalDataGridView", (IQueryable<KundendokumentForderungsversionViewModel>) new List<KundendokumentForderungsversionViewModel>() { viewModel });

        }

        /// <summary>
        /// Saves changes in spalte 1-10
        /// </summary>
        /// <param name="updateValues">The update values.</param>
        /// <param name="kundendokumentForderungsversionsID">The kundendokument forderungsversions identifier.</param>
        /// <param name="standortID">The standort identifier.</param>
        /// <param name="kundendokumentID">The kundendokument identifier.</param>
        /// <returns></returns>
        public ActionResult ForderungsversionAdditionalDataGridViewBatchEdit(
            MVCxGridViewBatchUpdateValues<KundendokumentForderungsversionViewModel, int> updateValues, int kundendokumentForderungsversionsID,
            int? standortID, int? kundendokumentID
        )
        {
            ViewData["KundendokumentID"] = kundendokumentID;
            ViewData["StandortID"] = standortID;
            ViewData["KundendokumentForderungsversionID"] = kundendokumentForderungsversionsID;
            foreach (var forderung in updateValues.Update)
            {
                GridViewUpdateHelper<KundendokumentForderungsversion, KundendokumentForderungsversionViewModel>.DoUpdateInsert(() =>
                {
                    if (updateValues.IsValid(forderung))
                    {
                        _unitOfWork.KundendokumentForderungsversionRepository.UpdateAdditionalData(forderung);
                        _unitOfWork.Save();
                    }
                }, forderung, updateValues);

            }
            var viewModel = _unitOfWork.KundendokumentForderungsversionRepository.GetViewModelByID(kundendokumentForderungsversionsID);
            return PartialView("ForderungsversionAdditionalDataGridView", (IQueryable<KundendokumentForderungsversionViewModel>) new List<KundendokumentForderungsversionViewModel>() { viewModel });

        }

        /// <summary>
        /// Show the changes form one forderungsversion to another
        /// </summary>
        /// <param name="forderungsversionID">The forderungsversion identifier.</param>
        /// <param name="standortID">The standort identifier.</param>
        /// <param name="kundendokumentID">The kundendokument identifier.</param>
        /// <returns></returns>
        public ActionResult ForderungsversionCompareDetailGridView(int forderungsversionID, int? standortID, int? kundendokumentID)
        {
            ViewData["KundendokumentID"] = kundendokumentID;
            ViewData["StandortID"] = standortID;
            var viewModel = new ForderungsversionsCompareViewModel();
            var newVersion = _unitOfWork.ForderungsversionRepository.GetForderungViewModelById(forderungsversionID);

            viewModel.InkraftretenNew = newVersion.Inkrafttretung.ToString("dd.MM.yyyy");

            if (newVersion.VorversionID.HasValue)
            {
                var vorversion = _unitOfWork.ForderungsversionRepository.GetForderungViewModelById(newVersion.VorversionID.Value);
                viewModel.SetOldVersion(vorversion);
                //var diffItems = StringHelper.CompareAndMarkDifferencesInList(vorversion.Beschreibung, newVersion.Beschreibung);
                //newVersion.Beschreibung = StringHelper.MarkDifferences(newVersion.Beschreibung, diffItems);
                viewModel.SetNewVersion(newVersion);
                viewModel.InkraftretenOld = vorversion.Inkrafttretung.ToString("dd.MM.yyyy");
            }


            return PartialView("ForderungsversionCompareDetailGridView", viewModel);

        }


        public ActionResult ErlassversionDetailPartial(int erlassId, int ErlassfassungID, int spracheID)
        {
            ViewData["ErlassID"] = erlassId;
            ViewData["ErlassfassungID"] = ErlassfassungID;
            ViewData["SpracheID"] = spracheID;
            var viewModels = _unitOfWork.ErlassfassungRepository.GetErlassversionDetailPartial(erlassId, spracheID);

            return PartialView("ErlassversionDetailPartial", viewModels);
        }

        public ActionResult NewStandortObjekteGridView(int kundendokumentID, int spracheID)
        {
            ViewData["KundendokumentID"] = kundendokumentID;

            Kundendokument kundendokument = _unitOfWork.KundendokumentRepository.GetByID(kundendokumentID);

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;
            Sprache sprache = _unitOfWork.SpracheRepository.Get(s => s.Lokalisierung == language).FirstOrDefault();
            var standortSprachen = _unitOfWork.StandortRepository.GetStanodrtById(kundendokument.StandortID).Sprachen.OrderBy(s => s.SpracheID);

            if (standortSprachen.Contains(sprache))
            {
                ViewData["SpracheID"] = sprache.SpracheID;
            }
            else
            {
                ViewData["SpracheID"] = standortSprachen.FirstOrDefault().SpracheID;
            }
            //var kundendokumentID = (int)ViewData["KundendokumentID"];
            var objektkategorien = _unitOfWork.ObjektkategorieRepository.GetTreeObjectsByKundendokument(kundendokumentID, spracheID);

            return PartialView("_RowSelectionPartial", objektkategorien);
        }

        public ActionResult ForderungenExportDataAware(int kundendokumentId)
        {
            var kundendokument = _unitOfWork.KundendokumentRepository.GetByID(kundendokumentId);
            var kundeID = kundendokument.KundeID;
            ViewData["KundendokumentID"] = kundendokumentId;
            ViewData["StandortID"] = kundendokument.StandortID;
            ViewData["showOnlyRemoved"] = false;
            ViewData["SpracheID"] = 1;
            var xlsxExportOptions = new XlsxExportOptionsEx { };
            xlsxExportOptions.CustomizeCell += options_CustomizeCell;
            //xlsxExportOptions.CustomizeSheetHeader += options_CustomizeSheetHeader;
            xlsxExportOptions.ExportType = DevExpress.Export.ExportType.DataAware;
            xlsxExportOptions.AllowSortingAndFiltering = DefaultBoolean.True;
            xlsxExportOptions.AllowGrouping = DefaultBoolean.True;
            xlsxExportOptions.GroupState = GroupState.ExpandAll;
            IQueryable<KundendokumentForderungsversionViewModel> model = Session["Model"] as IQueryable<KundendokumentForderungsversionViewModel>;
            List<KundendokumentForderungsversionViewModel> modelList = model.ToList();
            foreach (var viewmodel in modelList)
            {
                viewmodel.Beschreibung = WebUtility.HtmlDecode(StringHelper.StripAllTags(viewmodel.Beschreibung));
                viewmodel.InternerKommentar = WebUtility.HtmlDecode(StringHelper.StripAllTags(viewmodel.InternerKommentar));
                viewmodel.Kommentar = WebUtility.HtmlDecode(StringHelper.StripAllTags(viewmodel.Kommentar));
            }

            return GridViewExtension.ExportToXlsx(GetForderungenGridViewSettings(GetKundendokumentForderungenExportGridColumns(kundeID.Value, kundendokumentId), modelList), modelList, xlsxExportOptions); 
        }

        public ActionResult ErlassfassungExportDataAware(int kundendokumentId)
        {
            var kundendokument = _unitOfWork.KundendokumentRepository.GetByID(kundendokumentId);
            var kundeID = kundendokument.KundeID;
            ViewData["KundendokumentID"] = kundendokumentId;
            ViewData["StandortID"] = kundendokument.StandortID;
            //ViewData["showOnlyRemoved"] = false;
            ViewData["SpracheID"] = 1;
            var xlsxExportOptions = new XlsxExportOptionsEx { };
            xlsxExportOptions.CustomizeCell += options_CustomizeCell;
            //xlsxExportOptions.CustomizeSheetHeader += options_CustomizeSheetHeader;
            xlsxExportOptions.ExportType = DevExpress.Export.ExportType.DataAware;
            xlsxExportOptions.AllowSortingAndFiltering = DefaultBoolean.True;
            xlsxExportOptions.AllowGrouping = DefaultBoolean.True;
            xlsxExportOptions.GroupState = GroupState.ExpandAll;

            IQueryable<KundendokumentErlassfassungViewModel> model = Session["Model"] as IQueryable<KundendokumentErlassfassungViewModel>;
            List<KundendokumentErlassfassungViewModel> modelList = model.ToList();
            foreach (var viewmodel in modelList)
            {
                viewmodel.Kommentar = WebUtility.HtmlDecode(StringHelper.StripAllTags(viewmodel.Kommentar));
                viewmodel.InternerKommentar = WebUtility.HtmlDecode(StringHelper.StripAllTags(viewmodel.InternerKommentar));
                viewmodel.RelevanterKommentar = WebUtility.HtmlDecode(StringHelper.StripAllTags(viewmodel.RelevanterKommentar));
                viewmodel.Kerninhalte = WebUtility.HtmlDecode(StringHelper.StripAllTags(viewmodel.Kerninhalte));
            }

            return GridViewExtension.ExportToXlsx(GetErlassfassungGridViewSettings(GetKundendokumentErlassfassungExportGridColumns(kundeID.Value, kundendokumentId), modelList), modelList, xlsxExportOptions);
        }

        void options_CustomizeCell(CustomizeCellEventArgs e)
        {
            e.Formatting.Font = new DevExpress.Export.XlCellFont() { Name = "Arial", Size = 10 };
            if (e.AreaType == SheetAreaType.Header)
            {
                
                e.Formatting.BackColor = System.Drawing.Color.FromArgb(255, 255, 153);
            }
            else if (e.AreaType == SheetAreaType.GroupHeader)
            {
                e.Formatting.Font = new DevExpress.Export.XlCellFont() { Name = "Arial", Size = 10, Bold = true };
            }
            else
            {
                e.Formatting.Alignment = new DevExpress.Export.Xl.XlCellAlignment() { WrapText = true, HorizontalAlignment = DevExpress.Export.Xl.XlHorizontalAlignment.Left, VerticalAlignment = DevExpress.Export.Xl.XlVerticalAlignment.Top };
            }
            e.Handled = true;
        }

        public ActionResult ShowMassnahmePopupForForderungen(string erlassName, int standortID, string erlassNr, int originID, string artikelNummer, int artikelId)
        {
            var shortcuts = _unitOfWork.ShortcutRepository.GetShortcutCombobox(standortID);
            var standortName = _unitOfWork.StandortRepository.GetByID(standortID).Name;
            var model = new MassnahmeErfassenViewModel
            {
                StandortId = standortID,
                Betreff = standortName + " - " + Resources.Properties.Resources.View_Kundendokument_TabForderungen + " " + artikelNummer + " - " + erlassName,
                Shortcuts = shortcuts,
                OriginID = originID,
                ArtikelID = artikelId
            };
            return PartialView("_MassnahmeErfassenPopup", model);
        }

        public ActionResult ShowMassnahmePopupForGesetzesliste(string erlassName, int standortID, string erlassNr, int originID)
        {
            var shortcuts = _unitOfWork.ShortcutRepository.GetShortcutCombobox(standortID);
            var standortName = _unitOfWork.StandortRepository.GetByID(standortID).Name;

            var model = new MassnahmeErfassenViewModel
            {
                StandortId = standortID,
                Betreff = standortName + " - " + Resources.Properties.Resources.View_Kundendokument_TabErlassfassungen + " " + erlassNr + " - " + erlassName,
                Shortcuts = shortcuts,
                OriginID = originID
            };
            return PartialView("_MassnahmeErfassenPopup", model);
        }

        public ActionResult ShowMassnahmePopupForSuva(int checklistID, int checklistQuestionID)
        {
            var kundendokumentChecklist = _unitOfWork.kundendokumentChecklistRepository.GetByID(checklistID);

            int standortID = _unitOfWork.KundendokumentRepository.GetByID(kundendokumentChecklist.KundendokumentID).StandortID;
            int erlassID = _unitOfWork.ErlassfassungRepository.GetByID(kundendokumentChecklist.ErlassfassungID).ErlassID;
            ErlassViewModel erlassViewModel = _unitOfWork.ErlassRepository.GetErlassViewModelById(erlassID);

            var shortcuts = _unitOfWork.ShortcutRepository.GetShortcutCombobox(standortID);
            var standortName = _unitOfWork.StandortRepository.GetByID(standortID).Name;
            var model = new MassnahmeErfassenViewModel
            {
                StandortId = standortID,
                Betreff = standortName + " - " + "SUVA" + " " + erlassViewModel.SrNummer + " - " + erlassViewModel.Titel,
                Shortcuts = shortcuts,
                OriginID = checklistID,
                ChecklistQuestionID = checklistQuestionID,
            };
            return PartialView("_MassnahmeErfassenPopup", model);
        }

        private GridViewSettings GetForderungenGridViewSettings(MVCxGridViewColumnCollection columns, List<KundendokumentForderungsversionViewModel> model)
        {
            var settings = new GridViewSettings();
            IUnitOfWork unitOfWork = new UnitOfWork();
            NeoSysLCS_Dev context = new NeoSysLCS_Dev();
            var sessionHelper = new SessionHelper();
            ViewBag.EnableCheckedListMode = true;
            settings.SettingsPopup.CustomizationWindow.Width = 300;
            settings.SettingsCustomizationDialog.Enabled = true;
            settings.Styles.CustomizationDialog.CssClass = "addScroll";
            settings.Name = "PortalKundendokumentForderungenGridView";
            settings.SettingsExport.ExcelExportMode = DevExpress.Export.ExportType.DataAware;
            settings.SettingsExport.EnableClientSideExportAPI = true;

            ViewContext viewContext = new ViewContext();
            settings.Toolbars.Add(t =>
            {
                t.EnableAdaptivity = true;
                t.Items.Add(GridViewToolbarCommand.ExportToXlsx);
            });

            settings.KeyFieldName = "KundendokumentForderungsversionID";
            GridViewHelper.ApplyDefaultSettings(settings);
            settings.SettingsCookies.Enabled = false;

            settings.SettingsContextMenu.Enabled = true;
            settings.SettingsContextMenu.EnableRowMenu = DefaultBoolean.False;

            settings.Styles.Cell.Wrap = DefaultBoolean.True;
            settings.SettingsBehavior.AllowEllipsisInText = true;
            settings.SettingsResizing.ColumnResizeMode = ColumnResizeMode.Control;
            settings.SettingsCookies.StoreColumnsWidth = true;
            settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;
            settings.Styles.GroupPanel.CssClass = "noWrapClass";
            settings.ClientSideEvents.BatchEditEndEditing = "OnBatchEditEndEditing";
            settings.ClientSideEvents.BatchEditStartEditing = "OnBatchStartEditing";

            settings.CommandColumn.AllowDragDrop = DefaultBoolean.False;
            settings.CommandColumn.Visible = true;

            settings.CustomUnboundColumnData = (s, e) =>
            {
                if (e.Column.FieldName == "RechtsbereicheUnbound")
                {
                    var dataItem = e.GetListSourceFieldValue("Rechtsbereiche");
                    IQueryable<RechtsbereichViewModel> rechtsbereiche = dataItem as IQueryable<RechtsbereichViewModel>;
                    List<string> tbl = new List<string>();

                    foreach (RechtsbereichViewModel t in rechtsbereiche)
                    {
                        tbl.Add(t.Name);
                    }
                    e.Value = String.Join(", ", tbl);
                }
            };

            settings.Columns.Add(column =>
            {
                column.FieldName = "RechtsbereicheUnbound";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), "Rechtsbereiche");
                column.MinWidth = 140;
                column.ExportWidth = 140;
                column.ReadOnly = true;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.EditFormSettings.Visible = DefaultBoolean.False;

                column.Settings.ShowFilterRowMenu = DefaultBoolean.False;
                column.UnboundType = UnboundColumnType.String;
                column.Settings.AllowSort = DefaultBoolean.False;
            });


            settings.AutoFilterCellEditorCreate = (s, e) =>
            {
                if (e.Column.FieldName == "RechtsbereicheUnbound")
                {
                    ComboBoxProperties combo = new ComboBoxProperties();
                    e.EditorProperties = combo;
                }
            };

            settings.AutoFilterCellEditorInitialize = (s, e) =>
            {
                var gv = s as MVCxGridView;
                var expr = gv.FilterExpression;
                if (e.Column.FieldName == "RechtsbereicheUnbound")
                {
                    MVCxComboBox cb = ((MVCxComboBox)e.Editor);
                    cb.DataSource = unitOfWork.RechtsbereichRepository.GetRechtsbereichComboBox((int)ViewData["StandortID"], (int)ViewData["KundendokumentID"], (int)ViewData["SpracheID"]).Select(i => i.Name);
                    cb.DataBindItems();
                    if (Session["RechtsbereicheFilter"] != null && expr.Contains("Rechtsbereiche"))
                    {
                        e.Editor.Value = Session["RechtsbereicheFilter"];
                    }
                }
            };

            settings.ProcessColumnAutoFilter = (sender, e) =>
            {
                var gv = sender as MVCxGridView;
                string filter = gv.FilterExpression;
                var erfuellungList = from KundendokumentErfuellung value in Enum.GetValues(typeof(KundendokumentErfuellung))
                           where (int)value != 4
                           select new
                           {
                               Id = (int)value,
                               Name = value.GetTranslation()
                           };
                var test = (from x in model select x.Status).Distinct().ToList();
                var statusList = from KundendokumentItemStatus value in Enum.GetValues(typeof(KundendokumentItemStatus))
                                 where test.Contains(value)
                                     select new
                                     {
                                         Id = (int)value,
                                         Name = value.GetTranslation()
                                     };

                if (e.Column.FieldName == "RechtsbereicheUnbound")
                {
                    string value = e.Value;
                    if (gv.FilterExpression != "")
                    {
                        e.Criteria = DevExpress.Data.Filtering.CriteriaOperator.Parse(gv.FilterExpression + "OR Rechtsbereiche[contains(Name, ?)]", value);
                        gv.FilterExpression = null;
                        Session["RechtsbereicheFilter"] = "";
                    }
                    else
                    {
                        e.Criteria = DevExpress.Data.Filtering.CriteriaOperator.Parse("Rechtsbereiche[contains(Name, ?)]", value);
                        Session["RechtsbereicheFilter"] = e.Value;
                    }

                }
            };

            settings.Columns.Add(column =>
            {
                column.FieldName = "StandortObjektTitel";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 200;
                column.ExportWidth = 200;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.GroupIndex = 0;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassSrNummer";
                column.Caption = Resources.Properties.Resources.Erlassnummer;
                column.MinWidth = 125;
                column.ExportWidth = 125;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassTitel";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 200;
                column.ExportWidth = 200;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ExportCellStyle.Wrap = DefaultBoolean.True;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ArtikelNummer";
                column.Caption = Resources.Properties.Resources.Entitaet_Artikel_Singular;
                column.MinWidth = 110;
                column.ExportWidth = 110;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.CellStyle.HorizontalAlign = HorizontalAlign.Center;
                column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                column.SetDataItemTemplateContent(
                    container =>
                    {

                        var url = DataBinder.Eval(container.DataItem, "ArtikelQuelle");
                        var nr = DataBinder.Eval(container.DataItem, "ArtikelNummer");
                        string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, nr, false);
                        viewContext.Writer.Write(htmlLink);
                    });
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Beschreibung";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.HeaderStyle.Wrap = DefaultBoolean.True;

                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.MinWidth = 350;
                column.ExportWidth = 350;

                var memoProp = column.PropertiesEdit as MemoProperties;
                if (memoProp != null)
                {
                    memoProp.EncodeHtml = false;
                }
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "BewilligungspflichtText";
                column.Caption = Resources.Properties.Resources.View_Auswertungen_Bewilligungspflichtig;
                column.MinWidth = 75;
                column.ExportWidth = 75;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "NachweispflichtText";
                column.Caption = Resources.Properties.Resources.View_Auswertungen_Nachweispflichtig;
                column.MinWidth = 75;
                column.ExportWidth = 75;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Status";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);

                //define as combobox for filtern over the enum
                column.ColumnType = MVCxGridViewColumnType.ComboBox;
                //column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

                var test = (from x in model select x.Status).Distinct().ToList();

                var list = from KundendokumentItemStatus value in Enum.GetValues(typeof(KundendokumentItemStatus))
                           where test.Contains(value)
                           select new
                           {
                               Id = (int)value,
                               Name = value.GetTranslation()
                           };

                comboBoxProperties.DataSource = list;
                comboBoxProperties.ValueField = "Id";
                comboBoxProperties.TextField = "Name";


                column.MinWidth = 100;
                column.ExportWidth = 100;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.SetDataItemTemplateContent(container =>
                {
                    var statusCandidate = DataBinder.Eval(container.DataItem, "Status");
                    if (statusCandidate != null)
                    {
                        var status = (KundendokumentItemStatus)statusCandidate;
                        viewContext.Writer.Write("<span class=\"label label-" + status + "\">" + status.GetTranslation() + "</span>");
                    }
                });
            });

                settings.Columns.Add(column =>
                {
                    column.FieldName = "Erfuellung";
                    column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                    column.MinWidth = 150;
                    column.ExportWidth = 150;
                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                    column.ColumnType = MVCxGridViewColumnType.ComboBox;
                    //column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                    var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

                    var list = from KundendokumentErfuellung value in Enum.GetValues(typeof(KundendokumentErfuellung))
                               where (int)value != 4
                               select new
                               {
                                   Id = (int)value,
                                   Name = value.GetTranslation()
                               };

                    comboBoxProperties.DataSource = list;
                    comboBoxProperties.ValueField = "Id";
                    comboBoxProperties.TextField = "Name";
                    comboBoxProperties.ValueType = typeof(Int32);
                });

                settings.Columns.Add(column =>
                {
                    column.FieldName = "LetzterPruefZeitpunkt";
                    column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                    column.MinWidth = 150;
                    column.ExportWidth = 150;
                    column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                });
                settings.Columns.Add(column =>
                {
                    column.FieldName = "NaechstePruefungAm";
                    column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                    column.MinWidth = 180;
                    column.ExportWidth = 180;
                    column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                });
                settings.Columns.Add(column =>
                {
                    column.FieldName = "Pruefmethode";
                    column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                    column.MinWidth = 140;
                    column.ExportWidth = 140;
                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                    column.ColumnType = MVCxGridViewColumnType.Memo;
                    column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                    column.SetDataItemTemplateContent(
                        container =>
                        {

                            var url = DataBinder.Eval(container.DataItem, "Pruefmethode");
                            string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                            viewContext.Writer.Write(htmlLink);
                        });
                });
                settings.Columns.Add(column =>
                {
                    column.FieldName = "ShortcutID";
                    column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), "Verantwortlich");
                    column.MinWidth = 140;
                    column.ExportWidth = 140;
                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                    column.ColumnType = MVCxGridViewColumnType.ComboBox;
                    //column.Settings.FilterMode = ColumnFilterMode.DisplayText;
                    var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;
                    comboBoxProperties.DataSource = unitOfWork.ShortcutRepository.GetShortcutCombobox((int)ViewData["StandortID"]);
                    comboBoxProperties.ValueField = "ShortcutID";
                    comboBoxProperties.TextField = "Name";
                    comboBoxProperties.ValueType = typeof(Int32);
                    if (User.IsInRole(Role.Accountable))
                    {
                        column.EditFormSettings.Visible = DefaultBoolean.False;
                    }

                });
                settings.Columns.Add(column =>
                {
                    column.FieldName = "Ablageort";
                    column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                    column.MinWidth = 150;
                    column.ExportWidth = 150;
                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                    column.ColumnType = MVCxGridViewColumnType.Memo;
                    column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                    column.SetDataItemTemplateContent(
                        container =>
                        {

                            var url = DataBinder.Eval(container.DataItem, "Ablageort");
                            string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, false);
                            viewContext.Writer.Write(htmlLink);
                        });
                });
                settings.Columns.Add(column =>
                {
                    column.FieldName = "Kommentar";
                    column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                    column.MinWidth = 110;
                    column.ExportWidth = 110;
                    column.HeaderStyle.Wrap = DefaultBoolean.True;

                    column.ColumnType = MVCxGridViewColumnType.Memo;
                    column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                    var prop = column.PropertiesEdit as MemoProperties;
                    prop.Rows = 10;
                });

                //Additinal Columns
                var labels = unitOfWork.KundendokumentRepository.GetByID((int)ViewData["KundendokumentID"], "KundendokumentSpaltenlabel").KundendokumentSpaltenlabel;
                settings.Columns.Add(column =>
                {
                    column.FieldName = "Spalte1";
                    column.MinWidth = 135;
                    column.ExportWidth = 135;
                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                    column.ColumnType = MVCxGridViewColumnType.Memo;
                    column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                    column.Caption = (labels == null || labels.LabelSpalte1 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte1;
                    column.SetDataItemTemplateContent(
                        container =>
                        {

                            var url = DataBinder.Eval(container.DataItem, "Spalte1");
                            string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                            viewContext.Writer.Write(htmlLink);
                        });
                });
                settings.Columns.Add(column =>
                {
                    column.FieldName = "Spalte2";
                    column.MinWidth = 135;
                    column.ExportWidth = 135;
                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                    column.ColumnType = MVCxGridViewColumnType.Memo;
                    column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                    column.Caption = (labels == null || labels.LabelSpalte2 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte2;
                    column.SetDataItemTemplateContent(
                        container =>
                        {

                            var url = DataBinder.Eval(container.DataItem, "Spalte2");
                            string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, true);
                            viewContext.Writer.Write(htmlLink);
                        });
                });
                settings.Columns.Add(column =>
                {
                    column.FieldName = "Spalte3";
                    column.MinWidth = 135;
                    column.ExportWidth = 135;
                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                    column.ColumnType = MVCxGridViewColumnType.Memo;
                    column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                    column.Caption = (labels == null || labels.LabelSpalte3 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte3;
                    column.SetDataItemTemplateContent(
                        container =>
                        {

                            var url = DataBinder.Eval(container.DataItem, "Spalte3");
                            string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                            viewContext.Writer.Write(htmlLink);
                        });
                });
                settings.Columns.Add(column =>
                {
                    column.FieldName = "Spalte4";
                    column.MinWidth = 135;
                    column.ExportWidth = 135;
                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                    column.ColumnType = MVCxGridViewColumnType.Memo;
                    column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                    column.Caption = (labels == null || labels.LabelSpalte4 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte4;
                    column.SetDataItemTemplateContent(
                        container =>
                        {

                            var url = DataBinder.Eval(container.DataItem, "Spalte4");
                            string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, true);
                            viewContext.Writer.Write(htmlLink);
                        });

                });
                settings.Columns.Add(column =>
                {
                    column.FieldName = "Spalte5";
                    column.MinWidth = 135;
                    column.ExportWidth = 135;
                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                    column.ColumnType = MVCxGridViewColumnType.Memo;
                    column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                    column.Caption = (labels == null || labels.LabelSpalte5 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte5;
                    column.SetDataItemTemplateContent(
                        container =>
                        {

                            var url = DataBinder.Eval(container.DataItem, "Spalte5");
                            string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                            viewContext.Writer.Write(htmlLink);
                        });
                });
                settings.Columns.Add(column =>
                {
                    column.FieldName = "Spalte6";
                    column.MinWidth = 135;
                    column.ExportWidth = 135;
                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                    column.ColumnType = MVCxGridViewColumnType.Memo;
                    column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                    column.Caption = (labels == null || labels.LabelSpalte6 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte6;
                    column.SetDataItemTemplateContent(
                        container =>
                        {

                            var url = DataBinder.Eval(container.DataItem, "Spalte6");
                            string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, true);
                            viewContext.Writer.Write(htmlLink);
                        });
                });
                settings.Columns.Add(column =>
                {
                    column.FieldName = "Spalte7";
                    column.MinWidth = 135;
                    column.ExportWidth = 135;
                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                    column.ColumnType = MVCxGridViewColumnType.Memo;
                    column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                    column.Caption = (labels == null || labels.LabelSpalte7 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte7;
                    column.SetDataItemTemplateContent(
                        container =>
                        {
                            var url = DataBinder.Eval(container.DataItem, "Spalte7");
                            string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                            viewContext.Writer.Write(htmlLink);
                        });
                });
                settings.Columns.Add(column =>
                {
                    column.FieldName = "Spalte8";
                    column.MinWidth = 135;
                    column.ExportWidth = 135;
                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                    column.ColumnType = MVCxGridViewColumnType.Memo;
                    column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                    column.Caption = (labels == null || labels.LabelSpalte8 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte8;
                    column.SetDataItemTemplateContent(
                        container =>
                        {
                            var url = DataBinder.Eval(container.DataItem, "Spalte8");
                            string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                            viewContext.Writer.Write(htmlLink);
                        });
                });
                settings.Columns.Add(column =>
                {
                    column.FieldName = "Spalte9";
                    column.MinWidth = 135;
                    column.ExportWidth = 135;
                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                    column.ColumnType = MVCxGridViewColumnType.Memo;
                    column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                    column.Caption = (labels == null || labels.LabelSpalte9 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte9;
                    column.SetDataItemTemplateContent(
                        container =>
                        {
                            var url = DataBinder.Eval(container.DataItem, "Spalte9");
                            string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                            viewContext.Writer.Write(htmlLink);
                        });
                });
                settings.Columns.Add(column =>
                {
                    column.FieldName = "Spalte10";
                    column.MinWidth = 135;
                    column.ExportWidth = 135;
                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                    column.ColumnType = MVCxGridViewColumnType.Memo;
                    column.Settings.AllowEllipsisInText = DefaultBoolean.False;
                    column.Caption = (labels == null || labels.LabelSpalte10 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte10;
                    column.SetDataItemTemplateContent(
                        container =>
                        {
                            var url = DataBinder.Eval(container.DataItem, "Spalte10");
                            string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                            viewContext.Writer.Write(htmlLink);
                        });
                });                

                settings.Columns.Add(column =>
                {
                    column.FieldName = "ErlassfassungInkraftretung";
                    column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), "Inkrafttretung");
                    column.MinWidth = 160;
                    column.ExportWidth = 160;
                    column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                    column.ReadOnly = true;
                    column.EditFormSettings.Visible = DefaultBoolean.False;
                });

                settings.Columns.Add(column =>
                {
                    column.FieldName = "ErlassfassungBearbeitetAm";
                    column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), "Beschluss");
                    column.MinWidth = 160;
                    column.ExportWidth = 160;
                    column.ColumnType = MVCxGridViewColumnType.DateEdit;
                    column.HeaderStyle.Wrap = DefaultBoolean.True;
                    column.ReadOnly = true;
                    column.EditFormSettings.Visible = DefaultBoolean.False;
                });

            GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

            settings.HtmlDataCellPrepared += (sender, e) =>
            {
                var erfuellung = "";
                if (e.VisibleIndex >= 0)
                {
                    erfuellung = e.GetValue("Erfuellung").ToString();
                }
                var bewilligungspflichtig = Convert.ToBoolean(e.GetValue("Bewilligungspflicht"));
                var nachweispflichtig = Convert.ToBoolean(e.GetValue("Nachweispflicht"));
                if (bewilligungspflichtig)
                {
                    e.Cell.BackColor = System.Drawing.Color.Lavender;
                }
                if (nachweispflichtig)
                {
                    e.Cell.BackColor = System.Drawing.Color.BlanchedAlmond;
                }
                if (bewilligungspflichtig && nachweispflichtig)
                {
                    e.Cell.BackColor = System.Drawing.Color.PowderBlue;
                }
                if (erfuellung == "Yes")
                {
                    e.Cell.BackColor = System.Drawing.ColorTranslator.FromHtml("#E2EFDA");
                }
            };

            settings.ClientLayout = (sender, e) =>
            {
                MVCxGridView gridView = sender as MVCxGridView;
                var cookieID = gridView.SettingsCookies.CookiesID + "_" + ViewData["KundendokumentID"];
                if (e.LayoutMode == ClientLayoutMode.Loading)
                {
                    var layout = CookieHelper.GetCookie(cookieID);
                    if (layout != "")
                    {
                        e.LayoutData = layout;
                    }
                }
                gridView.ExpandAll();
            };

            settings.SettingsPopup.HeaderFilter.Height = Unit.Pixel(440);
            settings.SettingsPopup.HeaderFilter.Width = Unit.Pixel(300);
            foreach (GridViewDataColumn column in settings.Columns)
            {
                if (column.FieldName == "LetzterPruefZeitpunkt" || column.FieldName == "NaechstePruefungAm" || column.FieldName == "ErlassfassungInkraftretung" || column.FieldName == "ErlassfassungBearbeitetAm")
                {
                    column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                    column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.DateRangePicker;
                }
                else if (column.FieldName == "StandortObjektTitel" || column.FieldName == "Pruefmethode" || column.FieldName == "ShortcutID" || column.FieldName == "Ablageort" || column.FieldName == "ErlassID"
                                || column.FieldName == "ErlassSrNummer" || column.FieldName == "Erfuellung" || column.FieldName == "Status")
                {
                    column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                    column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.CheckedList;
                }
                else
                {
                    column.Settings.AllowHeaderFilter = DefaultBoolean.False;
                }

                string cookieId = "PortalKundendokumentForderungenGridView_" + sessionHelper.GetCurrentUserID() + "_" + ViewData["KundendokumentID"];
                var cookie = CookieHelper.GetCookie(cookieId);
                if (cookie == "")
                {
                    var columns2 = unitOfWork.StandortRepository.GetByID((int)ViewData["StandortID"], "KundendokumentSpaltenauswahl").KundendokumentSpaltenauswahl;

                    if (columns2 != null)
                    {
                        if (column.FieldName == "RechtsbereicheUnbound" && !columns2.RechtsbereicheUnbound) { column.Visible = false; }
                        else if (column.FieldName == "StandortObjektTitel" && !columns2.StandortObjektTitel) { column.Visible = false; }
                        else if (column.FieldName == "ErlassSrNummer" && !columns2.ErlassSrNummer) { column.Visible = false; }
                        else if (column.FieldName == "ErlassID" && !columns2.ErlassID) { column.Visible = false; }
                        else if (column.FieldName == "ArtikelNummer" && !columns2.ArtikelNummer) { column.Visible = false; }
                        else if (column.FieldName == "Beschreibung" && !columns2.Beschreibung) { column.Visible = false; }
                        else if (column.FieldName == "BewilligungspflichtText" && !columns2.Bewilligungspflicht) { column.Visible = false; }
                        else if (column.FieldName == "NachweispflichtText" && !columns2.Nachweispflicht) { column.Visible = false; }
                        else if (column.FieldName == "Status" && !columns2.Status) { column.Visible = false; }
                        else if (column.FieldName == "Erfuellung" && !columns2.Erfuellung) { column.Visible = false; }
                        else if (column.FieldName == "LetzterPruefZeitpunkt" && !columns2.LetzterPruefZeitpunkt) { column.Visible = false; }
                        else if (column.FieldName == "NaechstePruefungAm" && !columns2.NaechstePruefungAm) { column.Visible = false; }
                        else if (column.FieldName == "Pruefmethode" && !columns2.Pruefmethode) { column.Visible = false; }
                        else if (column.FieldName == "ShortcutID" && !columns2.ShortcutID) { column.Visible = false; }
                        else if (column.FieldName == "Ablageort" && !columns2.Ablageort) { column.Visible = false; }
                        else if (column.FieldName == "Kommentar" && !columns2.Kommentar) { column.Visible = false; }
                        else if (column.FieldName == "Spalte1" && !columns2.Spalte1) { column.Visible = false; }
                        else if (column.FieldName == "Spalte2" && !columns2.Spalte2) { column.Visible = false; }
                        else if (column.FieldName == "Spalte3" && !columns2.Spalte3) { column.Visible = false; }
                        else if (column.FieldName == "Spalte4" && !columns2.Spalte4) { column.Visible = false; }
                        else if (column.FieldName == "Spalte5" && !columns2.Spalte5) { column.Visible = false; }
                        else if (column.FieldName == "Spalte6" && !columns2.Spalte6) { column.Visible = false; }
                        else if (column.FieldName == "Spalte7" && !columns2.Spalte7) { column.Visible = false; }
                        else if (column.FieldName == "Spalte8" && !columns2.Spalte8) { column.Visible = false; }
                        else if (column.FieldName == "Spalte9" && !columns2.Spalte9) { column.Visible = false; }
                        else if (column.FieldName == "Spalte10" && !columns2.Spalte10) { column.Visible = false; }                        
                        else if (column.FieldName == "ErlassfassungInkraftretung" && !columns2.ErlassfassungInkraftretung) { column.Visible = false; }
                        else if (column.FieldName == "ErlassfassungBearbeitetAm" && !columns2.ErlassfassungBearbeitetAm) { column.Visible = false; }
                    }
                }
            }
            return settings;
        }

        private GridViewSettings GetErlassfassungGridViewSettings(MVCxGridViewColumnCollection columns, List<KundendokumentErlassfassungViewModel> model)
        {
            var settings = new GridViewSettings();
            IUnitOfWork unitOfWork = new UnitOfWork();
            NeoSysLCS_Dev context = new NeoSysLCS_Dev();
            var sessionHelper = new SessionHelper();
            ViewBag.EnableCheckedListMode = true;
            settings.SettingsPopup.CustomizationWindow.Width = 300;
            settings.SettingsCustomizationDialog.Enabled = true;
            settings.Styles.CustomizationDialog.CssClass = "addScroll";
            settings.Name = "ErlassfassungenGridView";
            settings.SettingsExport.ExcelExportMode = DevExpress.Export.ExportType.DataAware;
            settings.SettingsExport.EnableClientSideExportAPI = true;

            ViewContext viewContext = new ViewContext();
            settings.Toolbars.Add(t =>
            {
                t.EnableAdaptivity = true;
                t.Items.Add(GridViewToolbarCommand.ExportToXlsx);
            });

            settings.KeyFieldName = "KundendokumentErlassfassungID";
            GridViewHelper.ApplyDefaultSettings(settings);
            settings.SettingsCookies.Enabled = false;

            settings.SettingsContextMenu.Enabled = true;
            settings.SettingsContextMenu.EnableRowMenu = DefaultBoolean.False;

            settings.Styles.Cell.Wrap = DefaultBoolean.True;
            settings.SettingsBehavior.AllowEllipsisInText = true;
            settings.SettingsResizing.ColumnResizeMode = ColumnResizeMode.Control;
            settings.SettingsCookies.StoreColumnsWidth = true;
            settings.SettingsPager.SEOFriendly = SEOFriendlyMode.Disabled;
            settings.Styles.GroupPanel.CssClass = "noWrapClass";
            settings.ClientSideEvents.BatchEditEndEditing = "OnBatchEditEndEditing";
            settings.ClientSideEvents.BatchEditStartEditing = "OnBatchStartEditing";

            settings.CommandColumn.AllowDragDrop = DefaultBoolean.False;
            settings.CommandColumn.Visible = true;

            settings.Columns.Add(column =>
            {
                column.FieldName = "Betroffen";
                column.ColumnType = MVCxGridViewColumnType.CheckBox;
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.MinWidth = 70;
                column.Width = 70;
            });

            settings.AutoFilterCellEditorInitialize = (s, e) =>
            {
                if (e.Column.FieldName == "Betroffen")
                {
                    ASPxComboBox combo = e.Editor as ASPxComboBox;
                    combo.Items.Clear();
                    combo.ValueType = typeof(string);
                    combo.Items.Add(Resources.Properties.Resources.Entitaet_KundendokumentErlassfassung_Betroffen, true);
                    combo.Items.Add(Resources.Properties.Resources.Entitaet_KundendokumentErlassfassung_NichtBetroffen, false);
                }

            };

            bool eventIsHandled = false;
            settings.AutoFilterCellEditorCreate = (s, e) =>
            {
                MVCxGridView g = s as MVCxGridView;
                if (!eventIsHandled)
                {
                    g.ProcessColumnAutoFilter += (sender, args) =>
                    {
                        if (args.Column.FieldName == "Betroffen")
                        {
                            if (args.Kind == GridViewAutoFilterEventKind.ExtractDisplayText)
                            {

                                if (args.Value == "False")
                                {
                                    args.Value = Resources.Properties.Resources.Entitaet_KundendokumentErlassfassung_NichtBetroffen;
                                }
                                if (args.Value == "True")
                                {
                                    args.Value = Resources.Properties.Resources.Entitaet_KundendokumentErlassfassung_Betroffen;
                                }
                            }

                        }

                    };
                    eventIsHandled = true;
                }
            };

            settings.ProcessColumnAutoFilter = (sender, e) =>
            {
                var gv = sender as MVCxGridView;
                string filter = gv.FilterExpression;
                var erfuellungList = from KundendokumentErfuellung value in Enum.GetValues(typeof(KundendokumentErfuellung))
                                     where (int)value != 4
                                     select new
                                     {
                                         Id = (int)value,
                                         Name = value.GetTranslation()
                                     };
                var test = (from x in model select x.Status).Distinct().ToList();
                var statusList = from KundendokumentItemStatus value in Enum.GetValues(typeof(KundendokumentItemStatus))
                                 where test.Contains(value)
                                 select new
                                 {
                                     Id = (int)value,
                                     Name = value.GetTranslation()
                                 };
            };

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassTitel";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.MinWidth = 200;
                column.ExportWidth = 200;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ExportCellStyle.Wrap = DefaultBoolean.True;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "ErlassSrNummer";
                column.Caption = Resources.Properties.Resources.Erlassnummer;
                column.MinWidth = 70;
                column.Width = 70;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.SortAscending();
                column.SortIndex = 2;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "HerausgeberName";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
                column.MinWidth = 200;
                column.ExportWidth = 200;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ExportCellStyle.Wrap = DefaultBoolean.True;
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Beschluss";
                column.MinWidth = 100;
                column.Width = 100;
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
                column.PropertiesEdit.DisplayFormatString = "d";

            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Inkrafttretung";
                column.MinWidth = 100;
                column.Width = 100;
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.SortDescending();
                column.SortIndex = 3;
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
                column.PropertiesEdit.DisplayFormatString = "d";

            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Quelle";
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.MinWidth = 70;
                column.Width = 70;
                column.SetDataItemTemplateContent(
                container =>
                {
                    var url = DataBinder.Eval(container.DataItem, "Quelle");
                    string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                    viewContext.Writer.Write(htmlLink);
                });
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "RelevanterKommentar";
                column.MinWidth = 150;
                column.Width = 150;
                column.Caption = Resources.Properties.Resources.View_Kundendokument_Erlassfassung_NeosysKommentar;
                column.Settings.AutoFilterCondition = AutoFilterCondition.Contains;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.PropertiesEdit.EncodeHtml = false;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.CellStyle.HorizontalAlign = HorizontalAlign.Center;

                var memoProp = column.PropertiesEdit as MemoProperties;
                if (memoProp != null)
                {
                    memoProp.EncodeHtml = false;
                }

                column.SetDataItemTemplateContent(
                    container =>
                    {
                        var url = DataBinder.Eval(container.DataItem, "RelevanterKommentar");
                        if (url != null)
                        {
                            if ((url.ToString().StartsWith("www")) || (url.ToString().StartsWith("http")))
                            {
                                string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                                viewContext.Writer.Write(htmlLink);
                            }

                            else
                            {
                                viewContext.Writer.Write(container.Text);
                            }
                        }
                    });
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Kerninhalte";
                column.MinWidth = 150;
                column.Width = 150;
                column.Caption = Resources.Properties.Resources.Entitaet_Erlass_Kerninhalte;
                column.Settings.AutoFilterCondition = AutoFilterCondition.Contains;
                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.ColumnType = MVCxGridViewColumnType.Memo;
                column.PropertiesEdit.EncodeHtml = false;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.CellStyle.HorizontalAlign = HorizontalAlign.Center;

                var memoProp = column.PropertiesEdit as MemoProperties;
                if (memoProp != null)
                {
                    memoProp.EncodeHtml = false;
                }

                column.SetDataItemTemplateContent(
                    container =>
                    {
                        var url = DataBinder.Eval(container.DataItem, "Kerninhalte");
                        if (url != null)
                        {
                            if ((url.ToString().StartsWith("www")) || (url.ToString().StartsWith("http")))
                            {
                                string htmlLink = LinkHelper.GenerateHtmlLinkIfPossible(url, url, true);
                                viewContext.Writer.Write(htmlLink);
                            }

                            else
                            {
                                viewContext.Writer.Write(container.Text);
                            }
                        }
                    });
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Status";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
                column.MinWidth = 70;
                column.Width = 70;
                //define as combobox for filtern over the enum
                column.ColumnType = MVCxGridViewColumnType.ComboBox;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                var statusComboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

                var test = (from x in model select x.Status).Distinct().ToList();

                var list = from KundendokumentItemStatus value in Enum.GetValues(typeof(KundendokumentItemStatus))
                           where test.Contains(value)
                           select new
                           {
                               Id = (int)value,
                               Name = value.GetTranslation()
                           };

                statusComboBoxProperties.DataSource = list;
                statusComboBoxProperties.ValueField = "Id";
                statusComboBoxProperties.TextField = "Name";

                column.HeaderStyle.Wrap = DefaultBoolean.True;
                column.ReadOnly = true;
                column.EditFormSettings.Visible = DefaultBoolean.False;
                column.SetDataItemTemplateContent(container =>
                {
                    var statusCandidate = DataBinder.Eval(container.DataItem, "Status");
                    if (statusCandidate != null)
                    {
                        var status = (KundendokumentItemStatus)statusCandidate;
                        viewContext.Writer.Write("<span class=\"label label-" + status + "\">" + status.GetTranslation() + "</span>");
                    }
                });
            });

            settings.Columns.Add(column =>
            {
                column.FieldName = "Kommentar";
                column.Caption = Resources.Properties.Resources.View_Kundendokument_Erlassfassung_Kommentar;
                column.EditFormSettings.Visible = DefaultBoolean.True;
                column.Settings.AutoFilterCondition = AutoFilterCondition.Contains;
            });

            GridViewHelper.SetValidationSettingsOnColumns(settings, Display.Dynamic);

            settings.SettingsPopup.HeaderFilter.Height = Unit.Pixel(440);
            settings.SettingsPopup.HeaderFilter.Width = Unit.Pixel(300);
            foreach (GridViewDataColumn column in settings.Columns)
            {
                if (column.FieldName == "Beschluss" || column.FieldName == "Inkrafttretung")
                {
                    column.Settings.AllowHeaderFilter = DefaultBoolean.True;
                    column.SettingsHeaderFilter.Mode = GridHeaderFilterMode.DateRangePicker;
                }
                else
                {
                    column.Settings.AllowHeaderFilter = DefaultBoolean.False;
                }
            }

            settings.ClientLayout = (sender, e) =>
            {
                MVCxGridView gridView = sender as MVCxGridView;
                var cookieID = gridView.SettingsCookies.CookiesID + "_" + ViewData["KundendokumentID"];
                if (e.LayoutMode == ClientLayoutMode.Loading)
                {
                    var layout = CookieHelper.GetCookie(cookieID);
                    if (layout != "")
                    {
                        e.LayoutData = layout;
                    }
                }
                gridView.ExpandAll();
            };

            return settings;
        }


            /// <summary>
            ///  Inner Class for the Gridview Settings (this avoids a separat defninition of the settings in the view and the export).
            /// </summary>


            /// <summary>
            /// Gets the column definition
            /// </summary>
            /// <returns></returns>
            public static MVCxGridViewColumnCollection GetKundendokumentForderungenExportGridColumns(int kundeID, int kundendokumentID)
            {
            var columns = new MVCxGridViewColumnCollection();
            var _unitOfWork = new UnitOfWork();

            //columns.Add(column =>
            //{
            //    column.FieldName = "KundendokumentForderungsversionID";
            //    column.Caption = "ID";
            //});

            //columns.Add(column =>
            //{
            //    column.FieldName = "Inkrafttretung";
            //    column.ColumnType = MVCxGridViewColumnType.DateEdit;
            //    column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
            //});

            columns.Add(column =>
            {
                column.FieldName = "Rechtsbereiche";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
            });

            columns.Add(column =>
            {
                column.FieldName = "StandortObjektTitel";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
            });

            columns.Add(column =>
            {
                column.FieldName = "ArtikelNummer";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
            });

            columns.Add(column =>
            {
                column.FieldName = "ErlassTitel";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
            });


            columns.Add(column =>
            {
                column.FieldName = "SrNummer";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
            });

            columns.Add(column =>
            {
                column.FieldName = "Beschreibung";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.ExportWidth = 500;
                column.ColumnType = MVCxGridViewColumnType.Memo;
            });

            columns.Add(column =>
            {
                column.FieldName = "Bewilligungspflicht";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.ColumnType = MVCxGridViewColumnType.CheckBox;
            });

            columns.Add(column =>
            {
                column.FieldName = "Nachweispflicht";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.ColumnType = MVCxGridViewColumnType.CheckBox;
            });

            columns.Add(column =>
            {
                column.FieldName = "Status";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
            });
            columns.Add(column =>
            {
                column.FieldName = "Erfuellung";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.ColumnType = MVCxGridViewColumnType.ComboBox;

                var comboBoxProperties = column.PropertiesEdit as ComboBoxProperties;

                var list = from KundendokumentErfuellung value in Enum.GetValues(typeof(KundendokumentErfuellung))
                           where (int)value != 4
                           select new
                           {
                               Id = (int)value,
                               Name = value.GetTranslation()
                           };

                comboBoxProperties.DataSource = list;
                comboBoxProperties.ValueField = "Id";
                comboBoxProperties.TextField = "Name";
                comboBoxProperties.ValueType = typeof(Int32);
            });

            columns.Add(column =>
            {
                column.FieldName = "LetzterPruefZeitpunkt";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
            });

            columns.Add(column =>
            {
                column.FieldName = "NaechstePruefungAm";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
            });

            columns.Add(column =>
            {
                column.FieldName = "Pruefmethode";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
            });

            columns.Add(column =>
            {
                column.FieldName = "Verantwortlich";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
            });
            columns.Add(column =>
            {
                column.FieldName = "Ablageort";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.HeaderStyle.Wrap = DefaultBoolean.True;
            });

            columns.Add(column =>
            {
                column.FieldName = "Kommentar";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
                column.ColumnType = MVCxGridViewColumnType.Memo;
            });

            var labels = _unitOfWork.KundendokumentRepository.GetByID(kundendokumentID, "KundendokumentSpaltenlabel").KundendokumentSpaltenlabel;

            columns.Add(column =>
            {
                column.FieldName = "Spalte1";
                column.Caption = (labels == null || labels.LabelSpalte1 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte1;
            });
            columns.Add(column =>
            {
                column.FieldName = "Spalte2";
                column.Caption = (labels == null || labels.LabelSpalte2 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte2;
            });
            columns.Add(column =>
            {
                column.FieldName = "Spalte3";
                column.Caption = (labels == null || labels.LabelSpalte3 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte3;
            });
            columns.Add(column =>
            {
                column.FieldName = "Spalte4";
                column.Caption = (labels == null || labels.LabelSpalte4 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte4;
            });
            columns.Add(column =>
            {
                column.FieldName = "Spalte5";
                column.Caption = (labels == null || labels.LabelSpalte5 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte5;
            });
            columns.Add(column =>
            {
                column.FieldName = "Spalte6";
                column.Caption = (labels == null || labels.LabelSpalte6 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte6;
            });
            columns.Add(column =>
            {
                column.FieldName = "Spalte7";
                column.Caption = (labels == null || labels.LabelSpalte7 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte7;
            });
            columns.Add(column =>
            {
                column.FieldName = "Spalte8";
                column.Caption = (labels == null || labels.LabelSpalte8 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte8;
            });
            columns.Add(column =>
            {
                column.FieldName = "Spalte9";
                column.Caption = (labels == null || labels.LabelSpalte9 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte9;
            });
            columns.Add(column =>
            {
                column.FieldName = "Spalte10";
                column.Caption = (labels == null || labels.LabelSpalte10 == "") ? TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName) : labels.LabelSpalte10;
            });
            return columns;
        }

        public static MVCxGridViewColumnCollection GetKundendokumentErlassfassungExportGridColumns(int kundeID, int kundendokumentID)
        {
            var columns = new MVCxGridViewColumnCollection();
            var _unitOfWork = new UnitOfWork();

            //columns.Add(column =>
            //{
            //    column.FieldName = "KundendokumentForderungsversionID";
            //    column.Caption = "ID";
            //});

            //columns.Add(column =>
            //{
            //    column.FieldName = "Inkrafttretung";
            //    column.ColumnType = MVCxGridViewColumnType.DateEdit;
            //    column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentForderungsversionViewModel), column.FieldName);
            //});

            columns.Add(column =>
            {
                column.FieldName = "Betroffen";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
                column.ColumnType = MVCxGridViewColumnType.CheckBox;
            });

            columns.Add(column =>
            {
                column.FieldName = "ErlassTitel";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
            });

            columns.Add(column =>
            {
                column.FieldName = "ErlassSrNummer";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
            });

            columns.Add(column =>
            {
                column.FieldName = "HerausgeberName";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
            });


            columns.Add(column =>
            {
                column.FieldName = "Beschluss";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
            });

            columns.Add(column =>
            {
                column.FieldName = "Inkrafttretung";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
            });

            columns.Add(column =>
            {
                column.FieldName = "Quelle";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
            });

            columns.Add(column =>
            {
                column.FieldName = "RelevanterKommentar";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
            });

            columns.Add(column =>
            {
                column.FieldName = "Kerninhalte";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
            });
            columns.Add(column =>
            {
                column.FieldName = "Status";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
            });

            columns.Add(column =>
            {
                column.FieldName = "Kommentar";
                column.Caption = TranslationHelper.GetTranslation(typeof(KundendokumentErlassfassungViewModel), column.FieldName);
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
            });
            return columns;
        }
    }
}