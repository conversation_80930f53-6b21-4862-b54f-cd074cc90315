using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Web.Mvc;
using System.Web.WebPages;
using Microsoft.AspNet.Identity;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Helpers;
using Postal;

namespace NeoSysLCS.Site.Areas.Portal.Controllers
{
    [Authorize]
    public class DashboardController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;
        readonly IEmailService _emailService;
        public const string ErfuellungPartialView = "~/Areas/Portal/Views/Dashboard/_ErfuellungsgradePartialView.cshtml";
        public const string AllgemeineNewsPartialView = "~/Areas/Portal/Views/Dashboard/_AllgemeineNewsPartialView.cshtml";
        public const string GesetzesaenderungenPartialView = "~/Areas/Portal/Views/Dashboard/_GesetzesaenderungenPartialView.cshtml";
        public const string KommentarePV = "~/Areas/Portal/Views/Dashboard/_KommentarePartialView.cshtml";
        public const string VernehmlassungPartialView = "~/Areas/Portal/Views/Dashboard/_VernehmlassungPartialView.cshtml";
        private static readonly Dictionary<int, string> EmailTemplates
            = new Dictionary<int, string>
            {
                                { 1, "MassnahmeCreated_DE" },
                                { 2, "MassnahmeCreated_FR" },
                                { 3, "MassnahmeCreated_IT" },
                                { 4, "MassnahmeCreated_EN" }
            };

        public DashboardController()
        {
            _unitOfWork = new UnitOfWork();
            _emailService = new Postal.EmailService();
        }

        public ActionResult DashboardPageControlCallbacksPartial(int StandortID, int KundeID)
        {
            ViewData["StandortID"] = StandortID;
            ViewData["KundeID"] = KundeID;
            return PartialView("_DashboardPageControlCallbacksPartial");
        }


        public ActionResult Index(int standortId = 0)
        {
            var sh = new SessionHelper();
            var kundenId = sh.GetKundeIdOfCurrentUser();


            if (kundenId.HasValue)
            {
                ViewData["HasKundendokument"] = _unitOfWork.KundendokumentRepository.HasKundeAKundendokumentAtAnyStandort(kundenId.Value);
            }
            else
            {
                ViewData["HasKundendokument"] = false;
            }


            ViewData["StandortID"] = standortId;
            ViewData["KundeID"] = kundenId;

            return View("Index");
        }

        public ActionResult NewsPartialView()
        {
            var news = _unitOfWork.CustomerNewsRepository.GetAllCustomerNewsViewModels(CustomerNewsRepository.NewsForToday);
            return PartialView(AllgemeineNewsPartialView, news);
        }

        public ActionResult ConsultationPartialView(int kundeID)
        {
            ViewData["KundeID"] = kundeID;

            var consultations = _unitOfWork.ConsultationRepository.GetDashboardConsultationViewModels(kundeID);
            return PartialView(VernehmlassungPartialView, consultations);
        }

        public ActionResult KommentarePartialView(int standortID, int kundeID)
        {
            ViewData["StandortID"] = standortID;
            ViewData["KundeID"] = kundeID;

            return PartialView("_KommentarePartialView", _unitOfWork.DashboardRepository.GetAllKommentarNewsletterViewModelsByKunde(kundeID));
        }

        public ActionResult GesetzaenderungenPartialView(int standortID, int kundeID)
        {
            ViewData["StandortID"] = standortID;
            ViewData["KundeID"] = kundeID;

            return PartialView("_GesetzesaenderungenPartialView", _unitOfWork.DashboardRepository.GetAllGesetzaenderungViewModelsByKunde(kundeID));
        }

        public ActionResult ShowMassnahmePopupForGesetzaenderungen(int kommentarID, string standortIds, int kundeID, string erlassNr, string erlassTitel)
        {
            string[] ids = standortIds.Split(';');
            List<StandortViewModel> standortViewModels = new List<StandortViewModel>();
            foreach (string id in ids)
            {
                standortViewModels.Add(_unitOfWork.StandortRepository.GetStandortViewModelById(Convert.ToInt32(id)));
            }
            standortViewModels = standortViewModels.OrderBy(x => x.StandortID).ToList();

            var model = new MassnahmeErfassenViewModel
            {
                Betreff = "Gesetzesänderung - Nr. " + erlassNr + " - " + erlassTitel,
                Standorte = standortViewModels.AsQueryable(),
                OriginID = kommentarID // Kommentare are related to Erlasse, Objekte, Rechsbereiche
            };
            return PartialView("_MassnahmeErfassenMultiplePopup", model);
        }



        public ActionResult ShowMassnahmePopupForKommentare(int kommentarID, string standortIds, int kundeID, string erlassNr, string erlassTitel, bool betroffen)
        {
            string[] ids = standortIds.Split(';');
            List<StandortViewModel> standortViewModels = new List<StandortViewModel>();
            foreach (string id in ids)
            {
                standortViewModels.Add(_unitOfWork.StandortRepository.GetStandortViewModelById(Convert.ToInt32(id)));
            }
            standortViewModels = standortViewModels.OrderBy(x => x.StandortID).ToList();

            var model = new MassnahmeErfassenViewModel
            {
                Betreff = "Kommentare - Nr. " + erlassNr + " - " + erlassTitel,
                OriginID = kommentarID, // Kommentare are related to Erlasse, Objekte, Rechsbereiche
                Betroffen = betroffen,
                Standorte = standortViewModels.AsQueryable()
            };

            return PartialView("_MassnahmeErfassenMultiplePopup", model);
        }

        /// <summary>
        /// Goes to the default password reset view
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [ValidateInput(false)]
        public ActionResult CreateMassnahmeStandort(MassnahmeErfassenViewModel model)
        {
            var shortcuts = _unitOfWork.ShortcutRepository.GetShortcutCombobox(model.StandortId.Value);
            model.Shortcuts = shortcuts;

            return PartialView("_MassnahmeErfassenPopup", model);
        }

        public ActionResult ShowMassnahmePopupForVernehmlassung(int consultationId, string title, string quelle)
        {
            var kundeId = new SessionHelper().GetKundeIdOfCurrentUser().GetValueOrDefault(-1);
            List<StandortViewModel> standortViewModels = _unitOfWork.StandortRepository.GetAllStandortViewModels(kundeId).ToList();
            standortViewModels = standortViewModels.OrderBy(x => x.StandortID).ToList();

            var model = new MassnahmeErfassenViewModel
            {
                Betreff = "Vernehmlassung - " + title,
                OriginID = consultationId, //consultations have relation only to Erlass
                Standorte = standortViewModels.AsQueryable()
            };
            return PartialView("_MassnahmeErfassenMultiplePopup", model);
        }

        [HttpPost]
        [ValidateInput(false)]
        public ActionResult CreateMassnahme(MassnahmeErfassenViewModel model)
        {
            var kundeId = new SessionHelper().GetKundeIdOfCurrentUser().GetValueOrDefault(-1);
            var standortIds = _unitOfWork.StandortRepository.GetAllStandortViewModels(kundeId).Select(s => s.StandortID);
            var shortcuts = model.StandortId.HasValue 
                ? _unitOfWork.ShortcutRepository.GetShortcutCombobox(model.StandortId.Value)
                : _unitOfWork.ShortcutRepository.GetAllShortcutViewModelsByStandortIds(standortIds);
            model.Shortcuts = shortcuts;

            if (model.Massnahme.IsEmpty())
            {
                ModelState.AddModelError("Massnahme", "Das Feld \"Massnahme\" ist erforderlich.");
                return PartialView("_MassnahmeErfassenPopup", model);
            }

            if (model.ShortcutId == 0)
            {
                ModelState.AddModelError("ShortcutId", "Das Feld \"Verantwortlich\" ist erforderlich.");
                return PartialView("_MassnahmeErfassenPopup", model);
            }

            if (model.Termin == DateTime.MinValue)
            {
                ModelState.AddModelError("Termin", "Das Feld \"Termin\" ist erforderlich.");
                return PartialView("_MassnahmeErfassenPopup", model);
            }

            string linkDe = "";
            string linkFr = "";
            string linkIt = "";
            string linkEn = "";
            string standortName = "";
            if (model.StandortId.HasValue)
            {
                standortName = _unitOfWork.StandortRepository.GetByID(model.StandortId.Value).Name;
            }

            if (model.Betreff.StartsWith("Kommentare"))
            {
                List<KommentarViewModel> kommentarUebersetzungen = _unitOfWork.KommentarRepository.GetKommentarUebersetzungenByKommentar(model.OriginID).ToList();
                KommentarViewModel kommentarViewModel = _unitOfWork.KommentarRepository.GetKommentarViewModelById(model.OriginID);

                foreach (KommentarViewModel uebersetzung in kommentarUebersetzungen)
                {
                    if (model.Betroffen)
                    {
                        var linkBetroffenK = uebersetzung.LinkBetroffenKommentar;
                        var betroffenK = uebersetzung.BetroffenKommentar;
                        if (uebersetzung.SpracheID == 1)
                        {
                            linkDe = linkBetroffenK != Resources.Properties.Resources.Fehler_Keine_Uebersetzung ? linkBetroffenK : betroffenK;
                        }
                        else if (uebersetzung.SpracheID == 2)
                        {
                            linkFr = linkBetroffenK != Resources.Properties.Resources.Fehler_Keine_Uebersetzung ? linkBetroffenK : betroffenK;
                        }
                        else if (uebersetzung.SpracheID == 3)
                        {
                            linkIt = linkBetroffenK != Resources.Properties.Resources.Fehler_Keine_Uebersetzung ? linkBetroffenK : betroffenK;
                        }
                        else if (uebersetzung.SpracheID == 4)
                        {
                            linkEn = linkBetroffenK != Resources.Properties.Resources.Fehler_Keine_Uebersetzung ? linkBetroffenK : betroffenK;
                        }
                    }
                    else
                    {
                        var linkNichtBetroffenK = uebersetzung.LinkBetroffenKommentar;
                        var nichtBetroffenK = uebersetzung.BetroffenKommentar;
                        if (uebersetzung.SpracheID == 1)
                        {
                            linkDe = linkNichtBetroffenK != Resources.Properties.Resources.Fehler_Keine_Uebersetzung ? linkNichtBetroffenK : nichtBetroffenK;
                        }
                        else if (uebersetzung.SpracheID == 2)
                        {
                            linkFr = linkNichtBetroffenK != Resources.Properties.Resources.Fehler_Keine_Uebersetzung ? linkNichtBetroffenK : nichtBetroffenK;
                        }
                        else if (uebersetzung.SpracheID == 3)
                        {
                            linkIt = linkNichtBetroffenK != Resources.Properties.Resources.Fehler_Keine_Uebersetzung ? linkNichtBetroffenK : nichtBetroffenK;
                        }
                        else if (uebersetzung.SpracheID == 4)
                        {
                            linkEn = linkNichtBetroffenK != Resources.Properties.Resources.Fehler_Keine_Uebersetzung ? linkNichtBetroffenK : nichtBetroffenK;
                        }
                    }
                }
            }
            else if (model.Betreff.StartsWith("Gesetzesänderung"))
            {
                List<KommentarViewModel> kommentarUebersetzungen = _unitOfWork.KommentarRepository.GetKommentarUebersetzungenByKommentar(model.OriginID).ToList();

                foreach (KommentarViewModel uebersetzung in kommentarUebersetzungen)
                {
                    if (uebersetzung.SpracheID == 1)
                    {
                        linkDe = uebersetzung.Quelle;
                    }
                    else if (uebersetzung.SpracheID == 2)
                    {
                        linkFr = uebersetzung.Quelle;
                    }
                    else if (uebersetzung.SpracheID == 3)
                    {
                        linkIt = uebersetzung.Quelle;
                    }
                    else if (uebersetzung.SpracheID == 4)
                    {
                        linkEn = uebersetzung.Quelle;
                    }
                }
            }
            else if(model.Betreff.StartsWith("Vernehmlassung"))
            {
                List<ConsultationViewModel> consultationUebersetzungen = _unitOfWork.ConsultationRepository.GetConsultationUebersetzungenByConsultation(model.OriginID).ToList();

                foreach (ConsultationViewModel uebersetzung in consultationUebersetzungen)
                {
                    if (uebersetzung.SpracheID == 1)
                    {
                        linkDe = uebersetzung.Quelle;
                    }
                    else if (uebersetzung.SpracheID == 2)
                    {
                        linkFr = uebersetzung.Quelle;
                    }
                    else if (uebersetzung.SpracheID == 3)
                    {
                        linkIt = uebersetzung.Quelle;
                    }
                    else if (uebersetzung.SpracheID == 4)
                    {
                        linkEn = uebersetzung.Quelle;
                    }
                }
            }
            else if (model.Betreff.StartsWith(standortName + " - " + Resources.Properties.Resources.View_Kundendokument_TabForderungen))
            {
                if (model.ArtikelID.HasValue)
                {
                    List<ArtikelViewModel> artikelUebersetzungen = _unitOfWork.ArtikelRepository.GetArtikelUebersetzungenByArtikel(model.ArtikelID.Value).ToList();

                    foreach (ArtikelViewModel uebersetzung in artikelUebersetzungen)
                    {
                        if (uebersetzung.SpracheID == 1)
                        {
                            linkDe = uebersetzung.Quelle;
                        }
                        else if (uebersetzung.SpracheID == 2)
                        {
                            linkFr = uebersetzung.Quelle;
                        }
                        else if (uebersetzung.SpracheID == 3)
                        {
                            linkIt = uebersetzung.Quelle;
                        }
                        else if (uebersetzung.SpracheID == 4)
                        {
                            linkEn = uebersetzung.Quelle;
                        }
                    }
                }
            }
            else if (model.Betreff.StartsWith(standortName + " - " + Resources.Properties.Resources.View_Kundendokument_TabErlassfassungen))
            {
                KundendokumentErlassfassung kundendokumentErlassfassung = _unitOfWork.KundendokumentErlassfassungRepository.GetByID(model.OriginID);
                int erlassfassungId = kundendokumentErlassfassung.ErlassfassungID;
                List<ErlassfassungViewModel> erlassfassungUebersetzungen = _unitOfWork.ErlassfassungRepository.GetErlassfassungUebersetzungenByErlassfassung(erlassfassungId).ToList();


                foreach (ErlassfassungViewModel uebersetzung in erlassfassungUebersetzungen)
                {
                    string quelle = uebersetzung.Quelle;
                    string linkBetroffenKommentar = uebersetzung.LinkBetroffenKommentar;
                    string betroffenKommentar = uebersetzung.BetroffenKommentar;
                    string linkNichtBetroffenKommentar = uebersetzung.LinkBetroffenKommentar;
                    string nichtBetroffenKommentar = uebersetzung.LinkNichtBetroffenKommentar;
                    bool betroffen = kundendokumentErlassfassung.Betroffen;

                    if (uebersetzung.SpracheID == 1)
                    {
                        if (betroffen)
                        {
                            linkDe = linkBetroffenKommentar != "" ? linkBetroffenKommentar : (betroffenKommentar != "" ? betroffenKommentar : quelle);
                        }
                        else
                        {
                            linkDe = linkNichtBetroffenKommentar != "" ? linkNichtBetroffenKommentar : (nichtBetroffenKommentar != "" ? nichtBetroffenKommentar : quelle);
                        }
                    }
                    else if (uebersetzung.SpracheID == 2)
                    {
                        if (betroffen)
                        {
                            linkFr = linkBetroffenKommentar != "" ? linkBetroffenKommentar : (betroffenKommentar != "" ? betroffenKommentar : quelle);
                        }
                        else
                        {
                            linkFr = linkNichtBetroffenKommentar != "" ? linkNichtBetroffenKommentar : (nichtBetroffenKommentar != "" ? nichtBetroffenKommentar : quelle);
                        }
                    }
                    else if (uebersetzung.SpracheID == 3)
                    {
                        if (betroffen)
                        {
                            linkIt = linkBetroffenKommentar != "" ? linkBetroffenKommentar : (betroffenKommentar != "" ? betroffenKommentar : quelle);
                        }
                        else
                        {
                            linkIt = linkNichtBetroffenKommentar != "" ? linkNichtBetroffenKommentar : (nichtBetroffenKommentar != "" ? nichtBetroffenKommentar : quelle);
                        }
                    }
                    else if (uebersetzung.SpracheID == 4)
                    {
                        if (betroffen)
                        {
                            linkEn = linkBetroffenKommentar != "" ? linkBetroffenKommentar : (betroffenKommentar != "" ? betroffenKommentar : quelle);
                        }
                        else
                        {
                            linkEn = linkNichtBetroffenKommentar != "" ? linkNichtBetroffenKommentar : (nichtBetroffenKommentar != "" ? nichtBetroffenKommentar : quelle);
                        }
                    }
                }
            }
            else if (model.Betreff.StartsWith(standortName + " - " + "SUVA"))
            {
                KundendokumentChecklistViewModel kudoChkListViewModel = _unitOfWork.kundendokumentChecklistRepository.GetKundendokumentChecklistViewModelByChecklist(model.OriginID);
                List<ErlassViewModel> erlassUebersetzungen = _unitOfWork.ErlassRepository.GetErlassUebersetzungenByErlass(kudoChkListViewModel.ErlassID).ToList();

                foreach (ErlassViewModel uebersetzung in erlassUebersetzungen)
                {
                    if (uebersetzung.SpracheID == 1)
                    {
                        linkDe = uebersetzung.Quelle;
                    }
                    else if (uebersetzung.SpracheID == 2)
                    {
                        linkFr = uebersetzung.Quelle;
                    }
                    else if (uebersetzung.SpracheID == 3)
                    {
                        linkIt = uebersetzung.Quelle;
                    }
                    else if (uebersetzung.SpracheID == 4)
                    {
                        linkEn = uebersetzung.Quelle;
                    }
                }
            }

            MassnahmeViewModel viewModel = new MassnahmeViewModel
            {
                Betreff = model.Betreff,
                MassnahmeText = model.Massnahme,
                Bemerkung = model.Bemerkung,
                Termin = model.Termin,
                Status = MassnahmeStatus.New,
                ShortcutID = model.ShortcutId,
                StandortID = model.StandortId,
                ErstelltAm = DateTime.Now,
                ErstelltVonID = System.Web.HttpContext.Current.User.Identity.GetUserId(),
                LinkDe = linkDe,
                LinkFr = linkFr,
                LinkIt = linkIt,
                LinkEn = linkEn,
                AritkelID = model.ArtikelID,
                // KundendokumentChecklistQuestionID for SUVA, OriginID for the rest
                OriginID = model.ChecklistQuestionID ?? model.OriginID,
                EmailNotification = model.EmailNotification
            };

            // create massnahme
            int kundeMassnahmeId = _unitOfWork.MassnahmeRepository.Insert(viewModel);
            if (model.EmailNotification == true)
            {
                var mailHelper = new MassnahmeMailHelper();
                mailHelper.SendMassnahmeMail(viewModel, kundeId, kundeMassnahmeId);
            }
            _unitOfWork.Save();         

            return PartialView("_MassnahmeConfirmationPopup", model);
        }
    }
}