using System.Web;
using DevExpress.Web.Mvc;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Controllers;
using NeoSysLCS.Site.Helpers;
using System.Globalization;
using Microsoft.AspNet.Identity;
using System.Web.Mvc;
using System.Linq;
using System.Collections.Generic;
using DevExpress.Web;
using System;
using DevExpress.Utils;
using DevExpress.Data.Filtering;
using DevExpress.Data;
using DevExpress.Export;
using DevExpress.XtraPrinting;
using System.Web.UI;
using System.Web.UI.WebControls;
using Microsoft.Ajax.Utilities;

namespace NeoSysLCS.Site.Areas.Portal.Controllers
{
    public class LegalComplianceController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;

        public LegalComplianceController()
        {
            _unitOfWork = new UnitOfWork();
        }

        public LegalComplianceController(IUnitOfWork unitOfWorkCandidate)
        {
            _unitOfWork = unitOfWorkCandidate;
        }

        public ActionResult Index(int standortID = 0)
        {
            var sh = new SessionHelper();
            var currentUser = sh.GetCurrentUser();
            int kundeId = (int)currentUser.KundeID;
            ViewData["KundeId"] = kundeId;

            if (standortID == 0)
            {
                List<StandortViewModel> standorte = _unitOfWork.KundeRepository.LegalComplianceGetStandorteByKundeViewModel(Int16.Parse(ViewData["KundeID"].ToString())).ToList();
                ViewData["StandortID"] = standorte.First().StandortID;
            }
            else
            {
                ViewData["StandortID"] = standortID;
            }

            var model = new DateRangePickerModel();
            ViewData["startDate"] = model.Start;
            ViewData["endDate"] = model.Start;
            return View("DatePicker", model);
        }

        public ActionResult LegalCompliance(int standortID = 0)
        {
            var sh = new SessionHelper();
            var currentUser = sh.GetCurrentUser();
            int kundeId = (int)currentUser.KundeID;

            ViewData["KundeId"] = kundeId;
            if (standortID == 0)
            {
                List<StandortViewModel> standorte = _unitOfWork.KundeRepository.LegalComplianceGetStandorteByKundeViewModel(Int16.Parse(ViewData["KundeID"].ToString())).ToList();
                ViewData["StandortID"] = standorte.First().StandortID;
            }
            else
            {
                ViewData["StandortID"] = standortID;
            }

            return PartialView("_LegalCompliance");
        }

        public ActionResult StanodrtIndex(int standortID = 0)
        {
            if (standortID == 0)
            {
                List<StandortViewModel> standorte = _unitOfWork.KundeRepository.LegalComplianceGetStandorteByKundeViewModel(Int16.Parse(ViewData["KundeID"].ToString())).ToList();
                ViewData["StandortID"] = standorte.First().StandortID;
            }
            else
            {
                ViewData["StandortID"] = standortID;
            }
            var sh = new SessionHelper();
            var currentUser = sh.GetCurrentUser();
            int kundeId = (int)currentUser.KundeID;
            ViewData["KundeId"] = kundeId;

            var model = new DateRangePickerModel();
            //ViewData["selectedMonths"] = resolveSelectedMonths(model, kundeId, Int16.Parse(ViewData["StandortID"].ToString()));
            ViewData["startDate"] = model.Start;
            ViewData["endDate"] = model.Start;

            return View("DatePicker");
        }

        [HttpPost]
        public ActionResult DateRangePicker(DateRangePickerModel model)
        {
            var sh = new SessionHelper();
            var currentUser = sh.GetCurrentUser();
            int kundeId = (int)currentUser.KundeID;
            ViewData["KundeId"] = kundeId;

            var m = fixTheModel(model);

            var selectedStandort = ComboBoxExtension.GetValue<string>("comboBox5");
            ViewData["StandortID"] = _unitOfWork.StandortRepository.GetByName(selectedStandort, kundeId).StandortID;
            ViewData["startDate"] = model.Start;
            ViewData["endDate"] = model.End;

            if (Request.Params["Submit"] == null)
                ModelState.Clear();
            else
                ViewBag.SuccessValidation = true;
            return View("DatePicker", m);
        }

        private DateRangePickerModel fixTheModel(DateRangePickerModel original)
        {
            var model = new DateRangePickerModel();
            if (original.Start.Year > 1 && original.End.Year > 1 && original.Start.CompareTo(original.End) > 0)
            {
                model.Start = original.End;
                model.End = original.Start;
                return model;
            }
            else if (original.Start.Year > 1 && original.End.Year == 1)
            {
                model.Start = original.Start;
                model.End = original.Start.AddDays(1);
                return model;
            }
            else if (original.Start.Year == 1 && original.End.Year > 1)
            {
                model.End = original.End;
                model.Start = original.End.AddDays(-1);
                return model;
            }

            return original;
        }
    }
}