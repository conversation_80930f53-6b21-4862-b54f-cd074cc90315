using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Mail;
using NeoSysLCS.DomainModel.Models;
using Postal;

namespace NeoSysLCS.Site.Services.Newsletter
{
    /// <summary>
    /// Implementation of INewsletterEmailService that sends emails using SMTP
    /// </summary>
    public class NewsletterEmailService : INewsletterEmailService
    {
        private readonly IEmailService _emailService;
        
        private static readonly Dictionary<int, string> EmailTemplates = new Dictionary<int, string>
        {
            { 1, "Newsletter_DE" },
            { 2, "Newsletter_FR" },
            { 3, "Newsletter_IT" },
            { 4, "Newsletter_EN" }
        };

        /// <summary>
        /// Constructor with default email service
        /// </summary>
        public NewsletterEmailService() : this(new Postal.EmailService())
        {
        }

        /// <summary>
        /// Constructor with injected email service
        /// </summary>
        /// <param name="emailService">The email service to use</param>
        public NewsletterEmailService(IEmailService emailService)
        {
            _emailService = emailService ?? throw new ArgumentNullException(nameof(emailService));
        }

        /// <summary>
        /// Sends a newsletter email to a user with the generated PDF attachment
        /// </summary>
        /// <param name="user">The user to send the newsletter to</param>
        /// <param name="pdfStream">The PDF stream containing the newsletter</param>
        /// <param name="languageId">The language ID for the email template</param>
        public void SendNewsletterEmail(ApplicationUser user, Stream pdfStream, int? languageId)
        {
            if (user == null)
                throw new ArgumentNullException(nameof(user));
            
            if (pdfStream == null)
                throw new ArgumentNullException(nameof(pdfStream));
            
            // Position stream to beginning
            if (pdfStream.CanSeek)
            {
                pdfStream.Position = 0;
            }

            var template = EmailTemplates[user.SpracheID ?? 1];

            dynamic email = new Email(template);
            email.To = user.Email;
            email.Vorname = user.Vorname;
            email.Nachname = user.Nachname;

            MailMessage mailMessage = _emailService.CreateMailMessage(email);
            string mailMessageBody = mailMessage.Body
                .Replace("USERFIRSTNAME", user.Vorname)
                .Replace("USERLASTNAME", user.Nachname);
                
            MailMessage newMailMessage = new MailMessage(
                mailMessage.From.ToString(), 
                user.Email, 
                mailMessage.Subject, 
                mailMessageBody);
                
            newMailMessage.Attachments.Add(
                new Attachment(
                    pdfStream, 
                    "Newsletter_" + DateTime.Now.ToString("dd-MM-yyyy") + ".pdf", 
                    "application/pdf"));
                    
            SmtpClient client = new SmtpClient();
            client.Send(newMailMessage);
        }
    }
}
