using System;
using System.Collections.Generic;
using System.IO;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Site.Services.Newsletter
{
    /// <summary>
    /// Mock implementation of INewsletterEmailService for testing
    /// </summary>
    public class MockNewsletterEmailService : INewsletterEmailService
    {
        /// <summary>
        /// Record of an email that was "sent" by the mock service
        /// </summary>
        public class EmailRecord
        {
            public ApplicationUser User { get; set; }
            public long PdfSize { get; set; }
            public int? LanguageId { get; set; }
            public DateTime SentAt { get; set; }
        }

        /// <summary>
        /// List of all emails that were "sent" by this mock service
        /// </summary>
        public List<EmailRecord> SentEmails { get; } = new List<EmailRecord>();

        /// <summary>
        /// Records the email sending without actually sending it
        /// </summary>
        public void SendNewsletterEmail(ApplicationUser user, Stream pdfStream, int? languageId)
        {
            if (user == null)
                throw new ArgumentNullException(nameof(user));
            
            if (pdfStream == null)
                throw new ArgumentNullException(nameof(pdfStream));

            // Record that we would have sent an email
            SentEmails.Add(new EmailRecord
            {
                User = user,
                PdfSize = pdfStream.Length,
                LanguageId = languageId,
                SentAt = DateTime.Now
            });
        }
    }
}
