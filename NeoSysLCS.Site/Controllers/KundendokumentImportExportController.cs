using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Web.Mvc;
using System.Web.UI.WebControls;
using DevExpress.Utils;
using DevExpress.Web;
using DevExpress.Web.Mvc;
using DevExpress.XtraPrinting;
using DevExpress.XtraPrintingLinks;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Helpers;
using NeoSysLCS.Site.Utilities.Export;
using NeoSysLCS.Site.Utilities.Import;
using System.Web.UI;
using DevExpress.Data;
using System.Web.Mvc.Html;
using DevExpress.Web.Mvc.UI;
using System.Data.Entity.SqlServer;
using System.Drawing;
using System.Globalization;

namespace NeoSysLCS.Site.Controllers
{
    /// <summary>
    /// Controller for Import / Export of Kundendokumente
    /// </summary>
    public class KundendokumentImportExportController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;

        /// <summary>
        /// Initializes a new instance.
        /// </summary>
        public KundendokumentImportExportController()
        {
            _unitOfWork = new UnitOfWork();
        }

        /// <summary>
        /// Initializes a new instance.
        /// </summary>
        /// <param name="unitOfWorkCandidate">The unit of work candidate.</param>
        public KundendokumentImportExportController(IUnitOfWork unitOfWorkCandidate)
        {
            _unitOfWork = unitOfWorkCandidate;
        }

        /// <summary>
        /// Imports the specified kundendokument.
        /// </summary>
        /// <param name="id">The kundendokument id.</param>
        /// <returns></returns>
        public ActionResult Import(int id)
        {
            string[] strErrors;
            UploadControlExtension.GetUploadedFiles("fileUploadControl_" + id, KundendokumentImporter.ValidationSettings, out strErrors, UploadCompletet);
            return null;
        }

        /// <summary>
        /// Exports the kundendokument to XLS.
        /// </summary>
        /// <param name="standortId">The standort id.</param>
        /// <param name="kundendokumentId">The kundendokument id.</param>
        /// <returns></returns>
        public ActionResult ExportToXls(int standortId, int kundendokumentId)
        {
            var kundendokument = _unitOfWork.KundendokumentRepository.GetByID(kundendokumentId);
            var kundeID = kundendokument.KundeID;
            var kunde = _unitOfWork.KundeRepository.GetByID(kundeID);
            var standort = _unitOfWork.StandortRepository.GetByID(standortId);
            List<Int32> spracheIds = standort.Sprachen.Select(x => x.SpracheID).ToList();
            NeoSysLCS_Dev _context = new NeoSysLCS_Dev();
            var forderungenViewModels = _unitOfWork.KundendokumentForderungsversionRepository.GetAllFreigegebeneKundendokumentForderungsversionViewModelsToExport(kundendokumentId, false, standortId).ToList();
            var individuelleForderungenViewModels = _unitOfWork.IndividuelleForderungRepository.GetAllIndividuelleForderungenViewModels(kundendokumentId, kundeID, standortId).ToList();
            var erlasseViewModels = _unitOfWork.KundendokumentErlassfassungRepository.GetKundendokumentErlassfassungViewModelsByKundendokumentToExport(kundendokumentId).ToList();
            DateTimeFormatInfo fmt = (new CultureInfo("pt-BR")).DateTimeFormat;

            foreach (var viewModel in erlasseViewModels)
            {
                viewModel.RelevanterKommentar = viewModel.Betroffen ? (viewModel.LinkBetroffenKommentar != "---" ? viewModel.LinkBetroffenKommentar : viewModel.BetroffenKommentar)
                                                                         : (viewModel.LinkNichtBetroffenKommentar != "---" ? viewModel.LinkNichtBetroffenKommentar : viewModel.NichtBetroffenKommentar);
                viewModel.RelevanterKommentarFR = viewModel.Betroffen ? ((viewModel.LinkBetroffenKommentarFR != "---" ? viewModel.LinkBetroffenKommentarFR : viewModel.BetroffenKommentarFR) ??
                                                                                       (viewModel.LinkBetroffenKommentar != "---" ? viewModel.LinkBetroffenKommentarFR : viewModel.BetroffenKommentar))
                                                                         : ((viewModel.LinkNichtBetroffenKommentarFR != "---" ? viewModel.LinkNichtBetroffenKommentarFR : viewModel.NichtBetroffenKommentarFR) ??
                                                                                       (viewModel.LinkNichtBetroffenKommentar != "---" ? viewModel.LinkNichtBetroffenKommentarFR : viewModel.NichtBetroffenKommentar));
                viewModel.RelevanterKommentarIT = viewModel.Betroffen ? ((viewModel.LinkBetroffenKommentarIT != "---" ? viewModel.LinkBetroffenKommentarIT : viewModel.BetroffenKommentarIT) ??
                                                                                       (viewModel.LinkBetroffenKommentar != "---" ? viewModel.LinkBetroffenKommentar : viewModel.BetroffenKommentar)
                                                                                       ?? (viewModel.LinkBetroffenKommentarFR != "---" ? viewModel.LinkBetroffenKommentarFR : viewModel.BetroffenKommentarFR))
                                                                         : ((viewModel.LinkNichtBetroffenKommentarIT != "---" ? viewModel.LinkNichtBetroffenKommentarIT : viewModel.NichtBetroffenKommentarIT) ??
                                                                                       (viewModel.LinkNichtBetroffenKommentar != "---" ? viewModel.LinkNichtBetroffenKommentar : viewModel.NichtBetroffenKommentar)
                                                                                       ?? (viewModel.LinkNichtBetroffenKommentarFR != "---" ? viewModel.LinkNichtBetroffenKommentarFR : viewModel.NichtBetroffenKommentarFR));
                viewModel.RelevanterKommentarEN = viewModel.Betroffen ? ((viewModel.LinkBetroffenKommentarEN != "---" ? viewModel.LinkBetroffenKommentarEN : viewModel.BetroffenKommentarEN) ??
                                                                                       (viewModel.LinkBetroffenKommentar != "---" ? viewModel.LinkBetroffenKommentar : viewModel.BetroffenKommentar) ??
                                                                                       (viewModel.LinkBetroffenKommentarIT != "---" ? viewModel.LinkBetroffenKommentarFR : viewModel.BetroffenKommentarFR)
                                                                                       ?? (viewModel.LinkBetroffenKommentarIT != "---" ? viewModel.LinkBetroffenKommentarIT : viewModel.BetroffenKommentarIT))
                                                                         : ((viewModel.LinkNichtBetroffenKommentarEN != "---" ? viewModel.LinkNichtBetroffenKommentarEN : viewModel.NichtBetroffenKommentarEN) ?? 
                                                                                       (viewModel.LinkNichtBetroffenKommentar != "---" ? viewModel.LinkNichtBetroffenKommentar : viewModel.NichtBetroffenKommentar) ??
                                                                                       (viewModel.LinkNichtBetroffenKommentarFR != "---" ? viewModel.LinkNichtBetroffenKommentarFR : viewModel.NichtBetroffenKommentarFR)
                                                                                       ?? (viewModel.LinkNichtBetroffenKommentarIT != "---" ? viewModel.LinkNichtBetroffenKommentarIT : viewModel.NichtBetroffenKommentarIT));


                viewModel.BeschlussExport = viewModel.Beschluss.ToString("d", fmt);
                viewModel.InkraftretenExport = viewModel.Inkrafttretung.ToString("d", fmt);

                if (viewModel.RelevanterKommentar != null && viewModel.RelevanterKommentar != "")
                {
                    viewModel.RelevanterKommentar = viewModel.RelevanterKommentar.StartsWith("www") ? "http://" + viewModel.RelevanterKommentar : WebUtility.HtmlDecode(StringHelper.StripAllTags(viewModel.RelevanterKommentar));
                }
                if (viewModel.RelevanterKommentarFR != null && viewModel.RelevanterKommentarFR != "")
                {
                    viewModel.RelevanterKommentarFR = viewModel.RelevanterKommentarFR.StartsWith("www") ? "http://" + viewModel.RelevanterKommentarFR : WebUtility.HtmlDecode(StringHelper.StripAllTags(viewModel.RelevanterKommentarFR));
                }
                if (viewModel.RelevanterKommentarIT != null && viewModel.RelevanterKommentarIT != "")
                {
                    viewModel.RelevanterKommentarIT = viewModel.RelevanterKommentarIT.StartsWith("www") ? "http://" + viewModel.RelevanterKommentarIT : WebUtility.HtmlDecode(StringHelper.StripAllTags(viewModel.RelevanterKommentarIT));
                }
                if (viewModel.RelevanterKommentarEN != null && viewModel.RelevanterKommentarEN != "")
                {
                    viewModel.RelevanterKommentarEN = viewModel.RelevanterKommentarEN.StartsWith("www") ? "http://" + viewModel.RelevanterKommentarEN : WebUtility.HtmlDecode(StringHelper.StripAllTags(viewModel.RelevanterKommentarEN));
                }
                viewModel.BeschlussQuelle = viewModel.BeschlussQuelle.StartsWith("www") ? "http://" + viewModel.BeschlussQuelle : viewModel.BeschlussQuelle;
                viewModel.BeschlussQuelleFR = viewModel.BeschlussQuelleFR.StartsWith("www") ? "http://" + viewModel.BeschlussQuelleFR : viewModel.BeschlussQuelleFR;
                viewModel.BeschlussQuelleIT = viewModel.BeschlussQuelleIT.StartsWith("www") ? "http://" + viewModel.BeschlussQuelleIT : viewModel.BeschlussQuelleIT;
                viewModel.BeschlussQuelleEN = viewModel.BeschlussQuelleEN.StartsWith("www") ? "http://" + viewModel.BeschlussQuelleEN : viewModel.BeschlussQuelleEN;

                viewModel.InkraftretenQuelle = viewModel.InkraftretenQuelle.StartsWith("www") ? "http://" + viewModel.InkraftretenQuelle : viewModel.InkraftretenQuelle;
                viewModel.InkraftretenQuelleFR = viewModel.InkraftretenQuelleFR.StartsWith("www") ? "http://" + viewModel.InkraftretenQuelleFR : viewModel.InkraftretenQuelleFR;
                viewModel.InkraftretenQuelleIT = viewModel.InkraftretenQuelleIT.StartsWith("www") ? "http://" + viewModel.InkraftretenQuelleIT : viewModel.InkraftretenQuelleIT;
                viewModel.InkraftretenQuelleEN = viewModel.InkraftretenQuelleEN.StartsWith("www") ? "http://" + viewModel.InkraftretenQuelleEN : viewModel.InkraftretenQuelleEN;

                viewModel.ErlassQuelle = viewModel.ErlassQuelle.StartsWith("www") ? "http://" + viewModel.ErlassQuelle : viewModel.ErlassQuelle;
                viewModel.ErlassQuelleFR = viewModel.ErlassQuelleFR.StartsWith("www") ? "http://" + viewModel.ErlassQuelleFR : viewModel.ErlassQuelleFR;
                viewModel.ErlassQuelleIT = viewModel.ErlassQuelleIT.StartsWith("www") ? "http://" + viewModel.ErlassQuelleIT : viewModel.ErlassQuelleIT;
                viewModel.ErlassQuelleEN = viewModel.ErlassQuelleEN.StartsWith("www") ? "http://" + viewModel.ErlassQuelleEN : viewModel.ErlassQuelleEN;
            }

            erlasseViewModels = ErlassfassungExportHelper.GetAdditionalViewModels(erlasseViewModels, kunde, kundendokument, spracheIds, standort.Name);

            foreach (var viewmodel in forderungenViewModels)
            {
                viewmodel.Beschreibung = WebUtility.HtmlDecode(StringHelper.StripAllTags(viewmodel.Beschreibung));
            }

            //todo: remove comments to add pflichten
            //foreach (var viewmodel in pflichtenViewModels)
            //{
            //    viewmodel.Beschreibung = StringHelper.StripAllTags(viewmodel.Beschreibung);
            //}

            var ps = new PrintingSystem();

            ps.XlSheetCreated += (s, e) =>
            {
                switch (e.Index)
                {
                    case 0:
                        e.SheetName = Resources.Properties.Resources.View_Kundendokument_TabErlassfassungen;
                        break;
                    case 1:
                        e.SheetName = Resources.Properties.Resources.Entitaet_Forderung_Plural;
                        break;
                    case 2:
                        e.SheetName = Resources.Properties.Resources.Entitaet_IndividuelleForderung_Plural;
                        break;
                        /*case 3:
                            e.SheetName = Resources.Properties.Resources.Entitaet_Pflicht_Plural;
                            break;*/

                }


                //todo: remove comments to add pflichten
                //e.SheetNames[3] = Resources.Properties.Resources.Entitaet_Pflicht_Plural;

            };

            var erlassfassungenlink = new PrintableComponentLink(ps);
            var erlassfassungenSettings = ErlassfassungExportHelper.GetGridViewSettingsErlassfassungen(ErlassfassungExportHelper.GetErlassfassungExportGridColumns(kundeID.Value, spracheIds));
            erlassfassungenlink.Component = GridViewExtension.CreatePrintableObject(erlassfassungenSettings, erlasseViewModels/*.GroupBy(x => x.HerausgeberID)*/);

            var forderungenLink = new PrintableComponentLink(ps);            
            var forderungenSettings = ForderungExportHelper.GetForderungenGridViewSettings(ForderungExportHelper.GetKundendokumentForderungenExportGridColumns(kundeID.Value, standortId, kundendokumentId));

            //Set Header for Forderungen
            forderungenSettings.SettingsExport.PageHeader.VerticalAlignment = BrickAlignment.Near;
            forderungenSettings.SettingsExport.PageHeader.Left = Resources.Properties.Resources.Entitaet_Forderung_Plural
                + " " + kunde.Name + " " + CultureHelper.GetStandortMenuName((int)kundeID)
                + " " + standort.Name;
            forderungenSettings.SettingsExport.PageHeader.Font.Size = 14;
            forderungenSettings.SettingsExport.PageHeader.Font.Name = "Arial";
            forderungenSettings.SettingsExport.PageHeader.Font.Bold = true;

            forderungenLink.Component = GridViewExtension.CreatePrintableObject(forderungenSettings, forderungenViewModels);

            var individuelleForderungenlink = new PrintableComponentLink(ps);
            var individuelleForderungenSettings = ForderungExportHelper.GetForderungenGridViewSettings(GetIndividuelleForderungenExportGridColumns(kundeID.Value, standortId));
            individuelleForderungenlink.Component = GridViewExtension.CreatePrintableObject(individuelleForderungenSettings, individuelleForderungenViewModels);

            //todo: remove comments to add pflichten
            //var pflichtenLink = new PrintableComponentLink(ps);
            //var plichtSettings = GetGridViewSettings(GetKundendokumentPflichtenExportGridColumns(kundeID.Value, standortId));
            //pflichtenLink.Component = GridViewExtension.CreatePrintableObject(plichtSettings, pflichtenViewModels);

            var compositeLink = new CompositeLink(ps);
            compositeLink.Links.AddRange(new[] { erlassfassungenlink, forderungenLink, individuelleForderungenlink /*, pflichtenLink*/});
            compositeLink.CreateDocument();
            compositeLink.CreatePageForEachLink();
            var stream = new MemoryStream();
            compositeLink.PrintingSystem.ExportToXlsx(stream, new XlsxExportOptions()
            {
                ExportMode = XlsxExportMode.SingleFilePageByPage,
                ShowGridLines = true,
            });

            stream.Position = 0;
            var result = new FileStreamResult(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            result.FileDownloadName = Resources.Properties.Resources.Entitaet_Kundendokument_Singular + "_" + kunde.Name + "_" + DateTime.Now.ToString("yy-MM-dd") + ".xlsx";

            ps.Dispose();
            return result;
        }

        public ActionResult QuenticExportToXls(int standortId, int kundendokumentId)
        {
            var kundendokument = _unitOfWork.KundendokumentRepository.GetByID(kundendokumentId);
            var kundeID = kundendokument.KundeID;
            var kunde = _unitOfWork.KundeRepository.GetByID(kundeID);

            var quenticViewModels = _unitOfWork.KundendokumentErlassfassungRepository.GetKundendokumentErlassfassungViewModelsByKundendokumentToQuenticExport(kundendokumentId).ToList();
            var quenticTwoViewModels = _unitOfWork.KundendokumentForderungsversionRepository.GetKundendokumentForderungsversionViewModelsByKundendokumentToQuenticExport(kundendokumentId).ToList();

            var language = CultureInfo.CurrentUICulture.TwoLetterISOLanguageName;            
            Sprache sprache = _unitOfWork.SpracheRepository.Get(s => s.Lokalisierung == language, null, null).FirstOrDefault();
            int _currentLang = sprache.SpracheID;

            var ps = new PrintingSystem();

            ps.XlSheetCreated += (s, e) =>
            {
                switch (e.Index)
                {
                    case 0:
                        e.SheetName = Resources.Properties.Resources.Entitaet_Kundendokument_TabRechtsnormen;
                        break;
                    case 1:
                        e.SheetName = Resources.Properties.Resources.Entitaet_Kundendokument_TabRechtspflichten;
                        break;
                }
            };

            foreach (var model in quenticViewModels)
            {
                if (!string.IsNullOrEmpty(model.ErlassSrNummer))
                {
                    if (model.ErlassSrNummer.Length > 100)
                    {
                        model.ErlassSrNummer = _unitOfWork.ErlassRepository.GetByID(model.ErlassID).SrNummer + ", " + model.ErlassAbkuerzung;
                    }
                }
                if (!string.IsNullOrEmpty(model.ErlassAbkuerzung))
                {
                    if (model.ErlassAbkuerzung.Length > 100)
                    {
                        model.ErlassAbkuerzung = model.ErlassAbkuerzung.Substring(0, 100);
                    }
                }
                if (!string.IsNullOrEmpty(model.ErlassTitel))
                {
                    if (model.ErlassTitel.Length > 2000)
                    {
                        model.ErlassTitel = model.ErlassTitel.Substring(0, 2000);
                    }
                }
                if (!string.IsNullOrEmpty(model.HerausgeberName))
                {
                    if (model.HerausgeberName.Length > 45)
                    {
                        model.HerausgeberName = model.HerausgeberName.Substring(0, 45);
                    }
                }
                if (!string.IsNullOrEmpty(model.Erlasstyp))
                {
                    if (model.Erlasstyp.Length > 45)
                    {
                        model.Erlasstyp = model.Erlasstyp.Substring(0, 45);
                    }
                }
                if (!string.IsNullOrEmpty(model.Sachgebiete))
                {
                    if (model.Sachgebiete.Length > 100)
                    {
                        model.Sachgebiete = model.Sachgebiete.Substring(0, 100);
                    }
                }
                if (!string.IsNullOrEmpty(model.Kerninhalte))
                {
                    if (model.Kerninhalte.Length > 1000)
                    {
                        model.Kerninhalte = model.Kerninhalte.Substring(0, 1000);
                    }
                }

            }

            foreach (var model in quenticTwoViewModels)
            {
                if (!string.IsNullOrEmpty(model.SrNummer))
                {
                    if (model.SrNummer.Length > 100)
                    {
                        model.SrNummer = _unitOfWork.ErlassRepository.GetByID(model.ErlassID).SrNummer + ", " + model.Abkuerzung;
                    }
                }
                if (!string.IsNullOrEmpty(model.ArtikelNummer))
                {
                    if (model.ArtikelNummer.Length > 255)
                    {
                        model.ArtikelNummer = model.ArtikelNummer.Substring(0, 255) + ", " + model.StandortObjektTitel;
                    }
                }
                if (!string.IsNullOrEmpty(model.Beschreibung))
                {
                    model.Beschreibung = WebUtility.HtmlDecode(StringHelper.StripAllTags(model.Beschreibung));
                    if (model.Beschreibung.Length > 1000)
                    {
                        model.Beschreibung = model.Beschreibung.Substring(0, 1000);
                    }
                }
            }

            var quenticErlassfassungenlink = new PrintableComponentLink(ps);
            var quenticErlassfassungenSettings = ForderungExportHelper.GetForderungenGridViewSettings(QuenticExportHelper.GetQuenticExportGridColumns(kundeID.Value, standortId));
            quenticErlassfassungenlink.Component = GridViewExtension.CreatePrintableObject(quenticErlassfassungenSettings, quenticViewModels);

            var quenticTwoErlassfassungenlink = new PrintableComponentLink(ps);
            var quenticTwoErlassfassungenSettings = ForderungExportHelper.GetForderungenGridViewSettings(QuenticExportHelper.GetQuenticTwoExportGridColumns(kundeID.Value, standortId));
            quenticTwoErlassfassungenlink.Component = GridViewExtension.CreatePrintableObject(quenticTwoErlassfassungenSettings, quenticTwoViewModels);

            var compositeLink = new CompositeLink(ps);
            compositeLink.Links.AddRange(new[] { quenticErlassfassungenlink, quenticTwoErlassfassungenlink });
            compositeLink.CreateDocument();
            compositeLink.CreatePageForEachLink();

            var stream = new MemoryStream();
            compositeLink.PrintingSystem.ExportToXlsx(stream, new XlsxExportOptions()
            {

                ExportMode = XlsxExportMode.SingleFilePageByPage,
            });

            stream.Position = 0;
            var result = new FileStreamResult(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            result.FileDownloadName = Resources.Properties.Resources.Entitaet_Kundendokument_Quentic + "_" + kunde.Name + "_" + DateTime.Now.ToString("yy-MM-dd") + ".xlsx";

            ps.Dispose();
            return result;
        }

        private void UploadCompletet(object sender, FileUploadCompleteEventArgs e)
        {
            try
            {
                var id = int.Parse(Request.RequestContext.RouteData.Values["id"].ToString());
                var s = new StreamReader(e.UploadedFile.FileContent);
                var importer = new KundendokumentImporter();
                importer.ImportKundendokument(s.BaseStream, id);
                e.CallbackData = "Das Kundendokument wurde erfolgreich importiert. ";
            }
            catch (Exception ex)
            {
                e.IsValid = false;
                e.CallbackData = "Es ist ein Fehler beim Importieren von " + e.UploadedFile.FileName
                + " aufgetreten: " + ex.Message;

            }
        }      

        private MVCxGridViewColumnCollection GetIndividuelleForderungenExportGridColumns(int kundeID, int standortId)
        {
            //note: if you add or remove columns, please control also the import 
            var columns = new MVCxGridViewColumnCollection();

            //Columns
            columns.Add(column =>
            {
                column.FieldName = "IndividuelleForderungID";
                column.Caption = "ID";
            });

            columns.Add(column =>
            {
                column.FieldName = "StandortObjekt";
                column.Caption = TranslationHelper.GetTranslation(typeof(IndividuelleForderungViewModel), column.FieldName);
            });

            columns.Add(column =>
            {
                column.FieldName = "Kundenbezug";
                column.Caption = Resources.Properties.Resources.View_Kundenportal_Kundenbezug;
            });

            columns.Add(column =>
            {
                column.FieldName = "Beschreibung";
                column.ExportWidth = 500;
                column.Caption = TranslationHelper.GetTranslation(typeof(IndividuelleForderungViewModel), column.FieldName);
            });

            columns.Add(column =>
            {
                column.FieldName = "GueltigVon";
                column.Caption = TranslationHelper.GetTranslation(typeof(IndividuelleForderungViewModel), column.FieldName);
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
            });

            columns.Add(column =>
            {
                column.FieldName = "GueltigBis";
                column.Caption = TranslationHelper.GetTranslation(typeof(IndividuelleForderungViewModel), column.FieldName);
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
            });

            columns.Add(column =>
            {
                column.FieldName = "Erfuellung";
                column.Caption = TranslationHelper.GetTranslation(typeof(IndividuelleForderungViewModel), column.FieldName);
            });

            columns.Add(column =>
            {
                column.FieldName = "Erfuellungszeitpunkt";
                column.Caption = TranslationHelper.GetTranslation(typeof(IndividuelleForderungViewModel), column.FieldName);
                column.ColumnType = MVCxGridViewColumnType.DateEdit;
            });
            columns.Add(column =>
            {
                column.FieldName = "ErfuelltDurch";
                column.Caption = TranslationHelper.GetTranslation(typeof(IndividuelleForderungViewModel), column.FieldName);
            });
            columns.Add(column =>
            {
                column.FieldName = "Verantwortlich";
                column.Caption = TranslationHelper.GetTranslation(typeof(IndividuelleForderungViewModel), column.FieldName);
            });
            columns.Add(column =>
            {
                column.FieldName = "Ablageort";
                column.Caption = TranslationHelper.GetTranslation(typeof(IndividuelleForderungViewModel), column.FieldName);
            });

            columns.Add(column =>
            {
                column.FieldName = "Kommentar";
                column.Caption = TranslationHelper.GetTranslation(typeof(IndividuelleForderungViewModel), column.FieldName);
            });

            return columns;
        }
       
    }


}
