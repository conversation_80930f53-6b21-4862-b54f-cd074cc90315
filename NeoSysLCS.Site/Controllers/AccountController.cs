using System;
using System.Linq;
using NeoSysLCS.DomainModel.Models;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.Owin;
using Microsoft.Owin.Security;
using System.Threading.Tasks;
using System.Web;
using System.Web.Mvc;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Helpers;
using System.Collections.Generic;
using System.Globalization;
using System.Web.Security;
using System.Text.RegularExpressions;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Site.Areas.Admin.Controllers;

namespace NeoSysLCS.Site.Controllers
{
    /// <summary>
    /// Controller for all account actions
    /// </summary>
    [Authorize]
    public class AccountController : BaseController
    {
        private readonly UnitOfWork _unitOfWork;
        private readonly SessionHelper _sessionHelper;
        private ApplicationUserManager _userManager;
        private ApplicationSignInManager _signInManager;
        private UsersAdminController _uAController;

        public AccountController()
        {
            _sessionHelper = new SessionHelper();
            _unitOfWork = new UnitOfWork();
        }

        public AccountController(ApplicationUserManager userManager, ApplicationSignInManager signInManager)
        {
            UserManager = userManager;
            SignInManager = signInManager;
            _sessionHelper = new SessionHelper();
            _unitOfWork = new UnitOfWork();
            _uAController = new UsersAdminController();
        }


        private ApplicationUserManager UserManager
        {
            get
            {
                return _userManager ?? HttpContext.GetOwinContext().GetUserManager<ApplicationUserManager>();
            }
            set
            {
                _userManager = value;
            }
        }

        private ApplicationSignInManager SignInManager
        {
            get
            {
                return _signInManager ?? HttpContext.GetOwinContext().Get<ApplicationSignInManager>();
            }
            set { _signInManager = value; }
        }

        private IAuthenticationManager AuthenticationManager
        {
            get
            {
                return HttpContext.GetOwinContext().Authentication;
            }
        }

        /// <summary>
        /// Shows the login form
        /// </summary>
        /// <param name="returnUrl">The return URL.</param>
        /// <returns></returns>
        [AllowAnonymous]
        public ActionResult Login(string returnUrl)
        {
            //if (returnUrl.Contains("PasswordReset"))
            //{
            //    ViewBag.ReturnUrl = returnUrl;
            //    return View("_PasswordResetLockout");
            //}
            if (HttpContext.User.Identity.IsAuthenticated && _sessionHelper.IsPrivacyPolicyAccepted())
            {
                return RedirectToAction("Index", "Home");
            }

            ViewBag.ReturnUrl = returnUrl;
            return View();
        }

        /// <summary>
        /// Executes the Login
        /// </summary>
        /// <param name="model">The model.</param>
        /// <param name="returnUrl">The return URL.</param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        //[ValidateAntiForgeryToken]
        public ActionResult Login(LoginViewModel model, string returnUrl)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            var userViewModel = new UserViewModel();
            // To enable password failures to trigger lockout, change to shouldLockout: true
            // Find User by Email
            var user = UserManager.FindByEmail(model.Email);
            //get view model because ApplicationUser isn't serializable
            if (user != null)
            {
                userViewModel = _unitOfWork.UserRepository.GetUserViewModelById(user.Id);
            }
            else
            {
                ModelState.AddModelError("", Resources.Properties.Resources.Login_Error_Email);
                return View(model);
            }


            if (HttpContext.User.Identity.IsAuthenticated)
            {
                if (userViewModel != null && ShowPolicyConfirmation(userViewModel))
                {
                    _sessionHelper.SetPrivacyPolicyAccepted(false);
                    return RedirectToAction("Index", "PrivacyPolicyConfirmation");
                }

                _sessionHelper.SetPrivacyPolicyAccepted(true);
                return RedirectToAction("Index", "Home");
            }



            if (userViewModel != null)
            {
                UserManager.AccessFailed(userViewModel.Id);
                UserManager.SetLockoutEnabled(userViewModel.Id, true);

                var result = SignInManager.PasswordSignIn(model.Email, model.Password, model.RememberMe, false);


                switch (result)
                {
                    case SignInStatus.Success:
                        var lockoutEndDate = new DateTimeOffset();

                        _sessionHelper.ClearAllUserCookiesIfNeeded(user, Request, Response);

                        UserManager.ResetAccessFailedCount(userViewModel.Id);
                        UserManager.SetLockoutEndDate(userViewModel.Id, lockoutEndDate);

                        // set session user
                        _sessionHelper.SetCurrentUser(userViewModel);
                        // set fullname for login partial
                        ViewData["Fullname"] = userViewModel.Vorname + " " + userViewModel.Nachname;

                        // set langcookie on login
                        var userSpracheId = userViewModel.SpracheID;
                        var sprache = new Sprache().Lokalisierung;

                        if (userSpracheId != 0)
                        {
                            sprache = _unitOfWork.SpracheRepository.GetByID(userSpracheId).Lokalisierung;
                        }

                        var culture = CultureHelper.GetImplementedCulture(sprache);
                        //Fixed NEOS-303 Language is not set correctly if standort.sprachen doesn't contain user.sprache
                        if (user.KundeID == 1)
                        {
                            // Save culture in a cookie
                            HttpCookie cookie = Request.Cookies["_culture"];
                            if (cookie != null)
                                cookie.Value = culture; // update cookie value
                            else
                            {
                                cookie = new HttpCookie("_culture")
                                {
                                    Value = culture,
                                    Expires = DateTime.Now.AddYears(1)
                                };
                            }
                            Response.Cookies.Add(cookie);
                        }
                        else
                        {
                            var sprachen = new List<Sprache>();
                            var standorte = _unitOfWork.StandortRepository.GetAllStandortViewModels((int) user.KundeID);
                            foreach (StandortViewModel standortViewModel in standorte)
                            {
                                sprachen.AddRange(standortViewModel.Sprachen);
                            }
                            HttpCookie cookie = Request.Cookies["_culture"];
                            //check if any standort.sprache contains the user.sprache
                            var languageIsAvaiable = sprachen.Distinct().Select(x => x.Lokalisierung).Contains(sprache);
                            if (cookie != null && languageIsAvaiable)
                            {
                                cookie.Value = culture; // update cookie value
                            }
                            else
                            {
                                if (languageIsAvaiable)
                                {
                                    cookie = new HttpCookie("_culture")
                                    {
                                        Value = culture,
                                        Expires = DateTime.Now.AddYears(1)
                                    };
                                }
                                //set cookie to standort.sprache if user.sprache is not avaiable
                                else
                                {
                                    cookie = new HttpCookie("_culture")
                                    {
                                        Value = sprachen.Distinct().Select(x => x.Lokalisierung).FirstOrDefault(),
                                        Expires = DateTime.Now.AddYears(1)
                                    };
                                }
                            }
                            Response.Cookies.Add(cookie);

                        }

                        // Add last Login cookie
                        HttpCookie lastLogincookie = Request.Cookies["_lastLogin"];
                        if (lastLogincookie != null)
                            lastLogincookie.Value = DateTime.Now.ToString(); // update cookie value
                        else
                        {
                            lastLogincookie = new HttpCookie("_lastLogin")
                            {
                                Value = DateTime.Now.ToString(),
                                Expires = DateTime.Now.AddYears(1)
                            };
                        }

                        Response.Cookies.Add(lastLogincookie);
                        if (!string.IsNullOrEmpty(returnUrl))
                        {
                            var decodedUrl = Server.UrlDecode(returnUrl);
                            if (Url.IsLocalUrl(decodedUrl))
                            {
                                if (ShowPolicyConfirmation(userViewModel))
                                {
                                    _sessionHelper.SetPrivacyPolicyAccepted(false);
                                    return RedirectToAction("Index", "PrivacyPolicyConfirmation");
                                }
                                _sessionHelper.SetPrivacyPolicyAccepted(true);
                                return Redirect(decodedUrl);
                            }
                        }

                        if (ShowPolicyConfirmation(userViewModel))
                        {
                            _sessionHelper.SetPrivacyPolicyAccepted(false);
                            return RedirectToAction("Index", "PrivacyPolicyConfirmation");
                        }
                      
                        _sessionHelper.SetPrivacyPolicyAccepted(true);
                        return RedirectToAction("Index", "Home");
                    case SignInStatus.LockedOut:
                        return View("Lockout");
                    default:
                        ModelState.AddModelError("", Resources.Properties.Resources.Login_Error_Passwort);
                        RedirectToAction("Index", "Home");
                        return View(model);
                }
            }

            ModelState.AddModelError("", Resources.Properties.Resources.Login_Error_allgemein);
            return View(model);

        }

        private bool ShowPolicyConfirmation(UserViewModel user)
        {
            if (UserHasRole(user, Role.Admin))
            {
                return false;
            }

            // get all currently valid privacy policies
            var privacyPolicies = _unitOfWork.PrivacyPolicyRepository.GetAllViewModels(user.SpracheID);

            // if all currently valid privacy policies have version 0 and user has set termsaccepted, then returen false
            var atLeastOnePolicyFileChanged = privacyPolicies.Any(item => item.Version != "0");
            if (!atLeastOnePolicyFileChanged && user.TermsAccepted != null)
            {
                return false;
            }

            var userConfirmedAllPrivacyPolicies = true;
            // check if user confirmed all currently valid privacy policies
            foreach (var privacyPolicy in privacyPolicies)
            {
                userConfirmedAllPrivacyPolicies = userConfirmedAllPrivacyPolicies &&
                                                  _unitOfWork.ApplicationUserPrivacyPolicyRepository.Exists(user.Id,
                                                      privacyPolicy.PrivacyPolicyID,
                                                      Int32.Parse(privacyPolicy.Version));
            }

            return !userConfirmedAllPrivacyPolicies;
        }

        private bool UserHasRole(UserViewModel user, string role)
        {
            return user.GUIRoles.Contains(role);
        }

        private bool PrivacyPolicyAny()
        {
            return _unitOfWork.PrivacyPolicyRepository.GetAllViewModels().Any();
        }


        [AllowAnonymous]
        public ActionResult PasswordReset()
        {
            return View("_PasswordResetLockout");
        }

        [HttpPost]
        [ValidateInput(false)]
        public ActionResult PasswordResetConfirmation(ChangePasswordViewModel viewModel)
        {
            var userId = _unitOfWork.UserRepository.GetUserIdByEmail("string");
            string email = "test";
            if(!userId.Any())
            {
                var token = UserManager.GeneratePasswordResetToken(userId);

                //Create new password for the user
                var newPassword = "";

                var regExpCapital = new Regex("[A-Z]+");
                var regExpNumbers = new Regex("[0-9]+");
                var regExpLetters = new Regex("[a-z]+");

                var index = false;

                while (index == false)
                {
                    var rndTicks = new Random((int)DateTime.Now.Ticks);
                    var rndNum = rndTicks.Next(100, 999);
                    var rndPwd = Membership.GeneratePassword(8, 0);
                    var password = rndPwd + rndNum;
                    password = Regex.Replace(password, @"\W", "");

                    if (regExpCapital.IsMatch(password) && regExpLetters.IsMatch(password) &&
                        regExpNumbers.IsMatch(password))
                    {
                        newPassword = password;
                        index = true;
                    }

                }


                var result = UserManager.ResetPassword(userId, token, newPassword);

                if (result.Succeeded)
                {
                    System.Web.HttpContext.Current.Session["Authflag"] = false;
                    System.Web.HttpContext.Current.Session["userid"] = "";
                    var item = new UserViewModel();

                    item.Email = email;
                    item.Password = newPassword;

                    _uAController.SendPasswordReset(item);
                    return PartialView("ResetPasswordConfirmation");
                }
            }
            return View("_PasswordResetLockout");

        }

        /// <summary>
        /// Logoff of the current  user
        /// </summary>
        /// <returns></returns>
        public ActionResult LogOff()
        {
            AuthenticationManager.SignOut();
            return RedirectToAction("Login", "Account", new { area = "" });
        }

    }
}