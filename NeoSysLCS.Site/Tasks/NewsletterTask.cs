using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Reflection;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.WebPages;
using System.Xml.Linq;
using MigraDoc.DocumentObjectModel;
using MigraDoc.DocumentObjectModel.Shapes;
using MigraDoc.DocumentObjectModel.Tables;
using MigraDoc.Rendering;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.Helper;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.AzureStorage;
using NeoSysLCS.Site.Helpers;
using PdfSharp.Pdf;
using Postal;
using System.Web.Mvc;
using System.Configuration;
using NeoSysLCS.Site.Controllers;
using DevExpress.Web;
using Border = MigraDoc.DocumentObjectModel.Border;
using log4net;
using NeoSysLCS.Site.Services.Newsletter;

namespace NeoSysLCS.Site.Tasks
{
    public class NewsletterTask : BaseController
    {

        private static readonly ILog log = LogManager.GetLogger(typeof(NewsletterTask));

        private readonly IUnitOfWork _unitOfWork;
        private readonly INewsletterEmailService _newsletterEmailService;
        private System.Resources.ResourceManager resourceManager;
        private System.Globalization.CultureInfo languageInfo;

        /// <summary>
        /// Default constructor for Hangfire compatibility
        /// </summary>
        public NewsletterTask()
            : this(new UnitOfWork(), new NewsletterEmailService())
        {
        }

        /// <summary>
        /// Constructor with dependency injection for testing
        /// </summary>
        /// <param name="unitOfWork">Unit of work for data access</param>
        /// <param name="newsletterEmailService">Email service for sending newsletters</param>
        public NewsletterTask(IUnitOfWork unitOfWork, INewsletterEmailService newsletterEmailService)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _newsletterEmailService = newsletterEmailService ?? throw new ArgumentNullException(nameof(newsletterEmailService));
            resourceManager = new global::System.Resources.ResourceManager("NeoSysLCS.Resources.Properties.Resources", typeof(NeoSysLCS.Resources.Properties.Resources).Assembly);
        }

        public void SendNewsletters()
        {
            Debug.WriteLine("Newsletter job triggered!");
            log.Info("Newsletter job triggered!");

            var context = _unitOfWork.Context;

            var newsletterRole = context.ApplicationRoles.FirstOrDefault(r => r.Name == Role.Newsletter);

            var activeUsers = context.ApplicationUsers.Include("Kunde").Where(u => !u.LockoutEndDateUtc.HasValue);

            var sent = 0;
            var skipped = 0;
            var failed = 0;
            var processed = 0;
            foreach (var user in activeUsers)
            {
                try
                {
                    var lastSentNewsletterToUser = FindLastSentNewsletter(user);
                    if (ShouldSendNewsletter(user, newsletterRole, lastSentNewsletterToUser))
                    {
                        var pdfStream = GeneratePdf(user);
                        if (pdfStream != null)
                        {
                            var fileUrl = UploadPdf(user, pdfStream);
                            SaveNewsletterHistory(user, fileUrl);

                            SendEmail(user, pdfStream, user.SpracheID);

                            pdfStream.Close();

                            sent++;
                            Debug.WriteLine("SENT Newsletter to: " + user.FullName + ", TimeSpan " +
                                          user.NewsletterPeriod);
                            log.Info("SENT Newsletter to: " + user.FullName + ", TimeSpan " +
                                             user.NewsletterPeriod);
                        } else
                        {
                            log.Warn("Null PdfStream newsletter for: " + user.FullName + ", TimeSpan " +
                                            user.NewsletterPeriod);
                        }
                    }
                    else
                    {
                        skipped++;
                        Debug.WriteLine("SKIP SENDING Newsletter to: " + user.FullName + ", TimeSpan " + user.NewsletterPeriod);
                        log.Info("SKIP SENDING Newsletter to: " + user.FullName + ", TimeSpan " + user.NewsletterPeriod);
                    }
                } catch (Exception e)
                {
                    failed++;
                    Debug.WriteLine("FAILED Sending Newsletter to: " + user.FullName + ", TimeSpan " +
                                            user.NewsletterPeriod);
                    log.Error("FAILED Sending Newsletter to: " + user.FullName + ", TimeSpan " +
                                            user.NewsletterPeriod, e);
                }

                processed++;
            }

            log.Info("FINISHED with newsletter sending!");
            log.Info("Sent: " + sent);
            log.Info("Skipped: " + skipped);
            log.Info("Failed: " + failed);
            log.Info("Total processed: " + processed);
        }

        private bool ShouldSendNewsletter(ApplicationUser user, ApplicationRole role, ApplicationUserNewsletterHistory lastNewsletter)
        {
            if (!UserHasRole(user, role))
            {
                return false;
            }
            
            var sendingPeriod = user.NewsletterPeriod.GetValueOrDefault(6);

            return lastNewsletter == null || lastNewsletter.DateSent.AddMonths(sendingPeriod).Date <= DateTime.Now.Date;
        }

        private bool UserHasRole(ApplicationUser user, ApplicationRole role)
        {
            var foundRole = user.Roles.FirstOrDefault(ur => ur.RoleId == role.Id);
            return foundRole != null;
        }

        private void SaveNewsletterHistory(ApplicationUser user, string fileUrl)
        {
            var history = new ApplicationUserNewsletterHistory()
            {
                ID = Guid.NewGuid().ToString(),
                DateSent = DateTime.Now,
                UserID = user.Id,
                User = user,
                NewsletterFileLink = fileUrl
            };
            _unitOfWork.Context.ApplicationUserNewsletterHistories.Add(history);
            _unitOfWork.Context.SaveChanges();
        }

        private ApplicationUserNewsletterHistory FindLastSentNewsletter(ApplicationUser user)
        {
            return _unitOfWork.Context.ApplicationUserNewsletterHistories
                .OrderByDescending(h => h.DateSent)
                .FirstOrDefault(h => h.UserID == user.Id);
        }

        /**
         * We use http://www.pdfsharp.net/ for generating PDF.
         */
        Stream GeneratePdf(ApplicationUser user, ApplicationUserNewsletterHistory lastNewsletter = null)
        {

            if (user.Kunde == null)
            {
                return null;
            }

            int spracheID = user.SpracheID.Value;
            // last sent newsletter time, if there is no previous newsletter, the sending period is used
            var sendingPeriod = user.NewsletterPeriod.GetValueOrDefault(6);
            var lastNewsletterSentTime = lastNewsletter?.DateSent ?? DateTime.Today.AddMonths(-sendingPeriod);

            /*
             * Retrieves comments specifically marked for newsletter inclusion (Newsletter = true)
             * 
             * Filters for comments where kom.Newsletter.Value.Equals(true)
             * Uses NewsletterDate for time filtering: kom.NewsletterDate != null && kom.NewsletterDate > lastNewsletterSentTime.Value
             * Returns data ordered by NewsletterDate: OrderByDescending(x => x.NewsletterDate)
             */
            var kommentare =
                 _unitOfWork.DashboardRepository.GetAllKommentarNewsletterViewModelsByKunde(user.Kunde.KundeID, spracheID, lastNewsletterSentTime);

            /*
             * Retrieves law changes that are not specifically marked for newsletter (Newsletter = false) but are still relevant to include
             * 
             * Filters for comments where kom.Newsletter.Value.Equals(false)
             * Uses Eintrag date for time filtering: kom.Eintrag != null && kom.Eintrag > lastNewsletterSentTime.Value
             * Returns data ordered by PLFreigabeDate: OrderByDescending(x => x.PLFreigabeDate)
             */
            var gesetzeaenderungen =
                _unitOfWork.DashboardRepository.GetAllGesetzaenderungViewModelsByKunde(user.Kunde.KundeID, spracheID, lastNewsletterSentTime);


            var consultations = _unitOfWork.ConsultationRepository.GetConsultationViewModelsNewsletter(user.Kunde.KundeID, spracheID, lastNewsletterSentTime);


            // Create a MigraDoc document
            Document document = CreateDocument(kommentare, gesetzeaenderungen, consultations, user, spracheID);
            document.UseCmykColor = true;

            // ===== Unicode encoding and font program embedding in MigraDoc is demonstrated here =====

            // A flag indicating whether to create a Unicode PDF or a WinAnsi PDF file.
            // This setting applies to all fonts used in the PDF document.
            // This setting has no effect on the RTF renderer.
            const bool unicode = false;

            // An enum indicating whether to embed fonts or not.
            // This setting applies to all font programs used in the document.
            // This setting has no effect on the RTF renderer.
            // (The term 'font program' is used by Adobe for a file containing a font. Technically a 'font file'
            // is a collection of small programs and each program renders the glyph of a character when executed.
            // Using a font in PDFsharp may lead to the embedding of one or more font programms, because each outline
            // (regular, bold, italic, bold+italic, ...) has its own fontprogram)
            const PdfFontEmbedding embedding = PdfFontEmbedding.Always;

            // Create a renderer for the MigraDoc document.
            PdfDocumentRenderer pdfRenderer = new PdfDocumentRenderer(unicode, embedding);

            // Associate the MigraDoc document with a renderer
            pdfRenderer.Document = document;

            // Layout and render document to PDF
            pdfRenderer.RenderDocument();
            // Save the document...
            MemoryStream stream = new MemoryStream();
            pdfRenderer.PdfDocument.Save(stream, false);

            return stream;

        }

        /// <summary>
        /// Creates an absolutely minimalistic document.
        /// </summary>
        private Document CreateDocument(
            IQueryable<KommentarNewsletterViewModel> kommentare,
            IQueryable<GesetzaenderungViewModel> gesetzaenderungen,
            IQueryable<ConsultationViewModel> consultations,
            ApplicationUser user,
            int spracheID)
        {
            var document = new Document();


            if (spracheID == 1)
            {
                document.Info.Title = "Newsletter " + new CultureInfo("de-DE", false).DateTimeFormat.GetMonthName(DateTime.Now.Month) + " " + DateTime.Now.ToString("yyyy");
                document.Info.Subject = "Newsletter for " + new CultureInfo("de-DE", false).DateTimeFormat.GetMonthName(DateTime.Now.Month) + " " + DateTime.Now.ToString("yyyy");
                languageInfo = new CultureInfo("de");

            }
            else if (spracheID == 2)
            {
                document.Info.Title = "Newsletter " + new CultureInfo("fr-FR", false).DateTimeFormat.GetMonthName(DateTime.Now.Month) + " " + DateTime.Now.ToString("yyyy");
                document.Info.Subject = "Newsletter for " + new CultureInfo("fr-FR", false).DateTimeFormat.GetMonthName(DateTime.Now.Month) + " " + DateTime.Now.ToString("yyyy");
                languageInfo = new CultureInfo("fr");
            }
            else if (spracheID == 3)
            {
                document.Info.Title = "Newsletter " + new CultureInfo("it-IT", false).DateTimeFormat.GetMonthName(DateTime.Now.Month) + " " + DateTime.Now.ToString("yyyy");
                document.Info.Subject = "Newsletter for " + new CultureInfo("it-IT", false).DateTimeFormat.GetMonthName(DateTime.Now.Month) + " " + DateTime.Now.ToString("yyyy");
                languageInfo = new CultureInfo("it");
            }
            else if (spracheID == 4)
            {
                document.Info.Title = "Newsletter " + new CultureInfo("en-GB", false).DateTimeFormat.GetMonthName(DateTime.Now.Month) + " " + DateTime.Now.ToString("yyyy");
                document.Info.Subject = "Newsletter for " + new CultureInfo("en-GB", false).DateTimeFormat.GetMonthName(DateTime.Now.Month) + " " + DateTime.Now.ToString("yyyy");
                languageInfo = new CultureInfo("en");
            }

            document.Info.Author = "NeoSys";


            DefineStyles(document);

            DefineCover(document);

            DefineContentSection(document);

            // Erlassänderungen - section
            CreateKommentareParagraph(document, kommentare, user);

            // Weitere geänderte Erlasse in Ihrem Kundendokument - section
            CreateGesetzeAnderungParagraph(document, gesetzaenderungen, user);

            //CreateSuvaParagraph(document, kommentare, user);

            CreateConsultationParagraph(document, consultations, spracheID);

            return document;
        }

        private void DefineStyles(Document document)
        {
            // Get the predefined style Normal.
            Style style = document.Styles["Normal"];
            // Because all styles are derived from Normal, the next line changes the 
            // font of the whole document. Or, more exactly, it changes the font of
            // all styles and paragraphs that do not redefine the font.
            style.Font.Name = "Verdana";

            // Heading1 to Heading9 are predefined styles with an outline level. An outline level
            // other than OutlineLevel.BodyText automatically creates the outline (or bookmarks)
            // in PDF.
            style = document.Styles["Heading1"];
            style.Font.Name = "Tahoma";
            style.Font.Size = 14;
            style.Font.Bold = true;
            style.ParagraphFormat.PageBreakBefore = true;
            style.ParagraphFormat.SpaceBefore = "2cm";
            style.ParagraphFormat.SpaceAfter = 6;

            style = document.Styles["Heading2"];
            style.Font.Size = 12;
            style.Font.Bold = true;
            style.ParagraphFormat.PageBreakBefore = false;
            style.ParagraphFormat.SpaceBefore = 6;
            style.ParagraphFormat.SpaceAfter = 6;


            style = document.Styles["Heading3"];
            style.Font.Size = 10;
            style.Font.Bold = true;
            style.Font.Italic = true;
            style.ParagraphFormat.SpaceBefore = 6;
            style.ParagraphFormat.SpaceAfter = 3;


            style = document.Styles[StyleNames.Header];
            style.ParagraphFormat.AddTabStop("16cm", TabAlignment.Right);

            style = document.Styles[StyleNames.Footer];
            style.ParagraphFormat.AddTabStop("8cm", TabAlignment.Center);

            // Create a new style called TextBox based on style Normal
            style = document.Styles.AddStyle("TextBox", "Normal");
            style.ParagraphFormat.SpaceAfter = "1cm";
            //style.ParagraphFormat.Alignment = ParagraphAlignment.Justify;
            //style.ParagraphFormat.Borders.Width = 2.5;
            style.ParagraphFormat.Borders.Distance = "3pt";
            style.ParagraphFormat.Shading.Color = Color.Parse("#F3F5F7");
        }

        /// <summary>
        /// Defines the cover page.
        /// </summary>
        private void DefineCover(Document document)
        {
            Section section = document.AddSection();

            // Create header
            var header = section.Headers.Primary;
            var image = header.AddImage(getResourcePath("newsletter-logo.jpg"));
            image.Height = "2cm";
            image.LockAspectRatio = true;
            image.RelativeVertical = RelativeVertical.Line;
            image.RelativeHorizontal = RelativeHorizontal.Margin;
            image.Top = ShapePosition.Top;
            image.Left = ShapePosition.Left;
            image.WrapFormat.Style = WrapStyle.Through;

            var headerTitleParagraph = header.AddParagraph();
            headerTitleParagraph.Format.SpaceBefore = "1cm";
            headerTitleParagraph.Format.LeftIndent = "3cm";
            var headerTitle = headerTitleParagraph.AddFormattedText(resourceManager.GetString("Newsletter_Header", languageInfo));
            headerTitle.Font.Size = 10;

            var line = header.AddParagraph();
            line.Format.LeftIndent = "3cm";
            var hrBorder = new Border
            {
                Width = "2pt",
                Color = Colors.Red
            };
            line.Format.Borders.Bottom = hrBorder;
            line.Format.LineSpacing = 0;


            // Create footer
            Paragraph paragraph = section.Footers.Primary.AddParagraph();
            if (languageInfo.Name == "de")
            {
                var month = new CultureInfo("de-DE", false).DateTimeFormat.GetMonthName(DateTime.Now.Month);
                string monthFirstCharacter = char.ToUpper(month[0]).ToString();
                string monthRest = month.Substring(1);
                paragraph.AddFormattedText(monthFirstCharacter + monthRest + " " + DateTime.Now.ToString("yyyy"));
            }
            else if (languageInfo.Name == "fr")
            {
                var month = new CultureInfo("fr-FR", false).DateTimeFormat.GetMonthName(DateTime.Now.Month);
                string monthFirstCharacter = char.ToUpper(month[0]).ToString();
                string monthRest = month.Substring(1);
                paragraph.AddFormattedText(monthFirstCharacter + monthRest + " " + DateTime.Now.ToString("yyyy"));
            }
            else if (languageInfo.Name == "it")
            {
                var month = new CultureInfo("it-IT", false).DateTimeFormat.GetMonthName(DateTime.Now.Month);
                string monthFirstCharacter = char.ToUpper(month[0]).ToString();
                string monthRest = month.Substring(1);
                paragraph.AddFormattedText(monthFirstCharacter + monthRest + " " + DateTime.Now.ToString("yyyy"));
            }
            else if (languageInfo.Name == "en")
            {
                var month = new CultureInfo("en-GB", false).DateTimeFormat.GetMonthName(DateTime.Now.Month);
                string monthFirstCharacter = char.ToUpper(month[0]).ToString();
                string monthRest = month.Substring(1);
                paragraph.AddFormattedText(monthFirstCharacter + monthRest + " " + DateTime.Now.ToString("yyyy"));
            }
            paragraph.Format.Font.Size = 9;
            paragraph.Format.Alignment = ParagraphAlignment.Center;

            // Cover title and subtitle
            paragraph = section.AddParagraph();
            paragraph.Format.SpaceBefore = "3cm";
            var coverTitle = paragraph.AddFormattedText(resourceManager.GetString("Newsletter_Title", languageInfo));
            coverTitle.Font.Size = 14;
            paragraph = section.AddParagraph();
            if (languageInfo.Name == "de")
            {
                var month = new CultureInfo("de-DE", false).DateTimeFormat.GetMonthName(DateTime.Now.Month);
                string monthFirstCharacter = char.ToUpper(month[0]).ToString();
                string monthRest = month.Substring(1);
                var coverSubtitle = paragraph.AddFormattedText(monthFirstCharacter + monthRest + " " + DateTime.Now.ToString("yyyy"));
                coverSubtitle.Font.Size = 10;
            }
            else if (languageInfo.Name == "fr")
            {
                var month = new CultureInfo("fr-FR", false).DateTimeFormat.GetMonthName(DateTime.Now.Month);
                string monthFirstCharacter = char.ToUpper(month[0]).ToString();
                string monthRest = month.Substring(1);
                var coverSubtitle = paragraph.AddFormattedText(monthFirstCharacter + monthRest + " " + DateTime.Now.ToString("yyyy"));
                coverSubtitle.Font.Size = 10;
            }
            else if (languageInfo.Name == "it")
            {
                var month = new CultureInfo("it-IT", false).DateTimeFormat.GetMonthName(DateTime.Now.Month);
                string monthFirstCharacter = char.ToUpper(month[0]).ToString();
                string monthRest = month.Substring(1);
                var coverSubtitle = paragraph.AddFormattedText(monthFirstCharacter + monthRest + " " + DateTime.Now.ToString("yyyy"));
                coverSubtitle.Font.Size = 10;
            }
            else if (languageInfo.Name == "en")
            {
                var month = new CultureInfo("en-GB", false).DateTimeFormat.GetMonthName(DateTime.Now.Month);
                string monthFirstCharacter = char.ToUpper(month[0]).ToString();
                string monthRest = month.Substring(1);
                var coverSubtitle = paragraph.AddFormattedText(monthFirstCharacter + monthRest + " " + DateTime.Now.ToString("yyyy"));
                coverSubtitle.Font.Size = 10;
            }

            paragraph.Format.SpaceAfter = "5cm";

            // Cover image
            paragraph = section.AddParagraph();
            paragraph.Format.LeftIndent = Unit.Zero;
            paragraph.Format.RightIndent = Unit.Zero;
            paragraph.Format.Alignment = ParagraphAlignment.Center;

            image = paragraph.AddImage(getResourcePath("newsletter-cover-image.jpg"));
            image.Width = "22cm";
        }

        private void DefineContentSection(Document document)
        {
            Section section = document.AddSection();
            section.PageSetup.StartingNumber = 1;

            // Create header
            var header = section.Headers.Primary;
            var image = header.AddImage(getResourcePath("newsletter-logo.jpg"));
            image.Height = "1cm";
            image.LockAspectRatio = true;
            image.RelativeVertical = RelativeVertical.Line;
            image.RelativeHorizontal = RelativeHorizontal.Margin;
            image.Top = ShapePosition.Top;
            image.Left = ShapePosition.Left;
            image.WrapFormat.Style = WrapStyle.Through;

            var headerTitleParagraph = header.AddParagraph();
            //headerTitleParagraph.Format.SpaceBefore = "1cm";
            headerTitleParagraph.Format.LeftIndent = "3cm";
            var headerTitle = headerTitleParagraph.AddFormattedText(resourceManager.GetString("Newsletter_Header", languageInfo));
            headerTitle.Font.Size = 10;

            var line = header.AddParagraph();
            line.Format.LeftIndent = "3cm";
            var hrBorder = new Border
            {
                Width = "2pt",
                Color = Colors.Red
            };
            line.Format.Borders.Bottom = hrBorder;
            line.Format.LineSpacing = 0;

            // Create footer
            // Create a paragraph with centered page number. See definition of style "Footer".
            var paragraphPageNumber = new Paragraph();
            paragraphPageNumber.AddTab();
            paragraphPageNumber.AddPageField();

            var paragraphDate = new Paragraph();
            paragraphDate.AddTab();
            if (languageInfo.Name == "de")
            {
                var month = new CultureInfo("de-DE", false).DateTimeFormat.GetMonthName(DateTime.Now.Month);
                string monthFirstCharacter = char.ToUpper(month[0]).ToString();
                string monthRest = month.Substring(1);
                paragraphDate.AddFormattedText(monthFirstCharacter + monthRest + " " + DateTime.Now.ToString("yyyy"));
            }
            else if (languageInfo.Name == "fr")
            {
                var month = new CultureInfo("fr-FR", false).DateTimeFormat.GetMonthName(DateTime.Now.Month);
                string monthFirstCharacter = char.ToUpper(month[0]).ToString();
                string monthRest = month.Substring(1);
                paragraphDate.AddFormattedText(monthFirstCharacter + monthRest + " " + DateTime.Now.ToString("yyyy"));
            }
            else if (languageInfo.Name == "it")
            {
                var month = new CultureInfo("it-IT", false).DateTimeFormat.GetMonthName(DateTime.Now.Month);
                string monthFirstCharacter = char.ToUpper(month[0]).ToString();
                string monthRest = month.Substring(1);
                paragraphDate.AddFormattedText(monthFirstCharacter + monthRest + " " + DateTime.Now.ToString("yyyy"));
            }
            else if (languageInfo.Name == "en")
            {
                var month = new CultureInfo("en-GB", false).DateTimeFormat.GetMonthName(DateTime.Now.Month);
                string monthFirstCharacter = char.ToUpper(month[0]).ToString();
                string monthRest = month.Substring(1);
                paragraphDate.AddFormattedText(monthFirstCharacter + monthRest + " " + DateTime.Now.ToString("yyyy"));
            }

            section.Footers.Primary.Add(paragraphPageNumber);
            section.Footers.Primary.Add(paragraphDate);

        }

        private void CreateKommentareParagraph(Document document, IQueryable<KommentarNewsletterViewModel> kommentare, ApplicationUser user)
        {

            var section = document.LastSection;
            Paragraph paragraph = section.AddParagraph(resourceManager.GetString("Newsletter_Erlassaenderungen", languageInfo), "Heading1");
            paragraph.AddBookmark(resourceManager.GetString("Newsletter_Erlassaenderungen", languageInfo));

            foreach (var k in kommentare)
            {
                var table = section.AddTable();
                table.Borders.Visible = false;
                table.Format.KeepTogether = true;
                table.Format.Shading.Color = Color.Parse("#F3F5F7");
                table.Shading.Color = Color.Parse("#F3F5F7");
                table.TopPadding = 5;
                table.BottomPadding = 5;

                // define columns
                var imgCol = table.AddColumn(Unit.FromCentimeter(3));
                imgCol.Format.Alignment = ParagraphAlignment.Left;

                var textCol = table.AddColumn(Unit.FromCentimeter(14));
                textCol.Format.Alignment = ParagraphAlignment.Justify;

                table.Rows.HeightRule = RowHeightRule.Auto;

                // add title and subtitle row
                var row = table.AddRow();
                var titleParagraph = row.Cells[0].AddParagraph();
                row.Cells[0].MergeRight = 1;

                if (k.ErlassQuelle != null && (k.ErlassQuelle.StartsWith("https://") || k.ErlassQuelle.StartsWith("http://") || k.ErlassQuelle.StartsWith("www.")))
                {
                    if (k.ErlassQuelle.StartsWith("http://"))
                    {
                        k.ErlassQuelle = k.ErlassQuelle.Replace("http://", "https://");
                    }
                    else if (k.ErlassQuelle.StartsWith("www."))
                    {
                        k.ErlassQuelle = k.ErlassQuelle.Replace("www.", "https://");
                    }

                    var hyperlink = titleParagraph.AddHyperlink(k.ErlassQuelle, HyperlinkType.Web);
                    if (String.IsNullOrEmpty(k.ErlassNummer))
                    {
                        var hyperlinkText = hyperlink.AddFormattedText(k.ErlassName);
                        hyperlinkText.Underline = Underline.Single;
                        hyperlinkText.Font.Color = Colors.Blue;
                    }
                    else
                    {
                        var hyperlinkText = hyperlink.AddFormattedText(k.ErlassNummer);
                        hyperlinkText.Underline = Underline.Single;
                        hyperlinkText.Font.Color = Colors.Blue;
                        titleParagraph.AddText(" " + k.ErlassName);
                    }
                }
                else
                {
                    titleParagraph.AddText(k.ErlassNummer + " " + k.ErlassName);
                }

                titleParagraph.AddLineBreak();

                if (k.KommentarQuelle != null && (k.KommentarQuelle.StartsWith("https://") || k.KommentarQuelle.StartsWith("http://") || k.KommentarQuelle.StartsWith("www.")))
                {
                    if (k.KommentarQuelle.StartsWith("http://"))
                    {
                        k.KommentarQuelle = k.KommentarQuelle.Replace("http://", "https://");
                    }
                    else if (k.ErlassQuelle.StartsWith("www."))
                    {
                        k.KommentarQuelle = k.KommentarQuelle.Replace("www.", "https://");
                    }

                    var hyperlink = titleParagraph.AddHyperlink(k.KommentarQuelle, HyperlinkType.Web);
                    var hyperlinkText = hyperlink.AddFormattedText(resourceManager.GetString("Entitaet_Erlassfassung_Beschluss", languageInfo) + " " + k.Beschluss.ToString("dd.MM.yyyy"));
                    hyperlinkText.Underline = Underline.Single;
                    hyperlinkText.Font.Color = Colors.Blue;
                    hyperlinkText.Font.Size = 8;

                    var subtitleText = titleParagraph.AddFormattedText(", " + resourceManager.GetString("Entitaet_Forderungsversion_Inkrafttretung", languageInfo) + ": " + k.Inkrafttretung.ToString("dd.MM.yyyy"));
                    subtitleText.Font.Size = 8;
                }
                else
                {
                    var subtitleText = titleParagraph.AddFormattedText(resourceManager.GetString("Entitaet_Erlassfassung_Beschluss", languageInfo) + " " + k.Beschluss.ToString("dd.MM.yyyy") + ", " + resourceManager.GetString("Entitaet_Forderungsversion_Inkrafttretung", languageInfo) + ": " + k.Inkrafttretung.ToString("dd.MM.yyyy"));
                    subtitleText.Font.Size = 8;
                }

                // add image and text row
                row = table.AddRow();
                row.VerticalAlignment = VerticalAlignment.Top;

                // download and add image
                Image image = null;
                if (!k.KommentarImage.IsEmpty())
                {
                    string imagePath = k.KommentarImage;
                    string env = ConfigurationManager.AppSettings["Environment"];
                    if (env == "SxTest" || env == "Prod")
                    {
                        // load image from local directoy like "newsletter-logo.jpg" because sometimes the connection via url is not possible (reason unknown)
                        var imageName = imagePath.Replace("http://198.168.111.79/Upload/Kommentar/", "").Replace("https://lexplus.ch/Upload/Kommentar/", "");
                        image = row.Cells[0].AddImage("C:\\inetpub\\neosys\\Upload\\Kommentar\\" + imageName);
                    }
                    else
                    {
                        var imageBytes = DownloadImage(k.KommentarImage);
                        image = row.Cells[0].AddImage(MigraDocFilenameFromByteArray(imageBytes));
                    }
                }
                else
                {
                    image = row.Cells[0].AddImage(getResourcePath("newsletter-logo.jpg"));
                }
                image.Height = "2.5cm";
                image.LockAspectRatio = true;
                image.RelativeVertical = RelativeVertical.Line;
                image.RelativeHorizontal = RelativeHorizontal.Margin;
                image.Top = ShapePosition.Center;
                image.Left = ShapePosition.Left;
                image.WrapFormat.Style = WrapStyle.Through;

                // add text
                var textParagraph = row.Cells[1].AddParagraph();
                textParagraph.Format.Alignment = ParagraphAlignment.Left;
                NewsletterTextHelper.CreateFormattedTextFromHtml(textParagraph, k.KommentarShortText);

                // add hyperlink
                if (k.NeosysKommentar != null && (k.NeosysKommentar.StartsWith("https://") || k.NeosysKommentar.StartsWith("http://") || k.NeosysKommentar.StartsWith("www.")))
                {
                    if (k.NeosysKommentar.StartsWith("http://"))
                    {
                        k.NeosysKommentar = k.NeosysKommentar.Replace("http://", "https://");
                    }
                    else if (k.NeosysKommentar.StartsWith("www."))
                    {
                        k.NeosysKommentar = k.NeosysKommentar.Replace("www.", "https://");
                    }
                    var hyperlink = textParagraph.AddHyperlink(k.NeosysKommentar, HyperlinkType.Web);
                    var hyperlinkText = hyperlink.AddFormattedText(" " + resourceManager.GetString("Newsletter_Weiterlesen", languageInfo));
                    hyperlinkText.Font.Size = 9;
                    hyperlinkText.Bold = true;
                }
                // add space
                section.AddParagraph();
            }
        }

        private byte[] DownloadImage(string url)
        {
            using (var webClient = new WebClient())
            {
                return webClient.DownloadData(url);
            }
        }

        private static string MigraDocFilenameFromByteArray(byte[] image)
        {
            return "base64:" +
                   Convert.ToBase64String(image);
        }

        private void CreateGesetzeAnderungParagraph(Document document, IQueryable<GesetzaenderungViewModel> gesetzaenderungen, ApplicationUser user)
        {

            var section = document.LastSection;
            Paragraph paragraph = section.AddParagraph(resourceManager.GetString("Newsletter_Additional_Erlassaenderungen", languageInfo), "Heading1");
            paragraph.AddBookmark(resourceManager.GetString("Newsletter_Additional_Erlassaenderungen", languageInfo));

            var grouped = gesetzaenderungen
                .OrderBy(g => g.Herausgeber)
                .GroupBy(g => g.Herausgeber).ToList();

            foreach (var group in grouped)
            {
                var herausgeberParagraph = section.AddParagraph(group.Key, "Heading3");
                herausgeberParagraph.AddBookmark(group.Key);

                foreach (var g in group)
                {
                    var table = section.AddTable();
                    table.Borders.Visible = false;
                    table.Format.KeepTogether = true;
                    table.Format.Shading.Color = Color.Parse("#F3F5F7");
                    table.Shading.Color = Color.Parse("#F3F5F7");
                    table.TopPadding = 5;
                    table.BottomPadding = 5;

                    // define columns
                    var imgCol = table.AddColumn(Unit.FromCentimeter(3));
                    imgCol.Format.Alignment = ParagraphAlignment.Left;

                    var textCol = table.AddColumn(Unit.FromCentimeter(14));
                    textCol.Format.Alignment = ParagraphAlignment.Justify;

                    table.Rows.HeightRule = RowHeightRule.Auto;

                    // add title and subtitle row
                    var row = table.AddRow();
                    var titleParagraph = row.Cells[0].AddParagraph();
                    row.Cells[0].MergeRight = 1;

                    if (g.ErlassQuelle != null && (g.ErlassQuelle.StartsWith("https://") || g.ErlassQuelle.StartsWith("http://") || g.ErlassQuelle.StartsWith("www.")))
                    {
                        if (g.ErlassQuelle.StartsWith("http://"))
                        {
                            g.ErlassQuelle = g.ErlassQuelle.Replace("http://", "https://");
                        }
                        else if (g.ErlassQuelle.StartsWith("www."))
                        {
                            g.ErlassQuelle = g.ErlassQuelle.Replace("www.", "https://");
                        }

                        var hyperlink = titleParagraph.AddHyperlink(g.ErlassQuelle, HyperlinkType.Web);
                        if (String.IsNullOrEmpty(g.ErlassNummer))
                        {
                            var hyperlinkText = hyperlink.AddFormattedText(g.ErlassName);
                            hyperlinkText.Underline = Underline.Single;
                            hyperlinkText.Font.Color = Colors.Blue;
                        }
                        else
                        {
                            var hyperlinkText = hyperlink.AddFormattedText(g.ErlassNummer);
                            hyperlinkText.Underline = Underline.Single;
                            hyperlinkText.Font.Color = Colors.Blue;
                            titleParagraph.AddText(" " + g.ErlassName);
                        }
                    }
                    else
                    {
                        titleParagraph.AddText(g.ErlassNummer + " " + g.ErlassName);
                    }

                    titleParagraph.AddLineBreak();

                    if (g.KommentarQuelle != null && (g.KommentarQuelle.StartsWith("https://") || g.KommentarQuelle.StartsWith("http://") || g.KommentarQuelle.StartsWith("www.")))
                    {
                        if (g.KommentarQuelle.StartsWith("http://"))
                        {
                            g.KommentarQuelle = g.KommentarQuelle.Replace("http://", "https://");
                        }
                        else if (g.ErlassQuelle.StartsWith("www."))
                        {
                            g.KommentarQuelle = g.KommentarQuelle.Replace("www.", "https://");
                        }

                        var hyperlink = titleParagraph.AddHyperlink(g.KommentarQuelle, HyperlinkType.Web);
                        var hyperlinkText = hyperlink.AddFormattedText(resourceManager.GetString("Entitaet_Erlassfassung_Beschluss", languageInfo) + " " + g.Beschluss.ToString("dd.MM.yyyy"));
                        hyperlinkText.Underline = Underline.Single;
                        hyperlinkText.Font.Size = 8;

                        var subtitleText = titleParagraph.AddFormattedText(", " + resourceManager.GetString("Entitaet_Forderungsversion_Inkrafttretung", languageInfo) + ": " + g.Inkrafttretung.ToString("dd.MM.yyyy"));
                        subtitleText.Font.Size = 8;
                    }
                    else
                    {
                        var subtitleText = titleParagraph.AddFormattedText(resourceManager.GetString("Entitaet_Erlassfassung_Beschluss", languageInfo) + " " + g.Beschluss.ToString("dd.MM.yyyy") + ", " + resourceManager.GetString("Entitaet_Forderungsversion_Inkrafttretung", languageInfo) + ": " + g.Inkrafttretung.ToString("dd.MM.yyyy"));
                        subtitleText.Font.Size = 8;
                    }

                    section.AddParagraph();
                }
            }
        }

        private void CreateSuvaParagraph(Document document, IQueryable<KommentarNewsletterViewModel> kommentare, ApplicationUser user)
        {

            var section = document.LastSection;
            Paragraph paragraph = section.AddParagraph(resourceManager.GetString("Newsletter_SUVA", languageInfo), "Heading1");
            paragraph.AddBookmark(resourceManager.GetString("Newsletter_SUVA", languageInfo));

            var checklists = _unitOfWork.ChecklistRepository.GetAllChecklistViewModels();

            foreach (var k in kommentare)
            {
                var checklist = checklists.FirstOrDefault(c => c.ErlassID == k.ErlassID);
                if (checklist != null)
                {
                    var table = section.AddTable();
                    table.Borders.Visible = false;
                    table.Format.KeepTogether = true;
                    table.Format.Shading.Color = Color.Parse("#F3F5F7");
                    table.Shading.Color = Color.Parse("#F3F5F7");
                    table.TopPadding = 5;
                    table.BottomPadding = 5;

                    // define columns
                    var imgCol = table.AddColumn(Unit.FromCentimeter(3));
                    imgCol.Format.Alignment = ParagraphAlignment.Left;

                    var textCol = table.AddColumn(Unit.FromCentimeter(14));
                    textCol.Format.Alignment = ParagraphAlignment.Justify;

                    table.Rows.HeightRule = RowHeightRule.Auto;

                    // add title and subtitle row
                    var row = table.AddRow();
                    var titleParagraph = row.Cells[0].AddParagraph();
                    row.Cells[0].MergeRight = 1;

                    titleParagraph.AddText(checklist.Title);
                    titleParagraph.AddLineBreak();
                    var subtitleText = titleParagraph.AddFormattedText(resourceManager.GetString("Entitaet_Erlassfassung_Beschluss", languageInfo) + " " + k.Beschluss.ToString("dd.MM.yyyy") + ", " + resourceManager.GetString("Entitaet_Forderungsversion_Inkrafttretung", languageInfo) + ": " + k.Inkrafttretung.ToString("dd.MM.yyyy"));
                    subtitleText.Font.Size = 8;
                    section.AddParagraph();
                }
            }
        }

        private string getResourcePath(String resource)
        {
            var outPutDirectory = Path.GetDirectoryName(Assembly.GetExecutingAssembly().CodeBase);
            var logoImage = Path.Combine(outPutDirectory, "Views\\Emails\\" + resource);
            return new Uri(logoImage).LocalPath;
        }

        private void CreateConsultationParagraph(Document document, IQueryable<ConsultationViewModel> consultations, int spracheID)
        {
            var section = document.LastSection;
            Paragraph paragraph = section.AddParagraph(resourceManager.GetString("View_Dashboard_Tab_Vernehmlassungen", languageInfo), "Heading1");
            paragraph.AddBookmark(resourceManager.GetString("View_Dashboard_Tab_Vernehmlassungen", languageInfo));

            foreach (var c in consultations)
            {
                var table = section.AddTable();
                table.Borders.Visible = false;
                table.Format.KeepTogether = true;
                table.Format.Shading.Color = Color.Parse("#F3F5F7");
                table.Shading.Color = Color.Parse("#F3F5F7");
                table.TopPadding = 1;
                table.BottomPadding = 1;

                var firstCol = table.AddColumn(Unit.FromCentimeter(3));
                firstCol.Format.Alignment = ParagraphAlignment.Left;

                var secondCol = table.AddColumn(Unit.FromCentimeter(14));
                secondCol.Format.Alignment = ParagraphAlignment.Justify;

                table.Rows.HeightRule = RowHeightRule.Auto;

                Row row = table.AddRow();
                Cell cell = row.Cells[0];
                var titleParagraph = cell.AddParagraph(c.Title);
                titleParagraph.Format.Font.Bold = true;
                cell.MergeRight = 1;

                // add Erlasse
                row = table.AddRow();
                cell = row.Cells[0];
                cell.AddParagraph(resourceManager.GetString("Entitaet_Erlass_Plural", languageInfo));
                cell = row.Cells[1];

                List<Erlass> listErlasse = c.ErlasseCollection.ToList();

                foreach (Erlass erlass in c.ErlasseCollection)
                {
                    XDocument uebersetzung = XDocument.Parse(erlass.Uebersetzung);
                    var title = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Titel", spracheID);
                    var quelle = XMLHelper.GetUebersetzungFromXmlField(uebersetzung, "Quelle", spracheID);
                    var srNummer = erlass.SrNummer;

                    var newParagraph = cell.AddParagraph();
                    newParagraph.Format.Alignment = ParagraphAlignment.Left;
                    if (quelle != null && (quelle.StartsWith("https://") || quelle.StartsWith("http://") || quelle.StartsWith("www.")))
                    {
                        if (quelle.StartsWith("http://"))
                        {
                            quelle = quelle.Replace("http://", "https://");
                        }
                        else if (quelle.StartsWith("www."))
                        {
                            quelle = quelle.Replace("www.", "https://");
                        }
                        var hyperlink = newParagraph.AddHyperlink(quelle, HyperlinkType.Web);
                        if (!String.IsNullOrEmpty(erlass.SrNummer))
                        {
                            var hyperlinkText = hyperlink.AddFormattedText(srNummer + "; " + title);
                            hyperlinkText.Underline = Underline.Single;
                            hyperlinkText.Font.Color = Colors.Blue;
                        }
                        else
                        {
                            var hyperlinkText = hyperlink.AddFormattedText(title);
                            hyperlinkText.Underline = Underline.Single;
                            hyperlinkText.Font.Color = Colors.Blue;
                        }
                    }
                    else
                    {
                        if (String.IsNullOrEmpty(erlass.SrNummer))
                        {
                            newParagraph.AddFormattedText(title);
                        }
                        else
                        {
                            newParagraph.AddFormattedText(srNummer + "; " + title);
                        }
                    }

                    if (listErlasse.Last().ErlassID != erlass.ErlassID)
                    {
                        newParagraph.AddLineBreak();
                    }
                }

                // add status
                row = table.AddRow();
                cell = row.Cells[0];
                cell.AddParagraph(resourceManager.GetString("Entitaet_Consultation_Phase", languageInfo));
                cell = row.Cells[1];
                if (c.Status == ConsultationStatus.Completed)
                {
                    cell.AddParagraph(resourceManager.GetString("Enum_ConsultationStatus_Completed", languageInfo));
                }
                else if (c.Status == ConsultationStatus.Planned)
                {
                    cell.AddParagraph(resourceManager.GetString("Enum_ConsultationStatus_Planned", languageInfo));
                }
                else if (c.Status == ConsultationStatus.Ongoing)
                {
                    cell.AddParagraph(resourceManager.GetString("Enum_ConsultationStatus_Ongoing", languageInfo));
                }

                row = table.AddRow();
                cell = row.Cells[0];
                cell.AddParagraph(resourceManager.GetString("Entitaet_Consultation_EntryDate", languageInfo));
                cell = row.Cells[1];
                cell.AddParagraph(c.OpenedDate != null ? c.OpenedDate.GetValueOrDefault().ToString("dd.MM.yyyy") : "");

                row = table.AddRow();
                cell = row.Cells[0];
                cell.AddParagraph(resourceManager.GetString("Entitaet_Consultation_Deadline", languageInfo));
                cell = row.Cells[1];
                cell.AddParagraph(c.Deadline != null ? c.Deadline.GetValueOrDefault().ToString("dd.MM.yyyy") : "");

                row = table.AddRow();
                cell = row.Cells[0];
                cell.AddParagraph(resourceManager.GetString("Entitaet_Consultation_CompletedDate", languageInfo));
                cell = row.Cells[1];
                cell.AddParagraph(c.CompletedDate != null ? c.CompletedDate.GetValueOrDefault().ToString("dd.MM.yyyy") : "");

                //row = table.AddRow();
                //cell = row.Cells[0];

                row = table.AddRow();
                cell = row.Cells[0];
                cell.MergeRight = 1;

                //var quelleParagraph = row.Cells[0].AddParagraph();
                //row.Cells[0].MergeRight = 1;

                if (c.Quelle != null && (c.Quelle.StartsWith("https://") || c.Quelle.StartsWith("http://") || c.Quelle.StartsWith("www.")))
                {
                    if (c.Quelle.StartsWith("http://"))
                    {
                        c.Quelle = c.Quelle.Replace("http://", "https://");
                    }
                    else if (c.Quelle.StartsWith("www."))
                    {
                        c.Quelle = c.Quelle.Replace("www.", "https://");
                    }
                    var hyperlink = cell.AddParagraph().AddHyperlink(c.Quelle, HyperlinkType.Web);
                    var hyperlinkText = hyperlink.AddFormattedText(" " + resourceManager.GetString("Newsletter_Weiterlesen", languageInfo));
                    hyperlinkText.Font.Bold = true;
                }

                section.AddParagraph();
            }
        }

        private static string StripHTML(string input)
        {
            return Regex.Replace(input, "<.*?>", String.Empty);
        }

        private string UploadPdf(ApplicationUser user, Stream stream)
        {

            var newsletterName = "Newsletter_" + DateTime.Now.ToString("dd-MM-yyyy") + ".pdf";

            string filePath = ConfigurationManager.AppSettings["FilePath"];
            string resultFileUrl;
            if (filePath == "Azure")
            {
                IBlobStorageClientFactory factory = new BlobStorageClientFactory();
                IBlobStorageClient client = factory.CreateUserNewsletterBlobStorageClient(user.Id);
                var uploadResult = client.Upload(newsletterName, "application/pdf", stream);
                resultFileUrl = uploadResult.Url;
            }
            else
            {
                string uploadDirectory = "C:/inetpub/neosys/Upload/Newsletter/"; // Directly upload the file to the specific directory                
                newsletterName = newsletterName.Replace(".pdf", "_" + user.Id + ".pdf");

                string path = Path.Combine(uploadDirectory, newsletterName);
                using (FileStream outputFileStream = new FileStream(path, FileMode.Create))
                {
                    stream.CopyTo(outputFileStream);
                }
                resultFileUrl = ConfigurationManager.AppSettings["FilePath"] + "Newsletter/" + newsletterName;
            }



            return resultFileUrl;
        }

        private void SendEmail(ApplicationUser user, Stream stream, int? language)
        {
            /*
            // position stream to beginning
            if (stream.CanSeek)
            {
                stream.Position = 0;
            }

            var template = EmailTemplates[user.SpracheID ?? 1];

            dynamic email = new Email(template);
            email.To = user.Email;
            email.Vorname = user.Vorname;
            email.Nachname = user.Nachname;

            MailMessage mailMessage = _emailService.CreateMailMessage(email);
            string mailMessageBody = mailMessage.Body.Replace("USERFIRSTNAME", user.Vorname).Replace("USERLASTNAME", user.Nachname);
            MailMessage newMailMessage = new MailMessage(mailMessage.From.ToString(), user.Email, mailMessage.Subject, mailMessageBody);
            newMailMessage.Attachments.Add(new Attachment(stream, "Newsletter_" + DateTime.Now.ToString("dd-MM-yyyy") + ".pdf", "application/pdf"));
            SmtpClient client = new SmtpClient();
            client.Send(newMailMessage);
            */
            
            _newsletterEmailService.SendNewsletterEmail(user, stream, language);
        }

    }
}