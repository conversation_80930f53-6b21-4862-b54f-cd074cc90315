using System;
using System.Linq;
using Hangfire;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Site.Utilities;
using log4net;

namespace NeoSysLCS.Site.Tasks
{ public class CustomerStatusTask
    {

        private static readonly ILog log = LogManager.GetLogger(typeof(CustomerStatusTask));

        private readonly IUnitOfWork _unitOfWork;

        public CustomerStatusTask()
        {
            _unitOfWork = new UnitOfWork();
        }

        [DisableConcurrentExecution(timeoutInSeconds: 10 * 60)]
        public void HandleCustomerStatus()
        {
            HandleCustomerInquiryStatus();
        }

        /// <summary>
        /// If customer's InquiryDate == Today then set customer status to Inquiry
        /// </summary>
        private void HandleCustomerInquiryStatus()
        {
            try
            {
                var customersInAktualisierungStatus = _unitOfWork.Context.Kunden
                    .Where(x => x.Status == KundeStatus.Update)
                    .ToList();

                var customersToUpdate = customersInAktualisierungStatus.Where(c => c.InquiryDate?.Date <= DateTime.Now.Date).ToList();
                customersToUpdate.ForEach(c =>
                {
                    _unitOfWork.KundeRepository.UpdateCustomerStatus(c, KundeStatus.Inquiry);
                });
                _unitOfWork.Save();
                //log.Info("CustomerStatusTask - updated customer status!");
            }
            catch (Exception e)
            {
                ExceptionUtility.LogException(e, "CustomerStatusTask");
            }
        }
    }
}