using DevExpress.Web.Mvc;
using System;
using System.Web;
using System.Web.Mvc;
using System.Web.Optimization;
using System.Web.Routing;
using DevExpress.Web;
using NeoSysLCS.Site.Utilities;
using NeoSysLCS.Site.Areas.Admin.Controllers;
using log4net;
using System.Diagnostics;
using System.Configuration;

namespace NeoSysLCS.Site
{

    public class MvcApplication : System.Web.HttpApplication
    {

        private static readonly ILog log = LogManager.GetLogger(typeof(MvcApplication));

        protected void Application_Start()
        {
            log.Info("Starting Neosys_LCS application in env: " + ConfigurationManager.AppSettings["Environment"]);
            Debug.WriteLine("Starting Neosys_LCS application in env: " + ConfigurationManager.AppSettings["Environment"]); 

            DevExpress.ExpressApp.FrameworkSettings.DefaultSettingsCompatibilityMode = DevExpress.ExpressApp.FrameworkSettingsCompatibilityMode.v24_1;
            AreaRegistration.RegisterAllAreas();
            FilterConfig.RegisterGlobalFilters(GlobalFilters.Filters);
            RouteConfig.RegisterRoutes(RouteTable.Routes);
            BundleConfig.RegisterBundles(BundleTable.Bundles);
            log4net.Config.XmlConfigurator.Configure();
            ModelBinders.Binders.DefaultBinder = new DevExpressEditorsBinder();
            MvcHandler.DisableMvcResponseHeader = true;
            DevExpress.Data.Helpers.ServerModeCore.DefaultForceCaseInsensitiveForAnySource = true;

            //get last build date
            var assembly = System.Reflection.Assembly.GetExecutingAssembly();
            var fileInfo = new System.IO.FileInfo(assembly.Location);
            Application["lastModified"] = fileInfo.LastWriteTime;

            // Assign Application_Error as a callback error handler for DevExpress web controls 
            ASPxWebControl.CallbackError += new EventHandler(Application_Error);
            
        }

        /* redirect all traffic to https */
        protected void Application_BeginRequest(Object sender, EventArgs e)
        {
            if (HttpContext.Current.Request.IsSecureConnection.Equals(false) && HttpContext.Current.Request.IsLocal.Equals(false))
            {
                Response.Redirect("https://" + Request.ServerVariables["HTTP_HOST"] + HttpContext.Current.Request.RawUrl);
            }
        }

        // Redirect on Session End
        protected void Session_End(Object sender, EventArgs e)
        {
            ExceptionUtility.LogInfo("Session End occured");
        }



        void Application_Error(object sender, EventArgs e)
        {
            // Code that runs when an unhandled error occurs

            // Get the exception object.
            Exception exc = Server.GetLastError();
            if (exc == null) {
                log.Warn("Application_Error called but exception is null.");
            }


            // If the exception is an HttpUnhandledException, use the inner exception.
            if (exc != null && exc is HttpUnhandledException && exc.InnerException != null)
                exc = exc.InnerException;

            HttpException httpException = exc as HttpException;
            

            // Log the exception and notify system operators
            ExceptionUtility.LogException(exc, "DefaultPage");

            if (exc is HttpAntiForgeryException)
            {
                //redirect to home if user is authenticated
                if (HttpContext.Current.User.Identity.IsAuthenticated)
                {
                    Response.Clear();
                    Server.ClearError();
                    Response.Redirect("~/", true);
                    return;
                }
            }

            // Handle HTTP errors
            if (exc != null && exc.GetType() == typeof(HttpException))
            {
                // The Complete Error Handling Example generates
                // some errors using URLs with "NoCatch" in them;
                // ignore these here to simulate what would happen
                // if a global.asax handler were not implemented.
                if (exc.Message.Contains("NoCatch") || exc.Message.Contains("maxUrlLength"))
                    return;

                //Redirect HTTP errors to HttpError page
                //Server.Transfer("HttpErrorPage.aspx");
            }

            // For other kinds of errors give the user some information
            // but stay on the default page
            if (exc != null)
            {
                string action;

                if (httpException != null)
                {
                    switch (httpException.GetHttpCode())
                    {
                        case 403:
                            //Access denied
                            action = "HttpError403";
                            break;
                        case 404:
                            //Page not found
                            action = "HttpError404";
                            break;
                        case 500:
                            //server Error
                            action = "HttpError500";
                            break;
                        default:
                            action = "GeneralError";
                            break;
                    }
                }
                else
                {
                    action = "GeneralError";
                }

                // clear error in server
                Server.ClearError();

                Response.Redirect(String.Format("~/Home/{0}/?message={1}", action, exc.Message.Replace(System.Environment.NewLine, "" )));

     
            }
            
            // Clear the error from the server
            Server.ClearError();
        }

        
    }
}
