using System;
using System.Linq;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Site.Utilities.Import
{
    public class KundendokumentForderungsversionImporter : BaseImporter
    {
        public KundendokumentForderungsversionImporter(SpreadsheetDocument spreadsheetDocument,
            string sheetname, int kundendokumentId, UnitOfWork unitOfWork)
            : base(spreadsheetDocument, sheetname, kundendokumentId, unitOfWork)
        {
           
        }

        /// <summary>
        /// NOTE: if you add or remove columns in the export, please add or remove them also here
        /// </summary>
        // NOTE: column blank is required since the page header in Excel merges columns
        enum Columns
        {
            Id =                    0,
            blank =                 1,
            Rechtsbereiche =        2,
            RechtsbereicheFR =      3,
            RechtsbereicheIT =      4,
            Standortobjekt =        5,
            SrNummer =              6,
            Erlass =                7,
            Artikel =               8,
            Beschreibung =          9,
            StandortobjektFR =      10,
            SrNummerFR =            11,
            ErlassFR =              12,
            ArtikelFR =             13,
            BeschreibungFR =        14,
            StandortobjektIT =      15,
            SrNummerIT =            16,
            ErlassIT =              17,
            ArtikelIT =             18,
            BeschreibungIT =        19,
            Bewilligungspflicht =   20,
            Nachweispflicht =       21,
            Status =                22,
            Erfuellung =            23,
            Verantwortlich =        24,                        
            LetzterPruefZeitpunkt = 25,
            NaechstePruefungAm =    26,
            Ablageort =             27,
            Kommentar =             28,
            Pruefmethode =          29,
            Spalte1 =               30,
            Spalte2 =               31,
            Spalte3 =               32,
            Spalte4 =               33,
            Spalte5 =               34,
            Spalte6 =               35,
            Spalte7 =               36,
            Spalte8 =               37,
            Spalte9 =               38,
            Spalte10 =              39,
            Inkrafttretung =        40
        }

        enum ColumsOneLanguage
        {
            Id =                    0,
            blank =                 1,
            Rechtsbereiche =        2,
            Standortobjekt =        3,
            SrNummer =              4,
            Erlass =                5,
            Artikel =               6,
            Beschreibung =          7,            
            Bewilligungspflicht =   8,
            Nachweispflicht =       9,
            Status =                10,
            Erfuellung =            11,
            Verantwortlich =        12,                        
            LetzterPruefZeitpunkt = 13,
            NaechstePruefungAm =    14,
            Ablageort =             15,
            Kommentar =             16,
            Pruefmethode =          17,
            Spalte1 =               18,
            Spalte2 =               19,
            Spalte3 =               20,
            Spalte4 =               21,
            Spalte5 =               22,
            Spalte6 =               23,
            Spalte7 =               24,
            Spalte8 =               25,
            Spalte9 =               26,
            Spalte10 =              27,
            Inkrafttretung =        28
        }

        enum ColumnsTwoLanguages
        {
            Id =                    0,
            blank =                 1,
            Rechtsbereiche =        2,
            RechtsbereicheFR =      3,
            Standortobjekt =        4,
            SrNummer =              5,
            Erlass =                6,
            Artikel =               7,
            Beschreibung =          8,
            StandortobjektFR =      9,
            SrNummerFR =            10,
            ErlassFR =              11,
            ArtikelFR =             12,
            BeschreibungFR =        13,
            Bewilligungspflicht =   14,
            Nachweispflicht =       15,
            Status =                16,
            Erfuellung =            17,
            Verantwortlich =        18,                        
            LetzterPruefZeitpunkt = 19,
            NaechstePruefungAm =    20,
            Ablageort =             21,
            Kommentar =             22,
            Pruefmethode =          23,
            Spalte1 =               24,
            Spalte2 =               25,
            Spalte3 =               26,
            Spalte4 =               27,
            Spalte5 =               28,
            Spalte6 =               29,
            Spalte7 =               30,
            Spalte8 =               31,
            Spalte9 =               32,
            Spalte10 =              33,
            Inkrafttretung =        34
        }

        enum ColumnsNotMergeCell
        {
            Id = 0,
            Rechtsbereiche = 1,
            RechtsbereicheFR = 2,
            RechtsbereicheIT = 3,
            Standortobjekt = 4,
            SrNummer = 5,
            Erlass = 6,
            Artikel = 7,
            Beschreibung = 8,
            StandortobjektFR = 9,
            SrNummerFR = 10,
            ErlassFR = 11,
            ArtikelFR = 12,
            BeschreibungFR = 13,
            StandortobjektIT = 14,
            SrNummerIT = 15,
            ErlassIT = 16,
            ArtikelIT = 17,
            BeschreibungIT = 18,
            Bewilligungspflicht = 19,
            Nachweispflicht = 20,
            Status = 21,
            Erfuellung = 22,
            Verantwortlich = 23,
            LetzterPruefZeitpunkt = 24,
            NaechstePruefungAm = 25,
            Ablageort = 26,
            Kommentar = 27,
            Pruefmethode = 28,
            Spalte1 = 29,
            Spalte2 = 30,
            Spalte3 = 31,
            Spalte4 = 32,
            Spalte5 = 33,
            Spalte6 = 34,
            Spalte7 = 35,
            Spalte8 = 36,
            Spalte9 = 37,
            Spalte10 = 38,
            Inkrafttretung = 39
        }

        enum ColumsOneLanguageNotMergedCell
        {
            Id = 0,
            Rechtsbereiche = 1,
            Standortobjekt = 2,
            SrNummer = 3,
            Erlass = 4,
            Artikel = 5,
            Beschreibung = 6,
            Bewilligungspflicht = 7,
            Nachweispflicht = 8,
            Status = 9,
            Erfuellung = 10,
            Verantwortlich = 11,
            LetzterPruefZeitpunkt = 12,
            NaechstePruefungAm = 13,
            Ablageort = 14,
            Kommentar = 15,
            Pruefmethode = 16,
            Spalte1 = 17,
            Spalte2 = 18,
            Spalte3 = 19,
            Spalte4 = 20,
            Spalte5 = 21,
            Spalte6 = 22,
            Spalte7 = 23,
            Spalte8 = 24,
            Spalte9 = 25,
            Spalte10 = 26,
            Inkrafttretung = 27
        }

        enum ColumnsTwoLanguagesNotMergedCell
        {
            Id = 0,
            Rechtsbereiche = 1,
            RechtsbereicheFR = 2,
            Standortobjekt = 3,
            SrNummer = 4,
            Erlass = 5,
            Artikel = 6,
            Beschreibung = 7,
            StandortobjektFR = 8,
            SrNummerFR = 9,
            ErlassFR = 10,
            ArtikelFR = 11,
            BeschreibungFR = 12,
            Bewilligungspflicht = 13,
            Nachweispflicht = 14,
            Status = 15,
            Erfuellung = 16,
            Verantwortlich = 17,
            LetzterPruefZeitpunkt = 18,
            NaechstePruefungAm = 19,
            Ablageort = 20,
            Kommentar = 21,
            Pruefmethode = 22,
            Spalte1 = 23,
            Spalte2 = 24,
            Spalte3 = 25,
            Spalte4 = 26,
            Spalte5 = 27,
            Spalte6 = 28,
            Spalte7 = 29,
            Spalte8 = 30,
            Spalte9 = 31,
            Spalte10 = 32,
            Inkrafttretung = 33
        }

        protected override void CheckColumns(Row row)
        {
            var rowCells = row.Elements<Cell>();
            var test = rowCells.Count();
            if ((rowCells.Count() != 41) && (rowCells.Count() != 29) && (rowCells.Count() != 35) && (rowCells.Count() != 40) && (rowCells.Count() != 28) && (rowCells.Count() != 34))
            {
                throw new Exception(Resources.Properties.Resources.Fehler_Import_NumberOfColumns);
            }
        }

        protected override void ImportRow(Row row)
        {
            int entityId;
            KundendokumentForderungsversion entity = null;

            var rowCells = row.Elements<Cell>();
            var id = GetCellValue(rowCells.ElementAt((int)Columns.Id));

            //check for empty rows
            if (!string.IsNullOrEmpty(id))
            {
                if (int.TryParse(id, out entityId))
                {
                    entity = _unitOfWork.KundendokumentForderungsversionRepository.GetByID(entityId, "Kundendokument");
                }

                if (entity == null || entity.KundendokumentID != _kundendokument.KundendokumentID)
                {
                    throw new Exception(
                        Resources.Properties.Resources.Fehler_Import_ForderungNotInKundendokument.Replace("##ID##", id));
                }

                if (entity == null || entity.KundeID != _kundendokument.KundeID)
                {
                    throw new Exception(
                        Resources.Properties.Resources.Fehler_Import_ForderungNotFromKunde.Replace("##ID##", id));
                }

                if (rowCells.Count() == 41)
                {
                    try
                    {
                        //Erfüllung
                        entity.Erfuellung =
                            ConvertToKundendokumentErfuellung(GetCellValue(rowCells.ElementAt((int)Columns.Erfuellung)));

                        //LetztePruefungam
                        entity.LetztePruefungAm =
                            ConvertToDateTime(GetCellValue(rowCells.ElementAt((int)Columns.LetzterPruefZeitpunkt)));

                        //other fields
                        entity.NaechstePruefungAm = ConvertToDateTime(GetCellValue(rowCells.ElementAt((int)Columns.NaechstePruefungAm)));
                        entity.Pruefmethode = GetCellValue(rowCells.ElementAt((int)Columns.Pruefmethode));
                        entity.Ablageort = GetCellValue(rowCells.ElementAt((int)Columns.Ablageort));
                        entity.Kommentar = GetCellValue(rowCells.ElementAt((int)Columns.Kommentar));
                        entity.Spalte1 = GetCellValue(rowCells.ElementAt((int)Columns.Spalte1));
                        entity.Spalte2 = GetCellValue(rowCells.ElementAt((int)Columns.Spalte2));
                        entity.Spalte3 = GetCellValue(rowCells.ElementAt((int)Columns.Spalte3));
                        entity.Spalte4 = GetCellValue(rowCells.ElementAt((int)Columns.Spalte4));
                        entity.Spalte5 = GetCellValue(rowCells.ElementAt((int)Columns.Spalte5));
                        entity.Spalte6 = GetCellValue(rowCells.ElementAt((int)Columns.Spalte6));
                        entity.Spalte7 = GetCellValue(rowCells.ElementAt((int)Columns.Spalte7));
                        entity.Spalte8 = GetCellValue(rowCells.ElementAt((int)Columns.Spalte8));
                        entity.Spalte9 = GetCellValue(rowCells.ElementAt((int)Columns.Spalte9));
                        entity.Spalte10 = GetCellValue(rowCells.ElementAt((int)Columns.Spalte10));

                        //Fixed NEOS-303 Shortcuts not correctly imported
                        var test = GetCellValue(rowCells.ElementAt((int)Columns.Verantwortlich));
                        int standortId = entity.Kundendokument.StandortID;
                        ShortcutViewModel shortcut = _unitOfWork.ShortcutRepository.GetAllShortcutViewModelsByStandortId(standortId).Where(s => s.Name == GetCellValue(rowCells.ElementAt((int)Columns.Verantwortlich))).FirstOrDefault();
                        if (shortcut != null)
                        {
                            entity.Shortcut = _unitOfWork.ShortcutRepository.GetByID(shortcut.ShortcutID);
                        }
                        else if (GetCellValue(rowCells.ElementAt((int)Columns.Verantwortlich)) != null)
                        {
                            shortcut = new ShortcutViewModel();
                            shortcut.Name = GetCellValue(rowCells.ElementAt((int)Columns.Verantwortlich));
                            shortcut.StandortID = standortId;
                            _unitOfWork.ShortcutRepository.Insert(shortcut);
                            _unitOfWork.Save();

                            ShortcutViewModel newShortcut = _unitOfWork.ShortcutRepository.GetAllShortcutViewModelsByStandortId(standortId).Where(s => s.Name == shortcut.Name).FirstOrDefault();
                            entity.Shortcut = _unitOfWork.ShortcutRepository.GetByID(newShortcut.ShortcutID);
                        }

                        //update and save
                        _unitOfWork.KundendokumentForderungsversionRepository.Update(entity);
                    }
                    catch (Exception e)
                    {
                        throw new Exception(
                            Resources.Properties.Resources.Fehler_Import_ForderungGeneralError.Replace("##ID##", id)
                                .Replace("##error##", e.Message));
                    }
                }

                if (rowCells.Count() == 40)
                {
                    try
                    {
                        //Erfüllung
                        entity.Erfuellung =
                            ConvertToKundendokumentErfuellung(GetCellValue(rowCells.ElementAt((int)ColumnsNotMergeCell.Erfuellung)));

                        //LetztePruefungam
                        entity.LetztePruefungAm =
                            ConvertToDateTime(GetCellValue(rowCells.ElementAt((int)ColumnsNotMergeCell.LetzterPruefZeitpunkt)));

                        //other fields
                        entity.NaechstePruefungAm = ConvertToDateTime(GetCellValue(rowCells.ElementAt((int)ColumnsNotMergeCell.NaechstePruefungAm)));
                        entity.Pruefmethode = GetCellValue(rowCells.ElementAt((int)ColumnsNotMergeCell.Pruefmethode));
                        entity.Ablageort = GetCellValue(rowCells.ElementAt((int)ColumnsNotMergeCell.Ablageort));
                        entity.Kommentar = GetCellValue(rowCells.ElementAt((int)ColumnsNotMergeCell.Kommentar));
                        entity.Spalte1 = GetCellValue(rowCells.ElementAt((int)ColumnsNotMergeCell.Spalte1));
                        entity.Spalte2 = GetCellValue(rowCells.ElementAt((int)ColumnsNotMergeCell.Spalte2));
                        entity.Spalte3 = GetCellValue(rowCells.ElementAt((int)ColumnsNotMergeCell.Spalte3));
                        entity.Spalte4 = GetCellValue(rowCells.ElementAt((int)ColumnsNotMergeCell.Spalte4));
                        entity.Spalte5 = GetCellValue(rowCells.ElementAt((int)ColumnsNotMergeCell.Spalte5));
                        entity.Spalte6 = GetCellValue(rowCells.ElementAt((int)ColumnsNotMergeCell.Spalte6));
                        entity.Spalte7 = GetCellValue(rowCells.ElementAt((int)ColumnsNotMergeCell.Spalte7));
                        entity.Spalte8 = GetCellValue(rowCells.ElementAt((int)ColumnsNotMergeCell.Spalte8));
                        entity.Spalte9 = GetCellValue(rowCells.ElementAt((int)ColumnsNotMergeCell.Spalte9));
                        entity.Spalte10 = GetCellValue(rowCells.ElementAt((int)ColumnsNotMergeCell.Spalte10));

                        //Fixed NEOS-303 Shortcuts not correctly imported
                        var test = GetCellValue(rowCells.ElementAt((int)ColumnsNotMergeCell.Verantwortlich));
                        int standortId = entity.Kundendokument.StandortID;
                        ShortcutViewModel shortcut = _unitOfWork.ShortcutRepository.GetAllShortcutViewModelsByStandortId(standortId).Where(s => s.Name == GetCellValue(rowCells.ElementAt((int)Columns.Verantwortlich))).FirstOrDefault();
                        if (shortcut != null)
                        {
                            entity.Shortcut = _unitOfWork.ShortcutRepository.GetByID(shortcut.ShortcutID);
                        }
                        else if (GetCellValue(rowCells.ElementAt((int)ColumnsNotMergeCell.Verantwortlich)) != null)
                        {
                            shortcut = new ShortcutViewModel();
                            shortcut.Name = GetCellValue(rowCells.ElementAt((int)ColumnsNotMergeCell.Verantwortlich));
                            shortcut.StandortID = standortId;
                            _unitOfWork.ShortcutRepository.Insert(shortcut);
                            _unitOfWork.Save();

                            ShortcutViewModel newShortcut = _unitOfWork.ShortcutRepository.GetAllShortcutViewModelsByStandortId(standortId).Where(s => s.Name == shortcut.Name).FirstOrDefault();
                            entity.Shortcut = _unitOfWork.ShortcutRepository.GetByID(newShortcut.ShortcutID);
                        }

                        //update and save
                        _unitOfWork.KundendokumentForderungsversionRepository.Update(entity);
                    }
                    catch (Exception e)
                    {
                        throw new Exception(
                            Resources.Properties.Resources.Fehler_Import_ForderungGeneralError.Replace("##ID##", id)
                                .Replace("##error##", e.Message));
                    }
                }

                if (rowCells.Count() == 29)
                {
                    try
                    {
                        //Erfüllung
                        entity.Erfuellung =
                            ConvertToKundendokumentErfuellung(GetCellValue(rowCells.ElementAt((int)ColumsOneLanguage.Erfuellung)));

                        //LetztePruefungam
                        entity.LetztePruefungAm =
                            ConvertToDateTime(GetCellValue(rowCells.ElementAt((int)ColumsOneLanguage.LetzterPruefZeitpunkt)));

                        //other fields
                        entity.NaechstePruefungAm = ConvertToDateTime(GetCellValue(rowCells.ElementAt((int)ColumsOneLanguage.NaechstePruefungAm)));
                        entity.Pruefmethode = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguage.Pruefmethode));
                        entity.Ablageort = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguage.Ablageort));
                        entity.Kommentar = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguage.Kommentar));
                        entity.Spalte1 = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguage.Spalte1));
                        entity.Spalte2 = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguage.Spalte2));
                        entity.Spalte3 = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguage.Spalte3));
                        entity.Spalte4 = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguage.Spalte4));
                        entity.Spalte5 = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguage.Spalte5));
                        entity.Spalte6 = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguage.Spalte6));
                        entity.Spalte7 = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguage.Spalte7));
                        entity.Spalte8 = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguage.Spalte8));
                        entity.Spalte9 = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguage.Spalte9));
                        entity.Spalte10 = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguage.Spalte10));

                        //Fixed NEOS-303 Shortcuts not correctly imported
                        var test = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguage.Verantwortlich));
                        int standortId = entity.Kundendokument.StandortID;
                        ShortcutViewModel shortcut = _unitOfWork.ShortcutRepository.GetAllShortcutViewModelsByStandortId(standortId).Where(s => s.Name == GetCellValue(rowCells.ElementAt((int)ColumsOneLanguage.Verantwortlich))).FirstOrDefault();
                        if (shortcut != null)
                        {
                            entity.Shortcut = _unitOfWork.ShortcutRepository.GetByID(shortcut.ShortcutID);
                        }
                        else if (GetCellValue(rowCells.ElementAt((int)ColumsOneLanguage.Verantwortlich)) != null)
                        {
                            shortcut = new ShortcutViewModel();
                            shortcut.Name = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguage.Verantwortlich));
                            shortcut.StandortID = standortId;
                            _unitOfWork.ShortcutRepository.Insert(shortcut);
                            _unitOfWork.Save();

                            ShortcutViewModel newShortcut = _unitOfWork.ShortcutRepository.GetAllShortcutViewModelsByStandortId(standortId).Where(s => s.Name == shortcut.Name).FirstOrDefault();
                            entity.Shortcut = _unitOfWork.ShortcutRepository.GetByID(newShortcut.ShortcutID);
                        }

                        //update and save
                        _unitOfWork.KundendokumentForderungsversionRepository.Update(entity);
                    }
                    catch (Exception e)
                    {
                        throw new Exception(
                            Resources.Properties.Resources.Fehler_Import_ForderungGeneralError.Replace("##ID##", id)
                                .Replace("##error##", e.Message));
                    }
                }

                if (rowCells.Count() == 28)
                {
                    try
                    {
                        //Erfüllung
                        entity.Erfuellung =
                            ConvertToKundendokumentErfuellung(GetCellValue(rowCells.ElementAt((int)ColumsOneLanguageNotMergedCell.Erfuellung)));

                        //LetztePruefungam
                        entity.LetztePruefungAm =
                            ConvertToDateTime(GetCellValue(rowCells.ElementAt((int)ColumsOneLanguageNotMergedCell.LetzterPruefZeitpunkt)));

                        //other fields
                        entity.NaechstePruefungAm = ConvertToDateTime(GetCellValue(rowCells.ElementAt((int)ColumsOneLanguageNotMergedCell.NaechstePruefungAm)));
                        entity.Pruefmethode = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguageNotMergedCell.Pruefmethode));
                        entity.Ablageort = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguageNotMergedCell.Ablageort));
                        entity.Kommentar = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguageNotMergedCell.Kommentar));
                        entity.Spalte1 = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguageNotMergedCell.Spalte1));
                        entity.Spalte2 = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguageNotMergedCell.Spalte2));
                        entity.Spalte3 = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguageNotMergedCell.Spalte3));
                        entity.Spalte4 = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguageNotMergedCell.Spalte4));
                        entity.Spalte5 = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguageNotMergedCell.Spalte5));
                        entity.Spalte6 = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguageNotMergedCell.Spalte6));
                        entity.Spalte7 = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguageNotMergedCell.Spalte7));
                        entity.Spalte8 = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguageNotMergedCell.Spalte8));
                        entity.Spalte9 = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguageNotMergedCell.Spalte9));
                        entity.Spalte10 = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguageNotMergedCell.Spalte10));

                        //Fixed NEOS-303 Shortcuts not correctly imported
                        var test = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguageNotMergedCell.Verantwortlich));
                        int standortId = entity.Kundendokument.StandortID;
                        ShortcutViewModel shortcut = _unitOfWork.ShortcutRepository.GetAllShortcutViewModelsByStandortId(standortId).Where(s => s.Name == GetCellValue(rowCells.ElementAt((int)ColumsOneLanguageNotMergedCell.Verantwortlich))).FirstOrDefault();
                        if (shortcut != null)
                        {
                            entity.Shortcut = _unitOfWork.ShortcutRepository.GetByID(shortcut.ShortcutID);
                        }
                        else if (GetCellValue(rowCells.ElementAt((int)ColumsOneLanguageNotMergedCell.Verantwortlich)) != null)
                        {
                            shortcut = new ShortcutViewModel();
                            shortcut.Name = GetCellValue(rowCells.ElementAt((int)ColumsOneLanguageNotMergedCell.Verantwortlich));
                            shortcut.StandortID = standortId;
                            _unitOfWork.ShortcutRepository.Insert(shortcut);
                            _unitOfWork.Save();

                            ShortcutViewModel newShortcut = _unitOfWork.ShortcutRepository.GetAllShortcutViewModelsByStandortId(standortId).Where(s => s.Name == shortcut.Name).FirstOrDefault();
                            entity.Shortcut = _unitOfWork.ShortcutRepository.GetByID(newShortcut.ShortcutID);
                        }

                        //update and save
                        _unitOfWork.KundendokumentForderungsversionRepository.Update(entity);
                    }
                    catch (Exception e)
                    {
                        throw new Exception(
                            Resources.Properties.Resources.Fehler_Import_ForderungGeneralError.Replace("##ID##", id)
                                .Replace("##error##", e.Message));
                    }
                }

                if (rowCells.Count() == 35)
                {
                    try
                    {
                        //Erfüllung
                        entity.Erfuellung =
                            ConvertToKundendokumentErfuellung(GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguages.Erfuellung)));

                        //LetztePruefungam
                        entity.LetztePruefungAm =
                            ConvertToDateTime(GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguages.LetzterPruefZeitpunkt)));

                        //other fields
                        entity.NaechstePruefungAm = ConvertToDateTime(GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguages.NaechstePruefungAm)));
                        entity.Pruefmethode = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguages.Pruefmethode));
                        entity.Ablageort = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguages.Ablageort));
                        entity.Kommentar = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguages.Kommentar));
                        entity.Spalte1 = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguages.Spalte1));
                        entity.Spalte2 = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguages.Spalte2));
                        entity.Spalte3 = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguages.Spalte3));
                        entity.Spalte4 = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguages.Spalte4));
                        entity.Spalte5 = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguages.Spalte5));
                        entity.Spalte6 = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguages.Spalte6));
                        entity.Spalte7 = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguages.Spalte7));
                        entity.Spalte8 = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguages.Spalte8));
                        entity.Spalte9 = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguages.Spalte9));
                        entity.Spalte10 = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguages.Spalte10));

                        //Fixed NEOS-303 Shortcuts not correctly imported
                        var test = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguages.Verantwortlich));
                        int standortId = entity.Kundendokument.StandortID;
                        ShortcutViewModel shortcut = _unitOfWork.ShortcutRepository.GetAllShortcutViewModelsByStandortId(standortId).Where(s => s.Name == GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguages.Verantwortlich))).FirstOrDefault();
                        if (shortcut != null)
                        {
                            entity.Shortcut = _unitOfWork.ShortcutRepository.GetByID(shortcut.ShortcutID);
                        }
                        else if (GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguages.Verantwortlich)) != null)
                        {
                            shortcut = new ShortcutViewModel();
                            shortcut.Name = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguages.Verantwortlich));
                            shortcut.StandortID = standortId;
                            _unitOfWork.ShortcutRepository.Insert(shortcut);
                            _unitOfWork.Save();

                            ShortcutViewModel newShortcut = _unitOfWork.ShortcutRepository.GetAllShortcutViewModelsByStandortId(standortId).Where(s => s.Name == shortcut.Name).FirstOrDefault();
                            entity.Shortcut = _unitOfWork.ShortcutRepository.GetByID(newShortcut.ShortcutID);
                        }

                        //update and save
                        _unitOfWork.KundendokumentForderungsversionRepository.Update(entity);
                    }
                    catch (Exception e)
                    {
                        throw new Exception(
                            Resources.Properties.Resources.Fehler_Import_ForderungGeneralError.Replace("##ID##", id)
                                .Replace("##error##", e.Message));
                    }
                }

                if (rowCells.Count() == 34)
                {
                    try
                    {
                        //Erfüllung
                        entity.Erfuellung =
                            ConvertToKundendokumentErfuellung(GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguagesNotMergedCell.Erfuellung)));

                        //LetztePruefungam
                        entity.LetztePruefungAm =
                            ConvertToDateTime(GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguagesNotMergedCell.LetzterPruefZeitpunkt)));

                        //other fields
                        entity.NaechstePruefungAm = ConvertToDateTime(GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguagesNotMergedCell.NaechstePruefungAm)));
                        entity.Pruefmethode = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguagesNotMergedCell.Pruefmethode));
                        entity.Ablageort = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguagesNotMergedCell.Ablageort));
                        entity.Kommentar = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguagesNotMergedCell.Kommentar));
                        entity.Spalte1 = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguagesNotMergedCell.Spalte1));
                        entity.Spalte2 = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguagesNotMergedCell.Spalte2));
                        entity.Spalte3 = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguagesNotMergedCell.Spalte3));
                        entity.Spalte4 = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguagesNotMergedCell.Spalte4));
                        entity.Spalte5 = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguagesNotMergedCell.Spalte5));
                        entity.Spalte6 = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguagesNotMergedCell.Spalte6));
                        entity.Spalte7 = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguagesNotMergedCell.Spalte7));
                        entity.Spalte8 = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguagesNotMergedCell.Spalte8));
                        entity.Spalte9 = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguagesNotMergedCell.Spalte9));
                        entity.Spalte10 = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguagesNotMergedCell.Spalte10));

                        //Fixed NEOS-303 Shortcuts not correctly imported
                        var test = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguagesNotMergedCell.Verantwortlich));
                        int standortId = entity.Kundendokument.StandortID;
                        ShortcutViewModel shortcut = _unitOfWork.ShortcutRepository.GetAllShortcutViewModelsByStandortId(standortId).Where(s => s.Name == GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguagesNotMergedCell.Verantwortlich))).FirstOrDefault();
                        if (shortcut != null)
                        {
                            entity.Shortcut = _unitOfWork.ShortcutRepository.GetByID(shortcut.ShortcutID);
                        }
                        else if (GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguagesNotMergedCell.Verantwortlich)) != null)
                        {
                            shortcut = new ShortcutViewModel();
                            shortcut.Name = GetCellValue(rowCells.ElementAt((int)ColumnsTwoLanguagesNotMergedCell.Verantwortlich));
                            shortcut.StandortID = standortId;
                            _unitOfWork.ShortcutRepository.Insert(shortcut);
                            _unitOfWork.Save();

                            ShortcutViewModel newShortcut = _unitOfWork.ShortcutRepository.GetAllShortcutViewModelsByStandortId(standortId).Where(s => s.Name == shortcut.Name).FirstOrDefault();
                            entity.Shortcut = _unitOfWork.ShortcutRepository.GetByID(newShortcut.ShortcutID);
                        }

                        //update and save
                        _unitOfWork.KundendokumentForderungsversionRepository.Update(entity);
                    }
                    catch (Exception e)
                    {
                        throw new Exception(
                            Resources.Properties.Resources.Fehler_Import_ForderungGeneralError.Replace("##ID##", id)
                                .Replace("##error##", e.Message));
                    }
                }
            }
        }

    }

}