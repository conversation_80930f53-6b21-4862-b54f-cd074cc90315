using System;
using System.Collections.Generic;
using System.Linq;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Site.Helpers;

namespace NeoSysLCS.Site.Utilities.Import
{
    /// <summary>
    /// Base class for xslx importer
    /// </summary>
    public abstract class BaseImporter
    {
        protected SharedStringTablePart _stringTable;
        protected UnitOfWork _unitOfWork;
        protected Kundendokument _kundendokument;
        protected IEnumerable<Row> _rows;

        protected BaseImporter(SpreadsheetDocument spreadsheetDocument, string sheetname, int kundendokumentId, UnitOfWork unitOfWork)
        {
            var workbookPart = spreadsheetDocument.WorkbookPart;
            var worksheetPart = GetWorksheetPart(workbookPart, sheetname);
            var sheetData = worksheetPart.Worksheet.Elements<SheetData>().First();
            _stringTable = workbookPart.GetPartsOfType<SharedStringTablePart>().FirstOrDefault();
            _unitOfWork = unitOfWork;
            _rows = sheetData.Elements<Row>();
            _kundendokument = _unitOfWork.KundendokumentRepository.GetByID(kundendokumentId);
        }

        /// <summary>
        /// Imports a row.
        /// </summary>
        /// <param name="row">The row.</param>
        protected abstract void ImportRow(Row row);

        /// <summary>
        /// Checks the columns.
        /// </summary>
        /// <param name="row">The row.</param>
        protected abstract void CheckColumns(Row row);

        /// <summary>
        /// Imports a excel sheet.
        /// </summary>
        public void Import()
        {
          
            foreach (Row r in _rows)
            {
                if ((r == _rows.ElementAt(0)) || (r == _rows.ElementAt(1)) || (r == _rows.ElementAt(2)))
                {

                }
                //skip header row
                else if (r == _rows.ElementAt(3))
                {
                    CheckColumns(r);
                }
                else
                {
                    ImportRow(r);
                }
            }
        }

        protected string GetCellValue(Cell cell)
        {
            if (cell.CellValue != null)
            {
                if (cell.DataType != null && cell.DataType == CellValues.SharedString)
                {
                    if (_stringTable != null)
                    {
                        //All strings in an Excel worksheet are stored in a array like structure called the SharedSTringTable.
                        return _stringTable.SharedStringTable.ElementAt(int.Parse(cell.CellValue.InnerText)).InnerText;
                    }
                }
                else
                {
                    return cell.CellValue.Text;
                }
            }
            return null;
        }

        protected WorksheetPart GetWorksheetPart(WorkbookPart workbookPart, string sheetName)
        {
            string relId = workbookPart.Workbook.Descendants<Sheet>().First(s => sheetName.Equals(s.Name)).Id;
            return (WorksheetPart)workbookPart.GetPartById(relId);
        }

        protected DateTime? ConvertToDateTime(string value)
        {
            if (!string.IsNullOrEmpty(value))
            {
                return DateTime.FromOADate(double.Parse(value));
            }
            return null;
        }

        protected KundendokumentErfuellung? ConvertToKundendokumentErfuellung(string value)
        {
            System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("NeoSysLCS.Resources.Properties.Resources", typeof(NeoSysLCS.Resources.Properties.Resources).Assembly);
            System.Globalization.CultureInfo info = new System.Globalization.CultureInfo("de");
            System.Globalization.CultureInfo infoFr = new System.Globalization.CultureInfo("fr");
            System.Globalization.CultureInfo infoIt = new System.Globalization.CultureInfo("it");
            System.Globalization.CultureInfo infoEn = new System.Globalization.CultureInfo("en");
            if (!string.IsNullOrEmpty(value))
            {
                var trimedValue = value.Trim().ToLower();
                if ((trimedValue == temp.GetString("Enum_KundendokumentErfuellung_No", info).ToLower())
                    || (trimedValue == temp.GetString("Enum_KundendokumentErfuellung_No", infoFr).ToLower())
                    || (trimedValue == temp.GetString("Enum_KundendokumentErfuellung_No", infoIt).ToLower())
                    || (trimedValue == temp.GetString("Enum_KundendokumentErfuellung_No", infoEn).ToLower()))
                {
                    return KundendokumentErfuellung.No;
                }
                else if((trimedValue == temp.GetString("Enum_KundendokumentErfuellung_Yes", info).ToLower())
                    || (trimedValue == temp.GetString("Enum_KundendokumentErfuellung_Yes", infoFr).ToLower())
                    || (trimedValue == temp.GetString("Enum_KundendokumentErfuellung_Yes", infoIt).ToLower())
                    || (trimedValue == temp.GetString("Enum_KundendokumentErfuellung_Yes", infoEn).ToLower()))
                {
                    return KundendokumentErfuellung.Yes;
                }
                else if((trimedValue == temp.GetString("Enum_KundendokumentErfuellung_UnderClarification", info).ToLower())
                    || (trimedValue == temp.GetString("Enum_KundendokumentErfuellung_UnderClarification", infoFr).ToLower())
                    || (trimedValue == temp.GetString("Enum_KundendokumentErfuellung_UnderClarification", infoIt).ToLower())
                    || (trimedValue == temp.GetString("Enum_KundendokumentErfuellung_UnderClarification", infoEn).ToLower()))
                {
                    return KundendokumentErfuellung.UnderClarification;
                }
                else if((trimedValue == temp.GetString("Enum_KundendokumentErfuellung_NotRelevant", info).ToLower())
                    || (trimedValue == temp.GetString("Enum_KundendokumentErfuellung_NotRelevant", infoFr).ToLower())
                    || (trimedValue == temp.GetString("Enum_KundendokumentErfuellung_NotRelevant", infoIt).ToLower())
                    || (trimedValue == temp.GetString("Enum_KundendokumentErfuellung_NotRelevant", infoEn).ToLower()))
                {
                    return KundendokumentErfuellung.NotRelevant;
                }
                else if((trimedValue == temp.GetString("Enum_KundendokumentErfuellung_NotEdited", info).ToLower())
                    || (trimedValue == temp.GetString("Enum_KundendokumentErfuellung_NotEdited", infoFr).ToLower())
                    || (trimedValue == temp.GetString("Enum_KundendokumentErfuellung_NotEdited", infoIt).ToLower())
                    || (trimedValue == temp.GetString("Enum_KundendokumentErfuellung_NotEdited", infoEn).ToLower()))
                {
                    return KundendokumentErfuellung.NotEdited;
                }

                throw new Exception(Resources.Properties.Resources.Fehler_Import_ErfuellungNotReadable.Replace("##value##", value));
            }
            return null;
        }
    }
}
