using System;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using System.Globalization;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using System.Text.RegularExpressions;
using NeoSysLCS.Resources.Properties;

namespace NeoSysLCS.Site.Utilities.Import.Erlass
{
    public class ExcelFileValidator
    {
        private readonly Dictionary<int, (string Pattern, string Message)> columnPatterns =
          new Dictionary<int, (string, string)>
          {
            { 0, (@"^\d{3}-\d{4}$", Resources.Properties.Resources.FormatErr)  },  // A Column 1: ErlassId - Format 000-0000
            { 1, (@"^\d+(\s*,\s*\d+)*$", Resources.Properties.Resources.NumbersErr) },  // B Column 2: ObjektId - Numbers separated by commas  
            { 2, (@"^(\d+(\s*,\s*\d+)*)?$",Resources.Properties.Resources.NumbersErr) },  // C Column 3: RechtsbereicheIds - Optional number list
            { 3, (@"^[A-Za-züßÄÖÜäöüàèéìíòóùúâêîôûçÇœŒ\s,-]*$", Resources.Properties.Resources.LettersErr)},//D Column 4: HerausgeberDe - Only letters
            { 4, (@"^[A-Za-züßÄÖÜäöüàèéìíòóùúâêîôûçÇœŒ\s,-]*$", Resources.Properties.Resources.LettersErr) },//E Column 5: HerausgeberFr - Only letters
            { 5, (@"^[A-Za-züßÄÖÜäöüàèéìíòóùúâêîôûçÇœŒ\s,-]*$", Resources.Properties.Resources.LettersErr) },//F Column 6: HerausgeberIt - Only letters
            { 6, (@"^[A-Za-züßÄÖÜäöüàèéìíòóùúâêîôûçÇœŒ\s,-]*$", Resources.Properties.Resources.LettersErr) },//G Column 7: HerausgeberEn - Only letters
            { 7, (@".*",Resources.Properties.Resources.ValueErr) },  //H Column 8: ErlassNr - Any value
            { 8, (@".*",Resources.Properties.Resources.ValueErr) }, //I Column 9:  AbkuerzungDe - Any value
            { 9, (@".*",Resources.Properties.Resources.ValueErr)},  //J Column 10: AbkuerzungFr - Any value
            { 10, (@".*",Resources.Properties.Resources.ValueErr) }, //K Column 11: AbkuerzungIt - Any value
            { 11, (@".*",Resources.Properties.Resources.ValueErr) },// L  Column 12: AbkuerzungEn - Any value
            { 12, (@".*",Resources.Properties.Resources.ValueErr) }, //M Column 13: TitelDe - Any value
            { 13, (@".*",Resources.Properties.Resources.ValueErr) },//N Column 14: TitelFr - Any value
            { 14, (@".*",Resources.Properties.Resources.ValueErr) },//O Column 15: TitelIt - Any value
            { 15, (@".*",Resources.Properties.Resources.ValueErr) },//P  Column 16: TitelEn - Any value
            { 16, (@".*",Resources.Properties.Resources.ValueErr) }, //Q Column 17: QuelleDe - Any value
            { 17, (@".*",Resources.Properties.Resources.ValueErr)}, //R Column 18: QuelleFr - Any value
            { 18, (@".*",Resources.Properties.Resources.ValueErr) },//S  Column 19: QuelleIt - Any value
            { 19, (@".*",Resources.Properties.Resources.ValueErr)}, //T Any value Column 20: QuelleEn - Any value
            { 20, (@"^(0?[1-9]|1[0-2])/(0?[1-9]|[12]\d|3[01])/\d{4}$", Resources.Properties.Resources.DateErr)},   //U Column 21: Erlassfassung - Date MM/DD/YYYY
            { 21, (@"^(0?[1-9]|1[0-2])/(0?[1-9]|[12]\d|3[01])/\d{4}$", Resources.Properties.Resources.DateErr) },//V Column 22: ErlassfassungInkrafttretung - Date MM/DD/YYYY
            { 22, (@".*",Resources.Properties.Resources.ValueErr) },//W Column 23: QuelleErlassfassungDe - Any value
            { 23, (@".*",Resources.Properties.Resources.ValueErr) },//X Column 24: QuelleErlassfassungFr - Any value
            { 24, (@".*",Resources.Properties.Resources.ValueErr)},//Y Column 25: QuelleErlassfassungIt - Any value
            { 25, (@".*",Resources.Properties.Resources.ValueErr) },//Z Column 26: QuelleErlassfassungEn - Any value
            { 26, (@".*",Resources.Properties.Resources.ValueErr)},//AA Column 27: KommentarBetroffenDe - Any value
            { 27, (@".*",Resources.Properties.Resources.ValueErr)},//AB Column 28: KommentarNichtbetroffenDe - Any value
            { 28, (@".*",Resources.Properties.Resources.ValueErr)},//AC Column 29: KommentarBetroffenFr - Any value
            { 29, (@".*",Resources.Properties.Resources.ValueErr) },//AD Column 30: KommentarNichtbetroffenFr - Any value
            { 30, (@".*",Resources.Properties.Resources.ValueErr) },//AE Column 31: KommentarBetroffenIt - Any value
            { 31, (@".*",Resources.Properties.Resources.ValueErr) },//AF Column 32: KommentarNichtbetroffenIt - Any value
            { 32, (@".*",Resources.Properties.Resources.ValueErr) },//AG Column 33: KommentarBetroffenEn - Any value
            { 33, (@".*",Resources.Properties.Resources.ValueErr) },//AH Column 34: KommentarNichtbetroffenEn - Any value
            { 34, (@".*",Resources.Properties.Resources.ValueErr)},//AI Column 35: LinkKommentarBetroffenDe - Any value
            { 35, (@".*",Resources.Properties.Resources.ValueErr) },//AJ Column 36: LinkKommentarNichtbetroffenDe - Any value
            { 36, (@".*",Resources.Properties.Resources.ValueErr) },//AK Column 37:LinkKommentarBetroffenFr - Any value
            { 37, (@".*",Resources.Properties.Resources.ValueErr)},//AL Column 38: LinkKommentarNichtbetroffenFr - Any value
            { 38, (@".*",Resources.Properties.Resources.ValueErr) },//AM Column 39:LinkKommentarBetroffenIt - Any value
            { 39, (@".*",Resources.Properties.Resources.ValueErr) },//AN Column 40: LinkKommentarNichtbetroffenIt - Any value
            { 40, (@".*",Resources.Properties.Resources.ValueErr) },//AO Column 41:LinkKommentarBetroffenEn - Any value
            { 41, (@".*",Resources.Properties.Resources.ValueErr)},//AP Column 42: LinkKommentarNichtbetroffenEn - Any value
            { 42, (@".*",Resources.Properties.Resources.ValueErr) },//AQ Column 43: Art - Any value
            { 43, (@".*",Resources.Properties.Resources.ValueErr)},//AR Column 44: LinkArtDe - Any value
            { 44, (@".*",Resources.Properties.Resources.ValueErr) },//AS Column 45: LinkArtFr - Any value
            { 45, (@".*",Resources.Properties.Resources.ValueErr) },//AT Column 46: LinkArtIt - Any value
            { 46, (@".*",Resources.Properties.Resources.ValueErr) },//AU Column 47: LinkArtEn - Any value
            { 47, (@".*",Resources.Properties.Resources.ValueErr) },//AV Column 48: ForderungDe - Any value
            { 48, (@".*",Resources.Properties.Resources.ValueErr)},//AW Column 49: ForderungFr - Any value
            { 49, (@".*",Resources.Properties.Resources.ValueErr) },//AX Column 50: ForderungIt - Any value
            { 50, (@".*",Resources.Properties.Resources.ValueErr) },//AY Column 51: ForderungEn - Any value
            { 51, (@"^(0?[1-9]|1[0-2])/(0?[1-9]|[12]\d|3[01])/\d{4}$", Resources.Properties.Resources.DateErr) },//AZ Column 52: ForderungInkraftretung - Date MM/DD/YYYY
            { 52, (@"^(x)?$",Resources.Properties.Resources.XErr) },//BA Column 53: Bewilligung - Optional "x"
            { 53, (@"^(x)?$",Resources.Properties.Resources.XErr)},//BB Column 54: Nachweis - Optional "x"
            { 54, (@"^(x)?$",Resources.Properties.Resources.XErr)},//BC Column 54: Nachweis - Optional "x"
                
        };
        public List<string> ValidateExcel(Stream stream)
        {

            var errors = new List<string>();
            var validColumns = new HashSet<int>(); // Columns with headers

            using (var spreadsheetDocument = SpreadsheetDocument.Open(stream, false))
            {
                var workbookPart = spreadsheetDocument.WorkbookPart;
                var sharedStringTable = workbookPart.SharedStringTablePart?.SharedStringTable;
                var sheet = workbookPart.Workbook.Sheets.Elements<Sheet>().FirstOrDefault();
                if (sheet == null) throw new Exception("No sheets found in the Excel file");

                var worksheetPart = (WorksheetPart)workbookPart.GetPartById(sheet.Id);
                var sheetData = worksheetPart.Worksheet.Elements<SheetData>().FirstOrDefault();
                if (sheetData == null) throw new Exception("Invalid sheet data");

                var rows = sheetData.Elements<Row>().ToList();
                if (!rows.Any()) throw new Exception("The Excel file is empty.");

                // Identify valid columns (columns with headers)
                var headerCells = rows[0].Elements<Cell>().ToList();
                for (int i = 0; i < headerCells.Count; i++)
                {
                    string headerValue = GetCellValue(headerCells[i], sharedStringTable);
                    if (!string.IsNullOrWhiteSpace(headerValue))
                    {
                        validColumns.Add(i);
                    }
                }

                // Iterate through each column instead of rows
                foreach (var columnIndex in validColumns)
                {
                    ValidateColumn(columnIndex, rows, sharedStringTable, errors);
                }
            }

            return errors;
        }

        private void ValidateColumn(int columnIndex, List<Row> rows, SharedStringTable sharedStringTable, List<string> errors)
        {
            // Skip header row (start from row index 1)
            for (int rowIndex = 1; rowIndex < rows.Count; rowIndex++)
            {
                var row = rows[rowIndex];
                //var cells = row.Elements<Cell>().ToList();
                string columnLetter = GetExcelColumnLetter(columnIndex);
                Cell cell = row.Elements<Cell>()
                               .FirstOrDefault(c => c.CellReference != null && c.CellReference.Value.StartsWith(columnLetter));


                // Ensure the column exists in the row
                if (columnPatterns.ContainsKey(columnIndex))
                {
                    string cellValue = GetCellValue(cell, sharedStringTable);

                    if(columnIndex ==20 || columnIndex ==21 || columnIndex == 51)
                    {
                        // here i need to get date 
                        if (double.TryParse(cellValue, out double oaDate))
                        {
                            DateTime date = DateTime.FromOADate(oaDate);
                            cellValue = date.ToString("MM/dd/yyyy", CultureInfo.InvariantCulture); // Convert to required format
                        }
                    }
                         
                    // Validate only if value exists (avoid false positives for empty cells)
                    if (!string.IsNullOrWhiteSpace(cellValue) && !Regex.IsMatch(cellValue, columnPatterns[columnIndex].Pattern))
                    {
                        errors.Add($"{Resources.Properties.Resources.Column} {columnIndex + 1}, {Resources.Properties.Resources.Row} {rowIndex + 1}: {Resources.Properties.Resources.InvalidData} '{cellValue}' {columnPatterns[columnIndex].Message}");
                    }
                }
            }
        }

        private string GetCellValue(Cell cell, SharedStringTable sharedStringTable)
        {
            if (cell == null || cell.CellValue == null) return string.Empty;

            string value = cell.CellValue.Text;

            // Check if the cell is using a shared string
            if (cell.DataType != null && cell.DataType.Value == CellValues.SharedString)
            {
                if (int.TryParse(value, out int index) && sharedStringTable != null)
                {
                    return sharedStringTable.Elements<SharedStringItem>().ElementAt(index).InnerText.Trim();
                }
            }

            if (double.TryParse(value, NumberStyles.Any, CultureInfo.InvariantCulture, out double numericValue))
            {
                return numericValue.ToString("0.########", CultureInfo.InvariantCulture); // Ensures correct decimal formatting
            }
            return value.Trim();
        }
        private string GetExcelColumnLetter(int columnIndex)
        {
            int dividend = columnIndex + 1;
            string columnName = string.Empty;
            while (dividend > 0)
            {
                int modulo = (dividend - 1) % 26;
                columnName = Convert.ToChar('A' + modulo) + columnName;
                dividend = (dividend - modulo) / 26;
            }
            return columnName;
        }
    }
}
