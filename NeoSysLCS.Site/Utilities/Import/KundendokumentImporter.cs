using System.IO;
using DevExpress.Web;
using DevExpress.Web.ASPxHtmlEditor;
using DocumentFormat.OpenXml.Packaging;
using NeoSysLCS.Repositories;
using System.Globalization;
using NeoSysLCS.DomainModel.Models;

namespace NeoSysLCS.Site.Utilities.Import
{
    public class KundendokumentImporter
    {
        public static UploadControlValidationSettings ValidationSettings =  new UploadControlValidationSettings {
            AllowedFileExtensions = new[] {".xlsx"},
            NotAllowedFileExtensionErrorText = "Falsches Format (*.xlsx)"
        };



        /// <summary>
        /// Imports the kundendokument excel to the specified kundendokument
        /// </summary>
        /// <param name="stream">The stream.</param>
        /// <param name="kundendokumentId">The kundendokument identifier.</param>
        public void ImportKundendokument(Stream stream, int kundendokumentId)
        {
            using (var spreadsheetDocument =
                SpreadsheetDocument.Open(stream, false))
            {
                var unitOfWork = new UnitOfWork();

                string sheetNameForderungen = Resources.Properties.Resources.View_Kundendokument_TabForderungen;
                BaseImporter importer = new KundendokumentForderungsversionImporter(spreadsheetDocument, sheetNameForderungen, kundendokumentId, unitOfWork);
                importer.Import();

                string sheetNameIndividuelleForderungen = Resources.Properties.Resources.View_Kundendokument_TabIndividuelleForderungen;
                importer = new KundendokumentIndividuelleForderungImporter(spreadsheetDocument, sheetNameIndividuelleForderungen, kundendokumentId, unitOfWork);
                importer.Import();

                //importer = new KundendokumentPflichtImporter(spreadsheetDocument, "Pflichten", kundendokumentId, unitOfWork);
                //importer.Import();
                
                unitOfWork.Save();

            }
        }
     }
}