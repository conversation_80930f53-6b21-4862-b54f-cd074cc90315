/// <summary>
/// Js-Helper for entity assignemnt 
/// </summary>
/// <param name="postBackUrl">url that saves the selection</param>
/// <param name="itemFormat">Format string for selected items</param>
SelectionHelper = function (postBackUrl, itemFormat) {

    var grids = [];
    var selectboxes = [];
    var countselectors = [];
    var resultSelectors = [];
    var selectedIDs = [];
    var currentId;

    this.AddSelection = function (itemId, grid, selectbox, countSelector, resultSelector) {
        //init values
        grids[itemId] = grid;
        selectboxes[itemId] = selectbox;
        countselectors[itemId] = countSelector;
        resultSelectors[itemId] = resultSelector;
        currentId = itemId;
        selectedIDs[currentId] = [];
    }

    this.SelectionChanged = function(id, selectedFields, e) {

        grid = grids[id];
        if (e.visibleIndex === -1) {
            //init => get all selected fieldvalues
            grid.GetSelectedFieldValues(selectedFields, AddSelections);
        } else if (e.isSelected) {
            //select item
            grid.GetRowValues(e.visibleIndex, selectedFields, AddSelection);
        } else {
            //unselect item
            grid.GetRowValues(e.visibleIndex, selectedFields, RemoveSelection);

        }

        $(resultSelectors[id]).html("");
    }
    var RemoveSelection = function(values) {
        //remove the values from the selectbox and the selected id array
        var id = values[0];
        var selectbox = selectboxes[currentId];
        var index = selectedIDs[currentId].indexOf(id);

        if (index !== -1) {
            selectedIDs[currentId].splice(index, 1);
            selectbox.BeginUpdate();
            selectbox.RemoveItem(index);
            selectbox.EndUpdate();
        }
        $(countselectors[currentId]).html(grids[currentId].GetSelectedRowCount());
    }

    var AddSelection = function (values) {

        var id = values[0];
        var selectbox = selectboxes[currentId];

        //check if id is already in array
        if ($.inArray(id, selectedIDs[currentId]) === -1) {

            selectedIDs[currentId].push(id);

            selectbox.BeginUpdate();
          
            var args = [];
            for (var i = 1; i < values.length; i++) {
                if (values[i] instanceof Date) {
                    var date = values[i];
                    values[i] = date.getDate() + '.' + (date.getMonth() + 1) + '.' + date.getFullYear() + " ";
                }
                args.push(values[i]);
            }
            selectbox.AddItem(FormatString(itemFormat, args), id);

            selectbox.EndUpdate();
            //set counter
            $(countselectors[currentId]).html(grids[currentId].GetSelectedRowCount());
        }

    }

    var AddSelections = function (values) {
        //adds multiple items
        for (var j = 0; j < values.length; j++) {
            AddSelection(values[j]);
        }
    }

    this.OnSubmitClick = function (id, redirectId) {
        //submit and save ids
        $.post(postBackUrl, { 'selectedIDsHF': selectedIDs[id].join(","), id: id, redirectId : redirectId }, function (data) {
            if (data.Url != undefined && data.Url != null) {
                //url defined -> redirect 
                window.location = data.Url;
            } else {
                //show resultmessage
                $(resultSelectors[id]).html(data);
            }
            
        });
    }

    var FormatString = function (formatString, args) {

        for (var i = 0; i < args.length; i++) {
            
            // "gm" = RegEx options for Global search (more than one instance)
            // and for Multiline search
            var regEx = new RegExp("\\{" + (i) + "\\}", "gm");
            if (args[i] != null) {
                formatString = formatString.replace(regEx, args[i]);
            } else {
                formatString = formatString.replace(regEx, "");
            }
            
        }

        return formatString;
    }

}
