using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Site.Cortec.task.offer
{
    public class ExistingCustomerRejectedOfferStrategy : AbstractCortecTaskCreationStrategy
    {
        public ExistingCustomerRejectedOfferStrategy(bool postponeSending) : base(postponeSending)
        {
        }

        public override bool CanHandle<T>(ICortecTaskCreatable<T> creatable)
        {

            if (creatable.GetWrappedObject() is OfferViewModel offer)
            {
                return offer.ExistingCustomerID.HasValue 
                       && offer.Status == OfferStatus.Rejected;
            }

            return false;
        }

        public override List<CortecTask> CreateTasks<T>(ICortecTaskCreatable<T> creatable)
        {
            var offer = creatable.GetWrappedObject() as OfferViewModel;
            var kunde = GetCustomerById(offer.ExistingCustomerID);
            var customerName = kunde?.Name ?? "Unknown";
            var projectNumber = kunde?.ProjectNumberUpdate ?? "Unknown";
            var projectId = GetProjectId(projectNumber);
            var plId = _unitOfWork.UserRepository.GetById(offer?.ProjectManagerID).CortecId ?? -1;

            var cortecTask = creatable.InitializeCortecTask(plId);
            cortecTask.ProjectNumber = projectNumber;
            cortecTask.ProjectID = projectId;
            cortecTask.Title = "Absage Offerte " + customerName;
            cortecTask.TaskTemplateId = 29;
            cortecTask.StartDate = DateTime.Now;
            cortecTask.EndDate = DateTime.Now;
            cortecTask.TaskStateId = 1;
            cortecTask.AssignedTo = _unitOfWork.UserRepository.GetUserByEmail("<EMAIL>")?.CortecId ?? -1;
            cortecTask.WorkHours = 0.25M;
            AddTask(cortecTask);

            return HandleOrReturn();
        }
    }
}