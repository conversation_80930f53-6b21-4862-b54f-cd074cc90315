using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using Newtonsoft.Json;

namespace NeoSysLCS.Site.Cortec.task.offer
{
    public class OfferAdapter : ICortecTaskCreatable<OfferViewModel>
    {
        private readonly OfferViewModel _offer;
        private readonly IUnitOfWork _unitOfWork;
        private readonly int _appUserCortecId;
        private int _createdByCortecId;

        public OfferAdapter()
        {
        }

        [JsonConstructor]
        public OfferAdapter(OfferViewModel offer, IUnitOfWork unitOfWork, int appUserCortecId)
        {
            _offer = offer;
            _unitOfWork = unitOfWork;
            _appUserCortecId = appUserCortecId;
        }

        public OfferViewModel GetWrappedObject()
        {
            return _offer;
        }

        public CortecTask InitializeCortecTask(int? createdById)
        {
            var cortecTask = new CortecTask();

            _createdByCortecId = createdById ?? _appUserCortecId;

            cortecTask.CreatedBy = _createdByCortecId;
            cortecTask.TaskCategoryId = 9;
            cortecTask.ProficenterId = 8;
            cortecTask.TaskScheduledForCreationAt = DateTime.Now;
            return cortecTask;
        }

        public int GetCreatedByCortecId()
        {
            return _createdByCortecId;
        }

        public int GetUsersCortecId(string emailAddress)
        {
            return _unitOfWork.UserRepository.GetUserByEmail(emailAddress)?.CortecId ?? -1;
        }

        // Implement other methods defined in ICortecTaskCreatable based on the properties of Offer

        public IEnumerable<AbstractCortecTaskCreationStrategy> GetAllTaskCreationStrategies()
        {
            return new List<AbstractCortecTaskCreationStrategy>
            {
                new ExistingCustomerNewOfferStrategy(true),
                new ExistingCustomerQsOfferStrategy(true),
                new ExistingCustomerSentOfferStrategy(true),
                new ExistingCustomerRejectedOfferStrategy(true),
                new ExistingCustomerAcceptedOfferStrategy(true),
                new ExistingCustomerContractCreatedOfferStrategy(true),
                new ExistingCustomerCompletedOfferStrategy(true),
                new NewCustomerNewOfferStrategy(true),
                new NewCustomerQsOfferStrategy(true),
                new NewCustomerSentOfferStrategy(true),
                new NewCustomerRejectedOfferStrategy(true)
            };
        }

    }
}