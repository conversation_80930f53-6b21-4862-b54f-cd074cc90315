using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using DevExpress.Web.Internal.XmlProcessor;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Helpers;

namespace NeoSysLCS.Site.Cortec.task.offer
{
    public class ExistingCustomerSentOfferStrategy : AbstractCortecTaskCreationStrategy
    {
        public ExistingCustomerSentOfferStrategy(bool postponeSending) : base(postponeSending)
        {
        }

        public override bool CanHandle<T>(ICortecTaskCreatable<T> creatable)
        {

            if (creatable.GetWrappedObject() is OfferViewModel offer)
            {
                return offer.ExistingCustomerID.HasValue 
                       && offer.Status == OfferStatus.Sent;
            }

            return false;
        }

        public override List<CortecTask> CreateTasks<T>(ICortecTaskCreatable<T> creatable)
        {
            var offerViewModel = creatable.GetWrappedObject() as OfferViewModel;
            var offer = _unitOfWork.OfferRepository.GetByID(offerViewModel.OfferID);
            var kunde = GetCustomerById(offer.ExistingCustomerID);
            var customerName = kunde?.Name ?? "Unknown";
            var projectNumber = kunde?.ProjectNumberUpdate ?? "Unknown";
            var projectId = GetProjectId(projectNumber);
            var plId = _unitOfWork.UserRepository.GetById(offer?.ProjectManagerID)?.CortecId ?? -1;

            var cortecTask = creatable.InitializeCortecTask(null);
            cortecTask.ProjectNumber = projectNumber;
            cortecTask.ProjectID = projectId;
            cortecTask.Title = "Rückfrage Offerte " + customerName;
            cortecTask.TaskTemplateId = 28;
            cortecTask.StartDate = offer.ConsultationAt.Value;
            cortecTask.EndDate = offer.ConsultationAt.Value.AddDays(5);
            cortecTask.TaskStateId = 1;
            cortecTask.AssignedTo = plId;
            cortecTask.WorkHours = 0.25M;

            //overriding TaskScheduledForCreationAt to postpone task creation
            cortecTask.TaskScheduledForCreationAt = offer.ConsultationAt.Value;
            AddTask(cortecTask);

            return HandleOrReturn();
        }
    }
}