using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Site.Cortec.task.offer
{
    public class AnalyseProcessKundeStrategy : AbstractCortecTaskCreationStrategy
    {
        public AnalyseProcessKundeStrategy(bool postponeSending) : base(postponeSending){}
        public override bool CanHandle<T>(ICortecTaskCreatable<T> creatable)
        {

            if (creatable.GetWrappedObject() is KundeViewModel kunde)
            {
                return 
                    kunde.AnalysisDate != null
                    && kunde.DocumentReportDate != null
                    && kunde.Auftragsvolumen.HasValue
                    && kunde.Status == KundeStatus.Analysis
                    && kunde.AnalysisStatus == KundeAnalysisStatus.Analysis;
            }

            return false;
        }

        public override List<CortecTask> CreateTasks<T>(ICortecTaskCreatable<T> creatable)
        {
            var kunde = creatable.GetWrappedObject() as KundeViewModel;
            var customerName = kunde.Name;
            var projectNumber = kunde.ProjectNumberUpdate;
            var projectId = GetProjectId(projectNumber);
            var plId = _unitOfWork.UserRepository.GetById(kunde.ProjektleiterID).CortecId ?? -1;

            var cortecTask = creatable.InitializeCortecTask(plId);
            cortecTask.ProjectNumber = projectNumber;
            cortecTask.ProjectID = projectId;
            cortecTask.Title = "Analyse durchführen, Dokument erstellen, Bericht erstellen";
            cortecTask.TaskTemplateId = 9;
            cortecTask.StartDate = (DateTime)kunde.AnalysisDate;
            cortecTask.EndDate = (DateTime) kunde.DocumentReportDate;
            cortecTask.TaskStateId = 1;
            cortecTask.AssignedTo = plId;
            decimal workingHours = (decimal)(kunde.Auftragsvolumen.Value * 0.75) / 187.5M;
            cortecTask.WorkHours = workingHours;
            AddTask(cortecTask);

            return HandleOrReturn();
        }
    }
}