using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Site.Cortec.task.offer
{
    public class AnalyseContractCreatedKundeStrategy : AbstractCortecTaskCreationStrategy
    {
        public AnalyseContractCreatedKundeStrategy(bool postponeSending) : base(postponeSending){}
        public override bool CanHandle<T>(ICortecTaskCreatable<T> creatable)
        {

            if (creatable.GetWrappedObject() is KundeViewModel kunde)
            {
                return kunde.Status == KundeStatus.Analysis
                    && kunde.AnalysisStatus == KundeAnalysisStatus.ContractCreated
                    && kunde.ContractReturnedDate.HasValue;
            }

            return false;
        }

        public override List<CortecTask> CreateTasks<T>(ICortecTaskCreatable<T> creatable)
        {
            var kunde = creatable.GetWrappedObject() as KundeViewModel;
            var customerName = kunde.Name;
            var projectNumber = kunde.ProjectNumberUpdate;
            var projectId = GetProjectId(projectNumber);
            var plId = _unitOfWork.UserRepository.GetById(kunde.ProjektleiterID).CortecId ?? -1;

            var cortecTask = creatable.InitializeCortecTask(plId);
            cortecTask.ProjectNumber = projectNumber;
            cortecTask.ProjectID = projectId;
            cortecTask.Title = "Rückfrage Vertrag " + customerName;
            cortecTask.TaskTemplateId = 13;
            cortecTask.StartDate = ((DateTime) kunde.ContractReturnedDate).AddDays(-180);
            cortecTask.EndDate = (DateTime) kunde.ContractReturnedDate;
            cortecTask.TaskStateId = 1;
            cortecTask.AssignedTo = plId;
            cortecTask.WorkHours = 0.25M;
            AddTask(cortecTask);
            
            return HandleOrReturn();
        }
    }
}