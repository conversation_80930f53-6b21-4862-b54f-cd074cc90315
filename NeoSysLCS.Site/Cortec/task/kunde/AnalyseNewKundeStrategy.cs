using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Site.Cortec.task.offer
{
    public class AnalyseNewKundeStrategy : AbstractCortecTaskCreationStrategy
    {
        public AnalyseNewKundeStrategy(bool postponeSending) : base(postponeSending){}
        public override bool CanHandle<T>(ICortecTaskCreatable<T> creatable)
        {

            if (creatable.GetWrappedObject() is KundeViewModel kunde)
            {
                return kunde.Status == KundeStatus.Analysis && kunde.AnalysisStatus == KundeAnalysisStatus.New;
            }

            return false;
        }

        public override List<CortecTask> CreateTasks<T>(ICortecTaskCreatable<T> creatable)
        {
            var kunde = creatable.GetWrappedObject() as KundeViewModel;
            var customerName = kunde.Name;
            var projectNumber = NEW_CUSTOMER_PROJECT_NUMBER;
            var projectId = NEW_CUSTOMER_PROJECT_ID;
            var plId = _unitOfWork.UserRepository.GetById(kunde.ProjektleiterID).CortecId ?? -1;

            var cortecTask = creatable.InitializeCortecTask(plId);
            cortecTask.ProjectNumber = projectNumber;
            cortecTask.ProjectID = projectId;
            cortecTask.Title = "Datum Analyse und Schulung " + customerName + " fixieren";
            cortecTask.TaskTemplateId = 8;
            cortecTask.StartDate = DateTime.Now;
            cortecTask.EndDate = DateTime.Now.AddDays(5);
            cortecTask.TaskStateId = 1;
            cortecTask.AssignedTo = plId;
            cortecTask.WorkHours = 0.5M;

            AddTask(cortecTask);

            return HandleOrReturn();
        }
    }
}