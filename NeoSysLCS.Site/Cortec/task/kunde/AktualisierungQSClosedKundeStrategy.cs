using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Site.Cortec.task.offer
{
    public class AktualisierungQSClosedKundeStrategy : AbstractCortecTaskCreationStrategy
    {
        public AktualisierungQSClosedKundeStrategy(bool postponeSending) : base(postponeSending){}
        public override bool CanHandle<T>(ICortecTaskCreatable<T> creatable)
        {

            if (creatable.GetWrappedObject() is KundeViewModel kunde)
            {
                return kunde.Status == KundeStatus.QSClosed
                       && kunde.Auftragsvolumen.HasValue
                       && !string.IsNullOrEmpty(kunde.ProjektleiterQSID);
            }

            return false;
        }

        public override List<CortecTask> CreateTasks<T>(ICortecTaskCreatable<T> creatable)
        {
            var kunde = creatable.GetWrappedObject() as KundeViewModel;
            var customerName = kunde.Name;
            var projectNumber = kunde.ProjectNumberUpdate;
            var projectId = GetProjectId(projectNumber);
            var plId = _unitOfWork.UserRepository.GetById(kunde.ProjektleiterID).CortecId ?? -1;
            var plQSId = _unitOfWork.UserRepository.GetById(kunde.ProjektleiterQSID).CortecId ?? -1;

            var cortecTask = creatable.InitializeCortecTask(plId);
            cortecTask.ProjectNumber = projectNumber;
            cortecTask.ProjectID = projectId;
            cortecTask.Title = "Versenden Dokument " + customerName;
            cortecTask.TaskTemplateId = 22;
            var now = DateTime.Now;
            cortecTask.StartDate = now;
            cortecTask.EndDate = now.AddDays(1);
            cortecTask.TaskStateId = 5;
            cortecTask.AssignedTo = plQSId;
            cortecTask.WorkHours = 0.25M;
            AddTask(cortecTask);

            return HandleOrReturn();
        }
    }
}