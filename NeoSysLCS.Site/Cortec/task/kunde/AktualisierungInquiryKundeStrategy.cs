using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;

namespace NeoSysLCS.Site.Cortec.task.offer
{
    public class AktualisierungInquiryKundeStrategy : AbstractCortecTaskCreationStrategy
    {
        public AktualisierungInquiryKundeStrategy(bool postponeSending) : base(postponeSending){}
        public override bool CanHandle<T>(ICortecTaskCreatable<T> creatable)
        {

            if (creatable.GetWrappedObject() is KundeViewModel kunde)
            {
                return kunde.Status == KundeStatus.Inquiry
                    && kunde.InquiryDate.HasValue;
            }

            return false;
        }

        public override List<CortecTask> CreateTasks<T>(ICortecTaskCreatable<T> creatable)
        {
            var kunde = creatable.GetWrappedObject() as KundeViewModel;
            var customerName = kunde.Name;
            var projectNumber = kunde.ProjectNumberUpdate;
            var projectId = GetProjectId(projectNumber);
            var plId = _unitOfWork.UserRepository.GetById(kunde.ProjektleiterID).CortecId ?? -1;

            var cortecTask = creatable.InitializeCortecTask(plId);
            cortecTask.ProjectNumber = projectNumber;
            cortecTask.ProjectID = projectId;
            cortecTask.Title = "Anfrage Aktualisierung " + customerName;
            cortecTask.TaskTemplateId = 18;
            cortecTask.StartDate = (DateTime) kunde.InquiryDate;
            cortecTask.EndDate = ((DateTime) kunde.InquiryDate).AddDays(7);
            cortecTask.TaskStateId = 1;
            cortecTask.AssignedTo = plId;
            cortecTask.WorkHours = 0.25M;
            AddTask(cortecTask);

            return HandleOrReturn();
        }
    }
}