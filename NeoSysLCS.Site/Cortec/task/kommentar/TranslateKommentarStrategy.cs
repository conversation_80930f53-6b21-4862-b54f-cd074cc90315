using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Helpers;

namespace NeoSysLCS.Site.Cortec.task.kommentar
{
    public class TranslateKommentarStrategy : AbstractCortecTaskCreationStrategy
    {
        public TranslateKommentarStrategy(bool postponeSending) : base(postponeSending)
        {
        }
        public override bool CanHandle<T>(ICortecTaskCreatable<T> creatable)
        {

            if (creatable.GetWrappedObject() is KommentarViewModel kommentar)
            {
                return kommentar.HauptErlassID.HasValue 
                       && kommentar.Status == KommentarStatus.Translate;
            }

            return false;
        }

        public override List<CortecTask> CreateTasks<T>(ICortecTaskCreatable<T> creatable)
        {
            var kommentar = creatable.GetWrappedObject() as KommentarViewModel;
            int plId = _unitOfWork.UserRepository.GetById(kommentar.ProjektleiterID).CortecId ?? -1;
            var hauptErlassId = kommentar.HauptErlassID ?? 0;
            var erlassViewModel = hauptErlassId == 0 ? null : _unitOfWork.ErlassRepository.GetErlassViewModelById(hauptErlassId);
            var srNummer = erlassViewModel?.SrNummer;
            var erlassTitel = erlassViewModel?.Titel;
            int estherBinggeliCortecID = creatable.GetUsersCortecId("<EMAIL>");

            var cortecTask = creatable.InitializeCortecTask(plId);
            cortecTask.ProjectNumber = NEW_COMMENT_PROJECT_NUMBER;
            cortecTask.ProjectID = NEW_COMMENT_PROJECT_ID;
            cortecTask.TaskTemplateId = 25;
            cortecTask.Title = "Kommaservice und Übersetzung Kommentar " + kommentar.KommentarID + ", SR " + srNummer;
            cortecTask.Description = "Kommaservice und Übersetzung Kommentar " + kommentar.KommentarID + ", SR " + srNummer + ", Titel " + erlassTitel + " erstellen";
            cortecTask.StartDate = DateTime.Now;
            cortecTask.EndDate = DateTime.Now.AddDays(14);
            cortecTask.TaskStateId = 1;
            cortecTask.AssignedTo = estherBinggeliCortecID;
            cortecTask.WorkHours = 0.5M;

            AddTask(cortecTask);

            return HandleOrReturn();
        }
    }
}