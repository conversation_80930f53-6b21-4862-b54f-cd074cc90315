using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Repositories.ViewModels;
using NeoSysLCS.Site.Helpers;

namespace NeoSysLCS.Site.Cortec.task.kommentar
{
    public class QSKommentarStrategy : AbstractCortecTaskCreationStrategy
    {
        public QSKommentarStrategy(bool postponeSending) : base(postponeSending)
        {
        }
        public override bool CanHandle<T>(ICortecTaskCreatable<T> creatable)
        {

            if (creatable.GetWrappedObject() is KommentarViewModel kommentar)
            {
                return kommentar.HauptErlassID.HasValue
                       && kommentar.ProjektleiterQSID != null
                       && kommentar.Status == KommentarStatus.FreigabeQS;
            }

            return false;
        }

        public override List<CortecTask> CreateTasks<T>(ICortecTaskCreatable<T> creatable)
        {
            var kommentar = creatable.GetWrappedObject() as KommentarViewModel;

            int plId = _unitOfWork.UserRepository.GetById(kommentar.ProjektleiterID).CortecId ?? -1;
            var cortecTask = creatable.InitializeCortecTask(plId);
            var hauptErlassId = kommentar.HauptErlassID ?? 0;
            var erlassViewModel = hauptErlassId == 0 ? null : _unitOfWork.ErlassRepository.GetErlassViewModelById(hauptErlassId);
            var srNummer = erlassViewModel?.SrNummer;
            var erlassTitel = erlassViewModel?.Titel;

            int plQsCortecID = _unitOfWork.UserRepository.GetById(kommentar.ProjektleiterQSID).CortecId ?? -1;
            cortecTask.ProjectNumber = NEW_COMMENT_PROJECT_NUMBER;
            cortecTask.ProjectID = NEW_COMMENT_PROJECT_ID;
            cortecTask.TaskTemplateId = 30;
            cortecTask.Title = "QS Kommentar " + kommentar.KommentarID + ", SR " + srNummer;
            cortecTask.Description = "QS Kommentar " + kommentar.KommentarID + ", SR " + srNummer + ", Titel " + erlassTitel + " erstellen";
            cortecTask.StartDate = DateTime.Now;
            cortecTask.EndDate = DateTime.Now.AddDays(14);
            cortecTask.TaskStateId = 4;
            cortecTask.AssignedTo = plQsCortecID;
            cortecTask.WorkHours = 1;

            AddTask(cortecTask);

            return HandleOrReturn();
        }
    }
}