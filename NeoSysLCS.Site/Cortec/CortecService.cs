

using System;
using System.Configuration;
using System.Linq;
using System.Threading.Tasks;
using NeoSysLCS.DomainModel.Models;
using NeoSysLCS.DomainModel.Models.NeoSysLCS.DomainModel.Models;
using NeoSysLCS.Repositories;
using NeoSysLCS.Site.Cortec.Dtos;
using NeoSysLCS.Site.Utilities;

namespace NeoSysLCS.Site.Cortec
{
    public class CortecService
    {

        private readonly IUnitOfWork _unitOfWork;
        private readonly ICortecClient _client;

        public CortecService()
        {
            _unitOfWork = new UnitOfWork();
            _client = CortecClientFactory.CreateTaskCreationClient();
        }

        public void NewToDoTask(ToDoTask item)
        {
            
        }

        public async Task SendUnsentTasks()
        {
            var pendingTasks = _unitOfWork.CortecTaskRepository.GetPendingTasksForCreation().ToList();

            foreach (var cortecTask in pendingTasks)
            {
                var dto = ToDoTaskDto.fromCortecTask(cortecTask);
                try
                {
                    await ProcessTaskForCreation(cortecTask, dto);
                }
                catch (Exception ex)
                {
                    HandleTaskCreationError(cortecTask, ex);
                }
                finally
                {
                    SaveTaskUpdates(cortecTask);
                }
            }
        }

        private async Task ProcessTaskForCreation(CortecTask cortecTask, ToDoTaskDto dto)
        {
            cortecTask.TaskCreatedAt = DateTime.Now;
            var response = await _client.CreateToDoTask(dto);

            if (response.status.Equals("ok", StringComparison.OrdinalIgnoreCase))
            {
                cortecTask.ExternalTaskId = response.task_id;
            }
            else
            {
                LogTaskCreationFailure(dto, response.status);
                DecrementExternalTaskId(cortecTask);
            }
        }

        private void HandleTaskCreationError(CortecTask cortecTask, Exception ex)
        {
            DecrementExternalTaskId(cortecTask);
            ExceptionUtility.LogException(ex, "CortecService.SendUnsentTasks");
        }

        private void SaveTaskUpdates(CortecTask cortecTask)
        {
            _unitOfWork.CortecTaskRepository.UpdateTask(cortecTask);
            _unitOfWork.Save();
        }

        private void LogTaskCreationFailure(ToDoTaskDto dto, string status)
        {
            ExceptionUtility.LogInfo($"CortecTask creation (templateId: {dto.TaskTemplateId}, projectId: {dto.ProjectId}) failed. Status code is {status}");
        }

        private void DecrementExternalTaskId(CortecTask cortecTask)
        {
            cortecTask.ExternalTaskId = cortecTask.ExternalTaskId != null ? cortecTask.ExternalTaskId - 1 : 0;
        }
    }
}
