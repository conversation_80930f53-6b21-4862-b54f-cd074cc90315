/* -- ValidationSummary -- */
.dxvsValidationSummary_ModernoNeosys
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: Red;
}
.dxvsValidationSummary_ModernoNeosys td.dxvsRC_ModernoNeosys
{
	vertical-align: top;
	padding: 2px 5px 4px 6px;
}
.dxvsValidationSummary_ModernoNeosys .dxvsE_ModernoNeosys
{
}
.dxvsValidationSummary_ModernoNeosys table.dxvsHT_ModernoNeosys
{
	width: 100%;
}
.dxvsValidationSummary_ModernoNeosys td.dxvsH_ModernoNeosys
{
	padding: 2px 0;
}
.dxvsValidationSummary_ModernoNeosys a.dxvsHL_ModernoNeosys,
.dxvsValidationSummary_ModernoNeosys a.dxvsHL_ModernoNeosys:visited
{
	color: red;
}
.dxvsValidationSummary_ModernoNeosys a.dxvsHL_ModernoNeosys
{
	text-decoration: none;
	border-bottom: 1px dashed #f70;
}
.dxvsValidationSummary_ModernoNeosys a.dxvsHL_ModernoNeosys:hover
{
	color: #f70;
}
.dxvsValidationSummary_ModernoNeosys table.dxvsT_ModernoNeosys
{
}
.dxvsValidationSummary_ModernoNeosys tr.dxvsE_ModernoNeosys
{
}
.dxvsValidationSummary_ModernoNeosys td.dxvsETC_ModernoNeosys
{
	padding: 2px 0;
}
.dxvsValidationSummary_ModernoNeosys ul.dxvsL_ModernoNeosys
{
}
.dxvsValidationSummary_ModernoNeosys ol.dxvsL_ModernoNeosys
{
}
.dxvsValidationSummary_ModernoNeosys li.dxvsE_ModernoNeosys
{
	padding: 2px 0;
}

/* TrackBar */
.dxeTrackBar_ModernoNeosys
{
    font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	user-select: none;
	-moz-user-select: -moz-none;
	-khtml-user-select: none;
	-webkit-user-select: none;
	visibility:hidden;
}

.dxeDisabled_ModernoNeosys .dxeTBHSys a,
.dxeDisabled_ModernoNeosys .dxeTBVSys a
{
    cursor: default;
}

.dxeTBBarHighlight_ModernoNeosys
{
	font-size: 0;
}

.dxeTBScale_ModernoNeosys,
.dxeTBTrack_ModernoNeosys,
.dxeTBBarHighlight_ModernoNeosys
{
	cursor: pointer;
}

.dxeTBScale_ModernoNeosys
{
	z-index: 1;
}
.dxeTBSecondaryDH_ModernoNeosys,
.dxeTBMainDH_ModernoNeosys
{
	z-index: 4;
}

.dxeTrackBar_ModernoNeosys.dxeTBHSys
{
	width: 170px;
	height: 53px;
}
.dxeTrackBar_ModernoNeosys.dxeTBVSys
{
	height: 170px;
	width: 53px;
}
.dxeTrackBar_ModernoNeosys.dxeTBBScaleSys.dxeTBVSys
{
	width: 76px;
}
.dxeTrackBar_ModernoNeosys.dxeTBBScaleSys.dxeTBHSys
{	
	height: 76px;
}

.dxeTBTrack_ModernoNeosys
{
	z-index: 2;
	position: absolute;
}

.dxeTBTrack_ModernoNeosys, .dxeTBBarHighlight_ModernoNeosys 
{
	border-style: solid;
	border-color: #d1d1d1;
}

.dxeTBHSys .dxeTBTrack_ModernoNeosys, .dxeTBHSys .dxeTBBarHighlight_ModernoNeosys 
{
	border-width: 0px 1px 0px 1px;
}
.dxeTBVSys .dxeTBTrack_ModernoNeosys, .dxeTBVSys .dxeTBBarHighlight_ModernoNeosys 
{
	border-width: 1px 0px 1px 0px;
}

.dxeTBHSys .dxeTBTrack_ModernoNeosys,
.dxeTBVSys .dxeTBTrack_ModernoNeosys
{
	background: #F6F6F6;
	border: 1px solid #D1D1D1;
}

.dxeTBHSys .dxeTBTrack_ModernoNeosys, 
.dxeTBHSys .dxeTBBarHighlight_ModernoNeosys
{
	height: 3px;
}
.dxeTBVSys .dxeTBTrack_ModernoNeosys, 
.dxeTBVSys .dxeTBBarHighlight_ModernoNeosys
{
	width: 3px;
}

.dxeTBHSys .dxeTBMainDH_ModernoNeosys,
.dxeTBHSys .dxeTBSecondaryDH_ModernoNeosys,
.dxeTBVSys .dxeTBMainDH_ModernoNeosys,
.dxeTBVSys .dxeTBSecondaryDH_ModernoNeosys,
.dxeFocused_ModernoNeosys .dxeTBHSys .dxeFocusedMDHSys .dxeTBMainDH_ModernoNeosys,
.dxeFocused_ModernoNeosys .dxeTBHSys .dxeFocusedSDHSys .dxeTBSecondaryDH_ModernoNeosys,
.dxeFocused_ModernoNeosys .dxeTBVSys .dxeFocusedMDHSys .dxeTBMainDH_ModernoNeosys,
.dxeFocused_ModernoNeosys .dxeTBVSys .dxeFocusedSDHSys .dxeTBSecondaryDH_ModernoNeosys
{
	top: -8px;
	width: 18px;
	height: 18px;
	border-radius: 12px;
	border: 1px solid #D1D1D1;
	background: #E6E6E6;
	-webkit-border-radius: 11px;
}
.dxeFocused_ModernoNeosys .dxeTBHSys .dxeFocusedMDHSys .dxeTBMainDH_ModernoNeosys,
.dxeFocused_ModernoNeosys .dxeTBHSys .dxeFocusedSDHSys .dxeTBSecondaryDH_ModernoNeosys,
.dxeFocused_ModernoNeosys .dxeTBVSys .dxeFocusedMDHSys .dxeTBMainDH_ModernoNeosys,
.dxeFocused_ModernoNeosys .dxeTBVSys .dxeFocusedSDHSys .dxeTBSecondaryDH_ModernoNeosys
{
    border: 1px solid #9FBD92;

	box-shadow: 0px 0px 2px 0px rgba(159,189,146,1);
	-webkit-box-shadow: 0px 0px 2px 0px rgba(159,189,146,1);
}
.dxeTBHSys .dxeTBMainDH_ModernoNeosys,
.dxeTBHSys .dxeTBSecondaryDH_ModernoNeosys,
.dxeFocused_ModernoNeosys .dxeTBHSys .dxeFocusedMDHSys .dxeTBMainDH_ModernoNeosys,
.dxeFocused_ModernoNeosys .dxeTBHSys .dxeFocusedSDHSys .dxeTBSecondaryDH_ModernoNeosys
{
	top: -8px;
}
.dxeTBVSys .dxeTBMainDH_ModernoNeosys,
.dxeTBVSys .dxeTBSecondaryDH_ModernoNeosys,
.dxeFocused_ModernoNeosys .dxeTBVSys .dxeFocusedMDHSys .dxeTBMainDH_ModernoNeosys,
.dxeFocused_ModernoNeosys .dxeTBVSys .dxeFocusedSDHSys .dxeTBSecondaryDH_ModernoNeosys
{
	left: -8px;
}
.dxeTBHSys .dxeTBMainDH_ModernoNeosys.dxEditors_edtTBMainDHHover_ModernoNeosys,
.dxeTBHSys .dxeTBSecondaryDH_ModernoNeosys.dxEditors_edtTBSecondaryDHHover_ModernoNeosys,
.dxeTBVSys .dxeTBMainDH_ModernoNeosys.dxEditors_edtTBMainDHHover_ModernoNeosys,
.dxeTBVSys .dxeTBSecondaryDH_ModernoNeosys.dxEditors_edtTBSecondaryDHHover_ModernoNeosys,
.dxeFocused_ModernoNeosys .dxeTBHSys .dxeFocusedMDHSys .dxeTBMainDH_ModernoNeosys.dxEditors_edtTBMainDHHover_ModernoNeosys,
.dxeFocused_ModernoNeosys .dxeTBHSys .dxeFocusedSDHSys .dxeTBSecondaryDH_ModernoNeosys.dxEditors_edtTBSecondaryDHHover_ModernoNeosys,
.dxeFocused_ModernoNeosys .dxeTBVSys .dxeFocusedMDHSys .dxeTBMainDH_ModernoNeosys.dxEditors_edtTBMainDHHover_ModernoNeosys,
.dxeFocused_ModernoNeosys .dxeTBVSys .dxeFocusedSDHSys .dxeTBSecondaryDH_ModernoNeosys.dxEditors_edtTBSecondaryDHHover_ModernoNeosys
{
	background: #1d85cd;
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pg0KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIHZpZXdCb3g9IjAgMCAxIDEiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiPg0KICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQtdWNnZy1nZW5lcmF0ZWQiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjAlIiB5Mj0iMTAwJSI+DQogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzFEODVDRCIgc3RvcC1vcGFjaXR5PSIxIi8+DQogICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMDg2Q0IzIiBzdG9wLW9wYWNpdHk9IjEiLz4NCiAgPC9saW5lYXJHcmFkaWVudD4NCiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4NCjwvc3ZnPg0K);
	background: -ms-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -moz-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -o-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -webkit-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: linear-gradient(to bottom, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	border: 1px Solid #9FBD92;
}
.dxeTBHSys .dxeTBMainDH_ModernoNeosys.dxEditors_edtTBMainDHPressed_ModernoNeosys,
.dxeTBHSys .dxeTBSecondaryDH_ModernoNeosys.dxEditors_edtTBSecondaryDHPressed_ModernoNeosys,
.dxeTBVSys .dxeTBMainDH_ModernoNeosys.dxEditors_edtTBMainDHPressed_ModernoNeosys,
.dxeTBVSys .dxeTBSecondaryDH_ModernoNeosys.dxEditors_edtTBSecondaryDHPressed_ModernoNeosys
.dxeFocused_ModernoNeosys .dxeTBHSys .dxeFocusedMDHSys .dxeTBMainDH_ModernoNeosys.dxEditors_edtTBMainDHPressed_ModernoNeosys,
.dxeFocused_ModernoNeosys .dxeTBHSys .dxeFocusedSDHSys .dxeTBSecondaryDH_ModernoNeosys.dxEditors_edtTBSecondaryDHPressed_ModernoNeosys,
.dxeFocused_ModernoNeosys .dxeTBVSys .dxeFocusedMDHSys .dxeTBMainDH_ModernoNeosys.dxEditors_edtTBMainDHPressed_ModernoNeosys,
.dxeFocused_ModernoNeosys .dxeTBVSys .dxeFocusedSDHSys .dxeTBSecondaryDH_ModernoNeosys.dxEditors_edtTBSecondaryDHPressed_ModernoNeosys
{
	border: 1px Solid #d1d1d1;
	background: #dcdcdc;
}
.dxeTBHSys .dxeTBMainDH_ModernoNeosys.dxEditors_edtTBMainDHDisabled_ModernoNeosys,
.dxeTBHSys .dxeTBSecondaryDH_ModernoNeosys.dxEditors_edtTBSecondaryDHDisabled_ModernoNeosys,
.dxeTBVSys .dxeTBMainDH_ModernoNeosys.dxEditors_edtTBMainDHDisabled_ModernoNeosys,
.dxeTBVSys .dxeTBSecondaryDH_ModernoNeosys.dxEditors_edtTBSecondaryDHDisabled_ModernoNeosys
.dxeFocused_ModernoNeosys .dxeTBHSys .dxeFocusedMDHSys .dxeTBMainDH_ModernoNeosys.dxEditors_edtTBMainDHDisabled_ModernoNeosys,
.dxeFocused_ModernoNeosys .dxeTBHSys .dxeFocusedSDHSys .dxeTBSecondaryDH_ModernoNeosys.dxEditors_edtTBSecondaryDHDisabled_ModernoNeosys,
.dxeFocused_ModernoNeosys .dxeTBVSys .dxeFocusedMDHSys .dxeTBMainDH_ModernoNeosys.dxEditors_edtTBMainDHDisabled_ModernoNeosys,
.dxeFocused_ModernoNeosys .dxeTBVSys .dxeFocusedSDHSys .dxeTBSecondaryDH_ModernoNeosys.dxEditors_edtTBSecondaryDHDisabled_ModernoNeosys
{
	opacity: 0.5;
}

.dxeTBVSys .dxeTBRBScaleSys .dxeTBTrack_ModernoNeosys,
.dxeTBVSys .dxeTBTrack_ModernoNeosys
{
	left: 50%;
	margin-left: -3px;
}
.dxeTBVSys .dxeTBLTScaleSys .dxeTBTrack_ModernoNeosys
{
	right: 12px;
	left: auto;
}
.dxeTBHSys .dxeTBLTScaleSys .dxeTBTrack_ModernoNeosys
{
	bottom: 12px;
	top: auto;
}
.dxeTBHSys .dxeTBRBScaleSys .dxeTBTrack_ModernoNeosys,
.dxeTBHSys .dxeTBTrack_ModernoNeosys
{
	top: 50%;
	margin-top: -3px
}

.dxeTBBarHighlight_ModernoNeosys
{
	left: 0px;
	top: 0px;
	position:absolute;
	z-index: 3;
}

.dxeTBHSys .dxeTBBarHighlight_ModernoNeosys
{
	background: #009C49;
	border: 1px solid #009C49;
	margin: -1px 0;
}
.dxeTBVSys .dxeTBBarHighlight_ModernoNeosys
{
	background: #009C49;
	border: 1px solid #009C49;
	margin: 0 -1px;
}

.dxeTBRBLabel_ModernoNeosys,
.dxeTBLTLabel_ModernoNeosys
{
	font-size: 0.79em;
	color: #9f9f9f;
	text-align: center;
	cursor: default;
}
.dxeTBSmallTickSys .dxeTBRBLabel_ModernoNeosys,
.dxeTBSmallTickSys .dxeTBLTLabel_ModernoNeosys,
.dxeTBLargeTickSys .dxeTBRBLabel_ModernoNeosys,
.dxeTBLargeTickSys .dxeTBLTLabel_ModernoNeosys
{	
	position: absolute;
}

.dxeTBHSys .dxeTBRBLabel_ModernoNeosys,
.dxeTBHSys .dxeTBLTLabel_ModernoNeosys
{
	width: 100%;
}

.dxeTBIncBtn_ModernoNeosys,
.dxeTBDecBtn_ModernoNeosys
{
	width: 27px;
	height: 27px;
	border-radius: 15px;
	border: 1px solid #D1D1D1;
	background-color: #E6E6E6;
	-webkit-border-radius: 16px;

	box-shadow: inset 0px 1px 1px 0px rgba(255,255,255,0.35), 0px 1px 0px 0px rgba(0,0,0,0.05);
	-webkit-box-shadow: inset 0px 1px 1px 0px rgba(255,255,255,0.35), 0px 1px 0px 0px rgba(0,0,0,0.05);
}
.dxeTBIncBtn_ModernoNeosys
{
	background-image: url('edtTBIncBtn.png');
}
.dxeTBDecBtn_ModernoNeosys
{
	background-image: url('edtTBDecBtn.png');
}
.dxeTBIncBtn_ModernoNeosys.dxEditors_edtTBIncBtnHover_ModernoNeosys,
.dxeTBDecBtn_ModernoNeosys.dxEditors_edtTBDecBtnHover_ModernoNeosys
{
	border: 1px Solid #9FBD92;
}
.dxeTBIncBtn_ModernoNeosys.dxEditors_edtTBIncBtnHover_ModernoNeosys
{
	background: #1d85cd url('edtTBIncBtnHover.png');
	background: url('edtTBIncBtnHover.png'), url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pg0KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIHZpZXdCb3g9IjAgMCAxIDEiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiPg0KICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQtdWNnZy1nZW5lcmF0ZWQiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjAlIiB5Mj0iMTAwJSI+DQogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzFEODVDRCIgc3RvcC1vcGFjaXR5PSIxIi8+DQogICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMDg2Q0IzIiBzdG9wLW9wYWNpdHk9IjEiLz4NCiAgPC9saW5lYXJHcmFkaWVudD4NCiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4NCjwvc3ZnPg0K);
	background: url('edtTBIncBtnHover.png'), -ms-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: url('edtTBIncBtnHover.png'), -moz-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: url('edtTBIncBtnHover.png'), -o-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: url('edtTBIncBtnHover.png'), -webkit-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: url('edtTBIncBtnHover.png'), linear-gradient(to bottom, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
}
.dxeTBDecBtn_ModernoNeosys.dxEditors_edtTBDecBtnHover_ModernoNeosys
{
	background: #1d85cd url('edtTBDecBtnHover.png');
	background: url('edtTBDecBtnHover.png'), url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pg0KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIHZpZXdCb3g9IjAgMCAxIDEiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiPg0KICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQtdWNnZy1nZW5lcmF0ZWQiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjAlIiB5Mj0iMTAwJSI+DQogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzFEODVDRCIgc3RvcC1vcGFjaXR5PSIxIi8+DQogICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMDg2Q0IzIiBzdG9wLW9wYWNpdHk9IjEiLz4NCiAgPC9saW5lYXJHcmFkaWVudD4NCiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4NCjwvc3ZnPg0K);
	background: url('edtTBDecBtnHover.png'), -ms-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: url('edtTBDecBtnHover.png'), -moz-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: url('edtTBDecBtnHover.png'), -o-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: url('edtTBDecBtnHover.png'), -webkit-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: url('edtTBDecBtnHover.png'), linear-gradient(to bottom, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
}
.dxeTBIncBtn_ModernoNeosys.dxEditors_edtTBIncBtnPressed_ModernoNeosys,
.dxeTBDecBtn_ModernoNeosys.dxEditors_edtTBDecBtnPressed_ModernoNeosys
{
	border: 1px Solid #d1d1d1;
	background-color: #dcdcdc;

	box-shadow: none;
	-webkit-box-shadow: none;
}
.dxeTBIncBtn_ModernoNeosys.dxEditors_edtTBIncBtnPressed_ModernoNeosys
{
	background-image: url('edtTBIncBtnPressed.png');
}
.dxeTBDecBtn_ModernoNeosys.dxEditors_edtTBDecBtnPressed_ModernoNeosys
{
	background-image: url('edtTBDecBtnPressed.png');
}
.dxeTBIncBtn_ModernoNeosys.dxEditors_edtTBIncBtnDisabled_ModernoNeosys
{
	background-image: url('edtTBIncBtnDisabled.png');
}
.dxeTBDecBtn_ModernoNeosys.dxEditors_edtTBDecBtnDisabled_ModernoNeosys
{
	background-image: url('edtTBDecBtnDisabled.png');
}

.dxeTBVSys .dxeTBRBLabel_ModernoNeosys,
.dxeTBVSys .dxeTBLTLabel_ModernoNeosys
{
	padding-top: 2px;
}
.dxeTBHSys .dxeTBBScaleSys .dxeTBRBLabel_ModernoNeosys
{
	margin-left: -4px;
}
.dxeTBVSys .dxeTBRBScaleSys .dxeTBRBLabel_ModernoNeosys,
.dxeTBVSys .dxeTBBScaleSys .dxeTBLTLabel_ModernoNeosys
{
	right: 0px;
}
.dxeTBVSys .dxeTBLTScaleSys .dxeTBLTLabel_ModernoNeosys,
.dxeTBVSys .dxeTBBScaleSys .dxeTBRBLabel_ModernoNeosys
{
	left: 0px;
}
.dxeTBHSys .dxeTBRBScaleSys .dxeTBRBLabel_ModernoNeosys,
.dxeTBHSys .dxeTBBScaleSys .dxeTBLTLabel_ModernoNeosys
{
	bottom: 0px;
}
.dxeTBHSys .dxeTBLTScaleSys .dxeTBLTLabel_ModernoNeosys,
.dxeTBHSys .dxeTBBScaleSys .dxeTBRBLabel_ModernoNeosys
{
	top: 0px;
}

.dxeTBHSys .dxeTBItem_ModernoNeosys .dxeTBRBLabel_ModernoNeosys,
.dxeTBHSys .dxeTBItem_ModernoNeosys .dxeTBLTLabel_ModernoNeosys
{
	display: inline-block;
	margin-left: 0px!important;
}
.dxeTBHSys .dxeTBRBScaleSys .dxeTBItem_ModernoNeosys .dxeTBRBLabel_ModernoNeosys
{
	padding-top: 12px;
}
.dxeTBHSys .dxeTBLTScaleSys .dxeTBItem_ModernoNeosys .dxeTBLTLabel_ModernoNeosys
{
	margin-top: 8px;
}
.dxeTBVSys .dxeTBRBScaleSys .dxeTBItem_ModernoNeosys .dxeTBRBLabel_ModernoNeosys 
{
	margin-left: 5px;
}

.dxeTBSelectedItem_ModernoNeosys .dxeTBRBLabel_ModernoNeosys,
.dxeTBSelectedItem_ModernoNeosys .dxeTBLTLabel_ModernoNeosys,
.dxeTBSelectedTick_ModernoNeosys .dxeTBRBLabel_ModernoNeosys,
.dxeTBSelectedTick_ModernoNeosys .dxeTBLTLabel_ModernoNeosys
{
	color: #009C49;
}
.dxeDisabled_ModernoNeosys .dxeTBSelectedItem_ModernoNeosys .dxeTBRBLabel_ModernoNeosys,
.dxeDisabled_ModernoNeosys .dxeTBSelectedItem_ModernoNeosys .dxeTBLTLabel_ModernoNeosys,
.dxeDisabled_ModernoNeosys .dxeTBSelectedTick_ModernoNeosys .dxeTBRBLabel_ModernoNeosys,
.dxeDisabled_ModernoNeosys .dxeTBSelectedTick_ModernoNeosys .dxeTBLTLabel_ModernoNeosys 
{
	color: #FFDEB4;
}

.dxeDisabled_ModernoNeosys .dxeTBRBLabel_ModernoNeosys,
.dxeDisabled_ModernoNeosys .dxeTBLTLabel_ModernoNeosys,
.dxeDisabled_ModernoNeosys .dxeTBItem_ModernoNeosys
{
	color: #D1DFF3;
}

.dxeTBVSys .dxeTBLTScaleSys .dxeTBItem_ModernoNeosys
{
	text-align: left;
}

.dxeTBVSys .dxeTBRBScaleSys .dxeTBItem_ModernoNeosys
{
	text-align: right;
}

.dxeTBVSys .dxeTBBScaleSys .dxeTBItem_ModernoNeosys
{
	text-align: center;
}

.dxeTBHSys .dxeTBItem_ModernoNeosys
{
	background-image:  url('edtTrackBarLargeTickH.gif');
}
.dxeTBHSys .dxeTBBScaleSys .dxeTBItem_ModernoNeosys
{
	background-image:  url('edtTrackBarDoubleSmallTickH.gif');
}
.dxeTBVSys .dxeTBItem_ModernoNeosys 
{
	background-image:  url('edtTrackBarLargeTickV.gif');
}
.dxeTBVSys .dxeTBBScaleSys .dxeTBItem_ModernoNeosys
{
	background-image:  url('edtTrackBarDoubleSmallTickV.gif');
}

.dxeTBHSys .dxeTBLargeTick_ModernoNeosys
{
	background-image:  url('edtTrackBarLargeTickH.gif');
}
.dxeTBVSys .dxeTBLargeTick_ModernoNeosys
{
	background-image: url('edtTrackBarLargeTickV.gif');
}
.dxeTBHSys .dxeTBSmallTick_ModernoNeosys
{
	background-image: url('edtTrackBarSmallTickH.gif');
}
.dxeTBVSys .dxeTBSmallTick_ModernoNeosys
{
	background-image: url('edtTrackBarSmallTickV.gif');
}
.dxeTBHSys .dxeTBBScaleSys .dxeTBLargeTick_ModernoNeosys
{
	background-image: url('edtTrackBarDoubleLargeTickH.gif');
}
.dxeTBVSys .dxeTBBScaleSys .dxeTBLargeTick_ModernoNeosys
{
	background-image: url('edtTrackBarDoubleLargeTickV.gif');
}
.dxeTBHSys .dxeTBBScaleSys .dxeTBSmallTick_ModernoNeosys
{
	background-image: url('edtTrackBarDoubleSmallTickH.gif');
}
.dxeTBVSys .dxeTBBScaleSys .dxeTBSmallTick_ModernoNeosys
{
	background-image: url('edtTrackBarDoubleSmallTickV.gif');
}

.dxeTBVSys .dxeTBIncBtn_ModernoNeosys,
.dxeTBVSys .dxeReversedDirectionSys .dxeTBDecBtn_ModernoNeosys
{
	bottom: 0px;
	top: auto;
}
.dxeTBVSys .dxeTBDecBtn_ModernoNeosys,
.dxeTBVSys .dxeReversedDirectionSys .dxeTBIncBtn_ModernoNeosys
{
	top: 0px;
	bottom: auto;
}
.dxeTBHSys .dxeTBDecBtn_ModernoNeosys, 
.dxeTBHSys .dxeReversedDirectionSys .dxeTBIncBtn_ModernoNeosys
{
	left: 0px;
	right: auto;
}
.dxeTBHSys .dxeTBIncBtn_ModernoNeosys,
.dxeTBHSys .dxeReversedDirectionSys .dxeTBDecBtn_ModernoNeosys
{
	right: 0px;
	left: auto;
}
.dxeTBVSys .dxeTBBScaleSys .dxeTBDecBtn_ModernoNeosys,
.dxeTBVSys .dxeTBBScaleSys .dxeTBIncBtn_ModernoNeosys
{
	margin-left: -15px;
	left: 50%;
}
.dxeTBHSys .dxeTBBScaleSys .dxeTBDecBtn_ModernoNeosys,
.dxeTBHSys .dxeTBBScaleSys .dxeTBIncBtn_ModernoNeosys
{
	margin-top: -15px;
	top: 50%;
}
.dxeTBVSys .dxeTBBScaleSys .dxeTBTrack_ModernoNeosys
{ 
	margin-left: -3px;
	left: 50%;
}
.dxeTBHSys .dxeTBBScaleSys .dxeTBTrack_ModernoNeosys
{ 
	margin-top: -3px;
	top: 50%;
}

.dxeTBVSys .dxeTBLTScaleSys .dxeTBDecBtn_ModernoNeosys,
.dxeTBVSys .dxeTBLTScaleSys .dxeTBIncBtn_ModernoNeosys
{
	right: 0px;
	left: auto;
}
.dxeTBHSys .dxeTBLTScaleSys .dxeTBDecBtn_ModernoNeosys,
.dxeTBHSys .dxeTBLTScaleSys .dxeTBIncBtn_ModernoNeosys
{
	bottom: 0px;
	top: auto;
}
.dxeTBVSys .dxeTBRBScaleSys .dxeTBDecBtn_ModernoNeosys,
.dxeTBVSys .dxeTBRBScaleSys .dxeTBIncBtn_ModernoNeosys,
.dxeTBVSys .dxeTBDecBtn_ModernoNeosys,
.dxeTBVSys .dxeTBIncBtn_ModernoNeosys
{
	left: 12px;
}
.dxeTBHSys .dxeTBRBScaleSys .dxeTBDecBtn_ModernoNeosys,
.dxeTBHSys .dxeTBRBScaleSys .dxeTBIncBtn_ModernoNeosys,
.dxeTBHSys .dxeTBDecBtn_ModernoNeosys,
.dxeTBHSys .dxeTBIncBtn_ModernoNeosys
{
	top: 12px;
}

.dxeHelpText_ModernoNeosys,
.dxeTBValueToolTip_ModernoNeosys
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
}
div.dxeHelpText_ModernoNeosys,
.dxeTBValueToolTip_ModernoNeosys
{
	display: inline;
	position: absolute;
	z-index: 41998;
	padding: 3px 8px 4px;
	color: #2B2B2B;
	background: #E6E6E6;
	border: 1px solid #D1D1D1;
	border-radius: 3px;
	-webkit-border-radius: 3px;
	
	box-shadow: 0 1px 3px 0px rgba(0,0,0,0.1);
	-webkit-box-shadow: 0 1px 3px 0px rgba(0,0,0,0.1);
}
td.dxeHelpText_ModernoNeosys {
    visibility: visible;
    color: #9F9F9F;
}
td.dxeHelpText_ModernoNeosys label {
    font-size: 0.91em;
}
td.dxeHelpText_ModernoNeosys.dxeHHelpTextSys {
    padding: 0 6px;
}
td.dxeHelpText_ModernoNeosys.dxeVHelpTextSys {
    padding: 2px 0;
}

/* InternalCheckBox */
.dxeIRBFocused_ModernoNeosys
{
    outline: 1px dotted #d1d1d1;

	*border: 1px dotted #d1d1d1;
	*margin: 0;
}
.dxeIRadioButton_ModernoNeosys
{
}
.dxeBase_ModernoNeosys .dxichCellSys,
.dxeBase_ModernoNeosys.dxichCellSys
{
    padding: 6px 3px 4px;
}
span.dxeBase_ModernoNeosys.dxichCellSys.dxeTAR label,
span[dir='rtl'].dxeBase_ModernoNeosys.dxichCellSys.dxeTAL label
{
    padding: 1px 0 2px 7px;
}
span.dxeBase_ModernoNeosys.dxichCellSys.dxeTAL label,
span[dir='rtl'].dxeBase_ModernoNeosys.dxichCellSys.dxeTAR label
{
    padding: 1px 7px 2px 0;
}
.dxeTAR.dxeBase_ModernoNeosys .dxichTextCellSys,
.dxeTAL.dxeBase_ModernoNeosys[dir='rtl'] .dxichTextCellSys
{
    padding: 5px 0 4px 4px;
}
.dxeTAL.dxeBase_ModernoNeosys .dxichTextCellSys,
.dxeTAR.dxeBase_ModernoNeosys[dir='rtl'] .dxichTextCellSys
{
    padding: 5px 4px 4px 0;
}

.dxeButtonEditSys .dxic .dxeLoadingDiv_ModernoNeosys
{
	opacity: 1!important;
	filter: alpha(opacity=100)!important;
}

.dxeButtonEditSys .dxic .dxeLoadingPanel_ModernoNeosys
{
    background-color: transparent!important;
	border: none!important;
}

.dxeButtonEditSys .dxic .dxeLoadingPanel_ModernoNeosys td.dx
{
	white-space: nowrap;
	text-align: center;
    padding: 0px!important;
}

.dxeButtonEditSys .dxic .dxeLoadingPanel_ModernoNeosys td.dx > img
{
	height: 15px;
    width: auto;
    margin-bottom: 0px!important;
    vertical-align: middle;
}

.dxeButtonEditSys .dxic .dxeLoadingPanel_ModernoNeosys td.dx > span
{
	display: none;
}

.dxeBase_ModernoNeosys
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
}
/* -- ErrorFrame -- */
.dxeErrorCell_ModernoNeosys
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: Red;
    border-color: black;
	padding-left: 8px;
	padding-right: 8px;
}
.dxeErrorCell_ModernoNeosys img
{
    margin-right: 8px;
}
*[dir='rtl'] .dxeErrorCell_ModernoNeosys img
{
    margin-right: 0;
    margin-left: 8px;
}
.dxeErrorFrame_ModernoNeosys
{
}
.dxeErrorFrameWithoutError_ModernoNeosys
{
	border: 1px solid Red;
}
.dxeErrorFrameWithoutError_ModernoNeosys .dxeControlsCell_ModernoNeosys,
.dxeErrorFrameWithoutError_ModernoNeosys.dxeControlsCell_ModernoNeosys
{
	padding: 2px;
}
.dxeEditArea_ModernoNeosys,
body input.dxeEditArea_ModernoNeosys /*Bootstrap correction*/
{
	color: #2B2B2B;
}
input[type="text"].dxeEditArea_ModernoNeosys, /*Bootstrap correction*/
input[type="password"].dxeEditArea_ModernoNeosys /*Bootstrap correction*/
{
    margin-top: 2px;
    margin-bottom: 1px;
}
@-moz-document url-prefix() {
    input[type="text"].dxeEditArea_ModernoNeosys,
    input[type="password"].dxeEditArea_ModernoNeosys
    {
        margin-top: 1px;
    }
}

/* -- Buttons -- */
.dxeButtonEditButton_ModernoNeosys,
.dxeCalendarButton_ModernoNeosys,
.dxeSpinIncButton_ModernoNeosys,
.dxeSpinDecButton_ModernoNeosys,
.dxeSpinLargeIncButton_ModernoNeosys,
.dxeSpinLargeDecButton_ModernoNeosys,
.dxeColorEditButton_ModernoNeosys
{
	vertical-align: Middle;
	cursor: pointer;
	text-align: center;
	white-space: nowrap;
	border: 1px solid transparent;
} 
.dxeButtonEditButton_ModernoNeosys
{
	padding: 6px 10px;
}
.dxeSpinLargeIncButton_ModernoNeosys,
.dxeSpinLargeDecButton_ModernoNeosys
{
	padding: 10px 14px;
}
.dxeSpinIncButton_ModernoNeosys,
.dxeSpinDecButton_ModernoNeosys
{
	padding: 5px 11px 5px;
}
.dxeCalendarButton_ModernoNeosys
{
    display: block;
    float: left;
}
.dxeCalendarButton_ModernoNeosys,
.dxeColorEditButton_ModernoNeosys
{
	border: 1px solid #c3c3c3;
	border-radius: 4px;
	background: #eaeaea;
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pg0KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIHZpZXdCb3g9IjAgMCAxIDEiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiPg0KICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQtdWNnZy1nZW5lcmF0ZWQiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjAlIiB5Mj0iMTAwJSI+DQogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI0VBRUFFQSIgc3RvcC1vcGFjaXR5PSIxIi8+DQogICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjREZERkRGIiBzdG9wLW9wYWNpdHk9IjEiLz4NCiAgPC9saW5lYXJHcmFkaWVudD4NCiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4NCjwvc3ZnPg0K);
	background: -ms-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
	background: -moz-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
	background: -o-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
	background: -webkit-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
	background: linear-gradient(to bottom, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
	-webkit-border-radius: 4px;

	box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35), 0px 1px 3px 0px rgba(0,0,0,0.1);
	-webkit-box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35), 0px 1px 3px 0px rgba(0,0,0,0.1);

	cursor: pointer;
	vertical-align: middle;
	padding: 6px 15px 7px;
	min-width: 44px;
}
/* -- Pressed -- */
.dxeCalendarButtonPressed_ModernoNeosys,
.dxeColorEditButton_ModernoNeosys:active
{
	border: 1px solid #cccccc;
	background: #dcdcdc;
	color: #a7a7a7;
}
.dxeButtonEditButtonPressed_ModernoNeosys,
.dxeSpinIncButtonPressed_ModernoNeosys,
.dxeSpinDecButtonPressed_ModernoNeosys,
.dxeSpinLargeIncButtonPressed_ModernoNeosys, 
.dxeSpinLargeDecButtonPressed_ModernoNeosys
{
	border: 1px Solid #d1d1d1;
	background: Gainsboro;

	box-shadow: inset 0px 1px 1px 0px rgba(0,0,0,0.05);
	-webkit-box-shadow: inset 0px 1px 1px 0px rgba(0,0,0,0.05);
}
/* -- Hover -- */
.dxeButtonEditButtonHover_ModernoNeosys,
.dxeSpinIncButtonHover_ModernoNeosys,
.dxeSpinDecButtonHover_ModernoNeosys,
.dxeSpinLargeIncButtonHover_ModernoNeosys,
.dxeSpinLargeDecButtonHover_ModernoNeosys
{
    color: White;
	background: #1d85cd;
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pg0KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIHZpZXdCb3g9IjAgMCAxIDEiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiPg0KICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQtdWNnZy1nZW5lcmF0ZWQiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjAlIiB5Mj0iMTAwJSI+DQogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzFEODVDRCIgc3RvcC1vcGFjaXR5PSIxIi8+DQogICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMDg2Q0IzIiBzdG9wLW9wYWNpdHk9IjEiLz4NCiAgPC9saW5lYXJHcmFkaWVudD4NCiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4NCjwvc3ZnPg0K);
	background: -ms-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -moz-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -o-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -webkit-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: linear-gradient(to bottom, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	border: 1px Solid #9FBD92;

	box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35);
	-webkit-box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35);
}
.dxeCalendarButtonHover_ModernoNeosys,
.dxeColorEditButton_ModernoNeosys:hover
{
	border: 1px solid #9FBD92;
	background: #1d85cd;
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pg0KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIHZpZXdCb3g9IjAgMCAxIDEiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiPg0KICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQtdWNnZy1nZW5lcmF0ZWQiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjAlIiB5Mj0iMTAwJSI+DQogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzFEODVDRCIgc3RvcC1vcGFjaXR5PSIxIi8+DQogICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMDg2Q0IzIiBzdG9wLW9wYWNpdHk9IjEiLz4NCiAgPC9saW5lYXJHcmFkaWVudD4NCiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4NCjwvc3ZnPg0K);
	background: -ms-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -moz-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -o-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -webkit-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: linear-gradient(to bottom, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	color: white;
}

.dxeButtonEdit_ModernoNeosys
{
	background-color: #fafafa;
	border: 1px Solid #d1d1d1;
	border-collapse: separate;
	border-spacing: 0px;

	-webkit-box-shadow: inset 0px 2px 3px 0px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0px 2px 3px 0px rgba(0, 0, 0, 0.05);

	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
}
/*SharePoint 2013 Support*/
.dxeButtonEditSys.dxeButtonEdit_ModernoNeosys
{
	border-collapse: separate;
}
.dxeButtonEdit_ModernoNeosys td.dxic 
{
	padding: 6px 2px 6px 7px!important;
}
.dxeButtonEdit_ModernoNeosys .dxeEditArea_ModernoNeosys
{
	background-color: #fafafa;
}
.dxeButtonEdit_ModernoNeosys .dxeIIC img
{
	padding-left: 3px;
}
.dxeTextBox_ModernoNeosys
{
	background-color: #fafafa;
	border: 1px Solid #d1d1d1;
	
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;

	-webkit-box-shadow: inset 0px 2px 3px 0px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0px 2px 3px 0px rgba(0, 0, 0, 0.05);
}
.dxeTextBox_ModernoNeosys td.dxic 
{
	padding: 6px 2px 6px 7px!important;
}
.dxeTextBox_ModernoNeosys .dxeEditArea_ModernoNeosys
{
	background-color: #fafafa;
}
.dxeRadioButtonList_ModernoNeosys,
.dxeCheckBoxList_ModernoNeosys
{
	font: 14px 'Helvetica Neue', 'Segoe UI', 'Droid Sans', Arial, Tahoma, Geneva, Sans-serif;
	color: #2B2B2B;
}

.dxeCheckBoxList_ModernoNeosys .dxe > table,
.dxeRadioButtonList_ModernoNeosys .dxe > table 
{
    width: 100%;
}

.dxeRadioButtonList_ModernoNeosys,
.dxeCheckBoxList_ModernoNeosys
{
	border: 1px Solid #d1d1d1;
}
.dxeRadioButtonList_ModernoNeosys td.dxe,
.dxeCheckBoxList_ModernoNeosys td.dxe
{
	padding: 9px 6px 8px 12px;
}
.dxeRadioButtonList_ModernoNeosys[dir='rtl'] td.dxe,
.dxeCheckBoxList_ModernoNeosys[dir='rtl'] td.dxe
{
	padding: 9px 12px 8px 6px;
}

/* -- Memo -- */
.dxeMemo_ModernoNeosys
{
	background-color: #fafafa;
	border: 1px Solid #d1d1d1;
	
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;

	-webkit-box-shadow: inset 0px 2px 3px 0px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0px 2px 3px 0px rgba(0, 0, 0, 0.05);
}
.hintClass td
{
    border-style: none !important;
    background-color: transparent !important;
    font-size: 12px !important;
    color: red !important;
}
.dxeMemoEditArea_ModernoNeosys
{
	background-color: #fafafa;
	outline: none;
}

/* -- Hyperlink -- */
.dxeHyperlink_ModernoNeosys
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #009C49;
	text-decoration: underline;
}
a:hover.dxeHyperlink_ModernoNeosys
{
	color: #2B2B2B;
	text-decoration: underline;
}
a:visited.dxeHyperlink_ModernoNeosys
{
	color: #969696 !important;
	text-decoration: underline;
}

/* -- ListBox -- */
.dxeListBox_ModernoNeosys
{
	background-color: #fafafa;
	border: 1px Solid #d1d1d1;
	font: 14px 'Helvetica Neue', 'Segoe UI', 'Droid Sans', Arial, Tahoma, Geneva, Sans-serif;
	height: 113px;
	width: 70px;
	color: #2b2b2b;
}
.dxeListBox_ModernoNeosys div.dxlbd
{
	padding-top: 1px;
	height: 112px;
}
.dxeListBoxItemRow_ModernoNeosys
{
	cursor: default;
}
.dxeListBoxItem_ModernoNeosys
{
	border-left: 1px Solid #fafafa;
	border-right: 1px Solid #fafafa;
	padding: 8px 6px 8px 8px;
	white-space: nowrap;
	text-align: left;
    	width: 28px;
}
.dxeListBoxItem_ModernoNeosys em
{
	background: #FFE7A2 none repeat 0 0;
	color: #333333;
	font-weight: bold;
	font-style: normal;
}
.dxeListBox_ModernoNeosys td.dxeI,
.dxeListBox_ModernoNeosys td.dxeIM,
.dxeListBox_ModernoNeosys .dxeHIC,
.dxeListBox_ModernoNeosys td.dxeFTM,
.dxeListBox_ModernoNeosys td.dxeTM,
.dxeListBox_ModernoNeosys td.dxeC,
.dxeListBox_ModernoNeosys td.dxeCM,
.dxeListBox_ModernoNeosys td.dxeHCC,
.dxeListBox_ModernoNeosys td.dxeMI,
.dxeListBox_ModernoNeosys td.dxeMIM
{
	border-right-width: 0!important;
}
.dxeListBox_ModernoNeosys td.dxeIR,
.dxeListBox_ModernoNeosys td.dxeIMR,
.dxeListBox_ModernoNeosys .dxeHICR,
.dxeListBox_ModernoNeosys td.dxeFTMR,
.dxeListBox_ModernoNeosys td.dxeTMR,
.dxeListBox_ModernoNeosys td.dxeCR,
.dxeListBox_ModernoNeosys td.dxeCMR,
.dxeListBox_ModernoNeosys td.dxeHCCR,
.dxeListBox_ModernoNeosys td.dxeMIR,
.dxeListBox_ModernoNeosys td.dxeMIMR
{
	border-left-width: 0!important;
}
.dxeListBox_ModernoNeosys td.dxeCM,
.dxeListBox_ModernoNeosys td.dxeHCC,
.dxeListBox_ModernoNeosys td.dxeCMR,
.dxeListBox_ModernoNeosys td.dxeHCCR
{
	width: 50px;
    *width: 43px;
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
}
.dxeListBox_ModernoNeosys td.dxeIM,
.dxeListBox_ModernoNeosys td.dxeIMR
{
	width: 0;
}
.dxeListBox_ModernoNeosys td.dxeC
{
	padding-right: 8px!important;
}
.dxeListBox_ModernoNeosys td.dxeCR
{
	padding-left: 3px!important;
}
.dxeListBox_ModernoNeosys td.dxeC > span,
.dxeListBox_ModernoNeosys td.dxeCM > span
{
    margin: 2px 2px 2px 3px;
}
.dxeListBox_ModernoNeosys td.dxeCR > span,
.dxeListBox_ModernoNeosys td.dxeCMR > span
{
    margin: 2px 3px 2px 2px;
}
.dxeListBox_ModernoNeosys td.dxeT
{
	width: 100%;
	padding-left: 0!important;
}
.dxeListBox_ModernoNeosys td.dxeTR
{
	width: 100%;
	padding-right: 0!important;
}
.dxeListBox_ModernoNeosys td.dxeT,
.dxeListBox_ModernoNeosys td.dxeMI
{
	border-left-width: 0!important;
}
.dxeListBox_ModernoNeosys td.dxeTR,
.dxeListBox_ModernoNeosys td.dxeMIR
{
	border-right-width: 0!important;
}
.dxeListBox_ModernoNeosys td.dxeFTM,
.dxeListBox_ModernoNeosys td.dxeTM,
.dxeListBox_ModernoNeosys td.dxeLTM,
.dxeListBox_ModernoNeosys .dxeHFC,
.dxeListBox_ModernoNeosys .dxeHC,
.dxeListBox_ModernoNeosys .dxeHLC,
.dxeListBox_ModernoNeosys td.dxeFTMR,
.dxeListBox_ModernoNeosys td.dxeTMR,
.dxeListBox_ModernoNeosys td.dxeLTMR,
.dxeListBox_ModernoNeosys .dxeHFCR,
.dxeListBox_ModernoNeosys .dxeHCR,
.dxeListBox_ModernoNeosys .dxeHLCR
{
	overflow: hidden;
}
.dxeListBox_ModernoNeosys td.dxeFTM,
.dxeListBox_ModernoNeosys td.dxeTM,
.dxeListBox_ModernoNeosys .dxeHFC,
.dxeListBox_ModernoNeosys .dxeHC
{
	padding-right: 6px!important;
}
.dxeListBox_ModernoNeosys td.dxeFTMR,
.dxeListBox_ModernoNeosys td.dxeTMR,
.dxeListBox_ModernoNeosys .dxeHFCR,
.dxeListBox_ModernoNeosys .dxeHCR
{
	padding-left: 6px!important;
}
.dxeListBox_ModernoNeosys td.dxeLTM,
.dxeListBox_ModernoNeosys td.dxeTM, 
.dxeListBox_ModernoNeosys .dxeHC,
.dxeListBox_ModernoNeosys .dxeHLC
{
	padding-left: 6px!important;
}
.dxeListBox_ModernoNeosys td.dxeLTMR,
.dxeListBox_ModernoNeosys td.dxeTMR, 
.dxeListBox_ModernoNeosys .dxeHCR,
.dxeListBox_ModernoNeosys .dxeHLCR
{
	padding-right: 6px!important;
}
/*Grid lines*/
.dxeListBox_ModernoNeosys td.dxeTM,
.dxeListBox_ModernoNeosys td.dxeLTM,
.dxeListBox_ModernoNeosys td.dxeMIM
{
	border-left: 1px solid #d1d1d1!important;
}
.dxeListBox_ModernoNeosys td.dxeTMR,
.dxeListBox_ModernoNeosys td.dxeLTMR,
.dxeListBox_ModernoNeosys td.dxeMIMR
{
	border-right: 1px solid #d1d1d1!important;
}
.dxeListBox_ModernoNeosys td.dxeIM,
.dxeListBox_ModernoNeosys td.dxeFTM,
.dxeListBox_ModernoNeosys td.dxeTM,
.dxeListBox_ModernoNeosys td.dxeLTM,
.dxeListBox_ModernoNeosys td.dxeCM,
.dxeListBox_ModernoNeosys td.dxeMIM,
.dxeListBox_ModernoNeosys td.dxeIMR,
.dxeListBox_ModernoNeosys td.dxeFTMR,
.dxeListBox_ModernoNeosys td.dxeTMR,
.dxeListBox_ModernoNeosys td.dxeLTMR,
.dxeListBox_ModernoNeosys td.dxeCMR,
.dxeListBox_ModernoNeosys td.dxeMIMR
{
	border-bottom: 1px solid #d1d1d1;
}

.dxeListBoxItemSelected_ModernoNeosys     
{
	background-color: #dcdcdc;
}
.dxeListBoxItemHover_ModernoNeosys        
{
	background-color: #009C49;
	color: white;
}
.dxeListBoxItemSelected_ModernoNeosys em,
.dxeListBoxItemHover_ModernoNeosys em
{
	background-image: none;
}

/*Header*/
.dxeListBox_ModernoNeosys .dxeHD
{
	background: #fafafa;
	border-bottom: 1px solid #d1d1d1;
}
.dxeListBox_ModernoNeosys .dxeHFC,
.dxeListBox_ModernoNeosys .dxeHIC,
.dxeListBox_ModernoNeosys .dxeHC,
.dxeListBox_ModernoNeosys .dxeHLC, 
.dxeListBox_ModernoNeosys td.dxeHMIC,
.dxeListBox_ModernoNeosys .dxeHFCR,
.dxeListBox_ModernoNeosys .dxeHICR,
.dxeListBox_ModernoNeosys .dxeHCR,
.dxeListBox_ModernoNeosys .dxeHLCR, 
.dxeListBox_ModernoNeosys td.dxeHMICR
{
	color: #283B56;
	border-bottom-width: 0;
	padding-top: 9px;
	padding-bottom: 8px;
}
.dxeListBox_ModernoNeosys .dxeHC,
.dxeListBox_ModernoNeosys .dxeHLC,
.dxeListBox_ModernoNeosys td.dxeHMIC
{
	border-left: 1px solid #d1d1d1;
}
.dxeListBox_ModernoNeosys .dxeHCR,
.dxeListBox_ModernoNeosys .dxeHLCR,
.dxeListBox_ModernoNeosys td.dxeHMICR
{
	border-right: 1px solid #d1d1d1;
	text-align: right;
}
.dxeListBox_ModernoNeosys .dxeHIC
{
	border-left: 1px solid #d1d1d1;
}
.dxeListBox_ModernoNeosys .dxeHICR,
.dxeListBox_ModernoNeosys .dxeHFCR,
.dxeListBox_ModernoNeosys .dxeHCCR
{
	border-right: 1px solid #d1d1d1;
	text-align: right;
}
.dxeListBox_ModernoNeosys .dxeHFC,
.dxeListBox_ModernoNeosys .dxeHC,
.dxeListBox_ModernoNeosys .dxeHMIC
{
	border-right-width: 0;
}
.dxeListBox_ModernoNeosys .dxeHFCR,
.dxeListBox_ModernoNeosys .dxeHCR,
.dxeListBox_ModernoNeosys .dxeHMICR
{
	border-left-width: 0;
	text-align: right;
}
.dxeListBox_ModernoNeosys .dxeHLC
{
	border-right: 1px solid #d1d1d1;
}
.dxeListBox_ModernoNeosys .dxeHLCR
{
	border-left: 1px solid #d1d1d1;
	text-align: right;
}

/* -- Calendar -- */
.dxeCalendar_ModernoNeosys
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
	border: 1px solid #d1d1d1;
	background-color: #FFFFFF;
	font-weight: normal;
}
.dxeCalendar_ModernoNeosys td.dxMonthGrid
{
	padding: 3px 22px 6px 21px;
	cursor: default;
	background-color: #FFFFFF;
}
.dxeCalendar_ModernoNeosys td.dxMonthGridWithWeekNumbers
{
	padding: 3px 19px 6px 7px;
	cursor: default;
}
.dxeCalendar_ModernoNeosys td.dxMonthGridWithWeekNumbersRtl
{
	padding: 3px 7px 6px 19px;
	cursor: default;
	background-color: #FFFFFF;
}
.dxeCalendarDayHeader_ModernoNeosys
{
	color: #009C49;
	padding: 3px 5px 6px 6px;
}
.dxeCalendarWeekNumber_ModernoNeosys
{
	font-size: 0.79em;
	text-align: right;
	padding: 3px 3px 2px 4px;
	color: #2b2b2b;
}
.dxeCalendarDay_ModernoNeosys
{
	text-align: center;
	padding: 2px 11px 3px;
}
.dxeCalendarWeekend_ModernoNeosys        
{
	color: #c00000;
}
.dxeCalendarOtherMonth_ModernoNeosys     
{
	color: #959595;
}
.dxeCalendarOutOfRange_ModernoNeosys     
{
	color: #d0d0d0;
}
.dxeCalendarDayDisabled_ModernoNeosys {
	background-color: #fcfcfc;
	color: #ececec!important;
}
.dxeCalendarToday_ModernoNeosys         
{
	border: 1px solid #009C49;
    padding: 1px 10px 2px;
}
.dxeCalendarSelected_ModernoNeosys       
{
	background-color: #dcdcdc;
    color: #2B2B2B;
}
.dxeCalendarHeader_ModernoNeosys
{
	background: #fafafa;
	border-bottom: 1px solid #d1d1d1;
	padding: 10px 6px;
	margin: 10px;
}
.dxeCalendarHeader_ModernoNeosys td.dxe
{
	color: #009C49;
	text-align: center;
	cursor: pointer;
}
.dxeCalendarHeader_ModernoNeosys .dxeCHS
{
    min-width: 1px;
    *width: 1px;
}
.dxeCalendarFooter_ModernoNeosys
{
	background: #fafafa;
	border-top: 1px solid #d1d1d1;

	padding: 10px 0px;
}
.dxeCalendarFooter_ModernoNeosys .dxeCFS
{
    min-width: 10px;
    *width: 10px;
}
.dxeCalendarFastNav_ModernoNeosys
{
	color: #2B2B2B;
	background: White;
	border: 1px Solid #d1d1d1;
	border-bottom: 0px Solid #d1d1d1;
	padding: 12px 8px;
}
.dxeCalendarFastNavMonthArea_ModernoNeosys
{
	padding: 0px 12px;
}
.dxeCalendarFastNavYearArea_ModernoNeosys
{
    padding-top: 8px;
}
.dxeCalendarFastNavFooter_ModernoNeosys
{
	border: 1px Solid #d1d1d1;
	background: White;

	padding: 8px 0px;
}
.dxeCalendarFastNavFooter_ModernoNeosys .dxeCFNFS
{
    min-width: 10px;
    *width: 10px;
}
.dxeCalendarFastNavMonth_ModernoNeosys,
.dxeCalendarFastNavYear_ModernoNeosys
{
	padding: 3px 4px 4px;
	text-align: center;
	cursor: pointer;
	border: 1px Solid White;
}
.dxeCalendarFastNavMonthSelected_ModernoNeosys,
.dxeCalendarFastNavYearSelected_ModernoNeosys
{
	background-color: #dcdcdc;
}

.dxeCalendarFastNavMonthHover_ModernoNeosys,
.dxeCalendarFastNavYearHover_ModernoNeosys
{
	color: white;
	background: #009C49;
}
.dxeDateEditTimeEditCell_ModernoNeosys
{
    background-color: #FFFFFF;
    padding: 4px 32px 14px;
    width: 120px;
}
.dxeDateEditClockCell_ModernoNeosys
{
    background-color: #FFFFFF;
    padding: 9px 37px 0;
}
.dxeCalendarFooter_ModernoNeosys.dxeDETSF
{
    padding-left: 11px;
    padding-right: 11px;
}
/* Disabled */
.dxeDisabled_ModernoNeosys,
.dxeDisabled_ModernoNeosys td.dxe
{
	color: #9f9f9f;
	cursor: default;
}
.dxeEditArea_ModernoNeosys.dxeDisabled_ModernoNeosys
{
	color: #9f9f9f;
}
a.dxeDisabled_ModernoNeosys, a.dxeDisabled_ModernoNeosys:hover
{
	color: #A6A6A6;
}
.dxeButtonDisabled_ModernoNeosys
{
	cursor: default;
}
/* -- Button -- */
.dxbButton_ModernoNeosys {
	background: none;
	color: white;
	background-color: #db0243;
	border-color: #db0243;
	-webkit-border-radius: 4px;
	-webkit-background-clip: padding-box;
	-moz-border-radius: 4px;
	-moz-background-clip: padding;
	border-radius: 4px;
	background-clip: padding-box;
	-webkit-box-shadow: 0;
	-moz-box-shadow: 0;
	box-shadow: 0;
	display: inline-block !important;
	padding: 6px 12px;
}
.dxbButtonHover_ModernoNeosys
{
	border: 1px Solid #9FBD92;
	color: white;
	background: #1d85cd;
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pg0KPHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIHZpZXdCb3g9IjAgMCAxIDEiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiPg0KICA8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQtdWNnZy1nZW5lcmF0ZWQiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjAlIiB5Mj0iMTAwJSI+DQogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzFEODVDRCIgc3RvcC1vcGFjaXR5PSIxIi8+DQogICAgPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMDg2Q0IzIiBzdG9wLW9wYWNpdHk9IjEiLz4NCiAgPC9saW5lYXJHcmFkaWVudD4NCiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4NCjwvc3ZnPg0K);
	background: -ms-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -moz-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -o-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: -webkit-linear-gradient(top, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);
	background: linear-gradient(to bottom, rgba(196,229,182,1) 0%, rgba(168,201,154,1) 100%);

	box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.15), 0px 1px 3px 0px rgba(0,0,0,0.1);
	-webkit-box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.15), 0px 1px 3px 0px rgba(0,0,0,0.1);
}
.dxbButtonChecked_ModernoNeosys
{
	color: #2B2B2B!important;
	background: #dcdcdc;
	border: 1px solid #cccccc;

	box-shadow: inset 0px 1px 1px 0px rgba(0,0,0,0.05);
	-webkit-box-shadow: inset 0px 1px 1px 0px rgba(0,0,0,0.05);
}
.dxbButtonPressed_ModernoNeosys
{
	color: #a7a7a7;
	background: #dcdcdc;
	border: 1px solid #cccccc;

	box-shadow: inset 0px 1px 1px 0px rgba(0,0,0,0.05);
	-webkit-box-shadow: inset 0px 1px 1px 0px rgba(0,0,0,0.05);
}
.dxbButton_ModernoNeosys div.dxb
{
	padding: 1px 4px 2px;
	border: 1px dotted transparent;
}
.dxbButton_ModernoNeosys div.dxbf
{
	border: 1px dotted #2C4D79;
}
/* Button Link */
a.dxbButton_ModernoNeosys
{
    border-radius: 0;
    box-shadow: none;

    color: #009C49;
    text-decoration: underline;
}
a.dxbButton_ModernoNeosys:hover
{
	color: #2B2B2B;
}
a.dxbButton_ModernoNeosys:visited
{
	color: #A1A1A1;
}
/* Disabled */
.dxbDisabled_ModernoNeosys
{
	background: #e7e7e7;
	border-color: #d3d3d3;
}
.dxbDisabled_ModernoNeosys,
a.dxbButton_ModernoNeosys.dxbDisabled_ModernoNeosys
{
	color: #C3C3C3;
	cursor: default;
    text-decoration: none;
}
/* -- FilterControl -- */
.dxfcControl_ModernoNeosys
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
}
.dxfcTable_ModernoNeosys
{
	border-collapse: separate!important;
}
.dxfcTable_ModernoNeosys td.dxfc
{
    line-height: 36px;
	padding: 0px 0px 0px 3px;
}
a.dxfcPropertyName_ModernoNeosys
{
	white-space: nowrap!important;
	padding: 2px 4px!important;
	color: Blue!important;
}
a.dxfcGroupType_ModernoNeosys
{
	white-space: nowrap!important;
	padding: 2px 4px!important;
	color: Red!important;
}
a.dxfcOperation_ModernoNeosys
{
	white-space: nowrap!important;
	padding: 2px 4px!important;
	color: Green!important;
}
a.dxfcValue_ModernoNeosys
{
	white-space: nowrap!important;
	padding: 2px 4px!important;
	color: Gray!important;
}
.dxfcImageButton_ModernoNeosys
{
	cursor: pointer;
}

.dxeMaskHint_ModernoNeosys
{
	background: #ffffe1 none;
	border: 1px solid black;
	padding: 2px 5px 3px;
	color: Black;
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
}

/* -- ProgressBar -- */
.dxeProgressBar_ModernoNeosys
{
	background-color: #fafafa;
	border: 1px Solid #d1d1d1;
	
	-webkit-box-shadow: inset 0px 2px 3px 0px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0px 2px 3px 0px rgba(0, 0, 0, 0.05);

	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #9f9f9f;

    height: 36px;
}
.dxeProgressBar_ModernoNeosys .dxePBMainCell
{
	padding: 5px;
}
.dxeProgressBar_ModernoNeosys td.dxe
{
    background:  url('edtProgressPositionBack.png') no-repeat 50% center;
	padding: 2px 0;
}
.dxeProgressBarIndicator_ModernoNeosys
{
	background: #009C49;
}

/* -- DropDownWindow -- */
.dxpcDropDown_ModernoNeosys,
.dxeDropDownWindow_ModernoNeosys
{
    font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
}
.dxeDropDownWindow_ModernoNeosys
{
	background-color: White;
	border: 1px Solid #d1d1d1;
}
.dxeDropDownWindow_ModernoNeosys .dxgvTable_ModernoNeosys
{
	border-left: 0;
	border-right: 0;
}
.dxeDropDownWindow_ModernoNeosys .dxgvHeader_ModernoNeosys
{
	border-top: 0!important; 
}
.dxpcDropDown_ModernoNeosys .dxpc-shadow
{
	-moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

/*----------------- ColorTable -----------------*/
.dxeColorIndicator_ModernoNeosys
{
	width: 22px;
	height: 22px;
	margin: 6px 2px 6px 6px;
	cursor: pointer;
}
.dxeItemPicker_ModernoNeosys
{
	background-color: #F9F9F9;
	border: 1px solid #A3C0E8;
}
.dxeColorTable_ModernoNeosys td.dx,
.dxeItemPicker_ModernoNeosys td.dx
{
	padding: 4px;
}
.dxeColorTableCell_ModernoNeosys,
.dxeItemPickerCell_ModernoNeosys
{
	padding: 3px;
	cursor: pointer;
}
.dxeColorTableCellDiv_ModernoNeosys
{
	width: 22px;
	height: 22px;
	font-size: 0;
}
.dxeColorTableCellDiv_ModernoNeosys[style*="rgb(255, 255, 255)"],
.dxeColorTableCellDiv_ModernoNeosys:not([style*="background-color"])
{
    border: 1px solid #f0f0f0;
	width: 20px;
	height: 20px;
}
.dxeColorTableCellSelected_ModernoNeosys
{
	padding: 2px!important;
	border: 1px solid #dcdcdc;
	background: #dcdcdc;
}
.dxeColorTableCellHover_ModernoNeosys,
.dxeItemPickerCellHover_ModernoNeosys
{
	padding: 2px!important;
}
.dxeColorTableCellHover_ModernoNeosys
{
	border: 1px solid #d9d9d9;
	background: #e5e5e5;
    background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2VhZWFlYSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNkZmRmZGYiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
	background: -ms-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
	background: -moz-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
	background: -o-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
	background: -webkit-linear-gradient(top, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);
	background: linear-gradient(to bottom, rgba(234,234,234,1) 0%, rgba(223,223,223,1) 100%);

	box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35);
	-webkit-box-shadow: inset 0px 1px 0px 0px rgba(255,255,255,0.35);
}
.dxeItemPickerCellHover_ModernoNeosys
{
	background-color: #009C49;
	border: 1px solid #9FBD92;
}

/* -- Invalid Style -- */
.dxeInvalid_ModernoNeosys
{
}
.dxeInvalid_ModernoNeosys .dxeEditArea_ModernoNeosys,
.dxeInvalid_ModernoNeosys .dxeMemoEditArea_ModernoNeosys
{
}

/* -- Focused Style -- */
.dxeFocused_ModernoNeosys
{
    border: 1px solid #9FBD92;

	box-shadow: 0px 0px 2px 0px rgba(159,189,146,1);
	-webkit-box-shadow: 0px 0px 2px 0px rgba(159,189,146,1);
}

.dxeFocused_ModernoNeosys.dxeTrackBar_ModernoNeosys
{	
	border: 0px;

	box-shadow: none;
	-webkit-box-shadow: none;
}

/* -- Null Text Style -- */
.dxeNullText_ModernoNeosys .dxeEditArea_ModernoNeosys,
.dxeNullText_ModernoNeosys .dxeMemoEditArea_ModernoNeosys
{
	color: #a6a6a6;
}

/* -- Captcha -- */
.dxcaControl_ModernoNeosys 
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
}
.dxcaRefreshButton_ModernoNeosys
{
	text-decoration: none;
	color: #2B2B2B;
}
.dxcaDisabledRefreshButton_ModernoNeosys
{
	color: #A6A6A6;
}
.dxcaRefreshButtonCell_ModernoNeosys
{
    padding: 8px 0 10px!important;
}
.dxcaRefreshButtonCell_ModernoNeosys img
{
    margin-right: 8px!important;
}
.dxcaRefreshButtonText_ModernoNeosys
{
    color: #2B2B2B;
	text-decoration: none;
}
.dxcaDisabledRefreshButtonText_ModernoNeosys
{
	text-decoration: none;
}

.dxcaTextBoxCell_ModernoNeosys,
.dxcaTextBoxCellNoIndent_ModernoNeosys
{
	color: #2B2B2B;
}
.dxcaTextBoxCellNoIndent_ModernoNeosys .dxeErrorCell_ModernoNeosys
{
	padding-left: 0px;
	padding-top: 4px;
	color: Red;
}
.dxcaTextBoxLabel_ModernoNeosys
{
	padding-bottom: 4px;
	display: block;
}

.dxcaLoadingPanel_ModernoNeosys
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
	background-color: White;
	border: 1px solid #cfcfcf;

	box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.1);
	-webkit-box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.1);
}
.dxcaLoadingPanel_ModernoNeosys td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 15px 20px;
}
.dxcaLoadingPanel_ModernoNeosys .dxlp-loadingImage 
{
	margin:0px;
}
.dxpcLite_ModernoNeosys .dxHFBPS
{
    width: 10px;
}

.dxeTextBox_ModernoNeosys,
.dxeButtonEdit_ModernoNeosys,
.dxeIRadioButton_ModernoNeosys,
.dxeRadioButtonList_ModernoNeosys,
.dxeCheckBoxList_ModernoNeosys
{
    cursor: default;
}

/* Removes flicking in iOS Safari*/
.dxeTrackBar_ModernoNeosys, 
.dxeIRadioButton_ModernoNeosys, 
.dxeButtonEdit_ModernoNeosys, 
.dxeTextBox_ModernoNeosys, 
.dxeRadioButtonList_ModernoNeosys, 
.dxeCheckBoxList_ModernoNeosys, 
.dxeMemo_ModernoNeosys, 
.dxeListBox_ModernoNeosys, 
.dxeCalendar_ModernoNeosys, 
.dxeColorTable_ModernoNeosys
{
	-webkit-tap-highlight-color: rgba(0,0,0,0);
}
/* ASPxCololrEdit */
.dxcpParametersCell_ModernoNeosys input
{
	width: 71px;
	height: 24px;
    line-height: 24px;
    padding: 3px 0px 3px 5px;
	background: #fafafa;
}
.dxcpParametersCellSys .dxcpWebColorInput_ModernoNeosys
{
	float: right;
	margin-top: 9px;
	*margin-top: 3px;
}
.dxcpColorParameterMainDiv_ModernoNeosys
{
	padding: 0px 0 4px 9px;
	width: 95px;
	text-align: right;
}
.dxeCustomColorButton_ModernoNeosys
{
    padding-top: 6px;
    padding-bottom: 6px;
	margin: 3px 7px;
	cursor: pointer;
}
.dxcpCurrentColor_ModernoNeosys, 
.dxcpSavedColor_ModernoNeosys
{
	height: 35px;
}
.dxcpCurrentAndSaved_ModernoNeosys
{
	width: 76px;
	margin: 1px 0px 0px auto;
	cursor: pointer;
}
.dxeColorTablesMainDiv_ModernoNeosys,
.dxeColorSelectorMainDiv_ModernoNeosys
{
	background-color: #FFFFFF;
}
.dxeColorSelectorMainDiv_ModernoNeosys
{
	padding: 7px;
}
.dxcpColorArea_ModernoNeosys,
.dxcpHueAreaImage_ModernoNeosys,
.dxcpCurrentAndSaved_ModernoNeosys,
.dxeColorTablesMainDiv_ModernoNeosys,
.dxeColorSelectorMainDiv_ModernoNeosys,
.dxcpParametersCell_ModernoNeosys input
{
	border: 1px Solid #d1d1d1;
}
.dxeButtonsPanelDiv_ModernoNeosys
{
	margin-top: 15px;
	text-align: right;
}
.dxeButtonsPanelDiv_ModernoNeosys input
{
	min-width: 68px;
	margin-left: 10px;
}

/* -- ASPxTokenBox -- */
input.dxeTokenBoxInput_ModernoNeosys[type="text"] {
	display: inline-block;
	float: left;
	width: 30px;
	outline: none;
	padding: 6px 2px 4px 2px;
	*padding: 6px 2px 6px 2px;
}
.dxeToken_ModernoNeosys {
    display: inline-block;
	color: #ffffff;
    margin: 3px 3px 0px 0px;
	*margin: 1px 1px 0px 0px;
    padding: 0px 0px 0px 0px;
	*padding-bottom: 1px;
	float: left;
	min-width: 30px;
}
.dxeTokenText_ModernoNeosys, .dxeTokenRemoveButton_ModernoNeosys {
	display: block;
	float: left;
}
.dxeTokenText_ModernoNeosys {
	padding: 5px 7px 5px 5px;
	*padding: 5px 7px 5px 5px;
	overflow: hidden;
	text-overflow: ellipsis;
}
.dxeTokenRemoveButton_ModernoNeosys {
	cursor: pointer;
	margin-bottom: -1px;
}
.dxeToken_ModernoNeosys {
	background-color: #009C49;
}

.dxeButtonEdit_ModernoNeosys td.dxictb {
    padding: 0px 0px 3px 3px !important;
}

.dxeButtonEdit_ModernoNeosys td.dxictb {
    *padding: 0px 0px 1px 1px !important;
}
@-moz-document url-prefix() {
	.dxeTokenText_ModernoNeosys {
		padding: 4px 7px 0px 5px;
	}
}

/* Editor caption */
.dxeCaptionCell_ModernoNeosys
{
    font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
    white-space: nowrap;
    line-height: 16px;
    height: 100%;
}
.dxeRoot_ModernoNeosys
{
	margin-left: 0px !important;
}
.dxeCaption_ModernoNeosys
{
}
.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeTextEditCTypeSys
{
    padding-top: 10px;
}
.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeCheckBoxCTypeSys
{
    padding-top: 10px;
}
.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeRadioButtonCTypeSys
{
    padding-top: 11px;
}
.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeCheckBoxListCTypeSys
{
    padding-top: 18px;
}
.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeRadioButtonListCTypeSys
{
    padding-top: 18px;
}
.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeListBoxCTypeSys
{
    padding-top: 11px;
}
.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeTrackBarCTypeSys
{
    padding-top: 16px;
}
.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeProgressBarCTypeSys
{
    padding-top: 10px;
}
.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeMemoCTypeSys
{
    padding-top: 5px;
}
.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeCustomCTypeSys
{
    padding-top: 5px;
    padding-bottom: 5px;
}

/* IE 7 */
.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeTextEditCTypeSys
{
    *padding-top: 8px;
}
.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeCheckBoxCTypeSys
{
    *padding-top: 11px;
}
.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeRadioButtonCTypeSys
{
    *padding-top: 12px;
}
.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeCheckBoxListCTypeSys
{
    *padding-top: 19px;
}
.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeRadioButtonListCTypeSys
{
    *padding-top: 19px;
}
.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeListBoxCTypeSys
{
    *padding-top: 12px;
}
.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeProgressBarCTypeSys
{
    *padding-top: 11px;
}
.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeMemoCTypeSys
{
    *padding-top: 2px;
}

/* Firefox */
@-moz-document url-prefix() {
	.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeCheckBoxCTypeSys
    {
        padding-top: 11px;
    }
    .dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeRadioButtonCTypeSys
    {
        padding-top: 12px;
    }
    .dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeCheckBoxListCTypeSys
    {
        padding-top: 19px;
    }
    .dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeRadioButtonListCTypeSys
    {
        padding-top: 19px;
    }
    .dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeListBoxCTypeSys
    {
        padding-top: 12px;
    }
}

/* Chrome */
@media screen and (-webkit-min-device-pixel-ratio:0) {
    .dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeTextEditCTypeSys
    {
        padding-top: 11px;
    }
    .dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeCheckBoxCTypeSys
    {
        padding-top: 11px;
    }
    .dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeRadioButtonCTypeSys
    {
        padding-top: 12px;
    }
    .dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeCheckBoxListCTypeSys
    {
        padding-top: 19px;
    }
    .dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeRadioButtonListCTypeSys
    {
        padding-top: 19px;
    }
    .dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeListBoxCTypeSys
    {
        padding-top: 12px;
    }
    .dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeMemoCTypeSys
    {
        padding-top: 6px;
    }
}

/* Safari */
.dxeCaptionCellSafariSys.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeTextEditCTypeSys
{
    padding-top: 10px;
}
.dxeCaptionCellSafariSys.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeCheckBoxCTypeSys
{
    padding-top: 10px;
}
.dxeCaptionCellSafariSys.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeRadioButtonCTypeSys
{
    padding-top: 11px;
}
.dxeCaptionCellSafariSys.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeCheckBoxListCTypeSys
{
    padding-top: 18px;
}
.dxeCaptionCellSafariSys.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeRadioButtonListCTypeSys
{
    padding-top: 18px;
}
.dxeCaptionCellSafariSys.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeListBoxCTypeSys
{
    padding-top: 11px;
}
.dxeCaptionCellSafariSys.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeMemoCTypeSys
{
    padding-top: 5px;
}
.dxeCaptionCellSafariSys.dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeProgressBarCTypeSys
{
    padding-top: 9px;
}

/* Opera */
noindex:-o-prefocus, body:first-of-type .dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeTextEditCTypeSys
{
    padding-top: 11px;
}
noindex:-o-prefocus, body:first-of-type .dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeCheckBoxCTypeSys
{
    padding-top: 11px;
}
noindex:-o-prefocus, body:first-of-type .dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeRadioButtonCTypeSys
{
    padding-top: 12px;
}
noindex:-o-prefocus, body:first-of-type .dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeCheckBoxListCTypeSys
{
    padding-top: 19px;
}
noindex:-o-prefocus, body:first-of-type .dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeRadioButtonListCTypeSys
{
    padding-top: 19px;
}
noindex:-o-prefocus, body:first-of-type .dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeListBoxCTypeSys
{
    padding-top: 12px;
}
noindex:-o-prefocus, body:first-of-type .dxeCaptionCell_ModernoNeosys.dxeCaptionVATSys.dxeMemoCTypeSys
{
    padding-top: 6px;
}

.dxeRequiredMark_ModernoNeosys {
    color: green;
    font-style: normal;
}

.dxeOptionalMark_ModernoNeosys {
    color: gray;
    font-style: normal;
}
/*Loading Panel*/
.dxlpLoadingPanel_ModernoNeosys,
.dxlpLoadingPanelWithContent_ModernoNeosys
{
	font: 14px 'Segoe UI','Helvetica Neue','Droid Sans',Arial,Tahoma,Geneva,Sans-serif;
	color: #2B2B2B;
	background-color: White;
	border: 1px solid #cfcfcf;
	box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.1);
	-webkit-box-shadow: 0px 1px 3px 0px rgba(0,0,0,0.1);
}
.dxlpLoadingPanel_ModernoNeosys td.dx,
.dxlpLoadingPanelWithContent_ModernoNeosys td.dx
{
	white-space: nowrap;
	text-align: center;
	padding: 15px 34px 15px 20px;
}
.dxlpLoadingPanel_ModernoNeosys .dxlp-loadingImage,
.dxlpLoadingPanelWithContent_ModernoNeosys .dxlp-loadingImage, 
.dxcaLoadingPanel_ModernoNeosys .dxlp-loadingImage
{
	background-image: url('../Web/Loading.gif');
	height: 40px;
	width: 40px;
}
