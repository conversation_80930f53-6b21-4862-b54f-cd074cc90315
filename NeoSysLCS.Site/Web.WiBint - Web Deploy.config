<?xml version="1.0" encoding="utf-8"?>

<!-- For more information on using web.config transformation visit https://go.microsoft.com/fwlink/?LinkId=125889 -->

<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
	<!--
    In the example below, the "SetAttributes" transform will change the value of 
    "connectionString" to use "ReleaseSQLServer" only when the "Match" locator 
    finds an attribute "name" that has a value of "MyDB".
    
    <connectionStrings>
      <add name="MyDB" 
        connectionString="Data Source=ReleaseSQLServer;Initial Catalog=MyReleaseDB;Integrated Security=True" 
        xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
    </connectionStrings>
  -->
	<system.web>
		<compilation xdt:Transform="RemoveAttributes(debug)" />
		<!--
      In the example below, the "Replace" transform will replace the entire 
      <customErrors> section of your web.config file.
      Note that because there is only one customErrors section under the 
      <system.web> node, there is no need to use the "xdt:Locator" attribute.
      
      <customErrors defaultRedirect="GenericError.htm"
        mode="RemoteOnly" xdt:Transform="Replace">
        <error statusCode="500" redirect="InternalError.htm"/>
      </customErrors>
    -->
	</system.web>
	<appSettings>
		<add key="Environment" value="SxTest" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
	</appSettings>
	<appSettings>
		<add key="FilePath" value="http://198.168.111.79/Upload/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
		<add key="CortecAPI" value="https://office36.i2k.ch/boot/GlobalApplications/MandantSpezifisch/208/Powerbi/exportPMListeLexplus.asp?cortecKey=75f35117b69cacd9003aec7043951663" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
		<add key="CortecTaskAPI" value="Placeholder" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
		<add key="CortecTaskAPIKey" value="Placeholder" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
		<add key="CortecTaskCreationEnabled" value="false" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
		<add key="AvoidCortecTaskDuplicates" value="false" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
	</appSettings>
	<system.net>
		<mailSettings>
			<smtp deliveryMethod="network" xdt:Transform="Replace">
				<network host ="localhost" port="25" />
			</smtp>
		</mailSettings>
		<connectionManagement>
			<add address="*" maxconnection="200" />
		</connectionManagement>
	</system.net>
</configuration>