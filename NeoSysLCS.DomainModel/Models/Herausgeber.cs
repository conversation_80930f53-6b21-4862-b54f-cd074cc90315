using System;
using System.Collections.Generic;

namespace NeoSysLCS.DomainModel.Models
{
    public partial class Herausgeber : BaseModel
    {
        public Herausgeber()
        {
            this.Erlasse = new List<Erlass>();
            this.Standorte = new List<Standort>();
        }

        public int HerausgeberID { get; set; }
        public virtual ICollection<Standort> Standorte { get; set; }
        public virtual ICollection<Erlass> Erlasse { get; set; }
        public string NameDE { get; set; }
        public string NameFR { get; set; }
        public string NameIT { get; set; }
        public string NameEN { get; set; }
        public int? StandortID { get; set; }
    }
}
