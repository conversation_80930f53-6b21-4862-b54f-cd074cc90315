using System.ComponentModel.DataAnnotations;

namespace NeoSysLCS.DomainModel.Models
{
    public enum KundendokumentErfuellung
    {
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_KundendokumentErfuellung_No")]
        No = 0,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_KundendokumentErfuellung_Yes")]
        Yes = 1,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_KundendokumentErfuellung_UnderClarification")]
        UnderClarification = 2,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_KundendokumentErfuellung_NotRelevant")]
        NotRelevant = 3,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_KundendokumentErfuellung_NotDefined")]
        NotDefined = 4,
        [Display(ResourceType = typeof(Resources.Properties.Resources), Name = "Enum_KundendokumentErfuellung_NotEdited")]
        NotEdited = 5,
    }
}