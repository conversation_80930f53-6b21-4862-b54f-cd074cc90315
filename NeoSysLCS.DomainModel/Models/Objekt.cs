using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Drawing;
using System.Linq.Expressions;
using System.Reflection;

namespace NeoSysLCS.DomainModel.Models
{
    public class Objekt : BaseModel
    {
        public Objekt()
        {
            Forderungsversionen = new List<Forderungsversion>();
            Pflichten = new List<Pflicht>();
            Kommentare = new List<Kommentar>();
        }

        public int ObjektID { get; set; }
        public int ObjektkategorieID { get; set; }
        public Objektkategorie Objektkategorien { get; set; }
        public Boolean? Freigabe { get; set; }
        [StringLength(128)]
        public string FreigabeVon { get; set; }
        public DateTime? FreigabeAm { get; set; }
        public Boolean? QsFreigabe { get; set; }
        [StringLength(128)]
        public string QsFreigabeVon { get; set; }
        public DateTime? QsFreigabeAm { get; set; }
        public ICollection<Forderungsversion> Forderungsversionen { get; set; }
        public ICollection<Pflicht> Pflichten { get; set; }
        public ICollection<Kommentar> Kommentare { get; set; }
        [Column(TypeName = "xml")]
        public string Uebersetzung { get; set; }

        public void SetFreigabe(bool freigabe, string userID = null)
        {
            if (freigabe)
            {
                Freigabe = true;
                if (!FreigabeAm.HasValue)
                {
                    FreigabeAm = DateTime.Now;
                }
                if (string.IsNullOrEmpty(FreigabeVon))
                {
                    FreigabeVon = userID;
                }
            }
            else
            {
                Freigabe = null;
                FreigabeAm = null;
                FreigabeVon = null;
            }
        }

        public void SetQsFreigabe(bool freigabe, string userID = null)
        {
            if (freigabe)
            {
                QsFreigabe = true;
                if (!QsFreigabeAm.HasValue)
                {
                    QsFreigabeAm = DateTime.Now;
                }
                if (string.IsNullOrEmpty(QsFreigabeVon))
                {
                    QsFreigabeVon = userID;
                }
            }
            else
            {
                QsFreigabe = null;
                QsFreigabeAm = null;
                QsFreigabeVon = null;
            }
        }
    }
}
