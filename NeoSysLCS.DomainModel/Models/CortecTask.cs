using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace NeoSysLCS.DomainModel.Models
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Web;
    namespace NeoSysLCS.DomainModel.Models
    {
        public class CortecTask
        {
            public int CortecTaskID { get; set; }
            public string ProjectNumber { get; set; }
            public int ProjectID { get; set; }
            public string Title { get; set; }
            public string Description { get; set; }
            public decimal WorkHours { get; set; }
            public int CreatedBy { get; set; }
            public int AssignedTo { get; set; }
            public int TaskStateId { get; set; }
            public int TaskCategoryId { get; set; }
            public int TaskTemplateId { get; set; }
            public int ProficenterId { get; set; }
            public DateTime StartDate { get; set; }
            public DateTime EndDate { get; set; }
            public DateTime TaskScheduledForCreationAt { get; set; }
            public int? ExternalTaskId { get; set; }
            public DateTime? TaskCreatedAt { get; set; }
        }
    }
}