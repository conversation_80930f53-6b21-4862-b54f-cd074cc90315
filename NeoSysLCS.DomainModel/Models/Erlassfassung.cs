using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace NeoSysLCS.DomainModel.Models
{
    public class Erlassfassung : BaseModel
    {
        public Erlassfassung()
        {
            Forderungsversionen = new List<Forderungsversion>();
            Pflichten = new List<Pflicht>();
        }

        public int ErlassfassungID { get; set; }
        public int ErlassID { get; set; }
        public Erlass Erlass { get; set; }
        public DateTime Beschluss { get; set; }
        public DateTime Inkrafttretung { get; set; }

        public int? KommentarID { get; set; }
        public Kommentar Kommentar { get; set; }
        public ICollection<Forderungsversion> Forderungsversionen { get; set; }
        public ICollection<Pflicht> Pflichten { get; set; }

        [Column(TypeName = "xml")]
        public string Uebersetzung { get; set; }
    }
}
