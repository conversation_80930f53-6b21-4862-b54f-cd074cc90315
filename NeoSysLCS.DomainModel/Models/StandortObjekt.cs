using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace NeoSysLCS.DomainModel.Models
{
    public class StandortObjekt : KundenportalBaseModel
    {
        public StandortObjekt()
        {
            IndividuelleForderungen = new List<IndividuelleForderung>();
            KundendokumentForderungen = new List<KundendokumentForderungsversion>();
            KundendokumentPflichten = new List<KundendokumentPflicht>();
        }

        public int StandortObjektID { get; set; }
    
        public int? StandortID { get; set; }
        public Standort Standort { get; set; }

        public int? ObjektID { get; set; }
        public Objekt Objekt { get; set; }

        public ICollection<IndividuelleForderung> IndividuelleForderungen { get; set; }
        public ICollection<KundendokumentForderungsversion> KundendokumentForderungen { get; set; }
        public ICollection<KundendokumentPflicht> KundendokumentPflichten { get; set; }
        public bool? Archiviert { get; set; }
        [Column(TypeName = "xml")]
        public string Uebersetzung { get; set; }

        public Shortcut Shortcut { get; set; }
        public int? ShortcutID { get; set; }

    }
}