using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace NeoSysLCS.DomainModel.Models
{
    public class Kundendokument : KundenportalBaseModel
    {
        public Kundendokument()
        {
            KundendokumentForderungen = new List<KundendokumentForderungsversion>();
            KundendokumentPflichten = new List<KundendokumentPflicht>();
            KundendokumentErlassfassungen = new List<KundendokumentErlassfassung>();
            KundendokumentChecklists = new List<KundendokumentChecklist>();
        }
        public int KundendokumentID { get; set; }
        public DateTime? PublizierenAm { get; set; }
        public string QsKommentar { get; set; }
        public KundendokumentStatus Status { get; set; }
        public int? VorgaengerKundendokumentID { get; set; }

        public int StandortID { get; set; }
        [ForeignKey("StandortID")]
        public Standort Standort { get; set; }

        public KundendokumentSpaltenlabel KundendokumentSpaltenlabel { get; set; }

        public ICollection<KundendokumentForderungsversion> KundendokumentForderungen { get; set; }
        public ICollection<KundendokumentPflicht> KundendokumentPflichten { get; set; }
        public ICollection<KundendokumentErlassfassung> KundendokumentErlassfassungen { get; set; }
        public ICollection<KundendokumentChecklist> KundendokumentChecklists { get; set; }
        public ICollection<IndividuelleForderung> IndividuelleForderungen { get; set; }

    }
}