using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace NeoSysLCS.DomainModel.Models
{
    public partial class Sprache : BaseModel
    {
        public Sprache()
        {
            Standorte = new List<Standort>();
        }

        public int SpracheID { get; set; }
        public string Name { get; set; }

        [Index]
        [MaxLength(10)]
        public string Lokalisierung { get; set; }

        [Index]
        public short Reihenfolge { get; set; }

        public virtual ICollection<Standort> Standorte { get; set; }
    }
}
