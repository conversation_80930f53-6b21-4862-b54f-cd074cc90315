// <auto-generated />
namespace NeoSysLCS.DomainModel.Migrations
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.1.1-30610")]
    public sealed partial class cascadeDeleteKundendokument : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(cascadeDeleteKundendokument));
        
        string IMigrationMetadata.Id
        {
            get { return "201409170956256_cascadeDeleteKundendokument"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
