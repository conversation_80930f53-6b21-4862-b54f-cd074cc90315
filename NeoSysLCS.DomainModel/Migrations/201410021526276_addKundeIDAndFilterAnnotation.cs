namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Collections.Generic;
    using System.Data.Entity.Infrastructure.Annotations;
    using System.Data.Entity.Migrations;
    
    public partial class addKundeIDAndFilterAnnotation : DbMigration
    {
        public override void Up()
        {
            DropIndex("dbo.Kontakt", new[] { "KundeID" });
            DropIndex("dbo.Standort", new[] { "KundeID" });
            AlterTableAnnotations(
                "dbo.Kontakt",
                c => new
                    {
                        KontaktID = c.Int(nullable: false, identity: true),
                        Nachname = c.String(),
                        Vorname = c.String(),
                        EMail = c.String(),
                        Funktion = c.String(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KontaktFilter",
                        new AnnotationValues(oldValue: null, newValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition")
                    },
                });
            
            AlterTableAnnotations(
                "dbo.Standort",
                c => new
                    {
                        StandortID = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        InterneNotiz = c.String(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_StandortFilter",
                        new AnnotationValues(oldValue: null, newValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition")
                    },
                });
            
            AlterTableAnnotations(
                "dbo.IndividuelleForderung",
                c => new
                    {
                        IndividuelleForderungID = c.Int(nullable: false, identity: true),
                        Beschreibung = c.String(),
                        Kommentar = c.String(),
                        GueltigVon = c.DateTime(nullable: false),
                        GueltigBis = c.DateTime(),
                        StandortID = c.Int(),
                        ThemenbereichID = c.Int(),
                        StandortObjektID = c.Int(),
                        KundenbezugID = c.Int(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_IndividuelleForderungen",
                        new AnnotationValues(oldValue: null, newValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition")
                    },
                });
            
            AlterTableAnnotations(
                "dbo.Kundenbezug",
                c => new
                    {
                        KundenbezugID = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        StandortID = c.Int(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundenbezugFilter",
                        new AnnotationValues(oldValue: null, newValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition")
                    },
                });
            
            AlterTableAnnotations(
                "dbo.KundendokumentForderungsversion",
                c => new
                    {
                        KundendokumentForderungsversionID = c.Int(nullable: false, identity: true),
                        Kommentar = c.String(),
                        Erfuellung = c.Boolean(nullable: false),
                        Erfuellungszeitpunkt = c.DateTime(),
                        ErfuelltDurch = c.String(),
                        Verantwortlich = c.String(),
                        Relevant = c.Boolean(nullable: false),
                        Ablageort = c.String(),
                        QsFreigabe = c.Boolean(nullable: false),
                        Spalte1 = c.String(),
                        Spalte2 = c.String(),
                        Spalte3 = c.String(),
                        Spalte4 = c.String(),
                        Spalte5 = c.String(),
                        Spalte6 = c.String(),
                        Spalte7 = c.String(),
                        Spalte8 = c.String(),
                        Spalte9 = c.String(),
                        Spalte10 = c.String(),
                        Status = c.Int(nullable: false),
                        KundendokumentID = c.Int(nullable: false),
                        ForderungsversionID = c.Int(nullable: false),
                        StandortObjektID = c.Int(nullable: false),
                        KundenbezugID = c.Int(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundendokumentForderungsversionFilter",
                        new AnnotationValues(oldValue: null, newValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition")
                    },
                });
            
            AlterTableAnnotations(
                "dbo.Kundendokument",
                c => new
                    {
                        KundendokumentID = c.Int(nullable: false, identity: true),
                        PublizierenAm = c.DateTime(),
                        QsKommentar = c.String(),
                        Status = c.Int(nullable: false),
                        IsInitialeVersion = c.Boolean(nullable: false),
                        StandortID = c.Int(nullable: false),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundendokumentFilter",
                        new AnnotationValues(oldValue: null, newValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition")
                    },
                });
            
            AlterTableAnnotations(
                "dbo.KundendokumentErlassfassung",
                c => new
                    {
                        KundendokumentErlassfassungID = c.Int(nullable: false, identity: true),
                        Betroffen = c.Boolean(nullable: false),
                        QsFreigabe = c.Boolean(nullable: false),
                        Status = c.Int(nullable: false),
                        KundendokumentID = c.Int(nullable: false),
                        ErlassfassungID = c.Int(nullable: false),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundendokumentErlassfassungen",
                        new AnnotationValues(oldValue: null, newValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition")
                    },
                });
            
            AlterTableAnnotations(
                "dbo.KundendokumentIndividuelleForderung",
                c => new
                    {
                        KundendokumentIndividuelleForderungID = c.Int(nullable: false, identity: true),
                        Erfuellung = c.Boolean(nullable: false),
                        Erfuellungszeitpunkt = c.DateTime(),
                        ErfuelltDurch = c.String(),
                        Verantwortlich = c.String(),
                        Ablageort = c.String(),
                        QsFreigabe = c.Boolean(nullable: false),
                        Status = c.Int(nullable: false),
                        IndividuelleForderungID = c.Int(nullable: false),
                        KundendokumentID = c.Int(nullable: false),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundendokumentIndividuelleForderung",
                        new AnnotationValues(oldValue: null, newValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition")
                    },
                });
            
            AlterTableAnnotations(
                "dbo.KundendokumentPflicht",
                c => new
                    {
                        KundendokumentPflichtID = c.Int(nullable: false, identity: true),
                        Kommentar = c.String(),
                        Erfuellung = c.Boolean(nullable: false),
                        Erfuellungszeitpunkt = c.DateTime(),
                        ErfuelltDurch = c.String(),
                        Verantwortlich = c.String(),
                        Relevant = c.Boolean(nullable: false),
                        Ablageort = c.String(),
                        QsFreigabe = c.Boolean(nullable: false),
                        Status = c.Int(nullable: false),
                        KundendokumentID = c.Int(nullable: false),
                        PflichtID = c.Int(nullable: false),
                        StandortObjektID = c.Int(nullable: false),
                        KundenbezugID = c.Int(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundendokumentPflicht",
                        new AnnotationValues(oldValue: null, newValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition")
                    },
                });
            
            AlterTableAnnotations(
                "dbo.StandortObjekt",
                c => new
                    {
                        StandortObjektID = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        Beschreibung = c.String(),
                        StandortID = c.Int(),
                        ObjektID = c.Int(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_StandortObjektFilter",
                        new AnnotationValues(oldValue: null, newValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition")
                    },
                });
            
            AddColumn("dbo.IndividuelleForderung", "KundeID", c => c.Int());
            AddColumn("dbo.Kundenbezug", "KundeID", c => c.Int());
            AddColumn("dbo.KundendokumentForderungsversion", "KundeID", c => c.Int());
            AddColumn("dbo.Kundendokument", "KundeID", c => c.Int());
            AddColumn("dbo.KundendokumentErlassfassung", "KundeID", c => c.Int());
            AddColumn("dbo.KundendokumentIndividuelleForderung", "KundeID", c => c.Int());
            AddColumn("dbo.KundendokumentPflicht", "KundeID", c => c.Int());
            AddColumn("dbo.StandortObjekt", "KundeID", c => c.Int());
            AlterColumn("dbo.Kontakt", "KundeID", c => c.Int());
            AlterColumn("dbo.Standort", "KundeID", c => c.Int());
            CreateIndex("dbo.Kontakt", "KundeID");
            CreateIndex("dbo.Standort", "KundeID");
            CreateIndex("dbo.IndividuelleForderung", "KundeID");
            CreateIndex("dbo.Kundenbezug", "KundeID");
            CreateIndex("dbo.KundendokumentForderungsversion", "KundeID");
            CreateIndex("dbo.Kundendokument", "KundeID");
            CreateIndex("dbo.KundendokumentErlassfassung", "KundeID");
            CreateIndex("dbo.KundendokumentIndividuelleForderung", "KundeID");
            CreateIndex("dbo.KundendokumentPflicht", "KundeID");
            CreateIndex("dbo.StandortObjekt", "KundeID");
            AddForeignKey("dbo.IndividuelleForderung", "KundeID", "dbo.Kunde", "KundeID");
            AddForeignKey("dbo.Kundenbezug", "KundeID", "dbo.Kunde", "KundeID");
            AddForeignKey("dbo.KundendokumentForderungsversion", "KundeID", "dbo.Kunde", "KundeID");
            AddForeignKey("dbo.Kundendokument", "KundeID", "dbo.Kunde", "KundeID");
            AddForeignKey("dbo.KundendokumentErlassfassung", "KundeID", "dbo.Kunde", "KundeID");
            AddForeignKey("dbo.KundendokumentIndividuelleForderung", "KundeID", "dbo.Kunde", "KundeID");
            AddForeignKey("dbo.KundendokumentPflicht", "KundeID", "dbo.Kunde", "KundeID");
            AddForeignKey("dbo.StandortObjekt", "KundeID", "dbo.Kunde", "KundeID");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.StandortObjekt", "KundeID", "dbo.Kunde");
            DropForeignKey("dbo.KundendokumentPflicht", "KundeID", "dbo.Kunde");
            DropForeignKey("dbo.KundendokumentIndividuelleForderung", "KundeID", "dbo.Kunde");
            DropForeignKey("dbo.KundendokumentErlassfassung", "KundeID", "dbo.Kunde");
            DropForeignKey("dbo.Kundendokument", "KundeID", "dbo.Kunde");
            DropForeignKey("dbo.KundendokumentForderungsversion", "KundeID", "dbo.Kunde");
            DropForeignKey("dbo.Kundenbezug", "KundeID", "dbo.Kunde");
            DropForeignKey("dbo.IndividuelleForderung", "KundeID", "dbo.Kunde");
            DropIndex("dbo.StandortObjekt", new[] { "KundeID" });
            DropIndex("dbo.KundendokumentPflicht", new[] { "KundeID" });
            DropIndex("dbo.KundendokumentIndividuelleForderung", new[] { "KundeID" });
            DropIndex("dbo.KundendokumentErlassfassung", new[] { "KundeID" });
            DropIndex("dbo.Kundendokument", new[] { "KundeID" });
            DropIndex("dbo.KundendokumentForderungsversion", new[] { "KundeID" });
            DropIndex("dbo.Kundenbezug", new[] { "KundeID" });
            DropIndex("dbo.IndividuelleForderung", new[] { "KundeID" });
            DropIndex("dbo.Standort", new[] { "KundeID" });
            DropIndex("dbo.Kontakt", new[] { "KundeID" });
            AlterColumn("dbo.Standort", "KundeID", c => c.Int(nullable: false));
            AlterColumn("dbo.Kontakt", "KundeID", c => c.Int(nullable: false));
            DropColumn("dbo.StandortObjekt", "KundeID");
            DropColumn("dbo.KundendokumentPflicht", "KundeID");
            DropColumn("dbo.KundendokumentIndividuelleForderung", "KundeID");
            DropColumn("dbo.KundendokumentErlassfassung", "KundeID");
            DropColumn("dbo.Kundendokument", "KundeID");
            DropColumn("dbo.KundendokumentForderungsversion", "KundeID");
            DropColumn("dbo.Kundenbezug", "KundeID");
            DropColumn("dbo.IndividuelleForderung", "KundeID");
            AlterTableAnnotations(
                "dbo.StandortObjekt",
                c => new
                    {
                        StandortObjektID = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        Beschreibung = c.String(),
                        StandortID = c.Int(),
                        ObjektID = c.Int(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_StandortObjektFilter",
                        new AnnotationValues(oldValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition", newValue: null)
                    },
                });
            
            AlterTableAnnotations(
                "dbo.KundendokumentPflicht",
                c => new
                    {
                        KundendokumentPflichtID = c.Int(nullable: false, identity: true),
                        Kommentar = c.String(),
                        Erfuellung = c.Boolean(nullable: false),
                        Erfuellungszeitpunkt = c.DateTime(),
                        ErfuelltDurch = c.String(),
                        Verantwortlich = c.String(),
                        Relevant = c.Boolean(nullable: false),
                        Ablageort = c.String(),
                        QsFreigabe = c.Boolean(nullable: false),
                        Status = c.Int(nullable: false),
                        KundendokumentID = c.Int(nullable: false),
                        PflichtID = c.Int(nullable: false),
                        StandortObjektID = c.Int(nullable: false),
                        KundenbezugID = c.Int(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundendokumentPflicht",
                        new AnnotationValues(oldValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition", newValue: null)
                    },
                });
            
            AlterTableAnnotations(
                "dbo.KundendokumentIndividuelleForderung",
                c => new
                    {
                        KundendokumentIndividuelleForderungID = c.Int(nullable: false, identity: true),
                        Erfuellung = c.Boolean(nullable: false),
                        Erfuellungszeitpunkt = c.DateTime(),
                        ErfuelltDurch = c.String(),
                        Verantwortlich = c.String(),
                        Ablageort = c.String(),
                        QsFreigabe = c.Boolean(nullable: false),
                        Status = c.Int(nullable: false),
                        IndividuelleForderungID = c.Int(nullable: false),
                        KundendokumentID = c.Int(nullable: false),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundendokumentIndividuelleForderung",
                        new AnnotationValues(oldValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition", newValue: null)
                    },
                });
            
            AlterTableAnnotations(
                "dbo.KundendokumentErlassfassung",
                c => new
                    {
                        KundendokumentErlassfassungID = c.Int(nullable: false, identity: true),
                        Betroffen = c.Boolean(nullable: false),
                        QsFreigabe = c.Boolean(nullable: false),
                        Status = c.Int(nullable: false),
                        KundendokumentID = c.Int(nullable: false),
                        ErlassfassungID = c.Int(nullable: false),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundendokumentErlassfassungen",
                        new AnnotationValues(oldValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition", newValue: null)
                    },
                });
            
            AlterTableAnnotations(
                "dbo.Kundendokument",
                c => new
                    {
                        KundendokumentID = c.Int(nullable: false, identity: true),
                        PublizierenAm = c.DateTime(),
                        QsKommentar = c.String(),
                        Status = c.Int(nullable: false),
                        IsInitialeVersion = c.Boolean(nullable: false),
                        StandortID = c.Int(nullable: false),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundendokumentFilter",
                        new AnnotationValues(oldValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition", newValue: null)
                    },
                });
            
            AlterTableAnnotations(
                "dbo.KundendokumentForderungsversion",
                c => new
                    {
                        KundendokumentForderungsversionID = c.Int(nullable: false, identity: true),
                        Kommentar = c.String(),
                        Erfuellung = c.Boolean(nullable: false),
                        Erfuellungszeitpunkt = c.DateTime(),
                        ErfuelltDurch = c.String(),
                        Verantwortlich = c.String(),
                        Relevant = c.Boolean(nullable: false),
                        Ablageort = c.String(),
                        QsFreigabe = c.Boolean(nullable: false),
                        Spalte1 = c.String(),
                        Spalte2 = c.String(),
                        Spalte3 = c.String(),
                        Spalte4 = c.String(),
                        Spalte5 = c.String(),
                        Spalte6 = c.String(),
                        Spalte7 = c.String(),
                        Spalte8 = c.String(),
                        Spalte9 = c.String(),
                        Spalte10 = c.String(),
                        Status = c.Int(nullable: false),
                        KundendokumentID = c.Int(nullable: false),
                        ForderungsversionID = c.Int(nullable: false),
                        StandortObjektID = c.Int(nullable: false),
                        KundenbezugID = c.Int(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundendokumentForderungsversionFilter",
                        new AnnotationValues(oldValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition", newValue: null)
                    },
                });
            
            AlterTableAnnotations(
                "dbo.Kundenbezug",
                c => new
                    {
                        KundenbezugID = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        StandortID = c.Int(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KundenbezugFilter",
                        new AnnotationValues(oldValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition", newValue: null)
                    },
                });
            
            AlterTableAnnotations(
                "dbo.IndividuelleForderung",
                c => new
                    {
                        IndividuelleForderungID = c.Int(nullable: false, identity: true),
                        Beschreibung = c.String(),
                        Kommentar = c.String(),
                        GueltigVon = c.DateTime(nullable: false),
                        GueltigBis = c.DateTime(),
                        StandortID = c.Int(),
                        ThemenbereichID = c.Int(),
                        StandortObjektID = c.Int(),
                        KundenbezugID = c.Int(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_IndividuelleForderungen",
                        new AnnotationValues(oldValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition", newValue: null)
                    },
                });
            
            AlterTableAnnotations(
                "dbo.Standort",
                c => new
                    {
                        StandortID = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        InterneNotiz = c.String(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_StandortFilter",
                        new AnnotationValues(oldValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition", newValue: null)
                    },
                });
            
            AlterTableAnnotations(
                "dbo.Kontakt",
                c => new
                    {
                        KontaktID = c.Int(nullable: false, identity: true),
                        Nachname = c.String(),
                        Vorname = c.String(),
                        EMail = c.String(),
                        Funktion = c.String(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    },
                annotations: new Dictionary<string, AnnotationValues>
                {
                    { 
                        "DynamicFilter_KontaktFilter",
                        new AnnotationValues(oldValue: "NeoSysLCS.DomainModel.Filter.DynamicFilterDefinition", newValue: null)
                    },
                });
            
            CreateIndex("dbo.Standort", "KundeID");
            CreateIndex("dbo.Kontakt", "KundeID");
        }
    }
}
