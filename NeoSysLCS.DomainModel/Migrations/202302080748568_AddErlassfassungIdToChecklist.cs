namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class AddErlassfassungIdToChecklist : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.KundendokumentChecklist", "ErlassfassungID", c => c.Int(nullable: false));
            AddColumn("dbo.Checklist", "ErlassfassungID", c => c.Int());
            CreateIndex("dbo.Checklist", "ErlassfassungID");
            AddForeignKey("dbo.Checklist", "ErlassfassungID", "dbo.Erlassfassung", "ErlassfassungID");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.Checklist", "ErlassfassungID", "dbo.Erlassfassung");
            DropIndex("dbo.Checklist", new[] { "ErlassfassungID" });
            DropColumn("dbo.Checklist", "ErlassfassungID");
            DropColumn("dbo.KundendokumentChecklist", "ErlassfassungID");
        }
    }
}
