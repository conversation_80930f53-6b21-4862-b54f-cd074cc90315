<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Target" xml:space="preserve">
    <value>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</value>
  </data>
  <data name="DefaultSchema" xml:space="preserve">
    <value>dbo</value>
  </data>
</root>