namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class addConsultation : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.Consultation",
                c => new
                    {
                        ConsultationId = c.Int(nullable: false, identity: true),
                        Title = c.String(),
                        Status = c.Int(nullable: false),
                        EntryDate = c.DateTime(nullable: false),
                        OpenedDate = c.DateTime(),
                        Deadline = c.DateTime(),
                        CompletedDate = c.DateTime(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.ConsultationId)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.ConsultationErlass",
                c => new
                    {
                        Consultation_ConsultationId = c.Int(nullable: false),
                        Erlass_ErlassID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Consultation_ConsultationId, t.Erlass_ErlassID })
                .ForeignKey("dbo.Consultation", t => t.Consultation_ConsultationId)
                .ForeignKey("dbo.Erlass", t => t.Erlass_ErlassID)
                .Index(t => t.Consultation_ConsultationId)
                .Index(t => t.Erlass_ErlassID);
            
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.Consultation", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.ConsultationErlass", "Erlass_ErlassID", "dbo.Erlass");
            DropForeignKey("dbo.ConsultationErlass", "Consultation_ConsultationId", "dbo.Consultation");
            DropForeignKey("dbo.Consultation", "BearbeitetVonID", "dbo.AspNetUsers");
            DropIndex("dbo.ConsultationErlass", new[] { "Erlass_ErlassID" });
            DropIndex("dbo.ConsultationErlass", new[] { "Consultation_ConsultationId" });
            DropIndex("dbo.Consultation", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Consultation", new[] { "ErstelltVonID" });
            DropTable("dbo.ConsultationErlass");
            DropTable("dbo.Consultation");
        }
    }
}
