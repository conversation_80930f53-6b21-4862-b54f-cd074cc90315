// <auto-generated />
namespace NeoSysLCS.DomainModel.Migrations
{
    using System.CodeDom.Compiler;
    using System.Data.Entity.Migrations;
    using System.Data.Entity.Migrations.Infrastructure;
    using System.Resources;
    
    [GeneratedCode("EntityFramework.Migrations", "6.1.1-30610")]
    public sealed partial class removeKundendokumentQsFreigabeFlag : IMigrationMetadata
    {
        private readonly ResourceManager Resources = new ResourceManager(typeof(removeKundendokumentQsFreigabeFlag));
        
        string IMigrationMetadata.Id
        {
            get { return "201409150734274_removeKundendokumentQsFreigabeFlag"; }
        }
        
        string IMigrationMetadata.Source
        {
            get { return null; }
        }
        
        string IMigrationMetadata.Target
        {
            get { return Resources.GetString("Target"); }
        }
    }
}
