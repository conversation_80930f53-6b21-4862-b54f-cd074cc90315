namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class _10spalten : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.KundendokumentForderungsversion", "Spalte1", c => c.String());
            AddColumn("dbo.KundendokumentForderungsversion", "Spalte2", c => c.String());
            AddColumn("dbo.KundendokumentForderungsversion", "Spalte3", c => c.String());
            AddColumn("dbo.KundendokumentForderungsversion", "Spalte4", c => c.String());
            AddColumn("dbo.KundendokumentForderungsversion", "Spalte5", c => c.String());
            AddColumn("dbo.KundendokumentForderungsversion", "Spalte6", c => c.String());
            AddColumn("dbo.KundendokumentForderungsversion", "Spalte7", c => c.String());
            AddColumn("dbo.KundendokumentForderungsversion", "Spalte8", c => c.String());
            AddColumn("dbo.KundendokumentForderungsversion", "Spalte9", c => c.String());
            AddColumn("dbo.KundendokumentForderungsversion", "Spalte10", c => c.String());
        }
        
        public override void Down()
        {
            DropColumn("dbo.KundendokumentForderungsversion", "Spalte10");
            DropColumn("dbo.KundendokumentForderungsversion", "Spalte9");
            DropColumn("dbo.KundendokumentForderungsversion", "Spalte8");
            DropColumn("dbo.KundendokumentForderungsversion", "Spalte7");
            DropColumn("dbo.KundendokumentForderungsversion", "Spalte6");
            DropColumn("dbo.KundendokumentForderungsversion", "Spalte5");
            DropColumn("dbo.KundendokumentForderungsversion", "Spalte4");
            DropColumn("dbo.KundendokumentForderungsversion", "Spalte3");
            DropColumn("dbo.KundendokumentForderungsversion", "Spalte2");
            DropColumn("dbo.KundendokumentForderungsversion", "Spalte1");
        }
    }
}
