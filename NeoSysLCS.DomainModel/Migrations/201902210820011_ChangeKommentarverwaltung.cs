namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class ChangeKommentarverwaltung : DbMigration
    {
        public override void Up()
        {
            DropForeignKey("dbo.KommentarStandortObjekt", "Kommentar_KommentarID", "dbo.Kommentar");
            DropForeignKey("dbo.KommentarStandortObjekt", "StandortObjekt_StandortObjektID", "dbo.StandortObjekt");
            DropIndex("dbo.KommentarStandortObjekt", new[] { "Kommentar_KommentarID" });
            DropIndex("dbo.KommentarStandortObjekt", new[] { "StandortObjekt_StandortObjektID" });
            CreateTable(
                "dbo.KommentarObjekt",
                c => new
                    {
                        Kommentar_KommentarID = c.Int(nullable: false),
                        Objekt_ObjektID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Kommentar_KommentarID, t.Objekt_ObjektID })
                .ForeignKey("dbo.Kommentar", t => t.Kommentar_KommentarID)
                .ForeignKey("dbo.Objekt", t => t.Objekt_ObjektID)
                .Index(t => t.Kommentar_KommentarID)
                .Index(t => t.Objekt_ObjektID);
            
            DropTable("dbo.KommentarStandortObjekt");
        }
        
        public override void Down()
        {
            CreateTable(
                "dbo.KommentarStandortObjekt",
                c => new
                    {
                        Kommentar_KommentarID = c.Int(nullable: false),
                        StandortObjekt_StandortObjektID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Kommentar_KommentarID, t.StandortObjekt_StandortObjektID });
            
            DropForeignKey("dbo.KommentarObjekt", "Objekt_ObjektID", "dbo.Objekt");
            DropForeignKey("dbo.KommentarObjekt", "Kommentar_KommentarID", "dbo.Kommentar");
            DropIndex("dbo.KommentarObjekt", new[] { "Objekt_ObjektID" });
            DropIndex("dbo.KommentarObjekt", new[] { "Kommentar_KommentarID" });
            DropTable("dbo.KommentarObjekt");
            CreateIndex("dbo.KommentarStandortObjekt", "StandortObjekt_StandortObjektID");
            CreateIndex("dbo.KommentarStandortObjekt", "Kommentar_KommentarID");
            AddForeignKey("dbo.KommentarStandortObjekt", "StandortObjekt_StandortObjektID", "dbo.StandortObjekt", "StandortObjektID");
            AddForeignKey("dbo.KommentarStandortObjekt", "Kommentar_KommentarID", "dbo.Kommentar", "KommentarID");
        }
    }
}
