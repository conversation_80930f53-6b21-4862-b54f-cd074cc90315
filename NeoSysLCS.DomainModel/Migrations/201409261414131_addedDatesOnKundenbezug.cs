namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class addedDatesOnKundenbezug : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.Kundenbezug", "ErstelltVonID", c => c.<PERSON>(maxLength: 128));
            AddColumn("dbo.Kundenbezug", "BearbeitetVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.Kundenbezug", "ErstelltAm", c => c.DateTime());
            AddColumn("dbo.Kundenbezug", "BearbeitetAm", c => c.DateTime());
            CreateIndex("dbo.Kundenbezug", "ErstelltVonID");
            CreateIndex("dbo.Kundenbezug", "BearbeitetVonID");
            AddForeignKey("dbo.Kundenbezug", "BearbeitetVonID", "dbo.AspNetUsers", "Id");
            AddForeign<PERSON>ey("dbo.Kundenbezug", "ErstelltVonID", "dbo.AspNetUsers", "Id");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.Kundenbezug", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Kundenbezug", "BearbeitetVonID", "dbo.AspNetUsers");
            DropIndex("dbo.Kundenbezug", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Kundenbezug", new[] { "ErstelltVonID" });
            DropColumn("dbo.Kundenbezug", "BearbeitetAm");
            DropColumn("dbo.Kundenbezug", "ErstelltAm");
            DropColumn("dbo.Kundenbezug", "BearbeitetVonID");
            DropColumn("dbo.Kundenbezug", "ErstelltVonID");
        }
    }
}
