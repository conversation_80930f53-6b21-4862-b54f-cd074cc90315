namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class changeErfuellungToEnum : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.IndividuelleForderung", "ErfuellungsStatus", c => c.Int());
            AddColumn("dbo.KundendokumentForderungsversion", "ErfuellungsStatus", c => c.Int());
            AddColumn("dbo.KundendokumentPflicht", "ErfuellungStatus", c => c.Int());
            DropColumn("dbo.IndividuelleForderung", "Erfuellung");
            DropColumn("dbo.KundendokumentForderungsversion", "Erfuellung");
            DropColumn("dbo.KundendokumentPflicht", "Erfuellung");
        }
        
        public override void Down()
        {
            AddColumn("dbo.KundendokumentPflicht", "Erfuellung", c => c.Bo<PERSON>(nullable: false));
            AddColumn("dbo.KundendokumentForderungsversion", "Erfuellung", c => c.<PERSON>(nullable: false));
            AddColumn("dbo.IndividuelleForderung", "Erfuellung", c => c.Boolean(nullable: false));
            DropColumn("dbo.KundendokumentPflicht", "ErfuellungStatus");
            DropColumn("dbo.KundendokumentForderungsversion", "ErfuellungsStatus");
            DropColumn("dbo.IndividuelleForderung", "ErfuellungsStatus");
        }
    }
}
