namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;

    public partial class UpdateKommentarAddErlass : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.Kommentar", "HauptErlassID", c => c.Int());
            CreateIndex("dbo.Kommentar", "HauptErlassID");
            AddForeignKey("dbo.Kommentar", "HauptErlassID", "dbo.Erlass", "ErlassID");
            AddColumn("dbo.Kommentar", "PLFreigabeDate", c => c.DateTime());
            AddColumn("dbo.Kommentar", "NewsletterDate", c => c.DateTime());
            DropColumn("dbo.Kommentar", "NewsletterVersion");
        }

        public override void Down()
        {
            DropForeignKey("dbo.Kommentar", "HauptErlassID", "dbo.Erlass");
            DropIndex("dbo.Kommentar", new[] { "HauptErlassID" });
            DropColumn("dbo.Kommentar", "HauptErlassID");
            DropColumn("dbo.Kommentar", "PLFreigabeDate");
            DropColumn("dbo.Kommentar", "NewsletterDate");
            AddColumn("dbo.Kommentar", "NewsletterVersion", c => c.String());
        }
    }
}