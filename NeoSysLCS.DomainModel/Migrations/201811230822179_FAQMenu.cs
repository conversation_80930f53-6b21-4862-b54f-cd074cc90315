namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class FAQMenu : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.FAQ",
                c => new
                    {
                        FaqID = c.Int(nullable: false, identity: true),
                        Status = c.Int(nullable: false),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.FaqID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.FAQKategorie",
                c => new
                    {
                        FaqKategorieID = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                    })
                .PrimaryKey(t => t.FaqKategorieID);
            
            CreateTable(
                "dbo.FAQUebersetzung",
                c => new
                    {
                        FaqUebersetzungID = c.Int(nullable: false, identity: true),
                        FaqID = c.Int(nullable: false),
                        SpracheID = c.Int(nullable: false),
                        Frage = c.String(),
                        Quelle = c.String(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.FaqUebersetzungID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .ForeignKey("dbo.FAQ", t => t.FaqID, cascadeDelete: true)
                .ForeignKey("dbo.Sprache", t => t.SpracheID)
                .Index(t => t.FaqID)
                .Index(t => t.SpracheID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.FAQ_FAQKategorie",
                c => new
                    {
                        FAQ_FaqID = c.Int(nullable: false),
                        FAQKategorie_FaqKategorieID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.FAQ_FaqID, t.FAQKategorie_FaqKategorieID })
                .ForeignKey("dbo.FAQ", t => t.FAQ_FaqID)
                .ForeignKey("dbo.FAQKategorie", t => t.FAQKategorie_FaqKategorieID)
                .Index(t => t.FAQ_FaqID)
                .Index(t => t.FAQKategorie_FaqKategorieID);
            
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.FAQUebersetzung", "SpracheID", "dbo.Sprache");
            DropForeignKey("dbo.FAQUebersetzung", "FaqID", "dbo.FAQ");
            DropForeignKey("dbo.FAQUebersetzung", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.FAQUebersetzung", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.FAQFAQKategorie", "FAQKategorie_FaqKategorieID", "dbo.FAQKategorie");
            DropForeignKey("dbo.FAQFAQKategorie", "FAQ_FaqID", "dbo.FAQ");
            DropForeignKey("dbo.FAQ", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.FAQ", "BearbeitetVonID", "dbo.AspNetUsers");
            DropIndex("dbo.FAQFAQKategorie", new[] { "FAQKategorie_FaqKategorieID" });
            DropIndex("dbo.FAQFAQKategorie", new[] { "FAQ_FaqID" });
            DropIndex("dbo.FAQUebersetzung", new[] { "BearbeitetVonID" });
            DropIndex("dbo.FAQUebersetzung", new[] { "ErstelltVonID" });
            DropIndex("dbo.FAQUebersetzung", new[] { "SpracheID" });
            DropIndex("dbo.FAQUebersetzung", new[] { "FaqID" });
            DropIndex("dbo.FAQ", new[] { "BearbeitetVonID" });
            DropIndex("dbo.FAQ", new[] { "ErstelltVonID" });
            DropTable("dbo.FAQFAQKategorie");
            DropTable("dbo.FAQUebersetzung");
            DropTable("dbo.FAQKategorie");
            DropTable("dbo.FAQ");
        }
    }
}
