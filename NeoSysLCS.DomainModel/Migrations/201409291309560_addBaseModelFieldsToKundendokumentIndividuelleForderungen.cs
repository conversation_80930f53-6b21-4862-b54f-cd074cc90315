namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class addBaseModelFieldsToKundendokumentIndividuelleForderungen : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.KundendokumentIndividuelleForderung", "ErstelltVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.KundendokumentIndividuelleForderung", "BearbeitetVonID", c => c.String(maxLength: 128));
            AddColumn("dbo.KundendokumentIndividuelleForderung", "ErstelltAm", c => c.DateTime());
            AddColumn("dbo.KundendokumentIndividuelleForderung", "BearbeitetAm", c => c.DateTime());
            CreateIndex("dbo.KundendokumentIndividuelleForderung", "ErstelltVonID");
            CreateIndex("dbo.KundendokumentIndividuelleForderung", "BearbeitetVonID");
            AddForeignKey("dbo.KundendokumentIndividuelleForderung", "BearbeitetVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.KundendokumentIndividuelleForderung", "ErstelltVonID", "dbo.AspNetUsers", "Id");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.KundendokumentIndividuelleForderung", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.KundendokumentIndividuelleForderung", "BearbeitetVonID", "dbo.AspNetUsers");
            DropIndex("dbo.KundendokumentIndividuelleForderung", new[] { "BearbeitetVonID" });
            DropIndex("dbo.KundendokumentIndividuelleForderung", new[] { "ErstelltVonID" });
            DropColumn("dbo.KundendokumentIndividuelleForderung", "BearbeitetAm");
            DropColumn("dbo.KundendokumentIndividuelleForderung", "ErstelltAm");
            DropColumn("dbo.KundendokumentIndividuelleForderung", "BearbeitetVonID");
            DropColumn("dbo.KundendokumentIndividuelleForderung", "ErstelltVonID");
        }
    }
}
