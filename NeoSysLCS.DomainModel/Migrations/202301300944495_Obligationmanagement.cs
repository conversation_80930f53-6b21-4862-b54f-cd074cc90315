namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class Obligationmanagement : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.Obligation",
                c => new
                    {
                        ObligationID = c.Int(nullable: false, identity: true),
                        ObjektID = c.Int(nullable: false),
                        NameDE = c.String(),
                        NameEN = c.String(),
                        NameFR = c.String(),
                        NameIT = c.String(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.ObligationID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
            CreateTable(
                "dbo.ObligationForderungsversion",
                c => new
                    {
                        Obligation_ObligationID = c.Int(nullable: false),
                        Forderungsversion_ForderungsversionID = c.Int(nullable: false),
                    })
                .PrimaryKey(t => new { t.Obligation_ObligationID, t.Forderungsversion_ForderungsversionID })
                .ForeignKey("dbo.Obligation", t => t.Obligation_ObligationID)
                .ForeignKey("dbo.Forderungsversion", t => t.Forderungsversion_ForderungsversionID)
                .Index(t => t.Obligation_ObligationID)
                .Index(t => t.Forderungsversion_ForderungsversionID);
            
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.ObligationForderungsversion", "Forderungsversion_ForderungsversionID", "dbo.Forderungsversion");
            DropForeignKey("dbo.ObligationForderungsversion", "Obligation_ObligationID", "dbo.Obligation");
            DropForeignKey("dbo.Obligation", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Obligation", "BearbeitetVonID", "dbo.AspNetUsers");
            DropIndex("dbo.ObligationForderungsversion", new[] { "Forderungsversion_ForderungsversionID" });
            DropIndex("dbo.ObligationForderungsversion", new[] { "Obligation_ObligationID" });
            DropIndex("dbo.Obligation", new[] { "BearbeitetVonID" });
            DropIndex("dbo.Obligation", new[] { "ErstelltVonID" });
            DropTable("dbo.ObligationForderungsversion");
            DropTable("dbo.Obligation");
        }
    }
}
