namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class addMassnahmeShortcutID : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.Massnahme", "ShortcutID", c => c.Int());
            CreateIndex("dbo.Massnahme", "ShortcutID");
            AddForeignKey("dbo.Massnahme", "ShortcutID", "dbo.Shortcut", "ShortcutID");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.Massnahme", "ShortcutID", "dbo.Shortcut");
            DropIndex("dbo.Massnahme", new[] { "ShortcutID" });
            DropColumn("dbo.Massnahme", "ShortcutID");
        }
    }
}
