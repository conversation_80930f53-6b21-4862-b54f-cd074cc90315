namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class _refactoringErlasstypAndHerausgeber : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.Erlass", "Uebersetzung", c => c.String());
            AddColumn("dbo.Herausgeber", "Uebersetzung", c => c.String());
            AddColumn("dbo.Herausgeber", "NameDE", c => c.String());
            AddColumn("dbo.Herausgeber", "NameFR", c => c.String());
            AddColumn("dbo.Herausgeber", "NameIT", c => c.String());
            AddColumn("dbo.Herausgeber", "NameEN", c => c.String());
            AddColumn("dbo.Erlasstyp", "TitelDE", c => c.String());
            AddColumn("dbo.Erlasstyp", "TitelFR", c => c.String());
            AddColumn("dbo.Erlasstyp", "TitelIT", c => c.String());
            AddColumn("dbo.Erlasstyp", "TitelEN", c => c.String());
        }
        
        public override void Down()
        {
            DropColumn("dbo.Erlasstyp", "TitelEN");
            DropColumn("dbo.Erlasstyp", "TitelIT");
            DropColumn("dbo.Erlasstyp", "TitelFR");
            DropColumn("dbo.Erlasstyp", "TitelDE");
            DropColumn("dbo.Herausgeber", "NameEN");
            DropColumn("dbo.Herausgeber", "NameIT");
            DropColumn("dbo.Herausgeber", "NameFR");
            DropColumn("dbo.Herausgeber", "NameDE");
            DropColumn("dbo.Herausgeber", "Uebersetzung");
            DropColumn("dbo.Erlass", "Uebersetzung");
        }
    }
}
