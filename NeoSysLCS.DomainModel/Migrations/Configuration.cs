using System;
using Microsoft.AspNet.Identity;
using Microsoft.AspNet.Identity.EntityFramework;

namespace NeoSysLCS.DomainModel.Migrations
{
    using System.Data.Entity;
    using System.Data.Entity.Migrations;
    using System.Linq;
    using NeoSysLCS.DomainModel.Models;
    using System.Collections.Generic;

    internal sealed class Configuration : DbMigrationsConfiguration<NeoSysLCS_Dev>
    {
        public Configuration()
        {
           // Database.SetInitializer(new DropCreateDatabaseIfModelChanges<NeoSysLCS_Dev>());
           // AutomaticMigrationsEnabled = true;
        }

        protected override void Seed(NeoSysLCS_Dev context)
        {
            var roleManager = new RoleManager<ApplicationRole>(new RoleStore<ApplicationRole>(context));
            var sysAdminRole = new ApplicationRole { Name = "ADMIN", Displayname = "Systemadmin Neosys" };
            var superUser = new ApplicationRole { Name = "SUPERUSER", Displayname = "Systemadministrator" };
            var enduser = new ApplicationRole { Name = "ENDUSER", Displayname = "Poweruser" };
            var readonlyUser = new ApplicationRole {Name ="READONLY", Displayname = "User" };

            if (!roleManager.RoleExists("READONLY"))
            {
                roleManager.Create(readonlyUser);
            }
            else
            {
                roleManager.Update(readonlyUser);
            }


            if (!roleManager.RoleExists("SUPERUSER"))
            {
                roleManager.Create(superUser);
            }
            else
            {
                roleManager.Update(superUser);
            }


            if (!roleManager.RoleExists("ENDUSER"))
            {
                roleManager.Create(enduser);
            }
            else
            {
                roleManager.Update(enduser);
            }

            if (!roleManager.RoleExists("ADMIN"))
            {
                roleManager.Create(sysAdminRole);
            }
            {
                roleManager.Update(sysAdminRole);
            }

            var auditor = new ApplicationRole { Name = "AUDITOR", Displayname = "Auditor" };
            if (!roleManager.RoleExists("AUDITOR"))
            {
                roleManager.Create(auditor);
            }
            {
                roleManager.Update(auditor);
            }

            var accountable = new ApplicationRole { Name = "ACCOUNTABLE", Displayname = "Editor" };
            if (!roleManager.RoleExists("ACCOUNTABLE"))
            {
                roleManager.Create(accountable);
            }
            {
                roleManager.Update(accountable);
            }

            context.FAQKategorie.AddOrUpdate(new FAQKategorie { FaqKategorieID = 1, Name = "Dashboard", Systemname = "Dashboard" });
            context.FAQKategorie.AddOrUpdate(new FAQKategorie { FaqKategorieID = 2, Name = "Forderungen", Systemname = "Forderungen" });
            context.FAQKategorie.AddOrUpdate(new FAQKategorie { FaqKategorieID = 3, Name = "Gesetzesliste", Systemname = "Gesetzesliste" });
            context.FAQKategorie.AddOrUpdate(new FAQKategorie { FaqKategorieID = 4, Name = "Entfernte Forderungen", Systemname = "EntfernteForderungen" });
            context.FAQKategorie.AddOrUpdate(new FAQKategorie { FaqKategorieID = 5, Name = "Individuelle Forderungen", Systemname = "IndividuelleForderungen" });
            context.FAQKategorie.AddOrUpdate(new FAQKategorie { FaqKategorieID = 6, Name = "Verantwortlichkeiten", Systemname = "Verantwortlichkeiten" });
            context.FAQKategorie.AddOrUpdate(new FAQKategorie { FaqKategorieID = 7, Name = "Masteransicht", Systemname = "Masteransicht" });
            context.FAQKategorie.AddOrUpdate(new FAQKategorie { FaqKategorieID = 8, Name = "Spalten Beschriftung", Systemname = "SpaltenBeschriftung" });
            context.FAQKategorie.AddOrUpdate(new FAQKategorie { FaqKategorieID = 9, Name = "StandortObjekte", Systemname = "StandortObjekte" });
            context.FAQKategorie.AddOrUpdate(new FAQKategorie { FaqKategorieID = 10, Name = "Benutzerverwaltung", Systemname = "Benutzerverwaltung" });
            context.FAQKategorie.AddOrUpdate(new FAQKategorie { FaqKategorieID = 11, Name = "Diagramme", Systemname = "Diagramme" });
            context.FAQKategorie.AddOrUpdate(new FAQKategorie { FaqKategorieID = 12, Name = "�bersicht", Systemname = "Uebersicht" });
            context.FAQKategorie.AddOrUpdate(new FAQKategorie { FaqKategorieID = 13, Name = "Standorte", Systemname = "Standorte" });
            context.FAQKategorie.AddOrUpdate(new FAQKategorie { FaqKategorieID = 14, Name = "Standortverwaltung", Systemname = "Standortverwaltung" });
        }

        private void populateDummyData(NeoSysLCS_Dev context)
        {
        }
    }
}
