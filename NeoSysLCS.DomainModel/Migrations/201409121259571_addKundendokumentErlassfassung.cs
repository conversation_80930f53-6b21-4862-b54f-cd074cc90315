namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class addKundendokumentErlassfassung : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.KundendokumentErlassfassung",
                c => new
                    {
                        KundendokumentErlassfassungID = c.Int(nullable: false, identity: true),
                        Relevant = <PERSON><PERSON>(nullable: false),
                        QsFreigabe = <PERSON><PERSON>(nullable: false),
                        Status = c.Int(nullable: false),
                        KundendokumentID = c.Int(nullable: false),
                        ErlassfassungID = c.Int(nullable: false),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.KundendokumentErlassfassungID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.Erlassfassung", t => t.ErlassfassungID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .ForeignKey("dbo.Kundendokument", t => t.KundendokumentID)
                .Index(t => t.KundendokumentID)
                .Index(t => t.ErlassfassungID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.KundendokumentErlassfassung", "KundendokumentID", "dbo.Kundendokument");
            DropForeignKey("dbo.KundendokumentErlassfassung", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.KundendokumentErlassfassung", "ErlassfassungID", "dbo.Erlassfassung");
            DropForeignKey("dbo.KundendokumentErlassfassung", "BearbeitetVonID", "dbo.AspNetUsers");
            DropIndex("dbo.KundendokumentErlassfassung", new[] { "BearbeitetVonID" });
            DropIndex("dbo.KundendokumentErlassfassung", new[] { "ErstelltVonID" });
            DropIndex("dbo.KundendokumentErlassfassung", new[] { "ErlassfassungID" });
            DropIndex("dbo.KundendokumentErlassfassung", new[] { "KundendokumentID" });
            DropTable("dbo.KundendokumentErlassfassung");
        }
    }
}
