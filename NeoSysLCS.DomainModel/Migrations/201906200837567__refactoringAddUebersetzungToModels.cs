namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class _refactoringAddUebersetzungToModels : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.Erlassfassung", "Uebersetzung", c => c.String());
            AddColumn("dbo.StandortObjekt", "Uebersetzung", c => c.String());
            AddColumn("dbo.Objekt", "Uebersetzung", c => c.String());
            AddColumn("dbo.Kommentar", "Uebersetzung", c => c.String());
            AddColumn("dbo.FAQ", "Uebersetzung", c => c.String());
        }
        
        public override void Down()
        {
            DropColumn("dbo.FAQ", "Uebersetzung");
            DropColumn("dbo.Kommentar", "Uebersetzung");
            DropColumn("dbo.Objekt", "Uebersetzung");
            DropColumn("dbo.StandortObjekt", "Uebersetzung");
            DropColumn("dbo.Erlassfassung", "Uebersetzung");
        }
    }
}
