namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class added_Lang_indexes : DbMigration
    {
        public override void Up()
        {
            AlterColumn("dbo.Sprache", "Lokalisierung", c => c.String(maxLength: 10));
            CreateIndex("dbo.Sprache", "Lokalisierung");
            CreateIndex("dbo.Sprache", "Reihenfolge");
        }
        
        public override void Down()
        {
            DropIndex("dbo.Sprache", new[] { "Reihenfolge" });
            DropIndex("dbo.Sprache", new[] { "Lokalisierung" });
            AlterColumn("dbo.Sprache", "Lokalisierung", c => c.String());
        }
    }
}
