namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class addPrivacyPolicy : DbMigration
    {
        public override void Up()
        {
            CreateTable(
                "dbo.PrivacyPolicy",
                c => new
                    {
                        ID = c.Int(nullable: false, identity: true),
                        ChangedAt = c.DateTime(nullable: false),
                        Uebersetzung = c.String(storeType: "xml"),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.ID)
                .ForeignKey("dbo.AspNetUsers", t => t.BearbeitetVonID)
                .ForeignKey("dbo.AspNetUsers", t => t.ErstelltVonID)
                .Index(t => t.ErstelltVonID)
                .Index(t => t.BearbeitetVonID);
            
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.PrivacyPolicy", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.PrivacyPolicy", "BearbeitetVonID", "dbo.AspNetUsers");
            DropIndex("dbo.PrivacyPolicy", new[] { "BearbeitetVonID" });
            DropIndex("dbo.PrivacyPolicy", new[] { "ErstelltVonID" });
            DropTable("dbo.PrivacyPolicy");
        }
    }
}
