namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class UpdateErlassfassung : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.ErlassfassungUebersetzung", "LinkBetroffenKommentar", c => c.String());
            AddColumn("dbo.ErlassfassungUebersetzung", "LinkNichtBetroffenKommentar", c => c.String());
            AlterColumn("dbo.Kommentar", "Beschluss", c => c.DateTime(nullable: false));
            AlterColumn("dbo.Kommentar", "Inkrafttretung", c => c.DateTime(nullable: false));
        }
        
        public override void Down()
        {
            AlterColumn("dbo.Kommentar", "Inkrafttretung", c => c.DateTime());
            AlterColumn("dbo.Kommentar", "Beschluss", c => c.DateTime());
            DropColumn("dbo.ErlassfassungUebersetzung", "LinkNichtBetroffenKommentar");
            DropColumn("dbo.ErlassfassungUebersetzung", "LinkBetroffenKommentar");
        }
    }
}
