namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class KundenbezugAsString : DbMigration
    {
        public override void Up()
        {
            DropForeignKey("dbo.Kundenbezug", "BearbeitetVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.Kundenbezug", "ErstelltVonID", "dbo.AspNetUsers");
            DropForeignKey("dbo.IndividuelleForderung", "KundenbezugID", "dbo.Kundenbezug");
            DropForeignKey("dbo.Kundenbezug", "KundeID", "dbo.Kunde");
            DropForeignKey("dbo.KundendokumentForderungsversion", "KundenbezugID", "dbo.Kundenbezug");
            DropForeignKey("dbo.KundendokumentPflicht", "KundenbezugID", "dbo.Kundenbezug");
            DropForeignKey("dbo.Kundenbezug", "StandortID", "dbo.Standort");
            DropIndex("dbo.IndividuelleForderung", new[] { "KundenbezugID" });
            DropIndex("dbo.Kundenbezug", new[] { "StandortID" });
            DropIndex("dbo.Kundenbezug", new[] { "KundeID" });
            DropIndex("dbo.Kundenbezug", new[] { "ErstelltVonID" });
            DropIndex("dbo.Kundenbezug", new[] { "BearbeitetVonID" });
            DropIndex("dbo.KundendokumentForderungsversion", new[] { "KundenbezugID" });
            DropIndex("dbo.KundendokumentPflicht", new[] { "KundenbezugID" });
            AddColumn("dbo.IndividuelleForderung", "Kundenbezug", c => c.String());
            AddColumn("dbo.KundendokumentForderungsversion", "Kundenbezug", c => c.String());
            AddColumn("dbo.KundendokumentPflicht", "Kundenbezug", c => c.String());
            DropColumn("dbo.IndividuelleForderung", "KundenbezugID");
            DropColumn("dbo.KundendokumentForderungsversion", "KundenbezugID");
            DropColumn("dbo.KundendokumentPflicht", "KundenbezugID");
            DropTable("dbo.Kundenbezug");
        }
        
        public override void Down()
        {
            CreateTable(
                "dbo.Kundenbezug",
                c => new
                    {
                        KundenbezugID = c.Int(nullable: false, identity: true),
                        Name = c.String(),
                        StandortID = c.Int(),
                        KundeID = c.Int(),
                        ErstelltVonID = c.String(maxLength: 128),
                        BearbeitetVonID = c.String(maxLength: 128),
                        ErstelltAm = c.DateTime(),
                        BearbeitetAm = c.DateTime(),
                    })
                .PrimaryKey(t => t.KundenbezugID);
            
            AddColumn("dbo.KundendokumentPflicht", "KundenbezugID", c => c.Int());
            AddColumn("dbo.KundendokumentForderungsversion", "KundenbezugID", c => c.Int());
            AddColumn("dbo.IndividuelleForderung", "KundenbezugID", c => c.Int());
            DropColumn("dbo.KundendokumentPflicht", "Kundenbezug");
            DropColumn("dbo.KundendokumentForderungsversion", "Kundenbezug");
            DropColumn("dbo.IndividuelleForderung", "Kundenbezug");
            CreateIndex("dbo.KundendokumentPflicht", "KundenbezugID");
            CreateIndex("dbo.KundendokumentForderungsversion", "KundenbezugID");
            CreateIndex("dbo.Kundenbezug", "BearbeitetVonID");
            CreateIndex("dbo.Kundenbezug", "ErstelltVonID");
            CreateIndex("dbo.Kundenbezug", "KundeID");
            CreateIndex("dbo.Kundenbezug", "StandortID");
            CreateIndex("dbo.IndividuelleForderung", "KundenbezugID");
            AddForeignKey("dbo.Kundenbezug", "StandortID", "dbo.Standort", "StandortID");
            AddForeignKey("dbo.KundendokumentPflicht", "KundenbezugID", "dbo.Kundenbezug", "KundenbezugID");
            AddForeignKey("dbo.KundendokumentForderungsversion", "KundenbezugID", "dbo.Kundenbezug", "KundenbezugID");
            AddForeignKey("dbo.Kundenbezug", "KundeID", "dbo.Kunde", "KundeID");
            AddForeignKey("dbo.IndividuelleForderung", "KundenbezugID", "dbo.Kundenbezug", "KundenbezugID");
            AddForeignKey("dbo.Kundenbezug", "ErstelltVonID", "dbo.AspNetUsers", "Id");
            AddForeignKey("dbo.Kundenbezug", "BearbeitetVonID", "dbo.AspNetUsers", "Id");
        }
    }
}
