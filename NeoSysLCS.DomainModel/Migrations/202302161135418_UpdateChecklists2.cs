namespace NeoSysLCS.DomainModel.Migrations
{
    using System;
    using System.Data.Entity.Migrations;
    
    public partial class UpdateChecklists2 : DbMigration
    {
        public override void Up()
        {
            AddColumn("dbo.ChecklistQuestion", "ChecklistHeaderID", c => c.Int(nullable: false));
            CreateIndex("dbo.ChecklistQuestion", "ChecklistHeaderID");
            AddForeign<PERSON>ey("dbo.ChecklistQuestion", "ChecklistHeaderID", "dbo.ChecklistHeader", "ChecklistHeaderID");
        }
        
        public override void Down()
        {
            DropForeignKey("dbo.ChecklistQuestion", "ChecklistHeader<PERSON>", "dbo.ChecklistHeader");
            DropIndex("dbo.ChecklistQuestion", new[] { "ChecklistHeaderID" });
            DropColumn("dbo.ChecklistQuestion", "ChecklistHeaderID");
        }
    }
}
